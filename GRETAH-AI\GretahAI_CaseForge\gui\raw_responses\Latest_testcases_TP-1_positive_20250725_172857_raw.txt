```json
[
  {
    "scenario_name": "Successful Login After Account Creation",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify that a user can successfully log in to the system with valid credentials after account creation.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "The username should be successfully entered into the username field."},
      {"action": "Verify if user is able to enter valid password in the password field", "expected_result": "The password should be successfully entered into the password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "The user should be redirected to the home page."},
      {"action": "Verify if user is able to view the welcome message upon successful login", "expected_result": "The welcome message should be displayed on the home page."}
    ]
  },
  {
    "scenario_name": "Successful Logout",
    "type": "positive",
    "prerequisites": "User should have already successfully logged into the system.",
    "Test Case Objective": "Verify that a logged-in user can successfully log out of the system.",
    "steps": [
      {"action": "Verify if user is able to navigate to the user profile page", "expected_result": "The user profile page should be displayed."},
      {"action": "Verify if user is able to locate and click the 'Logout' button", "expected_result": "The user should be successfully redirected to the login page."},
      {"action": "Verify if user is able to confirm that all session cookies are cleared", "expected_result": "The user should not be automatically logged back in upon returning to the site."},
      {"action": "Verify if user is able to attempt to access a restricted page after logout", "expected_result": "The system should redirect the user to the login page."}
    ]
  }
]
```