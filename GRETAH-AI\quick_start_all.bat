@echo off
echo ========================================
echo     GRETAH-AI Suite Management Script
echo ========================================
echo.
echo This script provides comprehensive management for all GRETAH-AI applications:
echo.
echo 1. GretahAI CaseForge    - Test case generation and management
echo 2. GretahAI ScriptWeaver - PyTest script generation
echo 3. GretahAI TestInsight  - Test execution analysis and reporting
echo 4. Install All Dependencies
echo 5. Start All Applications (in separate windows)
echo 6. Check System Requirements
echo 7. Exit
echo.

:menu
set /p choice="Please select an option (1-7): "

if "%choice%"=="1" goto caseforge
if "%choice%"=="2" goto scriptweaver
if "%choice%"=="3" goto testinsight
if "%choice%"=="4" goto installall
if "%choice%"=="5" goto startall
if "%choice%"=="6" goto checkall
if "%choice%"=="7" goto exit
echo Invalid choice. Please try again.
echo.
goto menu

:caseforge
echo.
echo Starting GretahAI CaseForge...
echo.
cd GretahAI_CaseForge
call quick_start.bat
cd ..
goto end

:scriptweaver
echo.
echo Starting GretahAI ScriptWeaver...
echo.
cd GretahAI_ScriptWeaver
call quick_start.bat
cd ..
goto end

:testinsight
echo.
echo Starting GretahAI TestInsight...
echo.
cd GretahAI_TestInsight
call quick_start.bat
cd ..
goto end

:installall
echo.
echo ========================================
echo   Installing All Dependencies
echo ========================================
echo.

echo Installing CaseForge dependencies...
cd GretahAI_CaseForge
call quick_start.bat
echo 1 | call quick_start.bat >nul 2>&1
cd ..

echo Installing ScriptWeaver dependencies...
cd GretahAI_ScriptWeaver
echo 1 | call quick_start.bat >nul 2>&1
cd ..

echo Installing TestInsight dependencies...
cd GretahAI_TestInsight
echo 1 | call quick_start.bat >nul 2>&1
cd ..

echo.
echo ✅ All dependencies installed successfully!
echo.
pause
goto menu

:checkall
echo.
echo ========================================
echo   System Requirements Check (All Apps)
echo ========================================
echo.

echo Checking CaseForge requirements...
cd GretahAI_CaseForge
echo 5 | call quick_start.bat
cd ..

echo.
echo Checking ScriptWeaver requirements...
cd GretahAI_ScriptWeaver
echo 5 | call quick_start.bat
cd ..

echo.
echo Checking TestInsight requirements...
cd GretahAI_TestInsight
echo 5 | call quick_start.bat
cd ..

echo.
pause
goto menu



:startall
echo.
echo Starting all GRETAH-AI applications...
echo Each application will open in a separate command window.
echo.

REM Start CaseForge in new window
echo Starting CaseForge...
start "GretahAI CaseForge" cmd /k "cd GretahAI_CaseForge && echo 2 | quick_start.bat"

REM Wait a moment before starting next app
timeout /t 3 /nobreak >nul

REM Start ScriptWeaver in new window
echo Starting ScriptWeaver...
start "GretahAI ScriptWeaver" cmd /k "cd GretahAI_ScriptWeaver && echo 2 | quick_start.bat"

REM Wait a moment before starting next app
timeout /t 3 /nobreak >nul

REM Start TestInsight in new window
echo Starting TestInsight...
start "GretahAI TestInsight" cmd /k "cd GretahAI_TestInsight && echo 2 | quick_start.bat"

echo.
echo All applications are starting in separate windows.
echo Each will open in your default web browser on different ports.
echo.
echo Typical URLs:
echo - CaseForge:    http://localhost:8501
echo - ScriptWeaver: http://localhost:8502
echo - TestInsight:  http://localhost:8503
echo.
echo To stop any application, close its command window or press Ctrl+C.
echo.
goto end

:exit
echo Goodbye!
goto end

:end
echo.
echo Press any key to close this window...
pause >nul
