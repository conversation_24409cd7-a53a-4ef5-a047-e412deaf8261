# Render the Test Execution section of the Streamlit application, encompassing test suite upload, execution controls, real‑time monitoring, result processing, AI‑powered summaries and comparisons, and historical visualizations.
#     st_obj (module or object): The Streamlit instance used to render UI components.
#     db_path (str): Path to the SQLite database for storing and retrieving test run data and AI summaries.
#     base_dir (str): Directory where uploaded test suites, result XML files, and artifacts are stored.
#     logs_dir (str): Directory where execution logs are written and accessed during test runs.
#     reports_dir (str): Directory where generated PDF reports and other exports are saved.
#     debug_dir (str): Directory for saving debug output (e.g., raw collection logs on parse failure).
#     render_test_case_details_func (callable): Callback to render detailed information for a single test case.
# This function provides:
#     - Sidebar controls for uploading a pytest suite and launching or terminating test runs.
#     - Execution Monitor tab showing live status, logs, and result file detection.
#     - Results Dashboard tab that processes JUnit XML, persists results to the database, and resets state.
#     - Metrics summary and PDF report generation for the latest run.
#     - Bulk or selective AI summary generation for all or failed test cases.
#     - Selection of previous runs and classes for AI‑driven comparison summaries with trend analysis.
#     - Interactive expanders for side‑by‑side comparison of fixed, regressed, still failing, and new tests.
#     - Time‑series plots and tabular display of historical pass/fail counts and pass rate trends.

import streamlit as st
import subprocess
import os
import time
import re
from datetime import datetime
import signal
import pandas as pd
import plotly.express as px
import json
import sqlite3
from pathlib import Path

# Import helper functions (assuming helper.py and sql_lite_db_helpers.py are accessible)
from helper import (
    parse_junit_xml,
    format_timestamp_display,
    generate_and_save_summary_if_missing,
    generate_comparison_summary,
    safe_parse_json, # Assuming safe_parse_json is in helper
    extract_json_dict # Assuming extract_json_dict is in helper
)
from sql_lite_db_helpers import (
    save_test_results,
    load_test_run_history,
    get_test_run_details,
    compare_test_results,
    find_run_id_by_timestamp,
    get_comparison_summary,
    save_summary,
    get_classnames_for_run,
    find_previous_run_with_class,
    find_case_id,
    get_single_test_case_details,
    close_thread_local_connection
)
from report_generator import generate_report # Assuming report_generator.py is accessible

# Note: render_test_case_details is passed as an argument (render_test_case_details_func)

def render_test_execution(st_obj, db_path, base_dir, logs_dir, reports_dir, debug_dir, render_test_case_details_func):
    """
    Renders the Test Execution section of the application.

    Args:
        st_obj: The Streamlit object (st).
        db_path (str): Path to the SQLite database.
        base_dir (str): Base directory for test execution.
        logs_dir (str): Directory where logs are stored.
        reports_dir (str): Directory where reports are saved.
        debug_dir (str): Directory for debug output.
        render_test_case_details_func (callable): Function to render individual test case details.
    """
    st = st_obj # Use the passed Streamlit object
    DATABASE_PATH = db_path
    BASE_DIR = base_dir
    LOGS_DIR = logs_dir
    REPORTS_DIR = reports_dir
    debug_output_dir = debug_dir
    render_test_case_details = render_test_case_details_func # Assign the passed function

    st.markdown('<h1 class="main-header">Gretah AI TestInsight Pro - Execution Dashboard</h1>', unsafe_allow_html=True)

    st.markdown("""
    <div class="feature-panel">
    <strong>Automated Test Runner:</strong> Execute Pytest suites, monitor progress in real-time, and access detailed execution reports.
    </div>
    """, unsafe_allow_html=True)

    # --- Sidebar Controls (Now part of the main sidebar rendering) ---
    # The execution-specific controls remain here, but are placed within the sidebar context
    with st.sidebar:
        st.header("Execution Controls")
        test_suite_file = st.file_uploader("Select Test Suite (.py)", type=["py"], key="test_suite_upload",
                                           help="Upload Pytest file for execution")

        if st.button("▶️ Run Test Suite", help="Start test execution"):
            if test_suite_file:
                original_filename = test_suite_file.name
                test_suite_path = os.path.join(BASE_DIR, original_filename)
                with open(test_suite_path, "wb") as f:
                    f.write(test_suite_file.read())

                total_tests_collected = None
                collection_failed = False  # Flag to track collection failure
                try:
                    collect_command = f'cmd /c "cd /d {BASE_DIR} && pytest --collect-only -q {original_filename}"'
                    st.info(f"Collecting tests from {original_filename}...")
                    collect_result = subprocess.run(
                        collect_command,
                        shell=True,
                        capture_output=True,
                        text=True,
                        timeout=60  # Add a timeout for collection
                    )

                    if collect_result.returncode == 0:
                        output_lines = collect_result.stdout.strip().splitlines()
                        if output_lines:
                            last_line = output_lines[-1]
                            match = re.search(r"(\d+)\s+tests?\s+collected", last_line)
                            if match:
                                total_tests_collected = int(match.group(1))
                                st.success(f"Found {total_tests_collected} tests.")
                            else:
                                debug_filename = f"collect_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
                                debug_filepath = os.path.join(debug_output_dir, debug_filename)
                                try:
                                    with open(debug_filepath, "w", encoding="utf-8") as f:
                                        f.write("--- STDOUT ---\n")
                                        f.write(collect_result.stdout)
                                        f.write("\n--- STDERR ---\n")
                                        f.write(collect_result.stderr)
                                    st.warning(f"Could not parse test count. Raw output saved to: {debug_filepath}")
                                except Exception as save_err:
                                    st.warning(f"Could not parse test count. Also failed to save debug output: {save_err}")
                                print(f"DEBUG: Collection output:\n{collect_result.stdout}")  # Keep console debug
                        else:
                            st.warning("Test collection produced no output.")
                    else:
                        collection_failed = True  # Mark collection as failed
                        st.error(f"Test collection failed (Code: {collect_result.returncode}):")
                        st.code(collect_result.stderr or collect_result.stdout)  # Show error or output

                except subprocess.TimeoutExpired:
                    collection_failed = True  # Mark collection as failed
                    st.error("Test collection timed out.")
                    total_tests_collected = None  # Ensure it's None
                except Exception as collect_err:
                    collection_failed = True  # Mark collection as failed
                    st.error(f"Error during test collection: {collect_err}")
                    total_tests_collected = None  # Ensure it's None

                if not collection_failed:
                    st.session_state["last_execution_time"] = time.time()
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    result_xml_path = f"results_{timestamp}.xml"
                    command = (
                        f'cmd /c "cd /d {BASE_DIR} && '
                        f'pytest {original_filename} '
                        f'--junitxml={result_xml_path} '
                        f'--log-cli-level=DEBUG '
                        f'--capture=sys '
                    )
                    st.session_state["process"] = subprocess.Popen(
                        command,
                        shell=True,
                        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                    )
                    st.session_state["current_test_run"] = {
                        "timestamp": timestamp,
                        "result_path": os.path.join(BASE_DIR, result_xml_path),
                        "total_tests_collected": total_tests_collected
                    }
                    st.session_state.process_finished_flag = False
                    st.success(f"🚀 Test execution initiated: {original_filename}")
                    st.rerun()
            else:
                st.warning("⚠️ No test suite selected. Please upload a file.")

        process = st.session_state.get("process")
        if process and process.poll() is None:
            if st.button("⏹️ Terminate Run", help="Stop the current test execution"):
                try:
                    os.kill(process.pid, signal.CTRL_BREAK_EVENT)
                    try:
                        process.wait(timeout=0.5)
                    except subprocess.TimeoutExpired:
                        process.terminate()
                    st.session_state["process"] = None
                    st.session_state["current_test_run"] = None
                    st.session_state.process_finished_flag = False
                    st.warning("⚠️ Test execution terminated by user. Results may be incomplete.")
                    st.rerun()
                except ProcessLookupError:
                    st.warning("Process already finished or could not be found.")
                    st.session_state["process"] = None
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Termination failed: {e}")

    test_tabs = st.tabs(["Execution Monitor", "Results Dashboard"])

    with test_tabs[0]:
        st.markdown('<h3>Execution Monitor</h3>', unsafe_allow_html=True)
        process = st.session_state.get("process")
        current_run_info = st.session_state.get("current_test_run")
        xml_path = current_run_info["result_path"] if current_run_info else None
        xml_exists = xml_path and os.path.exists(xml_path)
        is_running = process and process.poll() is None

        collected_count = current_run_info.get("total_tests_collected") if current_run_info else None

        col1, col2 = st.columns([3, 1])
        with col1:
            if is_running and not xml_exists:
                if collected_count is not None:
                    st.info(f"⏳ Test execution in progress ({collected_count} tests). Logs available in '{LOGS_DIR}'.")
                else:
                    st.info(f"⏳ Test execution in progress. Logs available in '{LOGS_DIR}'.")
                time.sleep(1)
                st.rerun()
            elif xml_exists:
                display_path = os.path.relpath(xml_path, BASE_DIR) if xml_path else "results file"
                st.success(f"✅ Test execution complete. Results file generated: {display_path}")
                if st.button("Process Results", key="process_now"):
                    st.session_state.process_finished_flag = True
                    if process and process.poll() is None:
                        try:
                            process.wait(timeout=1)
                        except subprocess.TimeoutExpired:
                            pass
                    st.rerun()
            elif st.session_state.get("last_execution_time"):
                st.info("📋 Last execution finished. Check logs for details.")
            else:
                st.info("💡 Ready for execution. Upload and run a test suite.")
        with col2:
            if st.button("🔄 Refresh", key="refresh_status", help="Refresh execution status"):
                if process:
                    process_poll_result = process.poll()
                    if process_poll_result is not None:
                        st.session_state.process_finished_flag = True
                    elif current_run_info and os.path.exists(current_run_info["result_path"]):
                        st.session_state.process_finished_flag = True
                st.rerun()

    with test_tabs[1]:
        st.markdown('<h3>Results Dashboard</h3>', unsafe_allow_html=True)

        process = st.session_state.get("process")
        current_run_info = st.session_state.get("current_test_run")
        process_poll_result = None

        if process:
            process_poll_result = process.poll()
            xml_path = current_run_info["result_path"] if current_run_info else None
            if xml_path and os.path.exists(xml_path):
                st.session_state.process_finished_flag = True

            if process_poll_result is not None and not st.session_state.process_finished_flag:
                st.session_state.process_finished_flag = True

        if st.session_state.process_finished_flag:
            if current_run_info:
                result_path = current_run_info["result_path"]
                if os.path.exists(result_path):
                    try:
                        with st.spinner("Processing and saving test results..."):
                            xml_data = parse_junit_xml(result_path)
                            timestamp = current_run_info["timestamp"]
                            save_result = save_test_results(DATABASE_PATH, xml_data, timestamp)
                            if save_result:
                                st.success(f"Test results for run {timestamp} saved to database and artifacts linked.")
                            else:
                                st.error("Failed to save test results to database.")
                            st.session_state["current_test_run"] = None
                            st.session_state["process"] = None
                            st.session_state.process_finished_flag = False
                            close_thread_local_connection()
                            st.rerun()
                    except Exception as e:
                        st.error(f"Error processing test results: {e}")
                        st.session_state["current_test_run"] = None
                        st.session_state["process"] = None
                        st.session_state.process_finished_flag = False
                        close_thread_local_connection()
                        st.rerun()
                else:
                    display_path = os.path.relpath(result_path, BASE_DIR) if result_path else "results file"
                    st.error(f"Test process finished, but result file not found at expected path: {display_path}. Cannot save results.")
                    st.session_state["current_test_run"] = None
                    st.session_state["process"] = None
                    st.session_state.process_finished_flag = False
                    close_thread_local_connection()
                    st.rerun()
            else:
                st.session_state["process"] = None
                st.session_state.process_finished_flag = False
                close_thread_local_connection()
                st.rerun()

        test_history_db = load_test_run_history(DATABASE_PATH, limit=20)
        close_thread_local_connection()

        if not test_history_db:
            st.info("📊 No test run data available. Execute a test suite to populate the dashboard.")
        else:
            latest_run = test_history_db[0]
            latest_run_display_ts = format_timestamp_display(latest_run['timestamp'])
            st.markdown(f"### Latest Run Summary: {latest_run_display_ts}")

            # Test Results Metrics
            st.markdown("#### Test Results")
            col_summary1, col_summary2, col_summary3, col_summary4, col_actions = st.columns([1, 1, 1, 1, 2])  # Renamed last column
            with col_summary1:
                st.metric("Total Cases", latest_run.get("total_tests", 0))
            with col_summary2:
                st.metric("Passed", latest_run.get("passed_tests", 0))
            with col_summary3:
                st.metric("Failed", latest_run.get("failed_tests", 0))
            with col_summary4:
                pass_rate = 0
                total_tests_val = latest_run.get("total_tests", 0)
                if total_tests_val > 0:
                    pass_rate = round((latest_run.get("passed_tests", 0) / total_tests_val) * 100, 1)
                st.metric("Pass Rate", f"{pass_rate}%")
            with col_actions:  # Use the renamed column
                st.write("")  # Add space for alignment
                st.write("")

            # Performance Metrics
            performance_metrics = latest_run.get("performance", {})
            if performance_metrics:
                # Add a note about performance metrics being moved to the Analysis page
                st.info("📊 Performance metrics are now available in the Analysis page under the 'Performance Metrics' tab.")

                # --- PDF Report Generation Button ---
                if st.button("📄 Generate Report", key="generate_pdf_report"):
                    try:
                        report_run_ts = latest_run["timestamp"]
                        st.info("🚀 Starting PDF report generation...")
                        pdf_path = generate_report(report_run_ts, DATABASE_PATH, output_dir=REPORTS_DIR)
                        st.success(f"✅ Report generated: {pdf_path.name}")
                        try:
                            st.download_button(
                                "Download PDF Report",
                                data=pdf_path.read_bytes(),
                                file_name=pdf_path.name,
                                mime="application/pdf",
                                key="download_pdf_button"
                            )
                        except Exception as dl_err:
                            st.error(f"Failed to create download button: {dl_err}")
                    except Exception as report_err:
                        st.error(f"Error generating report: {report_err}") # Added error handling
                        # Optionally log the full traceback
                        # import traceback
                        # st.expander("Show Error Details").code(traceback.format_exc())
                # --- End PDF Report Generation Button ---

                gen_bulk_enabled = True
                if st.session_state['model_type'] == "Online" and not st.session_state.api_key:
                    gen_bulk_enabled = False
                    st.caption("API Key needed for Cloud analysis.")

                col_gen_all, col_gen_failed = st.columns(2)

                # Button for all cases
                with col_gen_all:
                    if st.button("Generate Missing AI Summaries (All Cases)", key="bulk_gen_latest_run_all", disabled=not gen_bulk_enabled):
                        try:
                            all_cases = get_test_run_details(DATABASE_PATH, latest_run['timestamp'])
                            close_thread_local_connection()
                        except Exception as e:
                            st.error(f"Error fetching test cases for latest run: {e}")
                            all_cases = []

                        if not all_cases:
                            st.info("No test cases found in the latest run to analyze.")
                        else:
                            progress_bar = st.progress(0)
                            status_placeholder = st.empty()
                            results_summary = {'generated': 0, 'skipped_exists': 0, 'skipped_missing_artifacts': 0, 'error': 0}
                            total_to_process = len(all_cases)

                            for i, case in enumerate(all_cases):
                                case_id = case.get('case_id')
                                if not case_id:
                                    continue
                                status_placeholder.text(f"Processing case {i+1}/{total_to_process}: {case.get('name')}...")
                                status = generate_and_save_summary_if_missing(
                                    case_id, DATABASE_PATH, st.session_state.model, st.session_state.api_key
                                )
                                if status == 'generated':
                                    results_summary['generated'] += 1
                                    keys_to_delete = [
                                        key for key in st.session_state
                                        if key.startswith(f"details_{latest_run['timestamp']}_")
                                    ]
                                    for key in keys_to_delete:
                                        del st.session_state[key]
                                elif status == 'skipped_exists':
                                    results_summary['skipped_exists'] += 1
                                elif status == 'skipped_missing_artifacts':
                                    results_summary['skipped_missing_artifacts'] += 1
                                else:
                                    results_summary['error'] += 1
                                progress_bar.progress((i + 1) / total_to_process)

                            status_placeholder.empty()
                            progress_bar.empty()
                            st.success(
                                f"Bulk generation complete: "
                                f"{results_summary['generated']} generated, "
                                f"{results_summary['skipped_exists']} skipped (already exist), "
                                f"{results_summary['skipped_missing_artifacts']} skipped (missing artifacts), "
                                f"{results_summary['error']} errors."
                            )
                            st.rerun()

                # Button for failed cases only
                with col_gen_failed:
                    if st.button("Generate Missing AI Summaries (Failed Only)", key="bulk_gen_latest_run_failed", disabled=not gen_bulk_enabled):
                        failed_cases_latest = []
                        try:
                            all_cases = get_test_run_details(DATABASE_PATH, latest_run['timestamp'])
                            close_thread_local_connection()
                            failed_cases_latest = [case for case in all_cases if case.get('result') == 'FAILED']
                        except Exception as e:
                            st.error(f"Error fetching failed cases for latest run: {e}")

                        if not failed_cases_latest:
                            st.info("No failed tests found in the latest run to analyze.")
                        else:
                            progress_bar = st.progress(0)
                            status_placeholder = st.empty()
                            results_summary = {'generated': 0, 'skipped_exists': 0, 'skipped_missing_artifacts': 0, 'error': 0}
                            total_to_process = len(failed_cases_latest)

                            for i, case in enumerate(failed_cases_latest):
                                case_id = case.get('case_id')
                                if not case_id:
                                    continue
                                status_placeholder.text(f"Processing case {i+1}/{total_to_process}: {case.get('name')}...")
                                status = generate_and_save_summary_if_missing(
                                    case_id, DATABASE_PATH, st.session_state.model, st.session_state.api_key
                                )
                                if status == 'generated':
                                    results_summary['generated'] += 1
                                    keys_to_delete = [
                                        key for key in st.session_state
                                        if key.startswith(f"details_{latest_run['timestamp']}_")
                                    ]
                                    for key in keys_to_delete:
                                        del st.session_state[key]
                                elif status == 'skipped_exists':
                                    results_summary['skipped_exists'] += 1
                                elif status == 'skipped_missing_artifacts':
                                    results_summary['skipped_missing_artifacts'] += 1
                                else:
                                    results_summary['error'] += 1
                                progress_bar.progress((i + 1) / total_to_process)

                            status_placeholder.empty()
                            progress_bar.empty()
                            st.success(
                                f"Bulk generation complete: "
                                f"{results_summary['generated']} generated, "
                                f"{results_summary['skipped_exists']} skipped (already exist), "
                                f"{results_summary['skipped_missing_artifacts']} skipped (missing artifacts), "
                                f"{results_summary['error']} errors."
                            )
                            st.rerun()

            latest_run_classnames = ["All Classes"] + get_classnames_for_run(DATABASE_PATH, latest_run['timestamp'])
            close_thread_local_connection()
            selected_comparison_class = st.selectbox(
                "Compare with previous run containing class:",
                options=latest_run_classnames,
                index=0,
                key="comparison_class_selector"
            )

            previous_run_to_compare = None
            previous_run_display_ts = "N/A"

            if selected_comparison_class == "All Classes":
                if len(test_history_db) > 1:
                    previous_run_to_compare = test_history_db[1]
            else:
                prev_ts = find_previous_run_with_class(DATABASE_PATH, latest_run['timestamp'], selected_comparison_class)
                close_thread_local_connection()
                if prev_ts:
                    full_history = load_test_run_history(DATABASE_PATH, limit=100)
                    close_thread_local_connection()
                    previous_run_to_compare = next((run for run in full_history if run['timestamp'] == prev_ts), None)

            if previous_run_to_compare:
                previous_run_display_ts = format_timestamp_display(previous_run_to_compare['timestamp'])
                comparison_target_info = f"vs {previous_run_display_ts}"
                if selected_comparison_class != "All Classes":
                    comparison_target_info += f" (Class: {selected_comparison_class})"
                st.markdown(f"### Run Comparison: {comparison_target_info}")

                # --- Get Run IDs ---
                current_run_id = find_run_id_by_timestamp(DATABASE_PATH, latest_run['timestamp'])
                previous_run_id = find_run_id_by_timestamp(DATABASE_PATH, previous_run_to_compare['timestamp'])
                close_thread_local_connection()
                # --- End Get Run IDs ---

                changes = compare_test_results(DATABASE_PATH, latest_run['timestamp'], previous_run_to_compare['timestamp'])
                close_thread_local_connection()

                if changes:
                    # --- AI Comparison Summary Section ---
                    st.markdown("#### AI Comparison Summary")
                    ai_summary_enabled = True
                    if st.session_state['model_type'] == "Online" and not st.session_state.api_key:
                        ai_summary_enabled = False
                        st.caption("API Key needed for AI comparison summary.")

                    existing_summary_raw = None

                    if ai_summary_enabled and current_run_id:
                        try:
                            existing_summary_raw = get_comparison_summary(
                                DATABASE_PATH,
                                current_run_id,
                                previous_run_to_compare['timestamp'],  # Use timestamp for matching metadata
                                st.session_state.model
                            )
                            close_thread_local_connection()
                        except Exception as e:
                            st.warning(f"Error checking for existing comparison summary: {e}")
                            close_thread_local_connection()


                    summary_placeholder = st.empty()  # Placeholder to display summary or messages
                    parsed_summary = None

                    if existing_summary_raw:
                        try:
                            # First examine what we actually have
                            if isinstance(existing_summary_raw, dict):
                                # Get the actual summary content from the metadata wrapper
                                summary_content = existing_summary_raw.get("summary", "")

                                # Check if summary contains a markdown JSON code block
                                if isinstance(summary_content, str) and "```json" in summary_content:
                                    # Extract JSON from between the code block markers
                                    json_pattern = re.search(r'```json\n(.*?)\n```', summary_content, re.DOTALL)
                                    if json_pattern:
                                        json_str = json_pattern.group(1)
                                        parsed_summary = json.loads(json_str)
                                    else:
                                        # Fallback if regex doesn't match
                                        summary_placeholder.warning("Could not extract JSON from code block")
                                        parsed_summary = {"error": "JSON extraction failed", "raw": summary_content}
                                elif isinstance(summary_content, str):
                                    # Try direct JSON parsing for non-code-block strings
                                    try:
                                        parsed_summary = json.loads(summary_content)
                                    except json.JSONDecodeError:
                                        # Not valid JSON, use as is
                                        parsed_summary = {"text": summary_content}
                                elif isinstance(summary_content, dict):
                                    # Already a dict
                                    parsed_summary = summary_content
                                else:
                                    # If it's neither string nor dict
                                    summary_placeholder.info("Displaying cached summary")
                                    parsed_summary = summary_content
                            else:
                                # If it's not even a dict at the top level
                                summary_placeholder.info("Using raw summary format")
                                parsed_summary = existing_summary_raw

                        except Exception as parse_err:
                            summary_placeholder.warning(f"Error parsing summary: {parse_err}")
                            # Last resort - use the raw content directly
                            parsed_summary = {"error": str(parse_err), "raw": existing_summary_raw}

                        # st.write("▶️ parsed_summary type:", type(parsed_summary))
                        # st.write(parsed_summary)


                    if parsed_summary and isinstance(parsed_summary, dict):
                        with summary_placeholder.container():

                            # --- Display Trend First ---
                            trend = parsed_summary.get("trend", "Not Available")
                            trend_lower = trend.lower() if isinstance(trend, str) else ""

                            # Determine appropriate emoji and color based on the trend content
                            if "improvement" in trend_lower or "better" in trend_lower or "positive" in trend_lower:
                                trend_emoji = "🚀"
                                trend_color = "#28a745"  # Green for positive
                            elif "regression" in trend_lower or "worse" in trend_lower or "negative" in trend_lower:
                                trend_emoji = "⚠️"
                                trend_color = "#dc3545"  # Red for negative
                            elif "stable" in trend_lower or "unchanged" in trend_lower or "neutral" in trend_lower:
                                trend_emoji = "📊"
                                trend_color = "#17a2b8"  # Blue for stable
                            else:
                                trend_emoji = "📈"
                                trend_color = "#6f42c1"  # Purple for unknown/neutral

                            # Create a styled card with the trend information
                            st.markdown(f"""
                            <div style="background: linear-gradient(to right, {trend_color}15, {trend_color}05);
                                        border-left: 8px solid {trend_color};
                                        border-radius: 10px; padding: 20px; margin: 15px 0px;
                                        box-shadow: 0 4px 8px rgba(0,0,0,0.12);">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-size: 3rem; margin-right: 20px;
                                               text-shadow: 2px 2px 4px rgba(0,0,0,0.1);">{trend_emoji}</div>
                                    <div>
                                        <h3 style="margin: 0; color: #333; font-weight: 600;
                                                  text-transform: uppercase; letter-spacing: 1px;">Trend Analysis</h3>
                                        <p style="margin: 8px 0 0 0; font-size: 1.4rem; color: {trend_color};
                                                 font-weight: 500;">{trend}</p>
                                    </div>
                                </div>
                            </div>
                            """, unsafe_allow_html=True)

                            st.markdown("---") # Separator

                            # --- Detailed Tabs ---
                            detail_tabs = st.tabs(["Highlights", "Regressions", "Still Failing", "New Tests", "Action Items"])

                            with detail_tabs[0]: # Highlights
                                highlights = parsed_summary.get("highlights")
                                if highlights and isinstance(highlights, list):
                                    st.markdown("**Key Observations & Improvements:**")
                                    for item in highlights:
                                        st.markdown(f"- {item}")
                                else:
                                    st.info("No specific highlights identified in the summary.")

                            with detail_tabs[1]: # Regressions
                                regressions = parsed_summary.get("regressions")
                                if regressions and isinstance(regressions, list):
                                    st.markdown("**Regressions Detected:**")
                                    for reg in regressions:
                                        if isinstance(reg, dict):
                                            st.markdown(f"- **Test:** `{reg.get('name', 'Unknown Test')}`")
                                            st.markdown(f"  - **Class:** `{reg.get('class', 'N/A')}`")
                                            reason = reg.get('failure_reason')
                                            if reason:
                                                st.markdown(f"  - **Reason:** {reason}")
                                            st.markdown("---") # Separator between regressions
                                        else:
                                             st.markdown(f"- {reg}") # Fallback for non-dict items
                                else:
                                    st.info("No regressions identified in the summary.")

                            with detail_tabs[2]: # Still Failing
                                still_failing = parsed_summary.get("still_failing_patterns")
                                if still_failing:
                                    st.markdown("**Patterns in Persisting Failures:**")
                                    st.markdown(still_failing)
                                else:
                                    st.info("No patterns identified for tests that continue to fail.")

                            with detail_tabs[3]: # New Tests
                                new_tests = parsed_summary.get("new_tests_impact")
                                if new_tests:
                                    st.markdown("**Impact of New Tests:**")
                                    st.markdown(new_tests)
                                else:
                                    st.info("No specific impact analysis provided for new tests.")

                            with detail_tabs[4]: # Action Items
                                action_items = parsed_summary.get("action_items")
                                if action_items and isinstance(action_items, list):
                                    st.markdown("**Suggested Action Items:**")
                                    for i, item in enumerate(action_items):
                                        st.markdown(f"{i+1}. {item}")
                                else:
                                    st.info("No specific action items suggested in the summary.")

                    elif isinstance(parsed_summary, str): # Handle case where summary is just text
                         with summary_placeholder.container():
                            st.markdown("##### 📄 AI Comparison Analysis (Cached - Text)")
                            st.markdown(parsed_summary)

                    elif not existing_summary_raw:
                         summary_placeholder.info("AI comparison summary not generated yet for this model and run pair.")
                    # If parsing failed, the messages are already in summary_placeholder from the except blocks


                    # --- Generate/Regenerate Button ---
                    button_text = "Regenerate AI Comparison Summary" if existing_summary_raw else "Generate AI Comparison Summary"
                    button_key = f"gen_compare_summary_{current_run_id}_{previous_run_to_compare['timestamp']}"

                    if st.button(button_text, key=button_key, disabled=not ai_summary_enabled):
                        if not current_run_id:
                            st.error("Cannot generate summary: Could not determine current run ID.")
                        else:
                            with st.spinner("Generating AI comparison summary..."):
                                try:
                                    # Prepare data for the summary function
                                    latest_run_stats = {
                                        "total_tests": latest_run.get("total_tests", 0),
                                        "passed_tests": latest_run.get("passed_tests", 0),
                                        "failed_tests": latest_run.get("failed_tests", 0),
                                        "pass_rate": pass_rate  # Calculated earlier
                                    }
                                    prev_total = previous_run_to_compare.get("total_tests", 0)
                                    prev_passed = previous_run_to_compare.get("passed_tests", 0)
                                    prev_failed = previous_run_to_compare.get("failed_tests", 0)
                                    prev_pass_rate = round((prev_passed / prev_total) * 100, 1) if prev_total > 0 else 0
                                    prev_run_stats = {
                                        "total_tests": prev_total,
                                        "passed_tests": prev_passed,
                                        "failed_tests": prev_failed,
                                        "pass_rate": prev_pass_rate
                                    }

                                    google_timestamps = st.session_state.google_request_timestamps if st.session_state['model_type'] == "Online" else None
                                    google_tokens = st.session_state.google_token_usage if st.session_state['model_type'] == "Online" else None

                                    comparison_summary_text = generate_comparison_summary(
                                        latest_run_stats,
                                        prev_run_stats,
                                        changes,  # Pass the full changes dict
                                        model_name=st.session_state.model,
                                        api_key=st.session_state.api_key,
                                        timestamps_list=google_timestamps,
                                        token_usage_list=google_tokens
                                    )

                                    if comparison_summary_text and not comparison_summary_text.startswith("Error"):
                                        # Prepare content for saving (add metadata)
                                        summary_to_save = {
                                            "metadata": {
                                                "current_run_timestamp": latest_run['timestamp'],
                                                "compared_run_timestamp": previous_run_to_compare['timestamp'],
                                                "model_name": st.session_state.model,
                                                "generated_at": datetime.now().isoformat()
                                            },
                                            "summary": comparison_summary_text
                                        }
                                        summary_content_str = json.dumps(summary_to_save)

                                        # Save the summary
                                        save_success = save_summary(
                                            DATABASE_PATH,
                                            case_id=None,  # Not linked to a specific case
                                            run_id=current_run_id,
                                            model_name=st.session_state.model,
                                            summary_type='comparison_summary',
                                            summary_content=summary_content_str
                                        )
                                        close_thread_local_connection()

                                        if save_success:
                                            st.success("AI comparison summary generated and saved.")
                                            # Update placeholder immediately
                                            summary_placeholder.info("**AI Summary (newly generated):**")
                                            summary_placeholder.markdown(comparison_summary_text)
                                            st.rerun()  # Rerun to ensure state consistency
                                        else:
                                            st.error("Failed to save the generated summary to the database.")
                                            # Display the generated summary anyway
                                            summary_placeholder.warning("**AI Summary (generated but not saved):**")
                                            summary_placeholder.markdown(comparison_summary_text)

                                    elif comparison_summary_text:  # Display error if generation failed
                                        st.warning(f"Could not generate AI summary: {comparison_summary_text}")
                                        summary_placeholder.warning(f"Could not generate AI summary: {comparison_summary_text}")
                                    else:
                                        st.warning("AI summary generation returned empty.")
                                        summary_placeholder.warning("AI summary generation returned empty.")

                                except Exception as summary_err:
                                    st.error(f"Error generating or saving AI comparison summary: {summary_err}")
                                    summary_placeholder.error(f"Error generating or saving AI comparison summary: {summary_err}")
                                    close_thread_local_connection()  # Ensure connection closed on error
                    # --- End AI Comparison Summary Section ---

                    # Display detailed metrics (existing code)
                    change_cols = st.columns(4)
                    summary = changes["summary"]
                    with change_cols[0]:
                        st.metric("Fixed", summary.get("fixed", 0),
                                  delta=f"+{summary.get('fixed', 0)}" if summary.get('fixed', 0) > 0 else "0")
                    with change_cols[1]:
                        st.metric("Regressions", summary.get("regression", 0),
                                  delta=f"-{summary.get('regression', 0)}" if summary.get('regression', 0) > 0 else "0",
                                  delta_color="inverse")
                    with change_cols[2]:
                        st.metric("Still Failing", summary.get("still_failing", 0))
                    with change_cols[3]:
                        st.metric("New Cases", summary.get("new_tests", 0))

                    change_tabs = st.tabs(["Fixed", "Regressions", "Still Failing", "New Cases"])

                    def render_comparison_list(tab_title, test_list, run_timestamp, list_type):
                        if test_list:
                            st.info(f"{len(test_list)} {list_type} found.")
                            for index, test in enumerate(test_list):
                                name = test.get('name', 'Unknown Name')
                                classname = test.get('classname', 'N/A')
                                case_id_from_comparison = test.get('case_id')
                                session_key = f"details_{run_timestamp}_{list_type}_{index}_{case_id_from_comparison or name}"

                                expander_title = f"{name} (Class: {classname})"

                                with st.expander(expander_title):
                                    if session_key in st.session_state:
                                        render_test_case_details(st.session_state[session_key], session_key, is_nested_in_expander=True)
                                    else:
                                        with st.spinner("Loading details..."):
                                            fetched_details = None
                                            actual_case_id = case_id_from_comparison

                                            if not actual_case_id and name != 'Unknown Name' and classname != 'N/A':
                                                try:
                                                    actual_case_id = find_case_id(DATABASE_PATH, run_timestamp, classname, name)
                                                    close_thread_local_connection()
                                                    if not actual_case_id:
                                                        st.warning("Could not automatically find database ID for this test case based on name/class. Details might be limited.")
                                                except Exception as find_err:
                                                    st.error(f"Error finding case ID: {find_err}")
                                                    close_thread_local_connection()
                                                    actual_case_id = None

                                            if actual_case_id:
                                                try:
                                                    fetched_details = get_single_test_case_details(DATABASE_PATH, actual_case_id)
                                                    close_thread_local_connection()
                                                    if fetched_details:
                                                        st.success("Details loaded.")
                                                    else:
                                                        st.error(f"Could not fetch details for case ID {actual_case_id}.")
                                                        fetched_details = {
                                                            "db_case_id": actual_case_id,
                                                            "test_name": name, "class_name": classname,
                                                            "result": test.get("result"), "duration": test.get("duration"),
                                                            "failure_message": test.get("failure_message"),
                                                            "screenshot_path": None, "page_source_path": None,
                                                            "log_path": None, "visual_analysis": None,
                                                            "performance_metrics": test.get("performance_metrics")
                                                        }
                                                except Exception as fetch_err:
                                                    st.error(f"Error fetching details: {fetch_err}")
                                                    close_thread_local_connection()
                                                    fetched_details = None
                                            else:
                                                fetched_details = {
                                                    "db_case_id": None,
                                                    "test_name": name, "class_name": classname,
                                                    "result": test.get("result"), "duration": test.get("duration"),
                                                    "failure_message": test.get("failure_message"),
                                                    "screenshot_path": None, "page_source_path": None,
                                                    "log_path": None, "visual_analysis": None,
                                                    "performance_metrics": test.get("performance_metrics")
                                                }

                                            if fetched_details:
                                                st.session_state[session_key] = fetched_details
                                                st.rerun()

                        else:
                            st.info(f"No {list_type} detected in this comparison" + (f" for class '{selected_comparison_class}'." if selected_comparison_class != "All Classes" else "."))

                    with change_tabs[0]:
                        fixed_tests = changes.get("fixed_tests", [])
                        render_comparison_list("Fixed", fixed_tests, latest_run['timestamp'], "fixed tests")

                    with change_tabs[1]:
                        regression_tests = changes.get("regression_tests", [])
                        render_comparison_list("Regressions", regression_tests, latest_run['timestamp'], "regressions")

                    with change_tabs[2]:
                        still_failing = changes.get("still_failing", [])
                        render_comparison_list("Still Failing", still_failing, latest_run['timestamp'], "still failing tests")

                    with change_tabs[3]:
                        new_tests = changes.get("new_tests", [])
                        render_comparison_list("New Cases", new_tests, latest_run['timestamp'], "new test cases")

                else:
                    st.info("Comparison data unavailable or no changes detected.")
            else:
                st.info("Select a valid previous run to compare against.")

            st.markdown("### Test Run History")

            history_data = []
            performance_data = []
            for entry in reversed(test_history_db):
                total = entry.get("total_tests", 0)
                passed = entry.get("passed_tests", 0)
                failed = entry.get("failed_tests", 0)
                pass_rate = round(passed / total * 100, 1) if total and total > 0 else 0

                # Basic test history data
                history_data.append({
                    "Timestamp": entry["timestamp"],
                    "Run Time": format_timestamp_display(entry["timestamp"]),
                    "Total": total,
                    "Passed": passed,
                    "Failed": failed,
                    "Pass Rate": pass_rate
                })

                # Extract performance metrics if available
                performance_metrics = entry.get("performance", {})
                if performance_metrics:
                    perf_entry = {
                        "Timestamp": entry["timestamp"],
                        "Run Time": format_timestamp_display(entry["timestamp"]),
                        "Avg Execution Time (s)": performance_metrics.get("avg_execution_time", 0),
                        "Avg Memory Usage (MB)": performance_metrics.get("avg_memory_usage", 0),
                        "Avg CPU Usage (%)": performance_metrics.get("avg_cpu_usage", 0),
                        "Avg Page Load Time (ms)": performance_metrics.get("avg_page_load_time", 0),
                        "Total Network Requests": performance_metrics.get("total_network_requests", 0),
                        "Total Network Data (KB)": performance_metrics.get("total_network_bytes", 0),
                        "Max Execution Time (s)": performance_metrics.get("max_execution_time", 0),
                        "Max Memory Usage (MB)": performance_metrics.get("max_memory_usage", 0),
                        "Max CPU Usage (%)": performance_metrics.get("max_cpu_usage", 0),
                        "Max Page Load Time (ms)": performance_metrics.get("max_page_load_time", 0)
                    }
                    performance_data.append(perf_entry)

            history_df = pd.DataFrame(history_data)
            performance_df = pd.DataFrame(performance_data)

            if not history_df.empty:
                try:
                    history_df_sorted = history_df.sort_values("Timestamp")

                    # Create tabs for different types of charts
                    chart_tabs = st.tabs(["Test Results", "Pass Rate", "Performance Metrics", "Resource Usage", "Network Metrics"])

                    with chart_tabs[0]:  # Test Results tab
                        fig = px.line(
                            history_df_sorted,
                            x="Run Time",
                            y=["Passed", "Failed"],
                            title="Test Results History",
                            labels={"value": "Test Count", "variable": "Status", "Run Time": "Test Run Time"},
                            markers=True
                        )
                        st.plotly_chart(fig, use_container_width=True)

                    with chart_tabs[1]:  # Pass Rate tab
                        fig2 = px.line(
                            history_df_sorted,
                            x="Run Time",
                            y="Pass Rate",
                            title="Test Pass Rate History",
                            labels={"Pass Rate": "Pass Rate (%)", "Run Time": "Test Run Time"},
                            markers=True
                        )
                        fig2.update_layout(yaxis_range=[0, 100])
                        st.plotly_chart(fig2, use_container_width=True)

                    # Performance Metrics Charts
                    if not performance_df.empty:
                        performance_df_sorted = performance_df.sort_values("Timestamp")

                        with chart_tabs[2]:  # Performance Metrics tab
                            st.markdown("#### Execution Time Metrics")
                            fig_perf = px.line(
                                performance_df_sorted,
                                x="Run Time",
                                y=["Avg Execution Time (s)", "Max Execution Time (s)"],
                                title="Test Execution Time Trends",
                                labels={"value": "Time (seconds)", "variable": "Metric", "Run Time": "Test Run Time"},
                                markers=True,
                                color_discrete_map={
                                    "Avg Execution Time (s)": "#1f77b4",
                                    "Max Execution Time (s)": "#ff7f0e"
                                }
                            )
                            st.plotly_chart(fig_perf, use_container_width=True)

                            st.markdown("#### Page Load Time Metrics")
                            fig_page_load = px.line(
                                performance_df_sorted,
                                x="Run Time",
                                y=["Avg Page Load Time (ms)", "Max Page Load Time (ms)"],
                                title="Page Load Time Trends",
                                labels={"value": "Time (milliseconds)", "variable": "Metric", "Run Time": "Test Run Time"},
                                markers=True,
                                color_discrete_map={
                                    "Avg Page Load Time (ms)": "#2ca02c",
                                    "Max Page Load Time (ms)": "#d62728"
                                }
                            )
                            st.plotly_chart(fig_page_load, use_container_width=True)

                        with chart_tabs[3]:  # Resource Usage tab
                            st.markdown("#### Memory Usage Metrics")
                            fig_memory = px.line(
                                performance_df_sorted,
                                x="Run Time",
                                y=["Avg Memory Usage (MB)", "Max Memory Usage (MB)"],
                                title="Memory Usage Trends",
                                labels={"value": "Memory (MB)", "variable": "Metric", "Run Time": "Test Run Time"},
                                markers=True,
                                color_discrete_map={
                                    "Avg Memory Usage (MB)": "#9467bd",
                                    "Max Memory Usage (MB)": "#8c564b"
                                }
                            )
                            st.plotly_chart(fig_memory, use_container_width=True)

                            st.markdown("#### CPU Usage Metrics")
                            fig_cpu = px.line(
                                performance_df_sorted,
                                x="Run Time",
                                y=["Avg CPU Usage (%)", "Max CPU Usage (%)"],
                                title="CPU Usage Trends",
                                labels={"value": "CPU Usage (%)", "variable": "Metric", "Run Time": "Test Run Time"},
                                markers=True,
                                color_discrete_map={
                                    "Avg CPU Usage (%)": "#e377c2",
                                    "Max CPU Usage (%)": "#7f7f7f"
                                }
                            )
                            fig_cpu.update_layout(yaxis_range=[0, 100])
                            st.plotly_chart(fig_cpu, use_container_width=True)

                        with chart_tabs[4]:  # Network Metrics tab
                            st.markdown("#### Network Metrics")
                            col1, col2 = st.columns(2)

                            with col1:
                                fig_network_req = px.line(
                                    performance_df_sorted,
                                    x="Run Time",
                                    y="Total Network Requests",
                                    title="Network Request Count",
                                    labels={"Total Network Requests": "Request Count", "Run Time": "Test Run Time"},
                                    markers=True,
                                    color_discrete_sequence=["#17becf"]
                                )
                                st.plotly_chart(fig_network_req, use_container_width=True)

                            with col2:
                                fig_network_data = px.line(
                                    performance_df_sorted,
                                    x="Run Time",
                                    y="Total Network Data (KB)",
                                    title="Network Data Transfer",
                                    labels={"Total Network Data (KB)": "Data (KB)", "Run Time": "Test Run Time"},
                                    markers=True,
                                    color_discrete_sequence=["#bcbd22"]
                                )
                                st.plotly_chart(fig_network_data, use_container_width=True)
                    else:
                        with chart_tabs[2]:
                            st.info("No performance metrics data available. Run tests with the updated conftest.py to collect performance metrics.")
                        with chart_tabs[3]:
                            st.info("No resource usage data available. Run tests with the updated conftest.py to collect resource usage metrics.")
                        with chart_tabs[4]:
                            st.info("No network metrics data available. Run tests with the updated conftest.py to collect network metrics.")

                    # Display data tables
                    st.markdown("### Test History Data")
                    st.dataframe(history_df.sort_values("Timestamp", ascending=False).drop(columns=["Timestamp"]).reset_index(drop=True))

                    if not performance_df.empty:
                        st.markdown("### Performance Metrics Data")
                        st.dataframe(performance_df.sort_values("Timestamp", ascending=False).drop(columns=["Timestamp"]).reset_index(drop=True))

                except ImportError:
                    st.warning("Plotly library not found. Please install it (`pip install plotly`) to see history charts.")
                    st.dataframe(history_df.sort_values("Timestamp", ascending=False).drop(columns=["Timestamp"]).reset_index(drop=True))
                except Exception as chart_err:
                    st.error(f"Error generating history charts: {chart_err}")
                    st.dataframe(history_df.sort_values("Timestamp", ascending=False).drop(columns=["Timestamp"]).reset_index(drop=True))
