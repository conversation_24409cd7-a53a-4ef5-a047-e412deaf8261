"""
GretahAI_CaseForge - Cache Clearing Utility

This script clears various types of caches to ensure latest changes are reflected
in the dashboard and application.

Author: GretahAI Development Team
Date: 2025-01-14
Version: 1.0.0
"""

import os
import shutil
import sys
from pathlib import Path
import streamlit as st

def clear_streamlit_cache():
    """Clear Streamlit's internal cache."""
    print("🧹 Clearing Streamlit cache...")
    
    try:
        # Clear Streamlit cache if running in Streamlit context
        if hasattr(st, 'cache_data'):
            st.cache_data.clear()
            print("✅ Streamlit cache_data cleared")
        
        if hasattr(st, 'cache_resource'):
            st.cache_resource.clear()
            print("✅ Streamlit cache_resource cleared")
        
        # Clear legacy cache if available
        if hasattr(st, 'legacy_caching'):
            if hasattr(st.legacy_caching, 'clear_cache'):
                st.legacy_caching.clear_cache()
                print("✅ Streamlit legacy cache cleared")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Streamlit cache clearing: {e}")
        return False

def clear_python_cache():
    """Clear Python bytecode cache files."""
    print("🧹 Clearing Python cache files...")
    
    try:
        cache_dirs_removed = 0
        cache_files_removed = 0
        
        # Find and remove __pycache__ directories
        for root, dirs, files in os.walk('.'):
            # Remove __pycache__ directories
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                shutil.rmtree(pycache_path)
                cache_dirs_removed += 1
                print(f"   Removed: {pycache_path}")
            
            # Remove .pyc files
            for file in files:
                if file.endswith('.pyc') or file.endswith('.pyo'):
                    file_path = os.path.join(root, file)
                    os.remove(file_path)
                    cache_files_removed += 1
                    print(f"   Removed: {file_path}")
        
        print(f"✅ Removed {cache_dirs_removed} __pycache__ directories")
        print(f"✅ Removed {cache_files_removed} .pyc/.pyo files")
        return True
        
    except Exception as e:
        print(f"❌ Error clearing Python cache: {e}")
        return False

def clear_streamlit_config_cache():
    """Clear Streamlit configuration and session cache."""
    print("🧹 Clearing Streamlit configuration cache...")
    
    try:
        # Common Streamlit cache locations
        cache_locations = [
            os.path.expanduser("~/.streamlit"),
            ".streamlit",
            "streamlit_cache",
            ".streamlit_cache"
        ]
        
        cleared_locations = 0
        
        for location in cache_locations:
            if os.path.exists(location):
                if os.path.isdir(location):
                    # Don't remove the entire .streamlit directory, just cache files
                    cache_files = [
                        os.path.join(location, "cache"),
                        os.path.join(location, "session_state"),
                        os.path.join(location, "uploaded_files")
                    ]
                    
                    for cache_file in cache_files:
                        if os.path.exists(cache_file):
                            if os.path.isdir(cache_file):
                                shutil.rmtree(cache_file)
                            else:
                                os.remove(cache_file)
                            cleared_locations += 1
                            print(f"   Cleared: {cache_file}")
        
        print(f"✅ Cleared {cleared_locations} Streamlit cache locations")
        return True
        
    except Exception as e:
        print(f"❌ Error clearing Streamlit config cache: {e}")
        return False

def clear_application_session_state():
    """Clear application-specific session state."""
    print("🧹 Clearing application session state...")
    
    try:
        # Clear session state if in Streamlit context
        if 'st' in globals() and hasattr(st, 'session_state'):
            # Clear specific session state keys that might cache data
            cache_keys = [
                'unified_interface_initialized',
                'test_cases_generated',
                'scenario_data',
                'jira_issue',
                'unified_filter_options',
                'unified_last_refresh',
                'last_filter_hash',
                'unified_export_ready',
                'unified_export_data'
            ]
            
            cleared_keys = 0
            for key in cache_keys:
                if key in st.session_state:
                    del st.session_state[key]
                    cleared_keys += 1
                    print(f"   Cleared session key: {key}")
            
            print(f"✅ Cleared {cleared_keys} session state keys")
        else:
            print("ℹ️ Not in Streamlit context, skipping session state clearing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing application session state: {e}")
        return False

def clear_temporary_files():
    """Clear temporary files and directories."""
    print("🧹 Clearing temporary files...")
    
    try:
        temp_locations = [
            "temp_excel",
            "Test_cases/unified_exports",
            "csv_exports",
            "temp",
            "tmp"
        ]
        
        cleared_locations = 0
        
        for location in temp_locations:
            if os.path.exists(location):
                if os.path.isdir(location):
                    # Clear contents but keep directory
                    for item in os.listdir(location):
                        item_path = os.path.join(location, item)
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                        else:
                            os.remove(item_path)
                    cleared_locations += 1
                    print(f"   Cleared contents of: {location}")
        
        print(f"✅ Cleared {cleared_locations} temporary locations")
        return True
        
    except Exception as e:
        print(f"❌ Error clearing temporary files: {e}")
        return False

def restart_streamlit_server():
    """Provide instructions to restart Streamlit server."""
    print("🔄 Streamlit Server Restart Instructions...")
    
    print("""
    To fully clear all caches and see latest changes:
    
    1. Stop current Streamlit server:
       - Press Ctrl+C in the terminal running Streamlit
    
    2. Restart Streamlit server:
       - Run: streamlit run gui/app.py
       - Or: streamlit run test_unified_interface.py
    
    3. Clear browser cache:
       - Press Ctrl+Shift+R (hard refresh)
       - Or open in incognito/private window
    
    4. If issues persist:
       - Close all browser tabs with the app
       - Wait 10 seconds
       - Restart browser
       - Open app in new tab
    """)

def main():
    """Run all cache clearing operations."""
    print("🧹 GretahAI_CaseForge Cache Clearing Utility")
    print("=" * 50)
    
    operations = [
        ("Python Cache", clear_python_cache),
        ("Streamlit Cache", clear_streamlit_cache),
        ("Streamlit Config Cache", clear_streamlit_config_cache),
        ("Application Session State", clear_application_session_state),
        ("Temporary Files", clear_temporary_files)
    ]
    
    successful = 0
    total = len(operations)
    
    for operation_name, operation_func in operations:
        print(f"\n{operation_name}:")
        try:
            if operation_func():
                successful += 1
        except Exception as e:
            print(f"❌ {operation_name} failed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Cache Clearing Results: {successful}/{total} operations successful")
    
    if successful == total:
        print("🎉 All cache clearing operations completed successfully!")
    else:
        print("⚠️ Some cache clearing operations had issues.")
    
    # Always show restart instructions
    restart_streamlit_server()
    
    print("\n✅ Cache clearing completed. Please restart Streamlit server to see latest changes.")

if __name__ == "__main__":
    main()
