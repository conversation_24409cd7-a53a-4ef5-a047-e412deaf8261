"""
File Upload UI Components for GretahAI ScriptWeaver

This module provides user interface components for file upload functionality
in the test data configuration phase.

Key Features:
- File upload widgets with drag-and-drop support
- File preview and management interfaces
- File type categorization and validation feedback
- Integration with existing Stage 5 UI workflow

© 2025 Cogniron All Rights Reserved.
"""

import streamlit as st
from pathlib import Path
from typing import Dict, List, Any, Optional
import mimetypes
from PIL import Image
import json

# Import GRETAH standardized logging
from debug_utils import debug
from core.file_upload_manager import FileUploadManager


class FileUploadComponents:
    """
    UI components for file upload functionality in ScriptWeaver.
    
    Provides reusable components for file upload, preview, and management
    that integrate seamlessly with the existing Stage 5 interface.
    """
    
    def __init__(self):
        """Initialize the FileUploadComponents."""
        self.file_manager = FileUploadManager()
        
    def render_file_upload_section(self, step_no: str, step_action: str) -> Optional[Dict[str, Any]]:
        """
        Render the complete file upload section for a test step.
        
        Args:
            step_no: Test step number
            step_action: Description of the test step action
            
        Returns:
            File metadata dictionary if a file was uploaded, None otherwise
        """
        st.markdown("### 📁 File Upload for Test Data")
        st.markdown("Upload files that will be used as test data for this step (images, documents, data files).")
        
        # Show supported file types
        with st.expander("📋 Supported File Types", expanded=False):
            self._render_supported_file_types()
        
        # File upload widget
        uploaded_file = st.file_uploader(
            f"Upload file for Step {step_no}",
            type=self._get_all_supported_extensions(),
            key=f"file_upload_step_{step_no}",
            help="Select a file to upload as test data for this step"
        )
        
        # File upload processing
        if uploaded_file is not None:
            return self._process_uploaded_file(uploaded_file, step_no, step_action)
        
        # Show existing uploaded files for this step
        self._render_existing_files(step_no)
        
        return None
    
    def _render_supported_file_types(self):
        """Render information about supported file types."""
        supported_types = self.file_manager.get_supported_file_types()
        
        for category, config in supported_types.items():
            st.markdown(f"**{category.title()}:**")
            st.markdown(f"- Extensions: {', '.join(config['extensions'])}")
            st.markdown(f"- Max size: {config['max_size_mb']}MB")
            st.markdown(f"- Use case: {config['description']}")
            st.markdown("")
    
    def _get_all_supported_extensions(self) -> List[str]:
        """Get all supported file extensions for the file uploader."""
        extensions = []
        supported_types = self.file_manager.get_supported_file_types()
        
        for config in supported_types.values():
            # Remove the dot from extensions for Streamlit file_uploader
            extensions.extend([ext[1:] for ext in config['extensions']])
        
        return extensions
    
    def _process_uploaded_file(self, uploaded_file, step_no: str, step_action: str) -> Optional[Dict[str, Any]]:
        """
        Process an uploaded file and save it with metadata.
        
        Args:
            uploaded_file: Streamlit uploaded file object
            step_no: Test step number
            step_action: Description of the test step action
            
        Returns:
            File metadata dictionary if successful, None otherwise
        """
        try:
            # Get file content
            file_content = uploaded_file.getvalue()
            
            # Show file information
            col1, col2 = st.columns(2)
            with col1:
                st.info(f"**File:** {uploaded_file.name}")
                st.info(f"**Size:** {len(file_content):,} bytes")
            
            with col2:
                file_type = uploaded_file.type or mimetypes.guess_type(uploaded_file.name)[0]
                st.info(f"**Type:** {file_type}")
            
            # Validate file
            is_valid, error_msg, file_category = self.file_manager.validate_file(uploaded_file, file_content)
            
            if not is_valid:
                st.error(f"❌ File validation failed: {error_msg}")
                return None
            
            st.success(f"✅ File validated successfully (Category: {file_category})")
            
            # Show file preview if possible
            self._render_file_preview(uploaded_file, file_content, file_category)
            
            # Description input
            description = st.text_input(
                "File Description (optional):",
                value=f"Test data file for {step_action}",
                key=f"file_description_{step_no}",
                help="Describe how this file will be used in the test"
            )
            
            # Save button
            col1, col2 = st.columns([1, 3])
            with col1:
                if st.button("💾 Save File", key=f"save_file_{step_no}"):
                    try:
                        # Save the file
                        metadata = self.file_manager.save_uploaded_file(
                            uploaded_file, file_content, step_no, description
                        )
                        
                        st.success(f"✅ File saved successfully: {uploaded_file.name}")
                        
                        debug(f"File uploaded and saved for step {step_no}",
                              stage="file_upload", operation="file_save_ui_success",
                              context={
                                  'step_no': step_no,
                                  'filename': uploaded_file.name,
                                  'category': file_category
                              })
                        
                        # Clear the file uploader by rerunning
                        st.rerun()
                        
                        return metadata
                        
                    except Exception as e:
                        st.error(f"❌ Failed to save file: {str(e)}")
                        debug(f"Failed to save uploaded file: {e}",
                              stage="file_upload", operation="file_save_ui_error",
                              context={'step_no': step_no, 'error': str(e)})
                        return None
            
            with col2:
                if st.button("🗑️ Cancel", key=f"cancel_file_{step_no}"):
                    st.rerun()
            
        except Exception as e:
            st.error(f"❌ Error processing file: {str(e)}")
            debug(f"Error processing uploaded file: {e}",
                  stage="file_upload", operation="file_process_error",
                  context={'step_no': step_no, 'error': str(e)})
            return None
        
        return None
    
    def _render_file_preview(self, uploaded_file, file_content: bytes, file_category: str):
        """
        Render a preview of the uploaded file based on its type.
        
        Args:
            uploaded_file: Streamlit uploaded file object
            file_content: File content as bytes
            file_category: Category of the file (images, documents, etc.)
        """
        try:
            with st.expander("👁️ File Preview", expanded=True):
                if file_category == 'images':
                    self._render_image_preview(uploaded_file, file_content)
                elif file_category == 'data_files':
                    self._render_data_file_preview(uploaded_file, file_content)
                elif file_category == 'documents':
                    self._render_document_preview(uploaded_file, file_content)
                else:
                    st.info(f"Preview not available for {file_category} files")
                    
        except Exception as e:
            st.warning(f"Could not generate preview: {str(e)}")
            debug(f"Error generating file preview: {e}",
                  stage="file_upload", operation="preview_error",
                  context={'file_category': file_category, 'error': str(e)})
    
    def _render_image_preview(self, uploaded_file, file_content: bytes):
        """Render preview for image files."""
        try:
            image = Image.open(uploaded_file)
            
            # Show image info
            col1, col2 = st.columns(2)
            with col1:
                st.write(f"**Dimensions:** {image.size[0]} x {image.size[1]} pixels")
                st.write(f"**Format:** {image.format}")
            
            with col2:
                if hasattr(image, 'mode'):
                    st.write(f"**Mode:** {image.mode}")
            
            # Show image (resized for display)
            max_width = 400
            if image.size[0] > max_width:
                ratio = max_width / image.size[0]
                new_size = (max_width, int(image.size[1] * ratio))
                image = image.resize(new_size)
            
            st.image(image, caption=uploaded_file.name, use_column_width=False)
            
        except Exception as e:
            st.error(f"Could not preview image: {str(e)}")
    
    def _render_data_file_preview(self, uploaded_file, file_content: bytes):
        """Render preview for data files."""
        try:
            file_ext = Path(uploaded_file.name).suffix.lower()
            
            if file_ext == '.json':
                # Preview JSON content
                try:
                    json_data = json.loads(file_content.decode('utf-8'))
                    st.json(json_data)
                except:
                    st.text_area("File Content (first 500 chars):", 
                               value=file_content.decode('utf-8', errors='ignore')[:500],
                               height=150, disabled=True)
            
            elif file_ext == '.csv':
                # Preview CSV content
                import pandas as pd
                try:
                    df = pd.read_csv(uploaded_file)
                    st.write(f"**Rows:** {len(df)}, **Columns:** {len(df.columns)}")
                    st.dataframe(df.head(10))  # Show first 10 rows
                except:
                    st.text_area("File Content (first 500 chars):", 
                               value=file_content.decode('utf-8', errors='ignore')[:500],
                               height=150, disabled=True)
            
            else:
                # Generic text preview
                st.text_area("File Content (first 500 chars):", 
                           value=file_content.decode('utf-8', errors='ignore')[:500],
                           height=150, disabled=True)
                
        except Exception as e:
            st.error(f"Could not preview data file: {str(e)}")
    
    def _render_document_preview(self, uploaded_file, file_content: bytes):
        """Render preview for document files."""
        try:
            file_ext = Path(uploaded_file.name).suffix.lower()
            
            if file_ext == '.txt':
                # Preview text content
                text_content = file_content.decode('utf-8', errors='ignore')
                st.text_area("File Content (first 1000 chars):", 
                           value=text_content[:1000],
                           height=200, disabled=True)
                
                if len(text_content) > 1000:
                    st.info(f"Showing first 1000 characters of {len(text_content)} total")
            
            else:
                st.info(f"Preview not available for {file_ext} files")
                st.write(f"**File size:** {len(file_content):,} bytes")
                
        except Exception as e:
            st.error(f"Could not preview document: {str(e)}")
    
    def _render_existing_files(self, step_no: str):
        """
        Render a list of existing uploaded files for the current step.
        
        Args:
            step_no: Test step number
        """
        existing_files = self.file_manager.get_uploaded_files_for_step(step_no)
        
        if existing_files:
            with st.expander(f"📂 Uploaded Files for Step {step_no} ({len(existing_files)})", expanded=True):
                for file_metadata in existing_files:
                    self._render_file_item(file_metadata)
    
    def _render_file_item(self, file_metadata: Dict[str, Any]):
        """
        Render a single file item with metadata and actions.
        
        Args:
            file_metadata: File metadata dictionary
        """
        col1, col2, col3 = st.columns([3, 1, 1])
        
        with col1:
            st.markdown(f"**{file_metadata['original_name']}**")
            st.markdown(f"*{file_metadata['description']}*")
            st.markdown(f"Size: {file_metadata['file_size']:,} bytes | "
                       f"Category: {file_metadata['file_category']} | "
                       f"Uploaded: {file_metadata['upload_timestamp'][:16]}")
        
        with col2:
            # View button (could open file in new tab or show detailed preview)
            if st.button("👁️ View", key=f"view_{file_metadata['file_hash']}"):
                self._show_file_details(file_metadata)
        
        with col3:
            # Delete button
            if st.button("🗑️ Delete", key=f"delete_{file_metadata['file_hash']}"):
                if self.file_manager.delete_uploaded_file(file_metadata['file_hash']):
                    st.success("File deleted successfully")
                    st.rerun()
                else:
                    st.error("Failed to delete file")
        
        st.markdown("---")
    
    def _show_file_details(self, file_metadata: Dict[str, Any]):
        """
        Show detailed information about a file.
        
        Args:
            file_metadata: File metadata dictionary
        """
        with st.expander(f"📄 File Details: {file_metadata['original_name']}", expanded=True):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**Original Name:** {file_metadata['original_name']}")
                st.write(f"**File Type:** {file_metadata['file_type']}")
                st.write(f"**Category:** {file_metadata['file_category']}")
                st.write(f"**Size:** {file_metadata['file_size']:,} bytes")
            
            with col2:
                st.write(f"**Upload Time:** {file_metadata['upload_timestamp']}")
                st.write(f"**Step Number:** {file_metadata['step_no']}")
                st.write(f"**File Hash:** {file_metadata['file_hash']}")
                st.write(f"**File Path:** {file_metadata['relative_path']}")
            
            if file_metadata['description']:
                st.write(f"**Description:** {file_metadata['description']}")
    
    def get_file_upload_suggestions(self, step_action: str) -> List[str]:
        """
        Get file upload suggestions based on the step action.
        
        Args:
            step_action: Description of the test step action
            
        Returns:
            List of suggested file types for this step
        """
        action_lower = step_action.lower()
        suggestions = []
        
        # Analyze step action to suggest appropriate file types
        if any(keyword in action_lower for keyword in ['upload', 'attach', 'select file']):
            if any(keyword in action_lower for keyword in ['image', 'photo', 'picture', 'avatar', 'profile']):
                suggestions.append("Upload an image file (JPG, PNG) for profile picture or avatar testing")
            elif any(keyword in action_lower for keyword in ['document', 'pdf', 'doc', 'file']):
                suggestions.append("Upload a document file (PDF, DOCX) for document upload testing")
            elif any(keyword in action_lower for keyword in ['data', 'csv', 'excel', 'import']):
                suggestions.append("Upload a data file (CSV, JSON, XLSX) for data import testing")
            else:
                suggestions.append("Upload any supported file type for general file upload testing")
        
        elif any(keyword in action_lower for keyword in ['import', 'load', 'read']):
            suggestions.append("Upload a data file (CSV, JSON, XLSX) for data import functionality")
        
        elif any(keyword in action_lower for keyword in ['profile', 'avatar', 'photo']):
            suggestions.append("Upload an image file (JPG, PNG) for profile picture testing")
        
        return suggestions
