"""
Module for UI element detection.
Provides functions for detecting and filtering UI elements on a webpage.
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import streamlit as st
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.elements")
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, StaleElementReferenceException
from webdriver_manager.chrome import ChromeDriverManager


def filter_natural_classes(class_string):
    """
    Filter out GRETAH artificial classes and return natural classes.

    Args:
        class_string (str): Space-separated class names

    Returns:
        list: List of natural class names (excluding GRETAH internal classes)
    """
    if not class_string:
        return []

    classes = class_string.strip().split()
    # Filter out GRETAH artificial classes
    natural_classes = [
        cls for cls in classes
        if cls and not cls.startswith('gretah-')
        and not cls.startswith('highlight-')
        and not cls.startswith('selected-')
        and len(cls) > 0
    ]

    return natural_classes


def setup_webdriver(headless=False):
    """
    Set up a Chrome WebDriver with anti-detection measures.

    Args:
        headless (bool): Whether to run in headless mode (default: False for better debugging)

    Returns:
        WebDriver: A configured WebDriver instance
    """
    chrome_options = Options()

    if headless:
        chrome_options.add_argument("--headless")

    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")

    # Enhanced anti-detection measures
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Add a realistic user agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    try:
        # Create the driver with ChromeDriverManager
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Additional anti-detection measures using JavaScript
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set CDP to disable automation flags
        driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        # Set navigator properties to make detection harder
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
            '''
        })

        return driver
    except Exception as e:
        print(f"Error setting up WebDriver: {e}")
        return None

def find_elements_by_css(driver):
    """
    Find important interactive elements using focused CSS selectors.

    Args:
        driver: WebDriver instance

    Returns:
        list: List of detected elements
    """
    elements = []

    # Focused list of selectors for key interactive elements
    selectors = [
        # Primary interactive elements
        "input[type='text']", "input[type='email']", "input[type='password']",
        "input[type='submit']", "input[type='button']", "input[type='checkbox']",
        "input[type='radio']",

        # Essential UI controls
        "button", "a[href]", "select", "textarea",

        # Interactive elements with role attributes
        "[role='button']", "[role='link']", "[role='checkbox']", "[role='radio']",
        "[role='tab']", "[role='menuitem']", "[role='combobox']", "[role='listbox']",

        # Elements with event handlers
        "[onclick]", "[onchange]", "[onsubmit]",

        # Form elements
        "form", "label", "fieldset",

        # Elements that might be clickable
        ".btn", ".button", "[class*='btn']", "[class*='button']",

        # Common UI elements with identifiers
        "div[id]", "span[id]"
    ]

    # Process each selector
    for selector in selectors:
        try:
            found_elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for element in found_elements:
                try:
                    # Create a unique selector for this element
                    unique_selector = generate_unique_selector(driver, element)

                    # Get element attributes
                    tag_name = element.tag_name
                    element_id = element.get_attribute("id") or ""
                    element_class = element.get_attribute("class") or ""
                    element_name = element.get_attribute("name") or ""
                    element_type = element.get_attribute("type") or ""
                    element_value = element.get_attribute("value") or ""
                    element_text = element.text.strip() or ""
                    element_placeholder = element.get_attribute("placeholder") or ""
                    element_role = element.get_attribute("role") or ""

                    # Check if the element is visible
                    visible = element.is_displayed()

                    # Generate a descriptive name for the element
                    name = generate_element_name({
                        "tag": tag_name,
                        "id": element_id,
                        "class": element_class,
                        "name": element_name,
                        "type": element_type,
                        "text": element_text,
                        "placeholder": element_placeholder,
                        "role": element_role
                    })

                    # Add the element to the list
                    elements.append({
                        "name": name,
                        "selector": unique_selector,
                        "attributes": {
                            "tag": tag_name,
                            "id": element_id,
                            "class": element_class,
                            "name": element_name,
                            "type": element_type,
                            "value": element_value,
                            "text": element_text,
                            "placeholder": element_placeholder,
                            "role": element_role,
                            "visible": visible
                        }
                    })
                except StaleElementReferenceException:
                    # Skip stale elements
                    continue
                except Exception as e:
                    # Skip elements that cause errors
                    logger.warning(f"Error processing element with selector {selector}: {e}")
                    continue
        except Exception as e:
            logger.warning(f"Error finding elements with selector {selector}: {e}")
            continue

    return elements

def find_elements_by_js(driver):
    """
    Find elements using JavaScript to detect all interactive elements.

    Args:
        driver: WebDriver instance

    Returns:
        list: List of detected elements
    """
    script = """
    function getAllInteractiveElements() {
        // Get all elements in the document
        const allElements = document.querySelectorAll('*');
        const interactiveElements = [];

        // Check each element
        for (let element of allElements) {
            // Skip hidden elements but include those that might be conditionally visible
            if (element.offsetParent === null && element.tagName !== 'BODY' &&
                getComputedStyle(element).display === 'none' &&
                getComputedStyle(element).visibility === 'hidden') continue;

            const tagName = element.tagName.toLowerCase();
            const hasClickHandler = element.onclick || element.getAttribute('onclick');
            const hasChangeHandler = element.onchange || element.getAttribute('onchange');
            const hasInputHandler = element.oninput || element.getAttribute('oninput');
            const hasKeyHandler = element.onkeydown || element.getAttribute('onkeydown') ||
                                element.onkeyup || element.getAttribute('onkeyup');
            const hasMouseHandler = element.onmouseover || element.getAttribute('onmouseover') ||
                                  element.onmouseout || element.getAttribute('onmouseout');
            const role = element.getAttribute('role');
            const id = element.id || '';
            const className = element.className || '';
            const name = element.name || '';
            const type = element.type || '';

            // Check if element has any attributes that suggest interactivity
            const hasInteractiveAttributes = element.hasAttribute('tabindex') ||
                                           element.hasAttribute('aria-label') ||
                                           element.hasAttribute('aria-labelledby') ||
                                           element.hasAttribute('aria-describedby') ||
                                           element.hasAttribute('aria-controls') ||
                                           element.hasAttribute('aria-expanded') ||
                                           element.hasAttribute('aria-haspopup') ||
                                           element.hasAttribute('aria-selected') ||
                                           element.hasAttribute('aria-checked');

            // Check if element has classes or IDs suggesting interactivity
            const hasInteractiveClassOrId =
                /btn|button|link|submit|cancel|menu|tab|dropdown|select|checkbox|radio|toggle|switch|slider|scroll|drag|click|tap|swipe/i.test(id) ||
                /btn|button|link|submit|cancel|menu|tab|dropdown|select|checkbox|radio|toggle|switch|slider|scroll|drag|click|tap|swipe/i.test(className);

            const isInteractive =
                // Standard interactive elements
                tagName === 'a' ||
                tagName === 'button' ||
                tagName === 'input' ||
                tagName === 'select' ||
                tagName === 'textarea' ||
                tagName === 'label' ||
                tagName === 'option' ||
                // Elements with interactive roles
                role === 'button' ||
                role === 'link' ||
                role === 'checkbox' ||
                role === 'radio' ||
                role === 'tab' ||
                role === 'menuitem' ||
                // Elements with event handlers
                hasClickHandler ||
                hasChangeHandler ||
                hasInputHandler ||
                hasKeyHandler ||
                hasMouseHandler ||
                // Elements with interactive attributes
                hasInteractiveAttributes ||
                // Elements with interactive classes or IDs
                hasInteractiveClassOrId;

            // Include all elements with id or name attributes as they might be important
            const isIdentifiable = id || name;

            if (isInteractive || isIdentifiable) {
                // Get element info
                const rect = element.getBoundingClientRect();

                // Include elements that might be conditionally visible
                if (rect.width > 0 || rect.height > 0 ||
                    getComputedStyle(element).display !== 'none' ||
                    getComputedStyle(element).visibility !== 'hidden') {

                    // Get computed style for more accurate visibility check
                    const style = getComputedStyle(element);
                    const isVisible = style.display !== 'none' && style.visibility !== 'hidden' &&
                                    style.opacity !== '0' && rect.width > 0 && rect.height > 0;

                    interactiveElements.push({
                        element: element,
                        tagName: tagName,
                        id: id,
                        name: name,
                        className: className,
                        type: type,
                        value: element.value || '',
                        text: element.textContent.trim() || '',
                        placeholder: element.placeholder || '',
                        role: role || '',
                        x: rect.x,
                        y: rect.y,
                        width: rect.width,
                        height: rect.height,
                        isVisible: isVisible
                    });
                }
            }
        }

        return interactiveElements;
    }

    return getAllInteractiveElements();
    """

    js_elements = []

    try:
        # Execute JavaScript to find interactive elements
        elements_data = driver.execute_script(script)

        # Process each element
        for data in elements_data:
            try:
                # Skip if element is None or invalid
                if not data:
                    continue

                # Generate a unique selector based on element data
                element_tag = data.get('tagName', '').lower()
                element_id = data.get('id', '')
                element_class = data.get('className', '')
                element_type = data.get('type', '')
                element_name = data.get('name', '')

                # Build the selector
                selector_parts = []
                if element_id:
                    selector_parts.append(f"#{element_id}")
                elif element_name:
                    selector_parts.append(f"{element_tag}[name='{element_name}']")
                elif element_class:
                    natural_classes = filter_natural_classes(element_class)
                    if natural_classes:
                        selector = element_tag
                        for class_name in natural_classes:
                            selector += f".{class_name}"
                        selector_parts.append(selector)
                else:
                    selector_parts.append(element_tag)

                selector = selector_parts[0]

                # Generate a name based on element data
                name = generate_element_name({
                    "tag": element_tag,
                    "id": element_id,
                    "class": element_class,
                    "name": element_name,
                    "type": element_type,
                    "text": data.get('text', ''),
                    "placeholder": data.get('placeholder', ''),
                    "role": data.get('role', '')
                })

                # Add the element to the list
                js_elements.append({
                    "name": name,
                    "selector": selector,
                    "attributes": {
                        "tag": element_tag,
                        "id": element_id,
                        "class": element_class,
                        "name": element_name,
                        "type": element_type,
                        "value": data.get('value', ''),
                        "text": data.get('text', ''),
                        "placeholder": data.get('placeholder', ''),
                        "role": data.get('role', ''),
                        "visible": data.get('isVisible', False)
                    }
                })
            except StaleElementReferenceException:
                # Skip stale elements
                continue
            except Exception as e:
                # Skip elements that cause errors
                logger.warning(f"Error processing JS element: {e}")
                continue
    except Exception as e:
        logger.error(f"Error executing JavaScript to find elements: {e}")

    return js_elements

def filter_important_elements(elements):
    """
    Filter and prioritize important UI elements while removing duplicates.

    Args:
        elements (list): List of detected elements

    Returns:
        list: Filtered list of important elements
    """
    # Define high-priority interactive elements only
    important_tags = {
        'input': 10,
        'button': 10,
        'a': 9,
        'select': 9,
        'textarea': 8
    }

    # Focus on key identifying attributes only
    important_attributes = [
        'id', 'name', 'type', 'role',
        'placeholder', 'value', 'href'
    ]

    filtered_elements = []
    seen_selectors = set()

    for element in elements:
        # Skip if we've seen this selector before
        if element['selector'] in seen_selectors:
            continue

        attrs = element['attributes']
        tag = attrs.get('tag', '').lower()

        # Calculate element importance score
        score = 0

        # Base score from tag type
        score += important_tags.get(tag, 0)

        # Boost score for interactive elements
        if attrs.get('type') in ['submit', 'button', 'text', 'password', 'email', 'checkbox', 'radio']:
            score += 5

        # Boost score for elements with important attributes
        for attr in important_attributes:
            if attrs.get(attr):
                score += 2

        # Boost score for visible elements
        if attrs.get('visible', False):
            score += 3

        # Boost score for elements with text content
        if attrs.get('text'):
            score += 2

        # Only include elements with a minimum importance score
        if score >= 5:
            element['importance_score'] = score
            filtered_elements.append(element)
            seen_selectors.add(element['selector'])

    # Sort by importance score
    filtered_elements.sort(key=lambda x: x.get('importance_score', 0), reverse=True)

    return filtered_elements

def filter_elements_by_locator_strategy(elements, locator_strategy, locator_value=None):
    """
    Filter elements based on the locator strategy specified in the step table.

    Args:
        elements (list): List of detected elements
        locator_strategy (str): Locator strategy from step table (css, xpath, id, name, etc.)
        locator_value (str, optional): Specific locator value to match

    Returns:
        list: Filtered list of elements matching the locator strategy
    """
    if not elements:
        return []

    if not locator_strategy or locator_strategy.lower() in ['none', 'n/a', '']:
        return elements

    filtered_elements = []
    locator_strategy = locator_strategy.lower()

    # Use the shared mapping of locator strategies to element attributes
    attribute = STRATEGY_TO_ATTRIBUTE.get(locator_strategy)

    # Get the attribute to check based on the locator strategy
    attribute = strategy_to_attribute.get(locator_strategy)

    if not attribute:
        logging.warning(f"Unknown locator strategy: {locator_strategy}")
        return elements

    for element in elements:
        attrs = element.get('attributes', {})

        # Check if the element has the attribute corresponding to the locator strategy
        if attribute == 'selector':
            # For CSS selectors, check if the element matches the provided selector
            try:
                matching_elements = driver.find_elements_by_css_selector(locator_value)
                element_matches = any(e == element['web_element'] for e in matching_elements)
            except Exception as e:
                logging.warning(f"Error validating CSS selector '{locator_value}': {e}")
                element_matches = False
        elif attribute == 'xpath':
            # For XPath, we need to check if the element can be found with XPath
            # Since we don't have XPath info in the element data, we'll just include all elements
            element_matches = True
        else:
            # For other attributes, check if the element has the attribute
            element_matches = bool(attrs.get(attribute))

            # If a specific locator value is provided, check if it matches
            if locator_value and element_matches:
                attr_value = attrs.get(attribute, '').lower()
                lower_locator_value = locator_value.lower()

                # For partial matches like partial_link_text
                if locator_strategy == 'partial_link_text':
                    element_matches = lower_locator_value in attr_value
                else:
                    element_matches = attr_value == lower_locator_value

        if element_matches:
            # Add a score boost for elements matching the locator strategy
            element['locator_strategy_match'] = True
            element['locator_strategy_score'] = 10

            # If a specific locator value is provided and matches, add an extra boost
            if locator_value and locator_value.lower() in str(attrs.get(attribute, '')).lower():
                element['locator_value_match'] = True
                element['locator_strategy_score'] += 10

            filtered_elements.append(element)

    # If no elements match the locator strategy, return all elements
    if not filtered_elements:
        logging.warning(f"No elements match locator strategy: {locator_strategy}")
        return elements

    # Sort by locator strategy score
    filtered_elements.sort(key=lambda x: x.get('locator_strategy_score', 0), reverse=True)

    return filtered_elements

def generate_element_name(attrs):
    """
    Generate a descriptive name for an element based on its attributes.

    Args:
        attrs (dict): Element attributes

    Returns:
        str: Descriptive name for the element
    """
    tag = attrs.get('tag', '').lower()
    element_id = attrs.get('id', '')
    element_name = attrs.get('name', '')
    element_text = attrs.get('text', '')
    element_placeholder = attrs.get('placeholder', '')
    element_type = attrs.get('type', '')
    element_role = attrs.get('role', '')

    name_parts = []

    # Add tag name
    if tag:
        name_parts.append(tag)

    # Add type if available
    if element_type:
        name_parts.append(element_type)

    # Add role if available
    if element_role:
        name_parts.append(element_role)

    # Add text or placeholder if available
    if element_text:
        # Limit text length
        if len(element_text) > 30:
            element_text = element_text[:27] + "..."
        name_parts.append(f"'{element_text}'")
    elif element_placeholder:
        # Limit placeholder length
        if len(element_placeholder) > 30:
            element_placeholder = element_placeholder[:27] + "..."
        name_parts.append(f"placeholder='{element_placeholder}'")

    # Add ID or name if available
    if element_id:
        name_parts.append(f"id='{element_id}'")
    elif element_name:
        name_parts.append(f"name='{element_name}'")

    return " ".join(name_parts)

def generate_unique_selector(driver, element):
    """
    Generate a unique CSS selector for an element.

    Args:
        driver: WebDriver instance
        element: Element to generate selector for

    Returns:
        str: Unique CSS selector
    """
    try:
        # Try to get element ID
        element_id = element.get_attribute("id")
        if element_id:
            selector = f"#{element_id}"
            if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                return selector

        # Try to get element name
        element_name = element.get_attribute("name")
        if element_name:
            selector = f"[name='{element_name}']"
            if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                return selector

        # Get tag name
        tag_name = element.tag_name

        # Try with class names (filtered to exclude GRETAH internal classes)
        class_name = element.get_attribute("class")
        if class_name:
            natural_classes = filter_natural_classes(class_name)
            if natural_classes:
                selector = tag_name
                for cls in natural_classes:
                    selector += f".{cls}"
                if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                    return selector

        # Try with other attributes
        for attr in ["type", "role", "aria-label", "placeholder"]:
            attr_value = element.get_attribute(attr)
            if attr_value:
                selector = f"{tag_name}[{attr}='{attr_value}']"
                if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                    return selector

        # If all else fails, use a general selector
        return tag_name
    except Exception as e:
        print(f"Error generating unique selector: {e}")
        return element.tag_name

def find_ui_elements(url, wait_time=5, scroll=True, headless=False, locator_strategy=None, locator_value=None):
    """
    Find important interactive UI elements on a webpage.

    Args:
        url (str): URL of the webpage to analyze
        wait_time (int): Time to wait for page to load in seconds
        scroll (bool): Whether to scroll the page to find more elements
        headless (bool): Whether to run in headless mode (default: False for better debugging)
        locator_strategy (str, optional): Locator strategy from step table (css, xpath, id, name, etc.)
        locator_value (str, optional): Specific locator value to match

    Returns:
        list: List of detected elements
    """
    print(f"Starting element detection for: {url}")
    if locator_strategy:
        print(f"Using locator strategy: {locator_strategy}" + (f" with value: {locator_value}" if locator_value else ""))

    # Setup driver with consistent default behavior
    driver = setup_webdriver(headless=headless)
    if not driver:
        print("Failed to set up WebDriver")
        return []

    try:
        # Navigate to URL with reduced wait time
        print(f"Navigating to {url}")
        driver.get(url)

        # Use WebDriverWait instead of sleep for better performance
        WebDriverWait(driver, wait_time).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # Single smooth scroll to handle dynamic loading
        if scroll:
            print("Quick scroll to load dynamic content...")
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)  # Brief wait for dynamic content

            # Scroll back to top
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

        # Try multiple methods to find elements
        print("Finding elements using CSS selectors...")
        css_elements = find_elements_by_css(driver)

        print("Finding elements using JavaScript...")
        js_elements = find_elements_by_js(driver)

        # Combine and deduplicate elements
        all_elements = css_elements + js_elements

        # Filter and deduplicate
        print("Filtering and deduplicating elements...")
        filtered_elements = filter_important_elements(all_elements)

        # Apply locator strategy filtering if provided
        if locator_strategy:
            print(f"Filtering elements by locator strategy: {locator_strategy}")
            filtered_elements = filter_elements_by_locator_strategy(
                filtered_elements,
                locator_strategy,
                locator_value
            )
            print(f"Found {len(filtered_elements)} elements matching locator strategy: {locator_strategy}")

        print(f"Found {len(filtered_elements)} important unique elements out of {len(all_elements)} total elements")
        return filtered_elements

    except Exception as e:
        print(f"Error during element detection: {e}")
        return []

    finally:
        try:
            driver.quit()
        except:
            pass
