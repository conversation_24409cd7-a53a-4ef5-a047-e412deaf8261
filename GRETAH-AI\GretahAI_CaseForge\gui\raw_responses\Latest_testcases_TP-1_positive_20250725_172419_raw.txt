```json
[
  {
    "scenario_name": "Successful Login with Valid Credentials",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify that the user is able to successfully log in with valid credentials.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the 'User ID' field.", "expected_result": "Username should be successfully entered in the 'User ID' field."},
      {"action": "Verify if user is able to enter valid password in the 'Password' field.", "expected_result": "Password should be successfully entered in the 'Password' field."},
      {"action": "Verify if user is able to click the 'Login' button.", "expected_result": "User should be redirected to the home page."}
    ]
  },
  {
    "scenario_name": "User ID and Password Field Presence",
    "type": "positive",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify the presence of 'User ID' and 'Password' fields on the login page.",
    "steps": [
      {"action": "Verify if user is able to navigate to the login page.", "expected_result": "Login page should be displayed."},
      {"action": "Verify if user is able to see the 'User ID' field.", "expected_result": "'User ID' field should be visible on the page."},
      {"action": "Verify if user is able to see the 'Password' field.", "expected_result": "'Password' field should be visible on the page."}
    ]
  }
]
```