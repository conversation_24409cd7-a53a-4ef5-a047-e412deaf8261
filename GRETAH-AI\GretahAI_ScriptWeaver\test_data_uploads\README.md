# Test Data Uploads Directory

This directory contains files uploaded as test data for ScriptWeaver test automation.

## Directory Structure

- `images/` - Image files (JPG, PNG, GIF, etc.) for UI testing
- `documents/` - Document files (PDF, DOCX, TXT, etc.) for upload testing  
- `data_files/` - Data files (CSV, JSON, XML, etc.) for data-driven testing
- `archives/` - Archive files (ZIP) for bulk upload testing
- `metadata/` - File metadata and reference information

## File Management

Files are automatically organized by type and include metadata for tracking.
Old files are automatically cleaned up to prevent storage bloat.

## Security

All uploaded files are validated for type and size before storage.
Suspicious content is rejected to maintain system security.

## File Naming Convention

Uploaded files are renamed using the following pattern:
```
step_{step_number}_{timestamp}_{hash}.{extension}
```

Example: `step_3_20250121_143022_a1b2c3d4.jpg`

## Supported File Types

### Images (Max: 10MB)
- `.jpg`, `.jpeg` - JPEG images
- `.png` - PNG images  
- `.gif` - GIF images
- `.bmp` - Bitmap images
- `.svg` - SVG vector images
- `.webp` - WebP images

### Documents (Max: 25MB)
- `.pdf` - PDF documents
- `.docx` - Word documents (new format)
- `.doc` - Word documents (legacy format)
- `.txt` - Plain text files
- `.rtf` - Rich text format

### Data Files (Max: 50MB)
- `.csv` - Comma-separated values
- `.json` - JSON data files
- `.xml` - XML data files
- `.xlsx` - Excel spreadsheets (new format)
- `.xls` - Excel spreadsheets (legacy format)

### Archives (Max: 100MB)
- `.zip` - ZIP archives

## Usage in Tests

Uploaded files are automatically integrated into generated test scripts with:

1. **File Path Resolution**: Absolute paths are resolved automatically
2. **Selenium Integration**: File upload automation code is generated
3. **Error Handling**: File existence checks and error handling
4. **Cross-Platform Support**: Works on Windows, macOS, and Linux

## Cleanup Policy

- Files older than 7 days are automatically cleaned up
- Orphaned metadata files are removed
- Quarantined files are retained for security analysis

## Security Features

- **File Type Validation**: Only approved file types are accepted
- **Size Limits**: Files exceeding limits are rejected
- **Content Scanning**: Files are scanned for malicious content
- **Quarantine System**: Suspicious files are isolated

## Best Practices

1. **Use Descriptive Names**: Upload files with clear, descriptive names
2. **Appropriate Sizes**: Keep files reasonably sized for test performance
3. **Clean Organization**: Remove unused test files regularly
4. **Security Awareness**: Only upload files from trusted sources

## Troubleshooting

### File Upload Issues
- Check file size against category limits
- Verify file type is supported
- Review security warnings in the UI

### Script Generation Problems
- Ensure uploaded files haven't been moved or deleted
- Check file paths in generated scripts
- Verify Selenium locators for file input elements

### Performance Issues
- Use smaller files for faster test execution
- Monitor disk space usage
- Clean up old test files regularly

## Integration with ScriptWeaver

Files uploaded here are automatically:

1. **Validated** for security and compatibility
2. **Stored** with metadata for tracking
3. **Referenced** in test data JSON files
4. **Integrated** into generated PyTest scripts
5. **Cleaned up** after test completion

## File Metadata

Each uploaded file includes metadata:

```json
{
  "type": "file_upload",
  "file_path": "test_data_uploads/images/step_3_20250121_143022_a1b2c3d4.jpg",
  "original_name": "profile_picture.jpg",
  "file_type": "image/jpeg",
  "file_category": "images",
  "file_size": 245760,
  "upload_timestamp": "2025-01-21T14:30:22",
  "step_no": "3",
  "description": "Profile picture for user registration testing"
}
```

## Security Reports

Security validation generates detailed reports:

```json
{
  "filename": "test_file.jpg",
  "file_size": 245760,
  "checks_performed": ["file_extension", "file_size", "magic_bytes", "content_patterns"],
  "threats_detected": [],
  "risk_level": "low"
}
```

## Support

For issues with file uploads:

1. Check the main documentation: `docs/FILE_UPLOAD_FEATURE.md`
2. Review debug logs for detailed error information
3. Consult the troubleshooting section above
4. Contact the development team for advanced support

---

**Note**: This directory is automatically managed by ScriptWeaver. 
Manual file modifications may cause issues with test execution.

© 2025 Cogniron All Rights Reserved.
