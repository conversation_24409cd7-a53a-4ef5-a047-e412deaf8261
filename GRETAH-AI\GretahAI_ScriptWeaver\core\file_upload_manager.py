"""
File Upload Manager for GretahAI ScriptWeaver

This module provides comprehensive file upload functionality for test data,
including file validation, storage management, and security measures.

Key Features:
- Multi-format file support (images, documents, data files)
- Secure file validation and storage
- File metadata tracking and management
- Integration with existing test data workflow
- Automatic cleanup and organization

© 2025 Cogniron All Rights Reserved.
"""

import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import json

# Import GRETAH standardized logging
from debug_utils import debug


class FileUploadManager:
    """
    Manages file uploads for test data in ScriptWeaver.
    
    Provides secure file handling, validation, storage, and metadata management
    for uploaded test files including images, documents, and data files.
    """
    
    # Supported file types and their categories
    SUPPORTED_FILE_TYPES = {
        'images': {
            'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'],
            'mime_types': ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp'],
            'max_size_mb': 10,
            'description': 'Image files for UI testing, profile pictures, etc.'
        },
        'documents': {
            'extensions': ['.pdf', '.docx', '.doc', '.txt', '.rtf'],
            'mime_types': ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
                          'application/msword', 'text/plain', 'application/rtf'],
            'max_size_mb': 25,
            'description': 'Document files for upload testing'
        },
        'data_files': {
            'extensions': ['.csv', '.json', '.xml', '.xlsx', '.xls'],
            'mime_types': ['text/csv', 'application/json', 'application/xml', 'text/xml',
                          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                          'application/vnd.ms-excel'],
            'max_size_mb': 50,
            'description': 'Data files for data-driven testing'
        },
        'archives': {
            'extensions': ['.zip'],
            'mime_types': ['application/zip'],
            'max_size_mb': 100,
            'description': 'Archive files for bulk upload testing'
        }
    }
    
    def __init__(self, base_upload_dir: str = "test_data_uploads"):
        """
        Initialize the FileUploadManager.

        Args:
            base_upload_dir: Base directory for storing uploaded files
        """
        # Ensure the path is relative to current working directory
        if os.path.isabs(base_upload_dir):
            self.base_upload_dir = Path(base_upload_dir)
        else:
            self.base_upload_dir = Path.cwd() / base_upload_dir

        self.metadata_dir = self.base_upload_dir / "metadata"

        # Create directory structure
        self._create_directory_structure()

        debug("FileUploadManager initialized",
              stage="file_upload", operation="initialization",
              context={'base_dir': str(self.base_upload_dir)})
    
    def _create_directory_structure(self):
        """Create the required directory structure for file uploads."""
        try:
            # Create main directories
            for category in self.SUPPORTED_FILE_TYPES.keys():
                category_dir = self.base_upload_dir / category
                category_dir.mkdir(parents=True, exist_ok=True)
            
            # Create metadata directory
            self.metadata_dir.mkdir(parents=True, exist_ok=True)
            
            debug("Directory structure created successfully",
                  stage="file_upload", operation="directory_creation",
                  context={'directories_created': list(self.SUPPORTED_FILE_TYPES.keys())})
                  
        except Exception as e:
            debug(f"Failed to create directory structure: {e}",
                  stage="file_upload", operation="directory_creation_error",
                  context={'error': str(e)})
            raise
    
    def validate_file(self, uploaded_file, file_content: bytes) -> Tuple[bool, str, str]:
        """
        Validate an uploaded file for security and compatibility.

        Args:
            uploaded_file: Streamlit uploaded file object
            file_content: File content as bytes

        Returns:
            Tuple of (is_valid, error_message, file_category)
        """
        try:
            filename = uploaded_file.name
            file_size = len(file_content)

            # Get file extension
            file_ext = Path(filename).suffix.lower()

            # Detect MIME type
            mime_type, _ = mimetypes.guess_type(filename)

            debug(f"Validating file: {filename}",
                  stage="file_upload", operation="file_validation",
                  context={
                      'filename': filename,
                      'file_size': file_size,
                      'file_extension': file_ext,
                      'mime_type': mime_type
                  })

            # Find matching category
            file_category = None
            for category, config in self.SUPPORTED_FILE_TYPES.items():
                if file_ext in config['extensions']:
                    file_category = category
                    break

            if not file_category:
                return False, f"Unsupported file type: {file_ext}", ""

            # Validate MIME type
            category_config = self.SUPPORTED_FILE_TYPES[file_category]
            if mime_type and mime_type not in category_config['mime_types']:
                return False, f"MIME type mismatch for {file_ext} files", ""

            # Validate file size
            max_size_bytes = category_config['max_size_mb'] * 1024 * 1024
            if file_size > max_size_bytes:
                return False, f"File too large. Maximum size: {category_config['max_size_mb']}MB", ""

            # Enhanced security validation
            try:
                from core.file_security_validator import FileSecurityValidator
                security_validator = FileSecurityValidator()

                is_safe, security_warnings, security_report = security_validator.validate_file_security(
                    file_content, filename, file_category
                )

                if not is_safe:
                    # Quarantine suspicious files
                    quarantine_path = security_validator.quarantine_file(
                        file_content, filename, security_report
                    )

                    # Get security recommendations
                    recommendations = security_validator.get_security_recommendations(security_report)

                    error_msg = f"Security validation failed: {'; '.join(security_warnings[:3])}"
                    if len(security_warnings) > 3:
                        error_msg += f" (and {len(security_warnings) - 3} more issues)"

                    debug(f"File security validation failed: {filename}",
                          stage="file_upload", operation="security_validation_failed",
                          context={
                              'filename': filename,
                              'threats': security_report.get('threats_detected', []),
                              'risk_level': security_report.get('risk_level', 'unknown'),
                              'quarantine_path': quarantine_path
                          })

                    return False, error_msg, ""

                # Log security warnings even for safe files
                if security_warnings:
                    debug(f"File security warnings for {filename}: {security_warnings}",
                          stage="file_upload", operation="security_warnings",
                          context={'filename': filename, 'warnings': security_warnings})

            except Exception as security_error:
                debug(f"Security validation error for {filename}: {security_error}",
                      stage="file_upload", operation="security_validation_error",
                      context={'filename': filename, 'error': str(security_error)})
                # Fall back to basic security checks if advanced validation fails
                if self._contains_suspicious_content(filename, file_content):
                    return False, "File contains suspicious content", ""

            debug(f"File validation successful: {filename}",
                  stage="file_upload", operation="file_validation_success",
                  context={'filename': filename, 'category': file_category})

            return True, "", file_category

        except Exception as e:
            debug(f"File validation error: {e}",
                  stage="file_upload", operation="file_validation_error",
                  context={'error': str(e), 'filename': getattr(uploaded_file, 'name', 'unknown')})
            return False, f"Validation error: {str(e)}", ""
    
    def _contains_suspicious_content(self, filename: str, file_content: bytes) -> bool:
        """
        Check for suspicious content in uploaded files.
        
        Args:
            filename: Name of the file
            file_content: File content as bytes
            
        Returns:
            True if suspicious content is detected
        """
        # Check for suspicious filename patterns
        suspicious_patterns = ['../', '..\\', '<script', '<?php', '#!/bin']
        filename_lower = filename.lower()
        
        for pattern in suspicious_patterns:
            if pattern in filename_lower:
                return True
        
        # Check for executable file signatures
        executable_signatures = [
            b'\x4d\x5a',  # PE executable
            b'\x7f\x45\x4c\x46',  # ELF executable
            b'\xfe\xed\xfa',  # Mach-O executable
        ]
        
        for signature in executable_signatures:
            if file_content.startswith(signature):
                return True
        
        return False
    
    def save_uploaded_file(self, uploaded_file, file_content: bytes, step_no: str, 
                          description: str = "") -> Dict[str, Any]:
        """
        Save an uploaded file to the appropriate directory with metadata.
        
        Args:
            uploaded_file: Streamlit uploaded file object
            file_content: File content as bytes
            step_no: Test step number this file is associated with
            description: Optional description of the file's purpose
            
        Returns:
            Dictionary containing file metadata
        """
        try:
            # Validate file first
            is_valid, error_msg, file_category = self.validate_file(uploaded_file, file_content)
            if not is_valid:
                raise ValueError(error_msg)
            
            # Generate unique filename
            original_name = uploaded_file.name
            file_ext = Path(original_name).suffix.lower()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_hash = hashlib.md5(file_content).hexdigest()[:8]
            unique_filename = f"step_{step_no}_{timestamp}_{file_hash}{file_ext}"
            
            # Determine storage path
            category_dir = self.base_upload_dir / file_category
            file_path = category_dir / unique_filename
            
            # Save file
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            # Create metadata
            metadata = {
                'type': 'file_upload',
                'file_path': str(file_path),
                'relative_path': str(file_path.relative_to(Path.cwd())),
                'original_name': original_name,
                'unique_filename': unique_filename,
                'file_type': uploaded_file.type or mimetypes.guess_type(original_name)[0],
                'file_category': file_category,
                'file_size': len(file_content),
                'file_hash': file_hash,
                'upload_timestamp': datetime.now().isoformat(),
                'step_no': step_no,
                'description': description,
                'mime_type': mimetypes.guess_type(original_name)[0]
            }
            
            # Save metadata
            self._save_file_metadata(metadata)
            
            debug(f"File saved successfully: {unique_filename}",
                  stage="file_upload", operation="file_save_success",
                  context={
                      'original_name': original_name,
                      'unique_filename': unique_filename,
                      'category': file_category,
                      'step_no': step_no
                  })
            
            return metadata
            
        except Exception as e:
            debug(f"Failed to save uploaded file: {e}",
                  stage="file_upload", operation="file_save_error",
                  context={'error': str(e), 'filename': getattr(uploaded_file, 'name', 'unknown')})
            raise
    
    def _save_file_metadata(self, metadata: Dict[str, Any]):
        """Save file metadata to JSON file."""
        try:
            metadata_file = self.metadata_dir / f"file_{metadata['file_hash']}.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        except Exception as e:
            debug(f"Failed to save file metadata: {e}",
                  stage="file_upload", operation="metadata_save_error",
                  context={'error': str(e)})
    
    def get_uploaded_files_for_step(self, step_no: str) -> List[Dict[str, Any]]:
        """
        Get all uploaded files for a specific test step.
        
        Args:
            step_no: Test step number
            
        Returns:
            List of file metadata dictionaries
        """
        try:
            files = []
            if not self.metadata_dir.exists():
                return files
            
            for metadata_file in self.metadata_dir.glob("file_*.json"):
                try:
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    
                    if metadata.get('step_no') == str(step_no):
                        # Verify file still exists
                        if Path(metadata['file_path']).exists():
                            files.append(metadata)
                        else:
                            # Clean up orphaned metadata
                            metadata_file.unlink()
                            
                except Exception as e:
                    debug(f"Error reading metadata file {metadata_file}: {e}",
                          stage="file_upload", operation="metadata_read_error",
                          context={'metadata_file': str(metadata_file), 'error': str(e)})
            
            return files
            
        except Exception as e:
            debug(f"Error getting uploaded files for step {step_no}: {e}",
                  stage="file_upload", operation="get_files_error",
                  context={'step_no': step_no, 'error': str(e)})
            return []
    
    def delete_uploaded_file(self, file_hash: str) -> bool:
        """
        Delete an uploaded file and its metadata.
        
        Args:
            file_hash: Hash identifier of the file to delete
            
        Returns:
            True if deletion was successful
        """
        try:
            metadata_file = self.metadata_dir / f"file_{file_hash}.json"
            
            if metadata_file.exists():
                # Load metadata to get file path
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # Delete actual file
                file_path = Path(metadata['file_path'])
                if file_path.exists():
                    file_path.unlink()
                
                # Delete metadata
                metadata_file.unlink()
                
                debug(f"File deleted successfully: {file_hash}",
                      stage="file_upload", operation="file_delete_success",
                      context={'file_hash': file_hash})
                
                return True
            
            return False
            
        except Exception as e:
            debug(f"Error deleting file {file_hash}: {e}",
                  stage="file_upload", operation="file_delete_error",
                  context={'file_hash': file_hash, 'error': str(e)})
            return False
    
    def cleanup_files_for_test_case(self, test_case_id: str):
        """
        Clean up all uploaded files for a specific test case.
        
        Args:
            test_case_id: Test case identifier
        """
        try:
            if not self.metadata_dir.exists():
                return
            
            deleted_count = 0
            for metadata_file in self.metadata_dir.glob("file_*.json"):
                try:
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    
                    # Check if file belongs to this test case (you may need to adjust this logic)
                    # For now, we'll clean up files older than 24 hours
                    upload_time = datetime.fromisoformat(metadata['upload_timestamp'])
                    if (datetime.now() - upload_time).days > 1:
                        file_hash = metadata['file_hash']
                        if self.delete_uploaded_file(file_hash):
                            deleted_count += 1
                            
                except Exception as e:
                    debug(f"Error processing metadata file {metadata_file}: {e}",
                          stage="file_upload", operation="cleanup_error",
                          context={'metadata_file': str(metadata_file), 'error': str(e)})
            
            debug(f"Cleanup completed: {deleted_count} files deleted",
                  stage="file_upload", operation="cleanup_success",
                  context={'deleted_count': deleted_count, 'test_case_id': test_case_id})
                  
        except Exception as e:
            debug(f"Error during cleanup for test case {test_case_id}: {e}",
                  stage="file_upload", operation="cleanup_error",
                  context={'test_case_id': test_case_id, 'error': str(e)})
    
    def get_supported_file_types(self) -> Dict[str, Dict[str, Any]]:
        """Get information about supported file types."""
        return self.SUPPORTED_FILE_TYPES.copy()
