"""
Excel File Operations Module for GretahAI CaseForge

This module handles Excel file upload, download, and processing operations
for test case management workflows.

Functions:
- upload_edited_excel: Handle Excel file uploads
- upload_test_cases_to_excel_and_jira: Upload test cases to Excel and JIRA

© 2025 GretahAI Team
"""

import pandas as pd
import os
import json
from datetime import datetime
from typing import Optional
import streamlit as st


def upload_edited_excel(uploaded_file, current_user=None):
    """
    Processes and validates uploaded Excel files containing edited test case data.
    
    This function handles the complete workflow for processing user-uploaded Excel files
    containing modified test cases. It performs comprehensive validation, data processing,
    and database integration to maintain data integrity while supporting user editing workflows.

    Args:
        uploaded_file (streamlit.UploadedFile): Streamlit uploaded file object containing
                                               Excel test case data
        current_user (str, optional): Username of the person uploading the file for 
                                     audit tracking. Defaults to None.

    Returns:
        tuple: (success: bool, message: str, details: dict)
            - success: True if upload processed successfully, False otherwise
            - message: Human-readable status message
            - details: Dictionary containing processing details and statistics
    """
    # If no user is provided, default to admin
    if not current_user:
        current_user = "admin"
    try:
        # Create edited_excel folder if it doesn't exist
        edited_folder = "edited_excel"
        os.makedirs(edited_folder, exist_ok=True)

        # Create csv_exports folder if it doesn't exist
        csv_folder = "csv_exports"
        os.makedirs(csv_folder, exist_ok=True)

        # Read the Excel file to validate it
        try:
            df = pd.read_excel(uploaded_file)

            # Basic validation - check for required columns
            required_columns = [
                "Timestamp", "Project", "Feature", "User Story ID", "Test Case ID",
                "Test Case Objective", "Prerequisite", "Step No", "Test Steps",
                "Expected Result", "Actual Result", "Test Status", "Priority",
                "Defect ID", "Comments", "Test Type", "Test Group"
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return False, f"Uploaded file is missing required columns: {', '.join(missing_columns)}", None

        except Exception as e:
            return False, f"Error reading Excel file: {str(e)}", None

        # Generate timestamp for the file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = uploaded_file.name
        base_name, extension = os.path.splitext(file_name)

        # Create new file name with timestamp
        new_excel_name = f"{base_name}_{timestamp}{extension}"
        new_excel_path = os.path.join(edited_folder, new_excel_name)

        # Create CSV file name with timestamp
        new_csv_name = f"{base_name}_{timestamp}.csv"
        new_csv_path = os.path.join(csv_folder, new_csv_name)

        # Save the Excel file to the edited_excel folder
        with open(new_excel_path, "wb") as f:
            f.write(uploaded_file.getbuffer())

        # Convert Excel to CSV
        df.to_csv(new_csv_path, index=False)

        # Save the edited test cases to the database
        try:
            from db_helper import save_test_cases_to_database, DATABASE_PATH

            # Extract unique JIRA IDs from the dataframe
            jira_ids = df['User Story ID'].unique().tolist()
            jira_ids = [str(id) for id in jira_ids if pd.notna(id)]

            if jira_ids:
                # Get the test type from the dataframe
                test_types = df['Test Type'].unique().tolist()
                test_types = [str(tt).lower() for tt in test_types if pd.notna(tt)]

                # Use the first test type if available, otherwise default to "all"
                test_type = test_types[0] if test_types else "all"

                # For each JIRA ID, save the test cases to the database with is_edited=True
                for jira_id in jira_ids:
                    # Filter the dataframe to only include rows for this JIRA ID
                    jira_df = df[df['User Story ID'] == jira_id]

                    if not jira_df.empty:
                        # Save to database with is_edited=True
                        db_success = save_test_cases_to_database(
                            DATABASE_PATH,
                            jira_id,
                            jira_df,
                            test_type,
                            user_name=current_user,  # Use the current logged-in user
                            is_edited=True
                        )

                        if db_success:
                            print(f"Successfully saved edited test cases for {jira_id} to database")
                        else:
                            print(f"Failed to save edited test cases for {jira_id} to database")
        except Exception as db_error:
            print(f"Error saving edited test cases to database: {str(db_error)}")

        # Try to read Jira credentials from config.json
        try:
            import json
            with open('config.json', 'r') as config_file:
                config = json.load(config_file)
                jira_url = config.get('jira_server')
                jira_username = config.get('jira_user')
                jira_password = config.get('jira_password')  # Use the password you added to config.json

                # If Jira credentials are provided in config, upload to Jira
                if jira_url and jira_username and jira_password:
                    try:
                        # Extract unique User Story IDs from the dataframe and convert to strings
                        user_story_ids = df['User Story ID'].unique().tolist()
                        user_story_ids = [str(id) for id in user_story_ids if pd.notna(id)]                        # Check if Zephyr integration is available
                        from helpers import ZEPHYR_AVAILABLE, upload_test_cases_to_jira

                        if not ZEPHYR_AVAILABLE:
                            # If still not available, return a message
                            return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. Zephyr integration is not available.", new_excel_path

                        # Upload the test cases to Jira
                        try:
                            success, message = upload_test_cases_to_jira(
                                csv_file_path=new_csv_path,
                                jira_url=jira_url,
                                jira_username=jira_username,
                                jira_password=jira_password,
                                user_story_ids=user_story_ids
                            )

                            if success:
                                return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, saved to database, and {message}", new_excel_path
                            else:
                                return False, f"Successfully saved files but failed to upload to Jira: {message}", new_excel_path
                        except Exception as e:
                            # If there's an error using the module, return a message
                            return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. Error with Zephyr integration: {str(e)}", new_excel_path
                    except Exception as e:
                        return False, f"Error uploading to Jira: {str(e)}", new_excel_path
                else:
                    return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. No Jira credentials found in config.json.", new_excel_path
        except Exception as e:
            # If there's an error reading the config file, just return a message
            return True, f"Successfully uploaded edited test cases to {new_excel_path}, converted to CSV at {new_csv_path}, and saved to database. Could not read config.json: {str(e)}", new_excel_path

    except Exception as e:
        return False, f"Error processing edited test cases: {str(e)}", None


def upload_test_cases_to_excel_and_jira(
    test_cases_df, 
    jira_id, 
    test_type, 
    jira_url=None, 
    jira_username=None, 
    jira_password=None, 
    is_edited=False, 
    user_name=None
):
    """
    Upload test cases to Excel and optionally to Jira Zephyr Scale.

    Args:
        test_cases_df: DataFrame containing test cases
        jira_id: JIRA ID (e.g., "TP-1")
        test_type: Test type (e.g., "positive", "negative", "all")
        jira_url: Jira URL (optional)
        jira_username: Jira username (optional)
        jira_password: Jira password/token (optional)
        is_edited: Whether these test cases are edited versions of existing test cases
        user_name: Name of the user who created the test cases

    Returns:
        Tuple of (success, message, excel_path)
    """
    try:
        # Create Test_cases folder if it doesn't exist
        test_cases_dir = "Test_cases"
        os.makedirs(test_cases_dir, exist_ok=True)

        # Generate timestamp for the file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create file names
        if test_type.lower() == "all":
            excel_filename = f"Latest_testcases_{jira_id}_ALL_{timestamp}.xlsx"
        else:
            excel_filename = f"Latest_testcases_{jira_id}_{test_type}_{timestamp}.xlsx"

        excel_path = os.path.join(test_cases_dir, excel_filename)

        # Save to Excel
        from helpers.excel.formatting import create_formatted_excel_from_scenarios
        create_formatted_excel_from_scenarios(
            test_cases_df,
            excel_path,
            is_dataframe=True,
            save_to_db=True,
            test_type=test_type,
            create_excel=True
        )

        # If Jira credentials are not provided, just return success for Excel
        if not jira_url or not jira_username or not jira_password:
            return True, f"Successfully saved test cases to {excel_path} and database. Jira upload skipped (credentials not provided).", excel_path        # Check if Zephyr integration is available
        from helpers import ZEPHYR_AVAILABLE, upload_test_cases_to_jira

        if not ZEPHYR_AVAILABLE:
            # If still not available, return a message
            return True, f"Successfully saved test cases to {excel_path} and database. Zephyr integration is not available.", excel_path

        # Upload directly to Jira using the REST API
        try:
            # Get user story IDs (split by comma if multiple)
            user_story_ids = [jira_id.strip()] if jira_id else []

            # Call the upload function with the DataFrame directly
            success, message = upload_test_cases_to_jira(
                dataframe=test_cases_df,  # Pass DataFrame directly instead of CSV
                jira_url=jira_url,
                jira_username=jira_username,
                jira_password=jira_password,
                user_story_ids=user_story_ids
            )

            if success:
                return True, f"Successfully saved test cases to {excel_path}, database, and uploaded to Jira Zephyr Scale.", excel_path
            else:
                return False, f"Saved test cases to {excel_path} and database, but failed to upload to Jira: {message}", excel_path

        except Exception as e:
            return False, f"Saved test cases to {excel_path} and database, but error uploading to Jira: {str(e)}", excel_path

    except Exception as e:
        return False, f"Error saving test cases: {str(e)}", None
