"""
Stage 2: Website Configuration

This module handles website URL configuration and API key management.
Maintains the StateManager pattern and follows the established architectural patterns.

Phase 3b Enhancement: Standardized logging with centralized infrastructure
"""

import os
import json
import logging
import streamlit as st

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
logger = get_stage_logger("stage2")

# Import configuration and debug utilities
from core.config import APP_CONFIG_FILE
from debug_utils import debug

@st.cache_data
def load_google_api_key():
    """
    Load Google API key from config file or environment variable.

    Uses caching to avoid repeated file I/O operations for better performance.
    Cache is automatically invalidated when the config file changes.
    """
    debug("Loading Google API key (cached)",
          stage="stage2",
          operation="api_key_loading",
          context={'config_file': APP_CONFIG_FILE, 'config_exists': os.path.exists(APP_CONFIG_FILE)})

    # Try to load from config file first
    try:
        if os.path.exists(APP_CONFIG_FILE):
            debug("Config file exists, attempting to load API key",
                  stage="stage2",
                  operation="config_file_read",
                  context={'config_file_path': APP_CONFIG_FILE})

            with open(APP_CONFIG_FILE, "r") as f:
                config = json.load(f)
                if "google_api_key" in config and config["google_api_key"]:
                    debug("API key loaded from config file",
                          stage="stage2",
                          operation="api_key_loaded_from_config",
                          context={
                              'source': 'config_file',
                              'config_file': APP_CONFIG_FILE,
                              'key_length': len(config["google_api_key"]) if config["google_api_key"] else 0
                          })
                    return config["google_api_key"]
                else:
                    debug("API key not found or empty in config file",
                          stage="stage2",
                          operation="api_key_not_in_config",
                          context={
                              'config_file': APP_CONFIG_FILE,
                              'has_key_field': "google_api_key" in config,
                              'key_empty': not config.get("google_api_key")
                          })
        else:
            debug("Config file does not exist",
                  stage="stage2",
                  operation="config_file_not_found",
                  context={'config_file_path': APP_CONFIG_FILE})
    except Exception as e:
        logger.error(f"Error loading config file: {e}")
        debug(f"Config file error: {e}",
              stage="stage2",
              operation="config_file_error",
              context={
                  'error_type': type(e).__name__,
                  'error_message': str(e),
                  'config_file': APP_CONFIG_FILE
              })

    # Fall back to environment variable
    env_key = os.environ.get("GOOGLE_API_KEY", "")
    if env_key:
        debug("API key loaded from environment variable",
              stage="stage2",
              operation="api_key_loaded_from_env",
              context={
                  'source': 'environment_variable',
                  'env_var_name': 'GOOGLE_API_KEY',
                  'key_length': len(env_key)
              })
    else:
        debug("No API key found in config or environment",
              stage="stage2",
              operation="api_key_not_found",
              context={
                  'config_file_checked': os.path.exists(APP_CONFIG_FILE),
                  'env_var_checked': True,
                  'sources_exhausted': ['config_file', 'environment_variable']
              })
    return env_key

# Import the enhanced URL validation from the dedicated module
from core.url_validation import validate_website_url

def stage2_enter_website(state):
    """Phase 2: Website Configuration."""
    debug("Entering Stage 2: Website Configuration", stage="stage2", operation="configuration")
    st.markdown("<h2 class='stage-header'>Phase 2: Website Configuration</h2>", unsafe_allow_html=True)

    # CRITICAL FIX: File dependency validation
    from ui_components.file_dependency_validator import validate_stage_dependencies
    if not validate_stage_dependencies(state, "Stage 2: Website Configuration", required_stage_number=2):
        return  # Block access if file dependencies not satisfied

    # Main configuration section
    with st.container():
        # Get existing URL from state with proper default handling
        default_url = getattr(state, 'website_url', "https://example.com")
        if not default_url:
            default_url = "https://example.com"

        website_url = st.text_input(
            "Website URL",
            value=default_url,
            help="Enter the URL of the website you want to test"
        )

        # Validate URL before processing
        is_valid_url, url_error = validate_website_url(website_url)

        # Store the URL in state manager only if it's different
        if getattr(state, 'website_url', None) != website_url:
            state.website_url = website_url
            debug("Website URL configured", stage="stage2", operation="configuration",
                  context={'url': website_url})
            logger.info(f"State change: website_url = {website_url}")

        # Show validation feedback
        if website_url and website_url != "https://example.com":
            if is_valid_url:
                st.success(f"✅ Valid URL: {website_url}")

                # Auto-advance to Stage 3 if valid URL is entered and we're EXACTLY in Stage 2
                # Don't auto-advance if we're already in Stage 3+ (cascading UI display)
                from state_manager import StateStage
                if state.current_stage == StateStage.STAGE2_WEBSITE:
                    debug("Auto-advancing to Stage 3 due to valid URL", stage="stage2", operation="validation")
                    success = state.advance_to(StateStage.STAGE3_CONVERT, f"Website URL configured: {website_url}")

                    if success:
                        # Force state update in session state and rerun to show Stage 3 immediately
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"✅ Website URL configured: {website_url}. Proceeding to Test Case Analysis."
                        debug("Stage 2 → Stage 3 transition successful", stage="stage2", operation="success")
                        st.rerun()
                        return  # Exit early since rerun will restart the function
                    else:
                        st.error("❌ Failed to advance to next stage. Please try again.")
                elif state.current_stage.get_stage_number() > 2:
                    # We're in Stage 3+ showing Stage 2 as part of cascading UI
                    # Don't auto-advance, just show the configuration
                    debug("Stage 2 UI displayed as part of cascading view", stage="stage2", operation="cascading_display",
                          context={'current_stage': state.current_stage.get_display_name()})
            else:
                st.error(f"❌ {url_error}")
        elif website_url == "https://example.com":
            st.info("💡 Please enter your actual website URL to proceed")

    # API Key section in collapsible expander
    with st.expander("API Configuration", expanded=False):
        # Try to get the API key from config or environment (cached)
        default_api_key = load_google_api_key()

        # Store the API key in state manager for later use with proper validation
        if not getattr(state, 'google_api_key', None):
            state.google_api_key = default_api_key
            if default_api_key:
                debug("API key loaded into state from config/environment", stage="stage2", operation="configuration")

        # Only show API key input if no key was found
        if not default_api_key:
            st.info("🔑 Google API key required for AI-powered features")
            api_key_col1, api_key_col2 = st.columns([3, 1])

            with api_key_col1:
                google_api_key = st.text_input(
                    "Google API Key",
                    type="password",
                    help="Required for AI features. Get your key from Google AI Studio.",
                    placeholder="Enter your Google AI Studio API key"
                )

                # Update state only if key is provided
                if google_api_key:
                    state.google_api_key = google_api_key

            with api_key_col2:
                save_button_disabled = not google_api_key
                if st.button("Save Key", help="Save to config.json", disabled=save_button_disabled):
                    if google_api_key:
                        try:
                            # Load existing config to preserve other settings
                            existing_config = {}
                            if os.path.exists(APP_CONFIG_FILE):
                                try:
                                    with open(APP_CONFIG_FILE, "r") as f:
                                        existing_config = json.load(f)
                                except Exception as e:
                                    debug("Could not load existing configuration", stage="stage2", operation="error_handling",
                                          context={'error': str(e), 'error_type': type(e).__name__})

                            # Update with new API key
                            existing_config["google_api_key"] = google_api_key

                            # Save updated config
                            with open(APP_CONFIG_FILE, "w") as f:
                                json.dump(existing_config, f, indent=2)

                            st.success("✅ API key saved to config.json")
                            debug("API key saved to config file", stage="stage2", operation="data_storage")

                            # Also set as environment variable for this session
                            os.environ["GOOGLE_API_KEY"] = google_api_key

                            # Clear cache to reload the new key
                            load_google_api_key.clear()

                            # Trigger rerun to refresh UI
                            st.rerun()

                        except Exception as e:
                            error_msg = f"Failed to save API key: {str(e)}"
                            st.error(f"❌ {error_msg}")
                            logger.error(error_msg, exc_info=True)
                            debug("API key save error", stage="stage2", operation="error_handling",
                                  context={'error': str(e), 'error_type': type(e).__name__})
                    else:
                        st.warning("⚠️ Please enter an API key first")
        else:
            # Show confirmation that API key is configured
            st.success("✅ Google API key configured")
            key_source = "config file" if os.path.exists(APP_CONFIG_FILE) else "environment variable"
            st.caption(f"Loaded from {key_source}")

            # Store the API key in state manager
            if getattr(state, 'google_api_key', None) != default_api_key:
                state.google_api_key = default_api_key
                debug("API key updated in state from cached source", stage="stage2", operation="general_operation")

    # Advanced options in collapsible expander
    with st.expander("Advanced Options", expanded=False):
        st.markdown("##### Element Detection Settings")

        # Note: AI element matching is now handled automatically through interactive element selection
        st.info("💡 Element matching is now handled automatically when you select elements interactively in Stage 4.")

        # Additional advanced options
        st.markdown("##### Browser Settings")
        col3, col4 = st.columns(2)
        with col3:
            # Headless browser mode toggle removed - using consistent default behavior
            st.info("🖥️ Browser runs in visible mode for better debugging")

        with col4:
            element_timeout = st.number_input(
                "UI Element Timeout (seconds)",
                min_value=5,
                max_value=60,
                value=state.element_timeout,
                help="How long to wait for UI elements to appear (buttons, inputs, etc.). Navigation timeouts are automatically set to 30 seconds."
            )

            if state.element_timeout != element_timeout:
                state.element_timeout = element_timeout
                debug("Element timeout preference updated", stage="stage2", operation="configuration",
                      context={'element_timeout': element_timeout})
                logger.info(f"State change: element_timeout = {element_timeout}")
                st.rerun()
                return

    debug("Stage 2 configuration completed", stage="stage2", operation="completion")
