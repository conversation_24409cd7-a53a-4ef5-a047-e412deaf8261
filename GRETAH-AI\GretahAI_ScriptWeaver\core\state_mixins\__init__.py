"""
State Management Mixins for GretahAI ScriptWeaver.

This package contains mixin classes that provide specialized functionality
for the StateManager class using composition pattern. Each mixin encapsulates
a specific domain of state management functionality.

Current state mixins:
- hybrid_editing.py: Hybrid editing methods and functionality (Phase 2)
- data_persistence.py: JSON data persistence methods and functionality (Phase 3)
- script_management.py: Script management methods and functionality (Phase 4)

Architecture Benefits:
- Clean separation of concerns using mixin pattern
- Modular functionality that can be composed as needed
- Maintainable codebase with focused responsibilities
- Type-safe interfaces with comprehensive documentation
- GRETAH logging compliance across all mixins

Usage Pattern:
    @dataclass
    class StateManager(HybridEditingMixin, DataPersistenceMixin, ScriptManagementMixin):
        # State fields defined in main class
        # Methods provided by mixins
        pass

© 2025 Cogniron All Rights Reserved.
"""

# Import submodules as they are created
from .hybrid_editing import HybridEditingMixin
from .data_persistence import DataPersistenceMixin
from .script_management import ScriptManagementMixin

__all__ = ['HybridEditingMixin', 'DataPersistenceMixin', 'ScriptManagementMixin']
