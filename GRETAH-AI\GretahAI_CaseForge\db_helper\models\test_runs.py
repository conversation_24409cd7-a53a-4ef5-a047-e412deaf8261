"""
Test run model operations for the GretahAI CaseForge database system.

This module handles CRUD operations for test runs including creation, retrieval,
updating, and management for the test run lifecycle.
"""

import sqlite3
import streamlit as st
from datetime import datetime
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_latest_test_run(database_path, jira_id, dashboard_test_type=None):
    """
    Retrieves the most recent test run for a specified JIRA ID and test type.
    
    This function queries the database to find the latest test run based on timestamp,
    filtering by JIRA ID and optionally by test type. It's essential for tracking
    the progression of test execution sessions and maintaining test run continuity.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier (e.g., "TP-1", "STORY-123")
        dashboard_test_type (str, optional): Test type filter ("positive", "negative", 
                                           "security", "performance", "all"). Defaults to None.

    Returns:
        dict or None: Dictionary containing test run details if found:
            - id: Test run unique identifier
            - jira_id: Associated JIRA ticket ID
            - test_type: Type of testing performed
            - timestamp: Creation timestamp
            - num_test_cases: Number of test cases in the run
            - status: Current status (pending, in_progress, completed, failed)
            - user_name: User who created the test run
            - notes: Additional comments or notes
        Returns None if no test run found.

    Process Flow:
        1. Establishes database connection with timeout handling
        2. Constructs SQL query with optional test type filtering
        3. Executes query to find latest test run by timestamp
        4. Converts database row to dictionary format
        5. Returns formatted test run data or None

    Example:
        latest_run = get_latest_test_run(db_path, "TP-1", "positive")
        if latest_run:
            print(f"Latest run ID: {latest_run['id']}")
            print(f"Status: {latest_run['status']}")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Build the query based on whether test type is specified
        if dashboard_test_type and dashboard_test_type.lower() != "all":
            query = """
                SELECT id, jira_id, test_type, timestamp, num_test_cases, status, user_name, notes
                FROM test_runs 
                WHERE jira_id = ? AND test_type = ?
                ORDER BY timestamp DESC 
                LIMIT 1
            """
            cursor.execute(query, (jira_id, dashboard_test_type.lower()))
        else:
            query = """
                SELECT id, jira_id, test_type, timestamp, num_test_cases, status, user_name, notes
                FROM test_runs 
                WHERE jira_id = ?
                ORDER BY timestamp DESC 
                LIMIT 1
            """
            cursor.execute(query, (jira_id,))

        result = cursor.fetchone()
        
        if result:
            return {
                'id': result['id'],
                'jira_id': result['jira_id'],
                'test_type': result['test_type'],
                'timestamp': result['timestamp'],
                'num_test_cases': result['num_test_cases'],
                'status': result['status'],
                'user_name': result['user_name'],
                'notes': result['notes']
            }
        return None
        
    except sqlite3.Error as e:
        print(f"Error getting latest test run: {e}")
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def create_test_run(database_path, jira_id, test_type, user_name=None, notes=None):
    """
    Creates a new test run record in the database.
    
    This function initializes a new test execution session by creating a test run
    record that will serve as a container for associated test cases. It handles
    proper initialization of timestamps, status, and user associations.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier (e.g., "TP-1", "STORY-123")
        test_type (str): Type of testing ("positive", "negative", "security", "performance", "all")
        user_name (str, optional): Name of user creating the test run. Defaults to None.
        notes (str, optional): Additional comments or description. Defaults to None.

    Returns:
        int or None: ID of the created test run if successful, None if failed

    Process Flow:
        1. Establishes database connection with proper settings
        2. Generates current timestamp for test run creation
        3. Inserts new test run record with initial status "pending"
        4. Returns the auto-generated test run ID
        5. Handles any database errors gracefully

    Example:
        test_run_id = create_test_run(
            db_path, "TP-1", "positive", "john_doe", "Initial test execution"
        )
        if test_run_id:
            print(f"Created test run with ID: {test_run_id}")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Generate current timestamp
        current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Insert new test run
        cursor.execute(
            """INSERT INTO test_runs (jira_id, test_type, timestamp, num_test_cases, status, user_name, notes) 
               VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (jira_id, test_type.lower(), current_timestamp, 0, "pending", user_name, notes)
        )
        
        test_run_id = cursor.lastrowid
        conn.commit()
        
        print(f"Created new test run with ID {test_run_id} for {jira_id} ({test_type})")
        return test_run_id
        
    except sqlite3.Error as e:
        print(f"Error creating test run: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def update_test_run(database_path, test_run_id, num_test_cases=None, status=None, notes=None):
    """
    Updates an existing test run with new information.
    
    This function modifies test run records to reflect changes in execution status,
    test case counts, or additional notes. It's used throughout the test execution
    lifecycle to maintain accurate test run state.

    Args:
        database_path (str): Absolute path to the SQLite database file
        test_run_id (int): Unique identifier of the test run to update
        num_test_cases (int, optional): Updated count of test cases. Defaults to None.
        status (str, optional): New status ("pending", "in_progress", "completed", "failed"). Defaults to None.
        notes (str, optional): Updated notes or comments. Defaults to None.

    Returns:
        bool: True if update successful, False otherwise

    Process Flow:
        1. Establishes database connection
        2. Builds dynamic UPDATE query based on provided parameters
        3. Executes update with only non-None values
        4. Commits changes and returns success status
        5. Handles rollback on errors

    Example:
        success = update_test_run(
            db_path, 123, num_test_cases=15, status="completed"
        )
        if success:
            print("Test run updated successfully")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Build dynamic update query
        update_fields = []
        values = []
        
        if num_test_cases is not None:
            update_fields.append("num_test_cases = ?")
            values.append(num_test_cases)
            
        if status is not None:
            update_fields.append("status = ?")
            values.append(status)
            
        if notes is not None:
            update_fields.append("notes = ?")
            values.append(notes)
            
        if not update_fields:
            print("No fields to update")
            return True
            
        # Add test_run_id to values for WHERE clause
        values.append(test_run_id)
        
        query = f"UPDATE test_runs SET {', '.join(update_fields)} WHERE id = ?"
        cursor.execute(query, values)
        
        conn.commit()
        print(f"Updated test run {test_run_id}")
        return True
        
    except sqlite3.Error as e:
        print(f"Error updating test run: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_runs_by_user(database_path, user_name):
    """
    Retrieves all test runs created by a specific user.
    
    This function queries the database to find all test execution sessions
    initiated by a particular user, useful for user activity tracking and
    personal test run management.

    Args:
        database_path (str): Absolute path to the SQLite database file
        user_name (str): Name of the user whose test runs to retrieve

    Returns:
        list: List of dictionaries containing test run details:
            Each dictionary contains: id, jira_id, test_type, timestamp,
            num_test_cases, status, user_name, notes
        Returns empty list if no test runs found or on error.

    Example:
        user_runs = get_test_runs_by_user(db_path, "john_doe")
        for run in user_runs:
            print(f"Run {run['id']}: {run['jira_id']} - {run['status']}")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        cursor.execute(
            """SELECT id, jira_id, test_type, timestamp, num_test_cases, status, user_name, notes
               FROM test_runs 
               WHERE user_name = ?
               ORDER BY timestamp DESC""",
            (user_name,)
        )
        
        results = cursor.fetchall()
        
        test_runs = []
        for result in results:
            test_runs.append({
                'id': result['id'],
                'jira_id': result['jira_id'],
                'test_type': result['test_type'],
                'timestamp': result['timestamp'],
                'num_test_cases': result['num_test_cases'],
                'status': result['status'],
                'user_name': result['user_name'],
                'notes': result['notes']
            })
            
        return test_runs
        
    except sqlite3.Error as e:
        print(f"Error getting test runs by user: {e}")
        return []
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_latest_test_run_id(database_path, jira_id, test_type=None, user_name=None):
    """
    Gets the ID of the most recent test run matching the specified criteria.
    
    This function is a lightweight version of get_latest_test_run that returns
    only the test run ID, useful when you need just the identifier for
    subsequent operations.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier (e.g., "TP-1", "STORY-123")
        test_type (str, optional): Test type filter. Defaults to None.
        user_name (str, optional): User name filter. Defaults to None.

    Returns:
        int or None: ID of the latest test run if found, None otherwise

    Example:
        latest_id = get_latest_test_run_id(db_path, "TP-1", "positive", "john_doe")
        if latest_id:
            print(f"Latest test run ID: {latest_id}")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Build query with optional filters
        conditions = ["jira_id = ?"]
        values = [jira_id]
        
        if test_type and test_type.lower() != "all":
            conditions.append("test_type = ?")
            values.append(test_type.lower())
            
        if user_name:
            conditions.append("user_name = ?")
            values.append(user_name)
            
        query = f"""
            SELECT id FROM test_runs 
            WHERE {' AND '.join(conditions)}
            ORDER BY timestamp DESC 
            LIMIT 1
        """
        
        cursor.execute(query, values)
        result = cursor.fetchone()
        
        return result['id'] if result else None
        
    except sqlite3.Error as e:
        print(f"Error getting latest test run ID: {e}")
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


def get_or_create_test_run(database_path, jira_id, test_type, user_name, notes=None):
    """
    Get existing test run or create a new one for the given parameters.
    
    FUNCTION TYPE: DATABASE FUNCTION
    
    This function manages test run creation and retrieval, ensuring proper
    tracking of test generation sessions in the database.

    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str): JIRA issue ID (e.g., "TP-1")
        test_type (str): Type of tests being generated
        user_name (str): Name of the user creating the test run
        notes (str, optional): Additional notes for the test run

    Returns:
        int: Test run ID for use in subsequent database operations

    Usage Example:
        test_run_id = get_or_create_test_run(
            db_path, "TP-1", "positive", "john_doe", 
            "Generated with Google AI Studio"
        )
    """
    try:
        # Import here to avoid circular imports
        import db_helper as db_helper
        
        return db_helper.create_test_run(
            database_path,
            jira_id,
            test_type,
            user_name=user_name,
            notes=notes or f"Generated test cases for {jira_id}"
        )
    except Exception as e:
        if 'st' in globals():
            import streamlit as st
            st.error(f"Error creating test run: {str(e)}")
        return None


def update_test_run_completion(database_path, test_run_id, total_test_cases, status="completed", notes=None):
    """
    Update a test run with completion information.
    
    FUNCTION TYPE: DATABASE FUNCTION
    
    Updates the test run record with final statistics and status information
    after test case generation is complete.

    Args:
        database_path (str): Path to the SQLite database file
        test_run_id (int): ID of the test run to update
        total_test_cases (int): Total number of test cases generated
        status (str): Final status ("completed", "partial", "failed")
        notes (str, optional): Additional completion notes

    Returns:
        bool: True if update successful, False otherwise

    Usage Example:
        success = update_test_run_completion(
            db_path, test_run_id, 25, "completed", 
            "Successfully generated all test types"
        )
    """
    try:
        # Import here to avoid circular imports
        import db_helper as db_helper
        
        db_helper.update_test_run(
            database_path,
            test_run_id,
            num_test_cases=total_test_cases,
            status=status,
            notes=notes
        )
        return True
    except Exception as e:
        if 'st' in globals():
            import streamlit as st
            st.error(f"Error updating test run: {str(e)}")
        return False
