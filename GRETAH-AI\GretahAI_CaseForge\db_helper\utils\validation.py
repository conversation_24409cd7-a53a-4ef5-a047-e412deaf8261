"""
Database validation and maintenance utilities for the GretahAI CaseForge database system.

This module provides functions for validating database connections, cleaning up
orphaned data, and performing maintenance operations.
"""

import sqlite3
import pandas as pd
import streamlit as st
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock


def validate_database_connection(database_path):
    """
    Validate that the database is accessible and has required tables.
    
    FUNCTION TYPE: DATABASE VALIDATION FUNCTION
    
    Checks database connectivity and verifies that all required tables
    exist for test case operations.

    Args:
        database_path (str): Path to the SQLite database file

    Returns:
        tuple: (is_valid: bool, error_message: str)
            - is_valid: True if database is valid and accessible
            - error_message: Description of any issues found

    Usage Example:
        is_valid, error = validate_database_connection(db_path)
        if not is_valid:
            st.error(f"Database error: {error}")
            return
    """
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()
        
        # Check for required tables
        required_tables = ['test_cases', 'test_steps', 'test_runs', 'jira_issues']
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = [table for table in required_tables if table not in existing_tables]
        
        conn.close()
        
        if missing_tables:
            return False, f"Missing required tables: {', '.join(missing_tables)}"
        
        return True, "Database connection valid"
        
    except Exception as e:
        return False, f"Database connection error: {str(e)}"


def cleanup_orphaned_test_data(database_path, jira_id=None):
    """
    Clean up orphaned test data in the database.
    
    FUNCTION TYPE: DATABASE MAINTENANCE FUNCTION
    
    Removes test steps that no longer have associated test cases
    and optionally cleans up data for a specific JIRA ID.

    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str, optional): Specific JIRA ID to clean up, or None for all

    Returns:
        tuple: (rows_cleaned: int, error_message: str)
            - rows_cleaned: Number of orphaned rows removed
            - error_message: Any errors encountered during cleanup

    Usage Example:
        cleaned, error = cleanup_orphaned_test_data(db_path, "TP-1")
        if error:
            st.warning(f"Cleanup warning: {error}")
        else:
            st.info(f"Cleaned up {cleaned} orphaned records")
    """
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()
        
        # Clean up orphaned test steps
        if jira_id:
            cursor.execute("""
                DELETE FROM test_steps 
                WHERE test_case_id NOT IN (
                    SELECT id FROM test_cases WHERE jira_id = ?
                )
            """, (jira_id,))
        else:
            cursor.execute("""
                DELETE FROM test_steps 
                WHERE test_case_id NOT IN (SELECT id FROM test_cases)
            """)
        
        rows_cleaned = cursor.rowcount
        conn.commit()
        conn.close()
        
        return rows_cleaned, None
        
    except Exception as e:
        return 0, f"Cleanup error: {str(e)}"


def count_test_cases_in_database_output(output_file, test_type):
    """
    Count test cases from a database output file reference.
    
    FUNCTION TYPE: DATABASE HELPER FUNCTION
    
    Parses database URL references and counts the actual number of
    test cases stored for a specific JIRA ID and test type.

    Args:
        output_file (str): Database URL in format "database://jira_id/test_type/latest"
        test_type (str): Test type to count cases for

    Returns:
        int: Number of test cases found, or 0 if error/not found

    Usage Example:
        count = count_test_cases_in_database_output(
            "database://TP-1/positive/latest", "positive"
        )
        st.info(f"Found {count} test cases in database")
    """
    try:
        if not output_file or not output_file.startswith("database://"):
            return 0

        # Extract JIRA ID and test type from the database URL
        parts = output_file.split("/")
        if len(parts) >= 4:
            db_jira_id = parts[2]
            db_test_type = parts[3]

            # Get the test cases from the database
            from ..operations.data_operations import get_test_cases_from_database
            from ..core.connection import DATABASE_PATH
            df = get_test_cases_from_database(
                DATABASE_PATH, db_jira_id, test_type
            )

            # Count unique test case IDs
            if not df.empty and "Test Case ID" in df.columns:
                valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                unique_test_case_ids = valid_test_case_ids.unique()
                return len(unique_test_case_ids)

        return 0

    except Exception as e:
        if 'st' in globals():
            import streamlit as st
            st.error(f"Error counting test cases from database: {str(e)}")
        return 0
