"""
Step Merger for Hybrid AI-Assisted Test Case Editing

This module handles the merging of AI-generated steps with user-added manual steps,
ensuring proper sequencing, step numbering, and logical flow validation.

Key Features:
- Merge AI and manual steps while preserving order
- Automatic step renumbering
- Flow validation and conflict detection
- Markdown table regeneration for combined steps
"""

import json
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
from debug_utils import debug


class StepMerger:
    """Handles merging of AI-generated and manual steps."""

    def __init__(self):
        self.ai_steps = []
        self.manual_steps = []
        self.insertion_points = {}

    def set_ai_steps(self, ai_steps: List[Dict[str, Any]]):
        """Set the AI-generated steps (editable in hybrid mode)."""
        self.ai_steps = ai_steps.copy() if ai_steps else []
        # Mark AI steps with origin tracking but allow editing
        for step in self.ai_steps:
            step["_is_ai_generated"] = True
            step["_is_locked"] = False  # Allow editing in hybrid mode
            step["_original_ai_step"] = True  # Track original AI origin

    def add_manual_step(self, step: Dict[str, Any], insertion_point: str = "end"):
        """
        Add a manual step at the specified insertion point.

        Args:
            step: Manual step data
            insertion_point: Where to insert ("before_X", "after_X", "start", "end")
        """
        step["_is_manual"] = True
        step["_is_locked"] = False
        step["_insertion_point"] = insertion_point
        step["_created_at"] = datetime.now().isoformat()

        if insertion_point not in self.insertion_points:
            self.insertion_points[insertion_point] = []

        self.insertion_points[insertion_point].append(step)
        debug(f"Added manual step at insertion point: {insertion_point}",
              stage="step_merger", operation="manual_step_added",
              context={"insertion_point": insertion_point, "step_id": step.get("_step_id"),
                      "step_description": step.get("step_description", "")[:50]})

    def remove_manual_step(self, step_id: str, insertion_point: str):
        """Remove a manual step from the specified insertion point."""
        if insertion_point in self.insertion_points:
            self.insertion_points[insertion_point] = [
                step for step in self.insertion_points[insertion_point]
                if step.get("_step_id") != step_id
            ]
            debug(f"Removed manual step {step_id} from insertion point: {insertion_point}",
                  stage="step_merger", operation="manual_step_removed",
                  context={"step_id": step_id, "insertion_point": insertion_point})

    def merge_steps(self) -> List[Dict[str, Any]]:
        """
        Merge AI and manual steps into a single ordered list.

        Returns:
            List of merged steps with proper numbering
        """
        merged_steps = []

        # Add steps at the start
        if "start" in self.insertion_points:
            merged_steps.extend(self.insertion_points["start"])

        # Process AI steps and insert manual steps at appropriate points
        for i, ai_step in enumerate(self.ai_steps):
            ai_step_no = ai_step.get("step_no", str(i + 1))

            # Add manual steps before this AI step
            before_key = f"before_{ai_step_no}"
            if before_key in self.insertion_points:
                merged_steps.extend(self.insertion_points[before_key])

            # Add the AI step
            merged_steps.append(ai_step)

            # Add manual steps after this AI step
            after_key = f"after_{ai_step_no}"
            if after_key in self.insertion_points:
                merged_steps.extend(self.insertion_points[after_key])

        # Add steps at the end
        if "end" in self.insertion_points:
            merged_steps.extend(self.insertion_points["end"])

        # Renumber all steps
        self._renumber_steps(merged_steps)

        return merged_steps

    def _renumber_steps(self, steps: List[Dict[str, Any]]):
        """Renumber steps sequentially starting from 1."""
        for i, step in enumerate(steps, 1):
            step["step_no"] = str(i)

    def generate_markdown_table(self, merged_steps: List[Dict[str, Any]]) -> str:
        """
        Generate markdown table representation of merged steps.

        Args:
            merged_steps: List of merged steps

        Returns:
            Markdown table string
        """
        if not merged_steps:
            return ""

        # Table header
        header = "| Step No | Step Type | Action | Locator Strategy | Locator | Test Data Param | Expected Result | Assertion Type | Condition | Timeout (s) | Description | Source |\n"
        separator = "|---------|-----------|--------|------------------|---------|-----------------|-----------------|----------------|-----------|-------------|-------------|--------|\n"

        # Table rows
        rows = []
        for step in merged_steps:
            if step.get("_is_ai_generated", False):
                if step.get("_ai_step_modified", False):
                    source = "🤖🔄 AI (Modified)"
                    # Debug: Log modified AI step details
                    debug(f"Rendering modified AI step in table: {step.get('step_no', 'N/A')} - {step.get('action', 'N/A')}",
                          stage="step_merger", operation="modified_ai_step_render",
                          context={'step_no': step.get('step_no'), 'action': step.get('action'),
                                  'expected_result': step.get('expected_result')})
                else:
                    source = "🤖 AI"
            else:
                source = "✏️ Manual"

            row = f"| {step.get('step_no', '')} | {step.get('step_type', '')} | {step.get('action', '')} | " \
                  f"{step.get('locator_strategy', '')} | {step.get('locator', '')} | {step.get('test_data_param', '')} | " \
                  f"{step.get('expected_result', '')} | {step.get('assertion_type', '')} | {step.get('condition', '')} | " \
                  f"{step.get('timeout', '')} | {step.get('step_description', '')} | {source} |\n"
            rows.append(row)

        return header + separator + "".join(rows)

    def validate_step_flow(self, merged_steps: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate the logical flow of merged steps.

        Args:
            merged_steps: List of merged steps to validate

        Returns:
            Dictionary containing validation results
        """
        validation_results = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "suggestions": []
        }

        # Check for proper navigation flow
        has_navigation = any(
            step.get("action", "").lower() in ["navigate", "navigate_to", "open"]
            for step in merged_steps
        )

        if not has_navigation:
            validation_results["warnings"].append(
                "No navigation steps detected. Consider adding navigation to start the test."
            )

        # Check for proper assertions
        assertion_steps = [
            step for step in merged_steps
            if step.get("step_type") == "assertion" or step.get("assertion_type") not in ["", "no_error"]
        ]

        if len(assertion_steps) == 0:
            validation_results["warnings"].append(
                "No assertion steps detected. Consider adding verification steps."
            )

        # Check for logical sequence issues
        prev_step = None
        for i, step in enumerate(merged_steps):
            if prev_step:
                # Check for potential sequence issues
                if (prev_step.get("step_type") == "teardown" and
                    step.get("step_type") in ["ui", "setup"]):
                    validation_results["warnings"].append(
                        f"Step {step.get('step_no')}: UI/Setup step after teardown step may cause issues"
                    )

                # Check for missing wait steps after navigation
                if (prev_step.get("action", "").lower() in ["navigate", "navigate_to", "click"] and
                    step.get("action", "").lower() not in ["wait", "wait_for_element", "wait_for_page_load"] and
                    step.get("step_type") != "assertion"):
                    validation_results["suggestions"].append(
                        f"Consider adding a wait step after step {prev_step.get('step_no')} to ensure page stability"
                    )

            prev_step = step

        # Check for proper cleanup
        has_cleanup = any(
            step.get("step_type") == "teardown"
            for step in merged_steps
        )

        if not has_cleanup:
            validation_results["suggestions"].append(
                "Consider adding cleanup steps (teardown) at the end of the test"
            )

        # Set overall validity
        validation_results["is_valid"] = len(validation_results["errors"]) == 0

        return validation_results

    def get_insertion_points(self) -> List[str]:
        """Get list of available insertion points based on AI steps."""
        points = ["start"]

        for ai_step in self.ai_steps:
            step_no = ai_step.get("step_no", "")
            if step_no:
                # Add AI step number as direct replacement option
                points.append(step_no)
                points.append(f"before_{step_no}")
                points.append(f"after_{step_no}")

        points.append("end")
        return points

    def get_manual_steps_at_point(self, insertion_point: str) -> List[Dict[str, Any]]:
        """Get manual steps at a specific insertion point."""
        return self.insertion_points.get(insertion_point, [])

    def clear_manual_steps(self):
        """Clear all manual steps."""
        self.insertion_points.clear()
        debug("Cleared all manual steps",
              stage="step_merger", operation="manual_steps_cleared",
              context={"previous_insertion_points": len(self.insertion_points)})

    def export_merged_steps(self, merged_steps: List[Dict[str, Any]]) -> Tuple[str, str]:
        """
        Export merged steps as both JSON and markdown.

        Args:
            merged_steps: List of merged steps

        Returns:
            Tuple of (json_string, markdown_string)
        """
        # Clean steps for JSON export (remove internal metadata)
        clean_steps = []
        for step in merged_steps:
            clean_step = {k: v for k, v in step.items() if not k.startswith("_")}
            clean_steps.append(clean_step)

        json_string = json.dumps(clean_steps, indent=2)
        markdown_string = self.generate_markdown_table(merged_steps)

        return json_string, markdown_string


def merge_ai_and_manual_steps(ai_steps: List[Dict[str, Any]],
                             insertion_points: Dict[str, List[Dict[str, Any]]]) -> Tuple[List[Dict[str, Any]], str, Dict[str, Any]]:
    """
    Convenience function to merge AI and manual steps.

    Args:
        ai_steps: List of AI-generated steps
        insertion_points: Dictionary of insertion points and their manual steps

    Returns:
        Tuple of (merged_steps, markdown_table, validation_results)
    """
    merger = StepMerger()
    merger.set_ai_steps(ai_steps)

    # Add manual steps from insertion points
    for point, steps in insertion_points.items():
        for step in steps:
            merger.add_manual_step(step, point)

    # Merge and validate
    merged_steps = merger.merge_steps()
    markdown_table = merger.generate_markdown_table(merged_steps)
    validation_results = merger.validate_step_flow(merged_steps)

    return merged_steps, markdown_table, validation_results
