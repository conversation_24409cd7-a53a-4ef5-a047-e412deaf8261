"""
Standardized debug utilities for GretahAI ScriptWeaver.

This module provides structured logging functionality with zero dependencies
on core modules to avoid circular imports. It's completely self-contained.

GRETAH Logging Standard Implementation
"""

import os
import logging
import streamlit as st
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime

# Environment-based debug control
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

# Simple logging setup without dependencies
_logger_cache = {}
_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

def _get_simple_logger(stage: str) -> logging.Logger:
    """Get a simple logger for the given stage without dependencies."""
    if stage not in _logger_cache:
        logger = logging.getLogger(f"gretah.scriptweaver.{stage}")

        # Only configure if not already configured
        if not logger.handlers:
            logger.setLevel(logging.DEBUG if DEBUG_MODE else logging.INFO)

            # Add console handler if debug mode is enabled
            if DEBUG_MODE:
                handler = logging.StreamHandler()
                formatter = logging.Formatter(
                    '%(asctime)s [%(levelname)s] %(name)s - %(message)s',
                    datefmt='%H:%M:%S'
                )
                handler.setFormatter(formatter)
                logger.addHandler(handler)

            # Add file handler if possible
            try:
                log_dir = Path("logs")
                log_dir.mkdir(parents=True, exist_ok=True)
                log_file = log_dir / f"scriptweaver_{_session_id}.log"

                file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
                file_formatter = logging.Formatter(
                    '%(asctime)s [%(levelname)s] %(name)s [%(funcName)s:%(lineno)d] - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                file_handler.setFormatter(file_formatter)
                logger.addHandler(file_handler)
            except Exception:
                # If file logging fails, continue without it
                pass

        _logger_cache[stage] = logger

    return _logger_cache[stage]

def debug(message: str, stage: str, operation: str, context: Optional[Dict[str, Any]] = None):
    """
    Standardized debug function with structured logging.

    All debug calls must provide stage and operation parameters for proper
    categorization and filtering in the centralized logging system.

    Args:
        message: Debug message (required)
        stage: Stage identifier (required - e.g., "stage1", "stage2", etc.)
        operation: Operation being performed (required - e.g., "file_upload", "validation")
        context: Additional context data (optional - dict with relevant metadata)

    Examples:
        debug("File upload initiated", stage="stage1", operation="file_upload")
        debug("Data processed successfully", stage="stage1", operation="file_processing",
              context={'rows': 100, 'filename': 'test.xlsx'})
        debug("Element detection started", stage="stage4", operation="element_detection",
              context={'step_count': 5, 'url': 'https://example.com'})
    """
    try:
        # Use simple logger without dependencies
        logger = _get_simple_logger(stage)

        # Create structured message
        structured_message = f"[{operation}] {message}"
        if context:
            structured_message += f" | Context: {context}"

        # Log with appropriate level
        logger.debug(structured_message)

        # Console output for debug mode
        if DEBUG_MODE:
            print(f"DEBUG [{stage}:{operation}]: {message}")

    except Exception as e:
        # Fallback to basic logging on any error to ensure reliability
        print(f"DEBUG [FALLBACK]: {message} (Logging error: {e})")

    # Maintain existing Streamlit integration
    _handle_streamlit_debug(message, stage, operation)

def _handle_streamlit_debug(message: str, stage: str, operation: str):
    """Handle Streamlit debug UI integration with structured logging."""
    try:
        # Check if we're in a Streamlit context with an active session state
        if st._is_running:
            # Determine if debug is enabled
            debug_enabled = DEBUG_MODE

            if debug_enabled:
                # Create a debug section in the sidebar if it doesn't exist
                if 'show_debug' not in st.session_state:
                    st.session_state.show_debug = False
                    st.session_state.debug_messages = []

                # Store the structured message
                structured_message = f"[{stage}:{operation}] {message}"
                st.session_state.debug_messages.append(structured_message)
    except:
        # If any error occurs, just continue without UI display
        pass

def get_debug_mode() -> bool:
    """Get current debug mode status."""
    return DEBUG_MODE

def get_session_info() -> Dict[str, str]:
    """Get current session information for debugging."""
    try:
        log_file = str(Path("logs") / f"scriptweaver_{_session_id}.log")
        return {
            'session_id': _session_id,
            'log_file': log_file,
            'debug_mode': str(DEBUG_MODE),
            'console_output': str(DEBUG_MODE),
            'ui_output': str(DEBUG_MODE)
        }
    except Exception as e:
        return {
            'session_id': _session_id,
            'log_file': 'unknown',
            'debug_mode': str(DEBUG_MODE),
            'error': str(e)
        }

# Compatibility functions for existing code that might expect these
def register_logging_manager(manager):
    """Compatibility function - no longer needed."""
    pass

def register_logging_functions(**functions):
    """Compatibility function - no longer needed."""
    pass
