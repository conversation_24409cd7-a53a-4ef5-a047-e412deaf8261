# GRETAH-AI Quick Start Guide for Windows

This guide provides comprehensive management scripts to set up, run, and maintain the GRETAH-AI applications on Windows machines.

## 📋 Prerequisites

Before running the quick start scripts, ensure you have:

1. **Python 3.13 or later** installed from [python.org](https://python.org)
2. **Internet connection** for downloading dependencies
3. **Chrome or Edge browser** (for ScriptWeaver's Selenium functionality)
4. **Ollama** (optional, for local AI models in TestInsight) from [ollama.ai](https://ollama.ai)
5. **JIRA API access** (for CaseForge JIRA integration)
6. **Google AI Studio API key** (for AI-powered features)

## 🚀 Quick Start Options

### Option 1: Individual Application Management

Each application has its own comprehensive management script with multiple options:

#### GretahAI CaseForge
```batch
cd /d "GretahAI_CaseForge"
quick_start.bat
```

#### GretahAI ScriptWeaver
```batch
cd /d "GretahAI_ScriptWeaver"
quick_start.bat
```

#### GretahAI TestInsight
```batch
cd /d "GretahAI_TestInsight"
quick_start.bat
```

Each script provides these options:
1. **Install dependencies only** - Set up environment without running
2. **Run application** - Install if needed and start the app
3. **Update dependencies** - Upgrade all packages to latest versions
4. **Clean environment** - Remove virtual environment for fresh start
5. **Check system requirements** - Verify prerequisites and system status
6. **Exit** - Close the script

### Option 2: Master Management Script (Recommended)

Use the master script for comprehensive suite management:

```batch
quick_start_all.bat
```

This provides a centralized menu with options to:
- **Individual app management** - Launch specific application scripts
- **Install all dependencies** - Set up all three applications at once
- **Start all applications** - Launch all apps simultaneously in separate windows
- **Check system requirements** - Verify prerequisites across all apps
- **Exit** - Close the script

## 🔧 What the Scripts Do

### Comprehensive Management Features

Each application script provides multiple management options:

#### Installation & Setup (Option 1)
- **Python version verification** (requires 3.13+)
- **Virtual environment creation** (`venv` folder)
- **Pip upgrade** to latest version
- **Dependency installation** from updated `requirements.txt`
- **Application-specific initialization**:
  - CaseForge: Database setup and JIRA integration check
  - ScriptWeaver: Browser availability verification
  - TestInsight: Ollama service status check

#### Run Application (Option 2)
- **Automatic dependency check** - installs if missing
- **Environment activation**
- **Streamlit application launch**
- **Error handling** with user-friendly messages

#### Update Dependencies (Option 3)
- **Package upgrade** to latest compatible versions
- **Virtual environment validation**
- **Dependency conflict resolution**

#### Clean Environment (Option 4)
- **Virtual environment removal** for fresh start
- **Clean slate preparation** for troubleshooting

#### System Requirements Check (Option 5)
- **Python installation verification**
- **Pip availability check**
- **Virtual environment status**
- **Internet connectivity test**
- **Application-specific requirements**:
  - CaseForge: JIRA connectivity
  - ScriptWeaver: Browser availability
  - TestInsight: Ollama service status



## 📦 Updated Dependencies

The scripts now install comprehensive, validated dependency sets:

### GretahAI CaseForge
- **Core**: streamlit, pandas, google-generativeai, plotly
- **JIRA Integration**: jira (newly added)
- **File Processing**: openpyxl, altair (newly added)
- **AI & Testing**: ollama, pytest, selenium
- **Utilities**: requests, PyYAML, Pillow, fpdf2, matplotlib

### GretahAI ScriptWeaver
- **Core**: streamlit, pandas, google-generativeai
- **Testing**: pytest, pytest-order, selenium, webdriver-manager
- **Utilities**: openpyxl, psutil, requests, pyperclip (newly added)

### GretahAI TestInsight
- **Core**: streamlit, pandas, google-generativeai, plotly
- **Testing**: pytest, selenium, psutil (newly added)
- **AI & Analysis**: ollama, requests (newly added)
- **Reporting**: Pillow, matplotlib (newly added), fpdf2 (newly added)
- **Data**: PyYAML (newly added)

## 🌐 Application URLs

When started, each application will be available at:

- **CaseForge**: http://localhost:8501
- **ScriptWeaver**: http://localhost:8502 (if CaseForge is already running)
- **TestInsight**: http://localhost:8503 (if other apps are already running)

Streamlit automatically assigns the next available port if the default is in use.

## 📁 Directory Structure After Setup

After running the scripts, each application directory will contain:

```
GretahAI_[AppName]/
├── venv/                 # Virtual environment (created by script)
├── quick_start.bat       # Quick start script
├── requirements.txt      # Python dependencies
├── [app files...]        # Application-specific files
└── ...
```

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### "Python is not installed or not in PATH"
- Install Python from [python.org](https://python.org)
- During installation, check "Add Python to PATH"
- Restart your command prompt after installation
- **Use Option 5** in any script to verify Python installation

#### "Failed to install requirements"
- Check your internet connection using **Option 5** (system check)
- Try running the script as Administrator
- Check the error messages for specific missing packages
- Manually install problematic packages: `pip install [package-name]`
- Try **Option 4** (clean environment) followed by **Option 1** (install)

#### "Failed to start the application"
- Check if the port is already in use
- Try closing other Streamlit applications
- Use **Option 5** to verify all system requirements
- Check the error message for specific issues
- Try **Option 3** (update dependencies) if packages are outdated

#### Browser doesn't open automatically
- Manually navigate to the URL shown in the command prompt
- Usually http://localhost:8501 (or next available port)
- Check Windows Firewall settings if accessing from another machine

#### JIRA Integration Issues (CaseForge)
- Verify JIRA server URL, username, and API token in config.json
- Ensure JIRA server is accessible from your network
- Check API token permissions in JIRA settings

#### Selenium Issues (ScriptWeaver)
- **Option 5** will check for Chrome/Edge browser availability
- Install Chrome or Edge if missing
- Update webdriver-manager: `pip install --upgrade webdriver-manager`

#### Ollama Issues (TestInsight)
- **Option 5** will check Ollama service status
- Install Ollama from [ollama.ai](https://ollama.ai) if needed
- Start Ollama service: `ollama serve`
- Pull required models: `ollama pull llama2`

### Getting Help

If you encounter issues:

1. **Check the error messages** in the command prompt
2. **Verify prerequisites** are properly installed
3. **Try running individual commands** manually to isolate the problem
4. **Check application-specific README files** for additional troubleshooting

## 🔄 Updating Applications

To update dependencies or pull latest changes:

1. **Stop the running application** (Ctrl+C)
2. **Delete the venv folder** in the application directory
3. **Run the quick start script again** - it will recreate the environment with latest dependencies

## 🚪 Stopping Applications

To stop any running application:
- Press **Ctrl+C** in the command prompt window
- Or simply **close the command prompt window**

## 📝 Notes

- Each application runs in its own virtual environment to avoid conflicts
- Virtual environments are created locally in each application folder
- The scripts are designed to be run multiple times safely
- If a virtual environment already exists, the script will use it
- All dependencies are installed locally and won't affect your system Python

## 🎯 Usage Examples

### Quick Start (Recommended)
```batch
# Navigate to GRETAH-AI directory and run master script
cd /d "D:\Gretah_AI\GRETAH-AI"
quick_start_all.bat
```

### Individual Application Management
```batch
# Navigate to GRETAH-AI root directory first
cd /d "D:\Gretah_AI\GRETAH-AI"

# Then navigate to specific application and run its script
cd /d "GretahAI_CaseForge"
quick_start.bat

# Or for ScriptWeaver
cd /d "..\GretahAI_ScriptWeaver"
quick_start.bat

# Or for TestInsight
cd /d "..\GretahAI_TestInsight"
quick_start.bat
```

### Complete Setup from Scratch
```batch
# 1. Navigate to your desired installation directory
cd /d "D:\Gretah_AI"

# 2. Clone or extract GRETAH-AI (if not already done)
# git clone https://github.com/saibez/GRETAH-AI.git

# 3. Navigate to the project root
cd /d "GRETAH-AI"

# 4. Run the master script for easy setup
quick_start_all.bat
```

## 🎯 Next Steps

After starting an application:

1. **Configure API keys** (Google AI Studio, JIRA, etc.) in the application settings
2. **Follow the application-specific documentation** for detailed usage instructions
3. **Check the sidebar** in each application for configuration options

Enjoy using GRETAH-AI! 🚀
