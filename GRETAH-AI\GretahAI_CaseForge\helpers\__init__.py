"""
GretahAI CaseForge - Helpers Package

This package provides comprehensive helper functions for the GretahAI CaseForge test case 
generation and management system. It has been modularized from the original helpers.py 
file to improve maintainability while preserving 100% backward compatibility.

The package is organized into logical modules:

- ai: AI integration with Ollama and Google AI Studio
- jira: JIRA operations and enhancement capabilities  
- excel: Excel file operations and formatting
- data: JSON parsing and DataFrame operations
- file: File management and versioning
- utils: Test case counters and common utilities
- csv: CSV export functionality

For backward compatibility, all functions are available at the package level
exactly as they were in the original helpers.py file.

© 2025 GretahAI Team
"""

# Import Zephyr integration handling
import importlib.util
import os

# Zephyr integration globals (preserved from original)
ZEPHYR_AVAILABLE = False
upload_test_cases_to_jira = None

# Global state variables (preserved from original helpers.py)
_test_case_counter = 0
_last_jira_id = None


def load_zephyr_integration():
    """
    Dynamically loads the Zephyr integration module for test management functionality.
    
    This function attempts to load the zephyr_integration.py module at runtime to enable
    uploading test cases to Zephyr test management system. It uses dynamic import to
    handle cases where the Zephyr integration may not be available or configured.

    Returns:
        bool: True if Zephyr integration loaded successfully, False otherwise

    Global Variables Modified:
        ZEPHYR_AVAILABLE (bool): Set to True if integration is available
        upload_test_cases_to_jira (function): Reference to Zephyr upload function
    """
    global ZEPHYR_AVAILABLE, upload_test_cases_to_jira
    try:
        # Get the full path to the zephyr_integration.py file
        module_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'zephyr_integration.py')

        if os.path.exists(module_path):
            # Load the module from the file path
            spec = importlib.util.spec_from_file_location('zephyr_integration', module_path)
            zephyr_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(zephyr_module)

            # Get the upload_test_cases_to_jira function from the module
            upload_test_cases_to_jira = zephyr_module.upload_test_cases_to_jira
            ZEPHYR_AVAILABLE = True
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Failed to load zephyr_integration: {e}")
        return False


# Try to load the Zephyr integration on import
load_zephyr_integration()


# ===== BACKWARD COMPATIBILITY IMPORTS =====
# Import all functions from submodules to maintain exact API compatibility

# AI Integration functions
from .ai import (
    run_ollama_with_chat,
    run_google_ai_studio,
    is_ollama_running,
    modify_test_cases_with_ai,
    generate_gemini_test_gen_prompt,
    extract_test_info_from_issue
)

# JIRA Operations functions  
from .jira import (
    save_enhanced_jira_description,
    parse_enhanced_jira_description,
    verify_jira_enhanced_description,
    extract_jira_issue,
    format_jira_description,
    parse_enhanced_description_json,
    handle_jira_extraction_workflow,
    handle_jira_enhancement_workflow,
    convert_jira_table_to_markdown
)

# Excel Operations functions
from .excel import (
    create_formatted_excel_from_scenarios,
    save_scenarios_to_excel,
    upload_edited_excel,
    upload_test_cases_to_excel_and_jira
)

# Data Processing functions
from .data import (
    parse_test_scenarios_json,
    validate_test_case_dataframe,
    count_valid_test_cases
)

# Note: parse_enhanced_description_json and format_jira_description are imported from .jira module above

# File Management functions
from .file import (
    get_latest_test_case_file,
    get_existing_test_cases,
    process_attachment_for_display,
    merge_excel_files
)

# UI Components functions
from .ui import (
    create_attachment_grid_html,
    create_jira_details_css,
    render_image_modal,
    render_pdf_modal,
    format_test_type_display_name,
    render_jira_extraction_form,
    render_enhancement_form,
    render_test_generation_controls,
    render_generate_button,
    render_jira_issue_section,
    show_extraction_guidance,
    initialize_session_state,
    load_usage_data,
    save_usage_data
)

# Utility functions
from .utils import (
    reset_test_case_counter,
    set_test_case_counter,
    get_test_case_counter,
    get_highest_test_case_id,
    generate_test_scenarios,
    _generate_test_scenarios_internal
)

# CSV Export functions
from .csv.export import (
    create_csv_from_dataframe,
    export_test_cases_to_csv,
    format_csv_for_external_tools
)

# ===== PACKAGE-LEVEL EXPORTS =====
# Define __all__ to maintain explicit API contract
__all__ = [
    # Zephyr integration
    'ZEPHYR_AVAILABLE',
    'upload_test_cases_to_jira',
    'load_zephyr_integration',
    
    # Global state variables (preserved from original)
    '_test_case_counter',
    '_last_jira_id',
    
    # AI Integration
    'run_ollama_with_chat',
    'run_google_ai_studio', 
    'is_ollama_running',
    'generate_gemini_test_gen_prompt',
    'extract_test_info_from_issue',
      # JIRA Operations
    'save_enhanced_jira_description',
    'parse_enhanced_jira_description',
    'verify_jira_enhanced_description',
    'extract_jira_issue',
    'format_jira_description',
    'parse_enhanced_description_json',
    'handle_jira_extraction_workflow',
    'handle_jira_enhancement_workflow',
    'convert_jira_table_to_markdown',
    
    # Excel Operations
    'create_formatted_excel_from_scenarios',
    'save_scenarios_to_excel',
    'upload_edited_excel',
    'upload_test_cases_to_excel_and_jira',
    
    # Data Processing
    'parse_test_scenarios_json',
    'validate_test_case_dataframe',
    'count_valid_test_cases',
    
    # File Management
    'get_latest_test_case_file',
    'get_existing_test_cases',
    'process_attachment_for_display',
    'merge_excel_files',
    
    # UI Components
    'create_attachment_grid_html',
    'create_jira_details_css',
    'render_image_modal',
    'render_pdf_modal',
    'format_test_type_display_name',
    'render_jira_extraction_form',
    'render_enhancement_form',
    'render_test_generation_controls',
    'render_generate_button',
    'render_jira_issue_section',
    'show_extraction_guidance',
    'initialize_session_state',
    'load_usage_data',
    'save_usage_data',
    
    # Utilities
    'reset_test_case_counter',
    'set_test_case_counter',
    'get_test_case_counter',
    'get_highest_test_case_id',
    'generate_test_scenarios',
    '_generate_test_scenarios_internal',
    
    # CSV Export
    'create_csv_from_dataframe',
    'export_test_cases_to_csv',
    'format_csv_for_external_tools'
]


# ===== MIGRATION INFORMATION =====
"""
MIGRATION GUIDE:

The helpers.py file has been modularized into a package structure for better
maintainability. All existing imports will continue to work exactly as before:

OLD: from helpers import function_name
NEW: from helpers import function_name  # Same - no change needed!

GLOBAL STATE PRESERVATION:
The global variables _test_case_counter and _last_jira_id are preserved at the 
package level to maintain shared state across all modules. This ensures that
test case numbering continues to work exactly as in the original helpers.py.

For new development, you can also use the modular imports:

from helpers.ai import run_ollama_with_chat
from helpers.excel import create_formatted_excel_from_scenarios
from helpers.data import parse_test_scenarios_json
etc.

This provides better organization while maintaining 100% backward compatibility.

CRITICAL FIX APPLIED:
- Fixed global variable state management issue in processing.py
- All modules now access shared global state through main helpers package
- Test case counter and JIRA ID tracking work exactly as before
"""
