```json
[
  {
    "scenario_name": "Successful Login After Account Creation",
    "type": "positive",
    "prerequisites": "User should have a valid user ID and password for the test environment.",
    "Test Case Objective": "Verify that a user is able to successfully log in to the system with valid credentials.",
    "steps": [
      {"action": "Verify if user is able to enter valid user ID in the 'User ID' field", "expected_result": "The user ID should be successfully entered into the field."},
      {"action": "Verify if user is able to enter valid password in the 'Password' field", "expected_result": "The password should be successfully entered into the field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "The system should initiate the login process."},
      {"action": "Verify if user is able to access the main application dashboard", "expected_result": "The main application dashboard should be displayed."}
    ]
  },
  {
    "scenario_name": "Login with Correct Credentials",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify a successful login using valid credentials.",
    "steps": [
      {"action": "Verify if user is able to navigate to the login page", "expected_result": "The login page should be displayed."},
      {"action": "Verify if user is able to enter the correct username in the username field", "expected_result": "The username should be entered successfully."},
      {"action": "Verify if user is able to enter the correct password in the password field", "expected_result": "The password should be entered successfully."},
      {"action": "Verify if user is able to click the 'Submit' button", "expected_result": "The user should be logged in and redirected to the main application page."}
    ]
  },
  {
    "scenario_name": "Successful Logout",
    "type": "positive",
    "prerequisites": "User should be successfully logged in to the application.",
    "Test Case Objective": "Verify the successful logout functionality of the application.",
    "steps": [
      {"action": "Verify if user is able to locate and click the 'Logout' button or link", "expected_result": "The logout process should be initiated."},
      {"action": "Verify if user is able to be redirected to the login page after clicking 'Logout'", "expected_result": "The login page should be displayed."},
      {"action": "Verify if user is able to confirm that the session has been terminated", "expected_result": "The application should no longer recognize the user's active session."}
    ]
  },
  {
    "scenario_name": "Password Reset Request",
    "type": "positive",
    "prerequisites": "User should have a registered account with a valid email address.",
    "Test Case Objective": "Verify user can successfully initiate a password reset request.",
    "steps": [
      {"action": "Verify if user is able to click the 'Forgot Password' link on the login page", "expected_result": "The password reset page should be displayed."},
      {"action": "Verify if user is able to enter the registered email address in the email field", "expected_result": "The email address should be entered successfully."},
      {"action": "Verify if user is able to click the 'Submit' or 'Reset Password' button", "expected_result": "A password reset request confirmation message should be displayed."}
    ]
  },
  {
    "scenario_name": "Account Lockout Prevention",
    "type": "positive",
    "prerequisites": "User should have valid login credentials and should have previously logged in successfully.",
    "Test Case Objective": "Verify user can consistently log in with correct credentials and avoid account lockout.",
    "steps": [
      {"action": "Verify if user is able to navigate to the login page", "expected_result": "The login page should load successfully."},
      {"action": "Verify if user is able to enter correct username and password", "expected_result": "The username and password fields should accept the input."},
      {"action": "Verify if user is able to click the Login button", "expected_result": "The system should process the login request."},
      {"action": "Verify if user is able to be successfully logged into the application", "expected_result": "The user should be redirected to the application's dashboard or home page."}
    ]
  }
]
```