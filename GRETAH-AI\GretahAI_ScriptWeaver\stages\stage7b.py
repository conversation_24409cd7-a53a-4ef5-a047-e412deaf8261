"""
Stage 7B: Test Case-Level Script Execution

This module handles Phase 7B of the GretahAI ScriptWeaver application workflow.
Stage 7B is responsible for executing complete test case scripts generated in Stage 6B
and providing feedback mechanisms before proceeding to optimization.

Key Features:
- Test case-level script execution using pytest with proper environment configuration
- Screenshot capture and display functionality
- Feedback mechanism for script review and modification
- Regeneration capability based on user feedback
- Direct transition to Stage 8 (optimizer) after user approval
- No step-by-step progression logic (unlike Stage 7)
- Integration with Stage 8 for script optimization

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions (Stage 7B → Stage 8)

Functions:
    stage7b_execute_test_case_script(state): Main Stage 7B function for test case script execution
    execute_test_case_script(state): Helper function to execute the complete test case script
    handle_feedback_and_regeneration(state): Handle user feedback and script regeneration
"""

import streamlit as st
import os
import time
import subprocess
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from debug_utils import debug

# Import state management
from state_manager import StateStage

# Set up logging
logger = logging.getLogger(__name__)


def stage7b_execute_test_case_script(state):
    """
    Phase 7B: Execute Complete Test Case Script.

    This stage allows the user to execute the complete test case script generated in Stage 6B,
    provide feedback, and proceed to optimization after approval.

    Args:
        state (StateManager): The application state manager instance
    """
    logger.info("=" * 80)
    logger.info("STAGE 7B: TEST CASE-LEVEL SCRIPT EXECUTION - ENTRY POINT")
    logger.info("=" * 80)

    st.markdown("<h2 class='stage-header'>Phase 7B: Test Case-Level Script Execution</h2>", unsafe_allow_html=True)

    # Check if we have a generated script
    if not hasattr(state, 'generated_script_path') or not state.generated_script_path:
        st.error("❌ No test case script found. Please complete Stage 6B first.")
        st.info("💡 Navigate to Stage 6B to generate a complete test case script.")
        return

    # Check if the script file exists
    if not os.path.exists(state.generated_script_path):
        st.error(f"❌ Script file not found: {state.generated_script_path}")
        st.info("💡 Please regenerate the script in Stage 6B.")
        return

    # Display test case information
    _display_test_case_info(state)

    # Display script information
    _display_script_info(state)

    # Script execution section
    st.markdown("---")
    st.markdown("### 🚀 Script Execution")

    # Check if script has been executed
    if hasattr(state, 'script_execution_results') and state.script_execution_results:
        _display_execution_results(state)
        _display_feedback_section(state)
    else:
        _display_execution_controls(state)


def _display_test_case_info(state):
    """Display information about the selected test case."""
    st.markdown("### 📋 Test Case Information")
    
    if hasattr(state, 'selected_test_case') and state.selected_test_case:
        test_case = state.selected_test_case
        
        col1, col2 = st.columns(2)
        with col1:
            st.info(f"**Test Case ID:** {test_case.get('Test Case ID', 'Unknown')}")
            st.info(f"**Test Case Objective:** {test_case.get('Test Case Objective', 'Not specified')}")
        
        with col2:
            st.info(f"**Priority:** {test_case.get('Priority', 'Not specified')}")
            st.info(f"**Test Status:** {test_case.get('Test Status', 'Not specified')}")
    else:
        st.warning("⚠️ Test case information not available")


def _display_script_info(state):
    """Display information about the generated script."""
    st.markdown("### 📄 Generated Script Information")
    
    script_path = state.generated_script_path
    
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**Script Path:** `{script_path}`")
        
        # Get file size and modification time
        if os.path.exists(script_path):
            file_stats = os.stat(script_path)
            file_size = file_stats.st_size
            mod_time = datetime.fromtimestamp(file_stats.st_mtime)
            
            st.info(f"**File Size:** {file_size} bytes")
            st.info(f"**Last Modified:** {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    with col2:
        # Display script content preview
        if os.path.exists(script_path):
            with open(script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            st.info(f"**Script Length:** {len(script_content)} characters")
            st.info(f"**Lines of Code:** {len(script_content.splitlines())} lines")
            
            # Show script preview in expander
            with st.expander("📖 Script Preview", expanded=False):
                st.code(script_content, language="python")


def _display_execution_controls(state):
    """Display controls for script execution."""
    st.info("💡 **Ready to execute the complete test case script**")
    st.markdown("This will run the entire test case in one operation, capturing screenshots and results.")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🚀 Execute Test Case Script", type="primary", use_container_width=True):
            _execute_script(state)
    
    with col2:
        # Option to view script before execution
        if st.button("👁️ View Full Script", use_container_width=True):
            if os.path.exists(state.generated_script_path):
                with open(state.generated_script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                
                st.markdown("### 📄 Complete Script Content")
                st.code(script_content, language="python")


def _execute_script(state):
    """Execute the test case script and capture results."""
    with st.spinner("🤖 Executing test case script... This may take a moment."):
        try:
            # Execute the script using pytest
            script_path = state.generated_script_path
            
            debug("Starting test case script execution", stage="stage7b", 
                  operation="script_execution",
                  context={'script_path': script_path})
            
            # Run pytest with verbose output and capture results
            result = subprocess.run([
                'python', '-m', 'pytest', script_path, '-v', '--tb=short'
            ], capture_output=True, text=True, cwd=os.getcwd())
            
            # Store execution results
            execution_results = {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode,
                'execution_time': datetime.now().isoformat(),
                'script_path': script_path
            }
            
            state.script_execution_results = execution_results
            st.session_state['state'] = state
            
            logger.info(f"Stage 7B: Script execution completed with return code: {result.returncode}")
            
            # Show immediate feedback
            if execution_results['success']:
                st.success("✅ Test case script executed successfully!")
            else:
                st.error("❌ Test case script execution failed. Check the results below.")
            
            # Rerun to show the detailed results
            st.rerun()
            
        except Exception as e:
            st.error(f"❌ Error executing script: {str(e)}")
            logger.error(f"Stage 7B: Script execution error: {str(e)}")
            debug("Script execution error", stage="stage7b", 
                  operation="script_execution_error",
                  context={'error': str(e), 'script_path': state.generated_script_path})


def _display_execution_results(state):
    """Display the results of script execution."""
    results = state.script_execution_results
    
    st.markdown("### 📊 Execution Results")
    
    # Status indicator
    if results['success']:
        st.success("✅ **Test Case Execution: PASSED**")
    else:
        st.error("❌ **Test Case Execution: FAILED**")
    
    # Execution details
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**Return Code:** {results['return_code']}")
        st.info(f"**Execution Time:** {results['execution_time']}")
    
    with col2:
        st.info(f"**Script Path:** `{results['script_path']}`")
    
    # Output details in expandable sections
    if results['stdout']:
        with st.expander("📄 Standard Output", expanded=True):
            st.code(results['stdout'], language="text")
    
    if results['stderr']:
        with st.expander("⚠️ Error Output", expanded=True):
            st.code(results['stderr'], language="text")
    
    # Look for screenshots
    _display_screenshots(state)


def _display_screenshots(state):
    """Display any screenshots captured during execution."""
    # Look for screenshots in the screenshots directory
    screenshots_dir = "screenshots"
    if os.path.exists(screenshots_dir):
        screenshot_files = [f for f in os.listdir(screenshots_dir) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        if screenshot_files:
            st.markdown("### 📸 Latest Screenshot")

            # Sort by modification time (newest first) and show only the most recent
            screenshot_files.sort(key=lambda x: os.path.getmtime(os.path.join(screenshots_dir, x)), reverse=True)

            # Display only the most recent screenshot
            latest_screenshot = screenshot_files[0]
            screenshot_path = os.path.join(screenshots_dir, latest_screenshot)
            st.image(screenshot_path, caption=f"Latest: {latest_screenshot}", use_container_width=True)

            # Show count of total screenshots if there are more
            if len(screenshot_files) > 1:
                st.info(f"📊 Total screenshots captured: {len(screenshot_files)} (showing most recent)")


def _display_feedback_section(state):
    """Display feedback section for script review and regeneration."""
    st.markdown("---")
    st.markdown("### 💬 Script Review & Feedback")
    
    results = state.script_execution_results
    
    if results['success']:
        st.success("🎉 **Great! The test case script executed successfully.**")
        st.info("You can now proceed to optimization or provide feedback for improvements.")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("✅ Approve & Proceed to Optimization", type="primary", use_container_width=True):
                _proceed_to_optimization(state)
        
        with col2:
            if st.button("🔄 Provide Feedback for Improvement", use_container_width=True):
                st.session_state['show_feedback_form'] = True
                st.rerun()
    
    else:
        st.error("❌ **The test case script failed to execute properly.**")
        st.info("Please provide feedback to regenerate an improved version.")
        
        if st.button("🔄 Provide Feedback & Regenerate", type="primary", use_container_width=True):
            st.session_state['show_feedback_form'] = True
            st.rerun()
    
    # Show feedback form if requested
    if st.session_state.get('show_feedback_form', False):
        _display_feedback_form(state)


def _display_feedback_form(state):
    """Display form for collecting user feedback."""
    st.markdown("#### 📝 Feedback Form")
    
    feedback_text = st.text_area(
        "Describe the issues or improvements needed:",
        height=150,
        placeholder="Please describe what went wrong or what improvements you'd like to see in the script..."
    )
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🚀 Regenerate Script", type="primary", use_container_width=True):
            if feedback_text.strip():
                _regenerate_script_with_feedback(state, feedback_text)
            else:
                st.warning("⚠️ Please provide feedback before regenerating.")
    
    with col2:
        if st.button("❌ Cancel", use_container_width=True):
            st.session_state['show_feedback_form'] = False
            st.rerun()


def _clear_regeneration_session_state():
    """Clear regeneration-related session state variables to fix persistent loading bug."""
    regeneration_keys_to_clear = [
        'regeneration_in_progress',
        'stage_progression_message',
        'regeneration_attempt',
        'feedback_guidelines_count'
    ]

    for key in regeneration_keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]
            debug(f"Cleared regeneration session state key: {key}",
                  stage="stage7b", operation="regeneration_cleanup")

    # Clear step-specific regeneration attempt keys
    step_regen_keys = [key for key in st.session_state.keys()
                     if key.startswith('regeneration_attempt_step_')]
    for key in step_regen_keys:
        del st.session_state[key]
        debug(f"Cleared step regeneration session state key: {key}",
              stage="stage7b", operation="regeneration_cleanup")


def _regenerate_script_with_feedback(state, feedback: str):
    """Regenerate the script based on user feedback."""
    with st.spinner("🤖 Regenerating script based on your feedback..."):
        try:
            # Import AI generation function
            from core.ai_generation import generate_complete_test_case_script

            # Store the feedback for the regeneration
            state.regeneration_feedback = feedback

            # Get the enhanced inputs from session state if available
            enhanced_inputs = st.session_state.get('stage6b_inputs', {})

            # Regenerate the script with feedback
            regenerated_script = generate_complete_test_case_script(
                test_case=state.selected_test_case,
                step_table=state.step_table_json,
                enhanced_inputs=enhanced_inputs,
                website_url=getattr(state, 'website_url', ''),
                generation_options={
                    'custom_instructions': f"Previous script failed. User feedback: {feedback}",
                    'include_comments': True,
                    'include_assertions': True,
                    'include_error_handling': True,
                    'include_screenshots': True
                },
                api_key=getattr(state, 'google_api_key', None),
                state=state
            )

            if regenerated_script and regenerated_script.strip():
                # Save the regenerated script
                script_path = _save_regenerated_script(state, regenerated_script, feedback)

                # Update state
                state.generated_script_path = script_path
                state.combined_script_path = script_path
                state.combined_script_content = regenerated_script

                # Clear previous execution results
                if hasattr(state, 'script_execution_results'):
                    delattr(state, 'script_execution_results')

                # Clear feedback form
                st.session_state['show_feedback_form'] = False

                # Clear regeneration-related session state variables to fix persistent loading bug
                _clear_regeneration_session_state()

                # Update session state
                st.session_state['state'] = state

                st.success("✅ Script regenerated successfully!")
                st.info("💡 You can now execute the improved script.")

                time.sleep(1)
                st.rerun()
            else:
                st.error("❌ Failed to regenerate script. Please try again.")
                # Clear regeneration state even on failure to prevent persistent loading
                _clear_regeneration_session_state()

        except Exception as e:
            st.error(f"❌ Error regenerating script: {str(e)}")
            logger.error(f"Stage 7B: Script regeneration error: {str(e)}")
            # Clear regeneration state on exception to prevent persistent loading
            _clear_regeneration_session_state()


def _save_regenerated_script(state, script_content: str, feedback: str) -> str:
    """Save the regenerated script to a file."""
    try:
        # Create script directory if it doesn't exist
        script_dir = SCRIPT_DIR
        os.makedirs(SCRIPT_DIR, exist_ok=True)

        # Generate filename with regeneration indicator
        test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown').replace(' ', '_').replace('-', '_')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_{test_case_id}_regenerated_{timestamp}.py"
        filepath = os.path.join(script_dir, filename)

        # Add header with regeneration info
        header = f'''# Regenerated Test Case Script for: {test_case_id}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Regeneration reason: {feedback[:100]}...
# Created in Stage 7B (Test Case-Level Script Execution)

'''
        final_script_content = header + script_content

        # Save the script
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(final_script_content)

        st.success(f"💾 Regenerated script saved to: `{filepath}`")
        debug("Regenerated script saved", stage="stage7b", operation="script_regeneration",
              context={'filepath': filepath, 'script_length': len(final_script_content)})

        return filepath

    except Exception as e:
        st.warning(f"⚠️ Could not save regenerated script to file: {str(e)}")
        debug("Regenerated script save error", stage="stage7b", operation="script_save_error",
              context={'error': str(e)})
        return ""


def _proceed_to_optimization(state):
    """Proceed to Stage 8 (optimization) after user approval."""
    try:
        # Set the flag to indicate script is ready for optimization
        state.all_steps_done = True
        
        # Automatically advance to Stage 8
        success = state.advance_to(StateStage.STAGE8_OPTIMIZE, 
                                 "Stage 7B → Stage 8: User approved script, proceeding to optimization")
        
        if success:
            debug("Stage 7B: Auto-navigation to Stage 8 successful", stage="stage7b",
                  operation="auto_navigation_success",
                  context={
                      'new_stage': state.current_stage.get_display_name(),
                      'transition_reason': 'User approved test case script'
                  })
            
            # Force state update and trigger rerun
            st.session_state['state'] = state
            st.session_state['stage_progression_message'] = "✅ Automatically advanced to Stage 8: Script Optimization"
            
            st.success("🎉 Proceeding to Script Optimization...")
            time.sleep(1)
            st.rerun()
        else:
            debug("Stage 7B: Auto-navigation to Stage 8 failed", stage="stage7b",
                  operation="auto_navigation_failed",
                  context={'reason': 'advance_to returned False'})
            st.error("❌ Failed to automatically advance to Stage 8. You can manually navigate using the sidebar.")
            
    except Exception as e:
        st.error(f"❌ Error proceeding to optimization: {str(e)}")
        logger.error(f"Stage 7B: Error proceeding to optimization: {str(e)}")
