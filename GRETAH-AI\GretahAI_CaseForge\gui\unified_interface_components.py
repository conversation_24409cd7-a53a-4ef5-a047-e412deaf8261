"""
<PERSON><PERSON>h<PERSON><PERSON>_CaseForge - Unified Interface UI Components

Professional UI components for the unified test case interface with comprehensive
filtering capabilities, consistent styling, and optimal user experience.

Author: GretahAI Development Team
Date: 2025-01-14
Version: 1.0.0
"""

import streamlit as st
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# Import database functions
from db_helper import (
    get_unified_filtered_test_cases,
    get_filter_options,
    DATABASE_PATH
)

# Import existing UI helpers
from helpers.ui import create_jira_details_css
from helpers.data import count_valid_test_cases

# Import existing export helpers
from helpers.csv.export import export_test_cases_to_csv, format_csv_for_external_tools
from helpers.excel.formatting import create_formatted_excel_from_scenarios

# Import configuration
from .unified_interface_config import (
    DEFAULT_FILTERS, UI_CONFIG, EDITABLE_COLUMNS, COLUMN_ORDER,
    EXPORT_CONFIG, SESSION_KEYS, HELP_TEXT, ERROR_MESSAGES,
    get_default_date_range, get_session_key, get_export_filename,
    get_mime_type, get_filter_help_text, get_error_message,
    get_ui_filter_key, get_applied_filter_key, get_smart_default_key
)

def create_unified_interface_css():
    """
    Create custom CSS for the unified interface with professional styling.
    
    Returns:
        str: CSS style definitions for unified interface
    """
    return """
    <style>
    /* Unified Interface Styling */
    
    /* Removed filter-container styling for cleaner HTML structure */
    
    /* Removed filter-section-header styling - using native Streamlit subheader */
    
    /* Removed count-indicator styling for cleaner UI */
    
    .filter-group {
        background: white;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e9ecef;
    }
    
    .filter-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
    
    /* Removed export-section styling for cleaner HTML structure */
    
    /* Removed export-header styling - using native Streamlit subheader */
    
    /* Removed data-table-container styling for cleaner HTML structure */
    
    /* Removed table-header styling - using native Streamlit subheader */
    
    /* Button styling */
    .stButton > button {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    /* Selectbox and input styling */
    .stSelectbox > div > div {
        border-radius: 6px;
        border: 1px solid #ced4da;
    }
    
    .stDateInput > div > div {
        border-radius: 6px;
        border: 1px solid #ced4da;
    }
    
    /* Expander styling */
    .streamlit-expanderHeader {
        background: #f8f9fa;
        border-radius: 6px;
        font-weight: 600;
    }
    
    /* Alert styling */
    .stAlert {
        border-radius: 6px;
    }
    
    /* Success message styling */
    .stSuccess {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: 1px solid #c3e6cb;
        border-radius: 6px;
    }
    
    /* Warning message styling */
    .stWarning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 1px solid #ffeaa7;
        border-radius: 6px;
    }
    
    /* Info message styling */
    .stInfo {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        border: 1px solid #bee5eb;
        border-radius: 6px;
    }
    </style>
    """

def has_valid_test_case_id_range_data() -> bool:
    """
    Check if we have valid Test Case ID range data that should be maintained.

    Returns:
        bool: True if valid Test Case ID range data exists
    """
    try:
        # Check if we have generation tracking data
        generation_start = st.session_state.get("generation_start_id")
        generation_end = st.session_state.get("generation_end_id")

        if generation_start is not None and generation_end is not None:
            print(f"🔍 Found generation tracking data: {generation_start} to {generation_end}")
            return True

        # Check if Test Case ID range is already set in session state
        start_key = get_session_key('filters', 'test_case_id_range_start')
        end_key = get_session_key('filters', 'test_case_id_range_end')

        start_value = st.session_state.get(start_key)
        end_value = st.session_state.get(end_key)

        if start_value is not None and end_value is not None:
            print(f"🔍 Found existing Test Case ID range filters: {start_value} to {end_value}")
            return True

        # Check if we can detect range from database
        range_info = get_current_generation_test_case_id_range()
        if range_info['range_available']:
            print(f"🔍 Found database-detected range: {range_info['start_id']} to {range_info['end_id']}")
            return True

        print("🔍 No valid Test Case ID range data found")
        return False

    except Exception as e:
        print(f"⚠️ Error checking Test Case ID range data: {e}")
        return False

def store_smart_defaults_from_generation():
    """
    Store smart default values from the current generation context.
    This should be called after test case generation to capture the generation context.
    """
    try:
        # Store smart defaults based on generation context
        smart_defaults = {
            'test_type': st.session_state.get("test_type_select", "all"),
            'ai_generated': 'Yes',  # Generated test cases are AI generated
            'jira_id': st.session_state.get("jira_case_id_input", "All"),
            'enhanced_status': 'Yes' if st.session_state.get("using_enhanced_for_generation", False) else 'All'
        }

        # Store Test Case ID range if available
        range_info = get_current_generation_test_case_id_range()
        if range_info['range_available']:
            smart_defaults['test_case_id_range_start'] = range_info['start_id']
            smart_defaults['test_case_id_range_end'] = range_info['end_id']

            # Set date range to today for additional context
            today = datetime.now().date()
            smart_defaults['date_start'] = today
            smart_defaults['date_end'] = today
        else:
            # Fallback to today's date
            today = datetime.now().date()
            smart_defaults['date_start'] = today
            smart_defaults['date_end'] = today

        # Store smart defaults in session state
        for key, value in smart_defaults.items():
            smart_default_key = get_smart_default_key(key)
            st.session_state[smart_default_key] = value
            print(f"🎯 Stored smart default: {smart_default_key} = {value}")

        print("✅ Smart defaults stored from generation context")

    except Exception as e:
        print(f"⚠️ Error storing smart defaults: {e}")

def apply_filters_to_database():
    """
    Apply current UI filter state to applied filter state and trigger database query.
    This function transfers UI filter values to the applied filter state used for database queries.
    """
    try:
        print("🔄 Applying UI filters to database query state...")

        # Transfer UI filter state to applied filter state - Enhanced with new filters
        ui_to_applied_mapping = {
            'test_type': 'test_type',
            'feature': 'feature',
            'project': 'project',
            'priority': 'priority',
            'test_group': 'test_group',
            'user_name': 'user_name',
            'test_status': 'test_status',
            'jira_id': 'jira_id',
            'date_start': 'date_start',
            'date_end': 'date_end',
            'ai_generated': 'ai_generated',
            'enhanced_status': 'enhanced_status',
            'is_edited': 'is_edited',
            'test_case_id_range_start': 'test_case_id_range_start',
            'test_case_id_range_end': 'test_case_id_range_end'
        }

        for ui_key, applied_key in ui_to_applied_mapping.items():
            ui_session_key = get_ui_filter_key(ui_key)
            applied_session_key = get_applied_filter_key(applied_key)

            if ui_session_key in st.session_state:
                ui_value = st.session_state[ui_session_key]
                st.session_state[applied_session_key] = ui_value
                print(f"📊 Applied filter: {applied_key} = {ui_value}")

        # Clear export state when filters are applied
        clear_export_state()

        # Clear AI-modified data when filters change (AI modifications might not match new filters)
        if "ai_modified_data" in st.session_state:
            del st.session_state["ai_modified_data"]
        if "ai_modification_timestamp" in st.session_state:
            del st.session_state["ai_modification_timestamp"]
        print("🧹 Cleared AI modifications due to filter change")

        print("✅ Filters applied to database query state")

    except Exception as e:
        print(f"⚠️ Error applying filters: {e}")

def reset_filters_to_smart_defaults():
    """
    Reset all filters to their smart default values from generation.
    This function restores the original smart defaults and applies them immediately.
    """
    try:
        print("🔄 Resetting filters to smart defaults...")

        # Get stored smart defaults
        smart_default_keys = ['test_type', 'date_start', 'date_end', 'ai_generated',
                             'enhanced_status', 'jira_id', 'test_case_id_range_start',
                             'test_case_id_range_end']

        for key in smart_default_keys:
            smart_default_key = get_smart_default_key(key)
            ui_filter_key = get_ui_filter_key(key)
            applied_filter_key = get_applied_filter_key(key)

            if smart_default_key in st.session_state:
                smart_value = st.session_state[smart_default_key]
                # Set both UI and applied state to smart default
                st.session_state[ui_filter_key] = smart_value
                st.session_state[applied_filter_key] = smart_value
                print(f"🎯 Reset to smart default: {key} = {smart_value}")
            else:
                # Fallback to regular default if smart default not available
                default_value = DEFAULT_FILTERS.get(key)
                if default_value is not None:
                    st.session_state[ui_filter_key] = default_value
                    st.session_state[applied_filter_key] = default_value
                    print(f"🔧 Reset to regular default: {key} = {default_value}")

        # Clear export state when filters are reset
        clear_export_state()

        print("✅ Filters reset to smart defaults")

    except Exception as e:
        print(f"⚠️ Error resetting filters: {e}")

def initialize_unified_session_state(apply_smart_defaults=False):
    """
    Initialize session state variables for unified interface.

    Args:
        apply_smart_defaults (bool): If True, apply smart defaults for newly generated test cases

    Sets up default filter values and ensures all necessary session state
    variables are properly initialized for the unified interface.
    """
    # Check if this is the first time showing the interface after generation
    first_time_after_generation = (
        apply_smart_defaults and
        st.session_state.get("test_cases_generated", False) and
        not st.session_state.get("unified_interface_initialized", False)
    )

    print(f"🔍 Session state analysis:")
    print(f"   first_time_after_generation: {first_time_after_generation}")
    print(f"   test_cases_generated: {st.session_state.get('test_cases_generated', False)}")
    print(f"   unified_interface_initialized: {st.session_state.get('unified_interface_initialized', False)}")

    # Store smart defaults from generation context if this is the first time
    if first_time_after_generation:
        store_smart_defaults_from_generation()

    # Initialize UI filter state and applied filter state
    # Removed feature, project, test_group as they're redundant with JIRA User Story ID
    filter_keys = ['test_type', 'priority', 'user_name', 'test_status', 'jira_id',
                   'date_start', 'date_end', 'ai_generated', 'enhanced_status',
                   'is_edited', 'test_case_id_range_start', 'test_case_id_range_end']

    for key in filter_keys:
        ui_filter_key = get_ui_filter_key(key)
        applied_filter_key = get_applied_filter_key(key)
        smart_default_key = get_smart_default_key(key)

        # Initialize UI filter state
        if ui_filter_key not in st.session_state:
            if first_time_after_generation and smart_default_key in st.session_state:
                # Use smart default for UI state
                st.session_state[ui_filter_key] = st.session_state[smart_default_key]
                print(f"🎯 Set UI filter from smart default: {key} = {st.session_state[smart_default_key]}")
            else:
                # Use regular default for UI state
                if key == 'date_start':
                    start_date, _ = get_default_date_range()
                    st.session_state[ui_filter_key] = start_date
                elif key == 'date_end':
                    _, end_date = get_default_date_range()
                    st.session_state[ui_filter_key] = end_date
                else:
                    default_value = DEFAULT_FILTERS.get(key)
                    st.session_state[ui_filter_key] = default_value
                    print(f"🔧 Set UI filter to default: {key} = {default_value}")

        # Initialize applied filter state
        if applied_filter_key not in st.session_state:
            if first_time_after_generation and smart_default_key in st.session_state:
                # Use smart default for applied state (auto-apply on first time)
                st.session_state[applied_filter_key] = st.session_state[smart_default_key]
                print(f"🎯 Set applied filter from smart default: {key} = {st.session_state[smart_default_key]}")
            else:
                # Use regular default for applied state
                if key == 'date_start':
                    start_date, _ = get_default_date_range()
                    st.session_state[applied_filter_key] = start_date
                elif key == 'date_end':
                    _, end_date = get_default_date_range()
                    st.session_state[applied_filter_key] = end_date
                else:
                    default_value = DEFAULT_FILTERS.get(key)
                    st.session_state[applied_filter_key] = default_value
                    print(f"🔧 Set applied filter to default: {key} = {default_value}")

    # Initialize export state using configuration
    for key in SESSION_KEYS['export']:
        session_key = get_session_key('export', key)
        if session_key not in st.session_state:
            if key == 'ready':
                st.session_state[session_key] = False
            else:
                st.session_state[session_key] = None

    # Initialize cache state
    for key in SESSION_KEYS['cache']:
        session_key = get_session_key('cache', key)
        if session_key not in st.session_state:
            if key == 'filter_options':
                st.session_state[session_key] = {}
            else:
                st.session_state[session_key] = None

    # Mark interface as initialized if applying smart defaults
    if first_time_after_generation:
        st.session_state["unified_interface_initialized"] = True

def ensure_test_case_id_range_filtering_applied():
    """
    Ensure that Test Case ID range filtering is applied to session state.
    This function maintains persistent filtering across UI interactions.
    """
    try:
        start_key = get_session_key('filters', 'test_case_id_range_start')
        end_key = get_session_key('filters', 'test_case_id_range_end')

        # Check if Test Case ID range filters are already set
        start_value = st.session_state.get(start_key)
        end_value = st.session_state.get(end_key)

        if start_value is not None and end_value is not None:
            print(f"🔒 Test Case ID range filtering already applied: TC_{start_value:03d} to TC_{end_value:03d}")
            return

        # Try to get range info and apply it
        range_info = get_current_generation_test_case_id_range()

        if range_info['range_available']:
            st.session_state[start_key] = range_info['start_id']
            st.session_state[end_key] = range_info['end_id']

            print(f"🎯 Applied persistent Test Case ID range filtering: TC_{range_info['start_id']:03d} to TC_{range_info['end_id']:03d}")
            print(f"🔄 Strategy: {range_info['strategy']} (persistent across interactions)")
        else:
            print("ℹ️ No Test Case ID range data available for persistent filtering")

    except Exception as e:
        print(f"⚠️ Error ensuring Test Case ID range filtering: {e}")

def track_test_case_id_before_generation():
    """
    Track the highest test case ID before generation starts.
    This should be called before test case generation begins.
    """
    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return

        # Get the highest test case ID from database
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH

        highest_id = get_highest_test_case_id_number(DATABASE_PATH, jira_id, "all")

        # Store the last known test case ID before generation
        st.session_state[get_session_key('generation_tracking', 'last_known_test_case_id')] = highest_id
        st.session_state[get_session_key('generation_tracking', 'generation_start_id')] = highest_id + 1

        print(f"Tracked test case ID before generation: Last known ID = {highest_id}, Next ID will be {highest_id + 1}")

    except Exception as e:
        print(f"Error tracking test case ID before generation: {e}")

def track_test_case_id_after_generation():
    """
    Track the highest test case ID after generation completes.
    This should be called after test case generation is complete.
    """
    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return

        # Get the current highest test case ID from database
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH

        current_highest_id = get_highest_test_case_id_number(DATABASE_PATH, jira_id, "all")

        # Store the current highest test case ID after generation
        st.session_state[get_session_key('generation_tracking', 'current_highest_test_case_id')] = current_highest_id
        st.session_state[get_session_key('generation_tracking', 'generation_end_id')] = current_highest_id

        print(f"Tracked test case ID after generation: Current highest ID = {current_highest_id}")

    except Exception as e:
        print(f"Error tracking test case ID after generation: {e}")

def get_current_generation_test_case_id_range():
    """
    Get the test case ID range for the current generation session using database queries.

    Returns:
        dict: Dictionary containing range information
    """
    range_info = {
        'start_id': None,
        'end_id': None,
        'range_available': False,
        'strategy': 'none'
    }

    try:
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id:
            return range_info

        # Strategy 1: Use tracked session state IDs (most reliable)
        start_id = st.session_state.get(get_session_key('generation_tracking', 'generation_start_id'))
        end_id = st.session_state.get(get_session_key('generation_tracking', 'generation_end_id'))

        if start_id is not None and end_id is not None and end_id >= start_id:
            range_info['start_id'] = start_id
            range_info['end_id'] = end_id
            range_info['range_available'] = True
            range_info['strategy'] = 'tracked_session'

            print(f"Test case ID range from tracked session: TC_{start_id:03d} to TC_{end_id:03d}")
            return range_info

        # Strategy 2: Database-driven detection for recent generation
        from db_helper import get_highest_test_case_id_number, DATABASE_PATH
        import sqlite3

        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the most recent test run for this JIRA ID
        cursor.execute("""
            SELECT id, timestamp FROM test_runs
            WHERE jira_id = ?
            ORDER BY timestamp DESC LIMIT 1
        """, (jira_id,))

        recent_run = cursor.fetchone()
        if recent_run:
            # Get test cases for this specific run
            cursor.execute("""
                SELECT test_case_id FROM test_cases
                WHERE test_run_id = ? AND jira_id = ?
                ORDER BY test_case_id
            """, (recent_run['id'], jira_id))

            test_case_ids = cursor.fetchall()
            if test_case_ids:
                # Extract numeric IDs and find range
                numeric_ids = []
                for row in test_case_ids:
                    tc_id = row['test_case_id']
                    if tc_id and tc_id.startswith('TC_'):
                        try:
                            numeric_id = int(tc_id.split('_')[1])
                            numeric_ids.append(numeric_id)
                        except (ValueError, IndexError):
                            continue

                if numeric_ids:
                    numeric_ids.sort()
                    range_info['start_id'] = numeric_ids[0]
                    range_info['end_id'] = numeric_ids[-1]
                    range_info['range_available'] = True
                    range_info['strategy'] = 'database_run'

                    print(f"Test case ID range from database run: TC_{numeric_ids[0]:03d} to TC_{numeric_ids[-1]:03d}")

        conn.close()

    except Exception as e:
        print(f"Error getting test case ID range: {e}")

    return range_info

def get_current_generation_run_info():
    """
    Get precise information about the current test generation run for tight timestamp filtering.

    This function captures the exact generation session timeframe by analyzing:
    1. Test run timestamps from database
    2. Test case creation timestamps
    3. Generation session markers

    Returns:
        dict: Dictionary containing precise run information with tight timestamp bounds
    """
    scenario_data = st.session_state.get("scenario_data", {})

    # Initialize run info with precise timestamp bounds
    run_info = {
        'run_id': None,
        'timestamp_start': None,  # Start of generation session
        'timestamp_end': None,    # End of generation session
        'jira_id': None,
        'test_type': None,
        'generation_session_id': None  # Unique identifier for this generation session
    }

    # Get current generation context
    run_info['jira_id'] = st.session_state.get("jira_case_id_input", "")
    run_info['test_type'] = st.session_state.get("test_type_select", "all")

    # Try to get precise generation session timing
    if scenario_data and scenario_data.get("output_file"):
        try:
            from db_helper import DATABASE_PATH
            import sqlite3
            from datetime import datetime, timedelta

            conn = sqlite3.connect(DATABASE_PATH)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Strategy 1: Find the most recent test run and its associated test cases
            cursor.execute("""
                SELECT tr.id, tr.timestamp, tr.jira_id, tr.test_type
                FROM test_runs tr
                WHERE tr.jira_id = ? AND tr.test_type = ?
                ORDER BY tr.timestamp DESC LIMIT 1
            """, (run_info['jira_id'], run_info['test_type'].lower()))

            recent_run = cursor.fetchone()
            if recent_run:
                run_info['run_id'] = recent_run['id']
                run_timestamp = datetime.strptime(recent_run['timestamp'], "%Y-%m-%d %H:%M:%S")

                # Strategy 2: Get the actual test case creation timestamps for this run
                cursor.execute("""
                    SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest, COUNT(*) as count
                    FROM test_cases
                    WHERE test_run_id = ? AND jira_id = ?
                """, (recent_run['id'], run_info['jira_id']))

                case_timing = cursor.fetchone()
                if case_timing and case_timing['count'] > 0:
                    # Use actual test case creation timestamps for precise bounds
                    earliest_case = datetime.strptime(case_timing['earliest'], "%Y-%m-%d %H:%M:%S")
                    latest_case = datetime.strptime(case_timing['latest'], "%Y-%m-%d %H:%M:%S")

                    # Set tight timestamp bounds (with small buffer for database timing variations)
                    buffer_seconds = 30  # 30-second buffer for timing variations
                    run_info['timestamp_start'] = (earliest_case - timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")
                    run_info['timestamp_end'] = (latest_case + timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")

                    # Create unique generation session identifier
                    run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_{recent_run['id']}_{earliest_case.strftime('%Y%m%d_%H%M%S')}"

                    print(f"Tight timestamp filtering: {run_info['timestamp_start']} to {run_info['timestamp_end']}")
                else:
                    # Fallback: Use test run timestamp with tight window
                    buffer_minutes = 5  # 5-minute window around test run creation
                    start_time = run_timestamp - timedelta(minutes=buffer_minutes)
                    end_time = run_timestamp + timedelta(minutes=buffer_minutes)

                    run_info['timestamp_start'] = start_time.strftime("%Y-%m-%d %H:%M:%S")
                    run_info['timestamp_end'] = end_time.strftime("%Y-%m-%d %H:%M:%S")
                    run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_{recent_run['id']}_fallback"

            # Strategy 3: If no test run found, look for recent test cases directly
            if not run_info['run_id']:
                cursor.execute("""
                    SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest, COUNT(*) as count
                    FROM test_cases
                    WHERE jira_id = ? AND dashboard_test_type = ?
                    AND timestamp >= datetime('now', '-1 hour')
                    ORDER BY timestamp DESC
                """, (run_info['jira_id'], run_info['test_type']))

                recent_cases = cursor.fetchone()
                if recent_cases and recent_cases['count'] > 0:
                    earliest_case = datetime.strptime(recent_cases['earliest'], "%Y-%m-%d %H:%M:%S")
                    latest_case = datetime.strptime(recent_cases['latest'], "%Y-%m-%d %H:%M:%S")

                    # Very tight window for direct case filtering
                    buffer_seconds = 10
                    run_info['timestamp_start'] = (earliest_case - timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")
                    run_info['timestamp_end'] = (latest_case + timedelta(seconds=buffer_seconds)).strftime("%Y-%m-%d %H:%M:%S")
                    run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_direct_{earliest_case.strftime('%Y%m%d_%H%M%S')}"

            conn.close()

        except Exception as e:
            print(f"Error getting precise run info: {e}")
            # Fallback to very recent timestamp window
            from datetime import datetime, timedelta
            now = datetime.now()
            run_info['timestamp_start'] = (now - timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M:%S")
            run_info['timestamp_end'] = now.strftime("%Y-%m-%d %H:%M:%S")
            run_info['generation_session_id'] = f"{run_info['jira_id']}_{run_info['test_type']}_fallback_{now.strftime('%Y%m%d_%H%M%S')}"

    return run_info

def get_smart_default_value(filter_key):
    """
    Get smart default values for filters when showing newly generated test cases.

    Enhanced to provide precise filtering based on current generation run
    instead of entire day filtering.

    Args:
        filter_key (str): The filter key to get smart default for

    Returns:
        Any: Smart default value for the filter
    """
    if filter_key == 'test_type':
        # Use the currently selected test type from generation
        return st.session_state.get("test_type_select", "all")
    elif filter_key == 'ai_generated':
        # Show only AI generated test cases (since they were just generated)
        return 'Yes'
    elif filter_key == 'jira_id':
        # Use the current JIRA ID from generation
        return st.session_state.get("jira_case_id_input", "All")
    elif filter_key == 'enhanced_status':
        # Check if enhanced description was used for generation
        if st.session_state.get("using_enhanced_for_generation", False):
            return 'Yes'
        else:
            return 'All'
    elif filter_key == 'test_status':
        # Default to show all test statuses for newly generated test cases
        return 'all'
    elif filter_key == 'current_run_only':
        # New filter for current generation run
        return True
    else:
        # For other filters, use regular defaults
        return DEFAULT_FILTERS.get(filter_key)

def clear_export_state():
    """
    Clear the export state to prevent stale data issues.
    """
    st.session_state[get_session_key('export', 'ready')] = False
    st.session_state[get_session_key('export', 'filename')] = None
    st.session_state[get_session_key('export', 'format')] = None
    st.session_state[get_session_key('export', 'data')] = None

def clear_all_cache_for_fresh_data():
    """
    Clear all cached data to ensure fresh database calls.
    This function forces the interface to make new database queries.
    """
    # Clear filter options cache
    if 'unified_filter_options' in st.session_state:
        del st.session_state.unified_filter_options
        print("🧹 Cleared filter options cache")

    # Clear any data caching flags
    cache_keys_to_clear = [
        'unified_last_refresh',
        'last_filter_hash',
        'unified_export_ready',
        'unified_export_data',
        'force_refresh',
        'unified_ai_modified_data',
        'unified_ai_modification_applied',
        'ai_modified_data',
        'ai_modification_timestamp'
    ]

    for key in cache_keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]
            print(f"🧹 Cleared session key: {key}")

    # Set force refresh flag
    st.session_state.force_refresh = True
    print("✅ All cache cleared - forcing fresh database calls")

def reset_unified_filters():
    """
    Reset all unified interface filters to their default values.

    Clears all filter session state and resets to default configuration
    as defined in the DEFAULT_FILTERS dictionary.
    """
    # Reset filter values using configuration
    for key, default_value in DEFAULT_FILTERS.items():
        session_key = get_session_key('filters', key)
        if key == 'date_range_days':
            # Reset date range
            start_date, end_date = get_default_date_range()
            st.session_state[get_session_key('filters', 'date_start')] = start_date
            st.session_state[get_session_key('filters', 'date_end')] = end_date
        else:
            st.session_state[session_key] = default_value

    # Reset export state
    clear_export_state()

def get_current_applied_filters() -> Dict:
    """
    Get current applied filter values from session state (used for database queries).

    Returns:
        Dict: Dictionary containing current applied filter values
    """
    filters = {}

    # Get applied filter values from session state - Unified filters (removed redundant feature, project, test_group)
    applied_test_type = st.session_state.get(get_applied_filter_key('test_type'), 'all')
    if applied_test_type != 'all':
        filters['test_type'] = applied_test_type

    applied_priority = st.session_state.get(get_applied_filter_key('priority'), 'all')
    if applied_priority != 'all':
        filters['priority'] = applied_priority

    applied_user_name = st.session_state.get(get_applied_filter_key('user_name'), 'all')
    if applied_user_name != 'all':
        filters['user_name'] = applied_user_name

    applied_test_status = st.session_state.get(get_applied_filter_key('test_status'), 'all')
    if applied_test_status != 'all':
        filters['test_status'] = applied_test_status

    applied_jira_id = st.session_state.get(get_applied_filter_key('jira_id'), 'All')
    if applied_jira_id != 'All':
        filters['jira_id'] = applied_jira_id

    # Date filters
    applied_date_start = st.session_state.get(get_applied_filter_key('date_start'))
    if applied_date_start:
        if hasattr(applied_date_start, 'strftime'):
            filters['date_start'] = applied_date_start.strftime('%Y-%m-%d')
        else:
            filters['date_start'] = str(applied_date_start)

    applied_date_end = st.session_state.get(get_applied_filter_key('date_end'))
    if applied_date_end:
        if hasattr(applied_date_end, 'strftime'):
            filters['date_end'] = applied_date_end.strftime('%Y-%m-%d')
        else:
            filters['date_end'] = str(applied_date_end)

    # Status filters
    applied_ai_generated = st.session_state.get(get_applied_filter_key('ai_generated'), 'All')
    if applied_ai_generated != 'All':
        filters['ai_generated'] = applied_ai_generated == 'Yes'

    applied_enhanced_status = st.session_state.get(get_applied_filter_key('enhanced_status'), 'All')
    if applied_enhanced_status != 'All':
        filters['enhanced_status'] = applied_enhanced_status == 'Yes'

    applied_is_edited = st.session_state.get(get_applied_filter_key('is_edited'))
    if applied_is_edited is not None:
        filters['is_edited'] = applied_is_edited

    # Test Case ID range filtering (primary strategy for current session detection)
    applied_start_id = st.session_state.get(get_applied_filter_key('test_case_id_range_start'))
    applied_end_id = st.session_state.get(get_applied_filter_key('test_case_id_range_end'))

    if applied_start_id is not None:
        filters['test_case_id_range_start'] = applied_start_id
        print(f"🎯 Applied Test Case ID range start filter: {applied_start_id}")

    if applied_end_id is not None:
        filters['test_case_id_range_end'] = applied_end_id
        print(f"🎯 Applied Test Case ID range end filter: {applied_end_id}")

    # Debug: Show if Test Case ID range filtering is active
    if 'test_case_id_range_start' in filters and 'test_case_id_range_end' in filters:
        start_id = filters['test_case_id_range_start']
        end_id = filters['test_case_id_range_end']
        print(f"🔍 Test Case ID range filtering ACTIVE: TC_{start_id:03d} to TC_{end_id:03d}")
    else:
        print("ℹ️ Test Case ID range filtering NOT active")

    return filters

def get_current_ui_filters() -> Dict:
    """
    Get current UI filter values from session state (displayed in filter controls).

    Returns:
        Dict: Dictionary containing current UI filter values
    """
    ui_filters = {}

    # Removed feature, project, test_group as they're redundant with JIRA User Story ID
    filter_keys = ['test_type', 'priority', 'user_name', 'test_status', 'jira_id',
                   'date_start', 'date_end', 'ai_generated', 'enhanced_status',
                   'is_edited', 'test_case_id_range_start', 'test_case_id_range_end']

    for key in filter_keys:
        ui_filter_key = get_ui_filter_key(key)
        if ui_filter_key in st.session_state:
            ui_filters[key] = st.session_state[ui_filter_key]

    return ui_filters

def count_unique_test_cases(data: pd.DataFrame) -> int:
    """
    Count unique test cases in the filtered data by grouping by Test Case ID.

    Args:
        data (pd.DataFrame): DataFrame containing test case data with potential duplicate rows per test case

    Returns:
        int: Number of unique test cases
    """
    if data.empty:
        return 0

    # Check if Test Case ID column exists
    if 'Test Case ID' not in data.columns:
        print("⚠️ Test Case ID column not found in data")
        return 0

    # Count unique test cases by grouping by Test Case ID
    # Filter out empty Test Case IDs (which represent additional test steps)
    test_case_data = data[data['Test Case ID'].notna() & (data['Test Case ID'] != '')]
    unique_test_cases = test_case_data['Test Case ID'].nunique()

    print(f"📊 Counted {unique_test_cases} unique test cases from {len(data)} total rows")
    return unique_test_cases

def get_test_case_summary(data: pd.DataFrame) -> Dict:
    """
    Get a summary of test cases including unique count and breakdown by type.

    Args:
        data (pd.DataFrame): DataFrame containing test case data

    Returns:
        Dict: Summary information including unique count, total rows, and type breakdown
    """
    if data.empty:
        return {
            'unique_test_cases': 0,
            'total_rows': 0,
            'test_types': {},
            'jira_ids': set()
        }

    # Check if required columns exist
    if 'Test Case ID' not in data.columns:
        print("⚠️ Test Case ID column not found in data for summary")
        return {
            'unique_test_cases': 0,
            'total_rows': len(data),
            'test_types': {},
            'jira_ids': set()
        }

    # Get unique test cases (rows with non-empty Test Case ID)
    test_case_rows = data[data['Test Case ID'].notna() & (data['Test Case ID'] != '')]

    unique_count = test_case_rows['Test Case ID'].nunique()
    total_rows = len(data)

    # Get test type breakdown
    test_types = {}
    if 'Test Type' in test_case_rows.columns and not test_case_rows.empty:
        # Filter out empty test types
        type_data = test_case_rows[test_case_rows['Test Type'].notna() & (test_case_rows['Test Type'] != '')]
        if not type_data.empty:
            type_counts = type_data.groupby('Test Type')['Test Case ID'].nunique()
            test_types = type_counts.to_dict()

    # Get unique JIRA IDs
    jira_ids = set()
    if 'User Story ID' in test_case_rows.columns and not test_case_rows.empty:
        # Filter out empty JIRA IDs
        jira_data = test_case_rows[test_case_rows['User Story ID'].notna() & (test_case_rows['User Story ID'] != '')]
        if not jira_data.empty:
            jira_ids = set(jira_data['User Story ID'].unique())

    summary = {
        'unique_test_cases': unique_count,
        'total_rows': total_rows,
        'test_types': test_types,
        'jira_ids': jira_ids
    }

    print(f"📈 Test case summary: {unique_count} unique test cases, {total_rows} total rows")
    if test_types:
        print(f"📊 Test type breakdown: {test_types}")

    return summary

def get_current_filters() -> Dict:
    """
    Get current applied filter values (for backward compatibility).
    This function now delegates to get_current_applied_filters().

    Returns:
        Dict: Dictionary containing current applied filter values
    """
    return get_current_applied_filters()

def render_filter_action_buttons():
    """
    Render the Apply Filters and Reset Filters buttons.

    Returns:
        tuple: (apply_clicked, reset_clicked) - Boolean values indicating button clicks
    """
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        # Show current filter status
        applied_filters = get_current_applied_filters()
        ui_filters = get_current_ui_filters()

        # Check if UI filters differ from applied filters
        filters_changed = False
        for key in ['test_type', 'date_start', 'date_end', 'ai_generated', 'enhanced_status',
                   'jira_id', 'test_case_id_range_start', 'test_case_id_range_end']:
            ui_value = ui_filters.get(key)
            applied_key = key
            if key in ['date_start', 'date_end'] and ui_value:
                # Convert date to string for comparison
                if hasattr(ui_value, 'strftime'):
                    ui_value = ui_value.strftime('%Y-%m-%d')

            applied_value = applied_filters.get(applied_key)
            if key == 'ai_generated' and applied_value is not None:
                applied_value = 'Yes' if applied_value else 'No'
            elif key == 'enhanced_status' and applied_value is not None:
                applied_value = 'Yes' if applied_value else 'No'
            elif key == 'test_type' and applied_value is None:
                applied_value = 'all'
            elif key == 'test_status' and applied_value is None:
                applied_value = 'all'
            elif key in ['ai_generated', 'enhanced_status'] and applied_value is None:
                applied_value = 'All'
            elif key == 'jira_id' and applied_value is None:
                applied_value = 'All'

            if ui_value != applied_value:
                filters_changed = True
                break

        # Filter status is now indicated through tooltips and button states

    with col2:
        apply_clicked = st.button(
            "🔍 Apply Filters",
            help="Apply current filter settings to update the test cases table",
            use_container_width=True,
            type="primary"
        )

    with col3:
        reset_clicked = st.button(
            "🔄 Reset Filters",
            help="Reset all filters to smart defaults from generation",
            use_container_width=True
        )

    return apply_clicked, reset_clicked

def render_count_indicator(filtered_count: int, total_count: int):
    """
    Render the count indicator showing filtered vs total test cases.

    Args:
        filtered_count (int): Number of filtered test cases
        total_count (int): Total number of test cases
    """
    col1, col2 = st.columns([4, 1])

    with col1:
        # Clean count display without emoji or excessive styling
        if filtered_count == total_count:
            st.markdown(f"**{filtered_count:,} test cases**")
        else:
            st.markdown(f"**{filtered_count:,}** of **{total_count:,}** test cases")

    with col2:
        if st.button("🔄 Refresh", help="Force fresh database query", use_container_width=True):
            # Clear all cached data to force fresh database calls
            clear_all_cache_for_fresh_data()
            st.rerun()

def render_filter_status_indicator():
    """
    Render a detailed filter status indicator showing which filters are currently applied.
    """
    applied_filters = get_current_applied_filters()

    # Filter status is now shown through tooltips and UI state, so this function is simplified
    pass

def render_count_indicator_with_refresh(filtered_data: pd.DataFrame, total_data: pd.DataFrame, refresh_timestamp: str):
    """
    Enhanced count indicator with timestamp and refresh functionality.
    Now uses unique test case counting instead of raw row counting.

    Args:
        filtered_data (pd.DataFrame): Filtered test case data
        total_data (pd.DataFrame): Total test case data
        refresh_timestamp (str): Timestamp of last database refresh
    """
    # Count unique test cases instead of raw rows
    filtered_count = count_unique_test_cases(filtered_data)
    total_count = count_unique_test_cases(total_data)

    # Get detailed summary for debugging
    filtered_summary = get_test_case_summary(filtered_data)
    total_summary = get_test_case_summary(total_data)

    col1, col2 = st.columns([4, 1])

    with col1:
        # Clean count display with timestamp
        if filtered_count == total_count:
            st.markdown(f"**{filtered_count:,} test cases**")
        else:
            st.markdown(f"**{filtered_count:,}** of **{total_count:,}** test cases")

        # Show additional details in caption
        if filtered_count != len(filtered_data):
            st.caption(f"🕒 Last refreshed: {refresh_timestamp} | {len(filtered_data):,} total rows (including test steps)")
        else:
            st.caption(f"🕒 Last refreshed: {refresh_timestamp}")

    with col2:
        if st.button("🔄 Refresh", help="Force fresh database query", use_container_width=True):
            # Clear all cached data to force fresh database calls
            clear_all_cache_for_fresh_data()
            st.rerun()

    # Show detailed filter status
    render_filter_status_indicator()

    # Show test type breakdown if available
    if filtered_summary['test_types']:
        with st.expander("📊 Test Case Breakdown", expanded=False):
            for test_type, count in filtered_summary['test_types'].items():
                st.write(f"• **{test_type.title()}**: {count} test cases")

            if filtered_summary['jira_ids']:
                st.write(f"• **JIRA Stories**: {len(filtered_summary['jira_ids'])} unique stories")
                if len(filtered_summary['jira_ids']) <= 5:  # Show JIRA IDs if not too many
                    jira_list = ", ".join(sorted(filtered_summary['jira_ids']))
                    st.caption(f"Stories: {jira_list}")

def render_unified_filters(filter_options: Dict):
    """
    Render the unified filter controls in a single section.
    Combines all filter fields without separate Basic/Advanced sections.
    Removes redundant Feature, Project, and Test Group filters as they're covered by JIRA User Story ID.
    Uses UI filter state that doesn't automatically update the table.

    Args:
        filter_options (Dict): Available filter options from database
    """
    st.subheader("🔍 Filters")

    # First row: Core filters
    with st.container():
        col1, col2, col3, col4 = st.columns(4)

    with col1:
        # Test Type filter - use UI state with custom display names
        # Map internal values to display names
        test_type_display_map = {
            'all': 'All Types',
            'positive': 'Positive Type',
            'negative': 'Negative Type',
            'security': 'Security Type',
            'performance': 'Performance Type'
        }
        # Build the list of internal values in the order you want
        test_type_internal_options = ['all', 'positive', 'negative', 'security', 'performance']
        test_type_options = [test_type_display_map.get(opt, opt.title()) for opt in test_type_internal_options]
        ui_test_type_key = get_ui_filter_key('test_type')
        # Get the current internal value from session state, default to 'all'
        current_ui_test_type = st.session_state.get(ui_test_type_key, 'all')
        # Map current internal value to display name
        current_display = test_type_display_map.get(current_ui_test_type, current_ui_test_type.title())
        # Selectbox returns display name, so map back to internal value after selection
        selected_display = st.selectbox(
            "Test Type:",
            options=test_type_options,
            index=test_type_options.index(current_display) if current_display in test_type_options else 0,
            key="unified_ui_type_filter_select",
            help="Filter by test case type (All Types, Positive Type, Negative Type, Security Type, Performance Type)"
        )
        # Reverse map: display name to internal value
        display_to_internal = {v: k for k, v in test_type_display_map.items()}
        st.session_state[ui_test_type_key] = display_to_internal.get(selected_display, selected_display.lower())

        with col2:
            # Priority filter - use UI state
            priority_options = ['all'] + filter_options.get('priorities', [])
            ui_priority_key = get_ui_filter_key('priority')
            current_ui_priority = st.session_state.get(ui_priority_key, 'all')

            st.session_state[ui_priority_key] = st.selectbox(
                "Priority:",
                options=priority_options,
                index=priority_options.index(current_ui_priority)
                      if current_ui_priority in priority_options else 0,
                key="unified_ui_priority_filter_select",
                help="Filter by test case priority level"
            )

        with col3:
            # Test Status filter - use UI state
            test_status_options = ['all'] + filter_options.get('test_statuses', [])
            ui_test_status_key = get_ui_filter_key('test_status')
            current_ui_test_status = st.session_state.get(ui_test_status_key, 'all')

            st.session_state[ui_test_status_key] = st.selectbox(
                "Test Status:",
                options=test_status_options,
                index=test_status_options.index(current_ui_test_status)
                      if current_ui_test_status in test_status_options else 0,
                key="unified_ui_test_status_filter_select",
                help="Filter by test execution status (Not Run, Pass, Fail, Blocked)"
            )

        with col4:
            # User Name filter - use UI state
            user_options = ['all'] + filter_options.get('users', [])
            ui_user_key = get_ui_filter_key('user_name')
            current_ui_user = st.session_state.get(ui_user_key, 'all')

            st.session_state[ui_user_key] = st.selectbox(
                "User:",
                options=user_options,
                index=user_options.index(current_ui_user)
                      if current_ui_user in user_options else 0,
                key="unified_ui_user_filter_select",
                help="Filter by user who created the test cases"
            )

    # Second row: JIRA, Run ID, and status filters
    with st.container():
        col1, col2, col3, col4, col5 = st.columns([1,1,1,1,1])

        with col1:
            # JIRA ID filter - use UI state
            ui_jira_id_key = get_ui_filter_key('jira_id')
            current_ui_jira_id = st.session_state.get(ui_jira_id_key, 'All')

            jira_options = ['All'] + filter_options.get('jira_ids', [])
            jira_index = jira_options.index(current_ui_jira_id) if current_ui_jira_id in jira_options else 0

            st.session_state[ui_jira_id_key] = st.selectbox(
                "JIRA User Story ID:",
                options=jira_options,
                index=jira_index,
                key="unified_ui_jira_id_filter",
                help="Filter by specific JIRA User Story ID"
            )

        with col2:
            # Run ID filter - use UI state and fetch from database
            import db_helper
            ui_run_id_key = get_ui_filter_key('run_id')
            jira_id_for_run = st.session_state.get(ui_jira_id_key, 'All')
            run_id_options = ['All']
            run_id_map = {}
            if jira_id_for_run != 'All':
                # Query all run IDs for this JIRA ID from the database
                from db_helper import DATABASE_PATH
                import sqlite3
                conn = sqlite3.connect(DATABASE_PATH)
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM test_runs WHERE jira_id = ? ORDER BY timestamp DESC", (jira_id_for_run,))
                run_ids = [str(row[0]) for row in cursor.fetchall()]
                conn.close()
                run_id_options += run_ids
                for rid in run_ids:
                    run_id_map[rid] = int(rid)
            current_ui_run_id = st.session_state.get(ui_run_id_key, 'All')
            run_id_index = run_id_options.index(current_ui_run_id) if current_ui_run_id in run_id_options else 0
            selected_run_id = st.selectbox(
                "Run ID:",
                options=run_id_options,
                index=run_id_index,
                key="unified_ui_run_id_filter",
                help="Filter by specific Test Run ID for the selected JIRA User Story"
            )
            st.session_state[ui_run_id_key] = selected_run_id

            # If a specific Run ID is selected, update the Test Case ID Range Start/End using DB
            if selected_run_id != 'All' and selected_run_id in run_id_map:
                from db_helper import get_test_cases_by_test_run, DATABASE_PATH
                df = get_test_cases_by_test_run(DATABASE_PATH, run_id_map[selected_run_id])
                if df is not None and not df.empty and 'Test Case ID' in df.columns:
                    # Only use numeric IDs for range
                    try:
                        case_ids = [int(str(tc).replace('TC_', '')) for tc in df['Test Case ID'] if str(tc).startswith('TC_') and str(tc)[3:].isdigit()]
                        if case_ids:
                            min_id = min(case_ids)
                            max_id = max(case_ids)
                            ui_range_start_key = get_ui_filter_key('test_case_id_range_start')
                            ui_range_end_key = get_ui_filter_key('test_case_id_range_end')
                            st.session_state[ui_range_start_key] = min_id
                            st.session_state[ui_range_end_key] = max_id
                    except Exception as e:
                        print(f"Error parsing Test Case IDs for run {selected_run_id}: {e}")

        with col3:
            # AI Generated filter - use UI state
            ui_ai_generated_key = get_ui_filter_key('ai_generated')
            current_ui_ai_generated = st.session_state.get(ui_ai_generated_key, 'All')

            st.session_state[ui_ai_generated_key] = st.selectbox(
                "AI Generated:",
                options=['All', 'Yes', 'No'],
                index=['All', 'Yes', 'No'].index(current_ui_ai_generated),
                key="unified_ui_ai_generated_filter",
                help="Filter by whether test cases were generated by AI"
            )

        with col4:
            # Date range start - use UI state
            ui_date_start_key = get_ui_filter_key('date_start')
            current_ui_date_start = st.session_state.get(ui_date_start_key, datetime.now().date() - timedelta(days=30))

            st.session_state[ui_date_start_key] = st.date_input(
                "Start Date:",
                value=current_ui_date_start,
                key="unified_ui_date_start_filter",
                help="Filter test cases from this date onwards"
            )

        with col5:
            # Date range end - use UI state
            ui_date_end_key = get_ui_filter_key('date_end')
            current_ui_date_end = st.session_state.get(ui_date_end_key, datetime.now().date())

            st.session_state[ui_date_end_key] = st.date_input(
                "End Date:",
                value=current_ui_date_end,
                key="unified_ui_date_end_filter",
                help="Filter test cases up to this date"
            )

    # Third row: Test Case ID Range filters
    with st.container():
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            # Test Case ID Range Start - use UI state
            ui_range_start_key = get_ui_filter_key('test_case_id_range_start')
            current_ui_range_start = st.session_state.get(ui_range_start_key)
            # Determine min/max for range based on selected run
            min_id = 1
            max_id = 9999
            ui_run_id_key = get_ui_filter_key('run_id')
            selected_run_id = st.session_state.get(ui_run_id_key, 'All')
            # If a run is selected and range is set, use those as bounds
            if selected_run_id != 'All':
                min_id = st.session_state.get(ui_range_start_key, min_id)
                max_id = st.session_state.get(get_ui_filter_key('test_case_id_range_end'), max_id)
            st.session_state[ui_range_start_key] = st.number_input(
                "Test Case ID Range Start:",
                min_value=min_id,
                max_value=max_id,
                value=current_ui_range_start if current_ui_range_start is not None and min_id <= current_ui_range_start <= max_id else min_id,
                step=1,
                key="unified_ui_test_case_id_range_start",
                help="Start of Test Case ID range (e.g., 46 for TC_046)"
            )

        with col2:
            # Test Case ID Range End - use UI state
            ui_range_end_key = get_ui_filter_key('test_case_id_range_end')
            current_ui_range_end = st.session_state.get(ui_range_end_key)
            # Use the same min/max as above
            if selected_run_id != 'All':
                min_id = st.session_state.get(get_ui_filter_key('test_case_id_range_start'), 1)
                max_id = st.session_state.get(ui_range_end_key, 9999)
            else:
                min_id = 1
                max_id = 9999
            st.session_state[ui_range_end_key] = st.number_input(
                "Test Case ID Range End:",
                min_value=min_id,
                max_value=max_id,
                value=current_ui_range_end if current_ui_range_end is not None and min_id <= current_ui_range_end <= max_id else max_id,
                step=1,
                key="unified_ui_test_case_id_range_end",
                help="End of Test Case ID range (e.g., 55 for TC_055)"
            )

        with col3:
            # Show current range if set
            if current_ui_range_start is not None and current_ui_range_end is not None:
                st.markdown("**Current Range:**")
                st.code(f"TC_{current_ui_range_start:03d} to TC_{current_ui_range_end:03d}")
            else:
                st.markdown("**Range Status:**")
                st.info("No ID range set")

        with col4:
            # Empty column for spacing
            st.empty()

def render_save_changes_button(edited_df: pd.DataFrame) -> bool:
    """
    Render a 'Save Changes to Database' button below the test case table.
    
    Args:
        edited_df (pd.DataFrame): The edited dataframe from the data editor
    
    Returns:
        bool: True if save was successful, False otherwise
    """
    if edited_df.empty:
        return False
    
    # Validate that we have test case data with required columns
    required_columns = ["Test Case ID"]
    missing_columns = [col for col in required_columns if col not in edited_df.columns]
    if missing_columns:
        # Don't show save button if essential columns are missing
        return False
    
    # Only show save button if we have data and it contains test cases
    valid_test_cases = edited_df['Test Case ID'].dropna()
    if len(valid_test_cases) == 0:
        return False
    
    if st.button("💾 Save Changes to Database", key="save_unified_test_cases", 
                help="Save all modifications made to the test cases to the database"):
        
        # Get the current JIRA ID from session state
        jira_id = st.session_state.get("jira_case_id_input", "")
        if not jira_id or jira_id == "All":
            st.error("❌ Cannot save: No JIRA ID found. Please extract a JIRA issue first.")
            return False
        
        # Get current user from session state
        current_user = st.session_state.get("admin_username", "anonymous")
        
        # Import the save function
        from db_helper import save_test_cases_to_database, DATABASE_PATH
        
        try:
            with st.spinner("Saving changes to database..."):
                # Validate data before saving
                if 'Test Case ID' not in edited_df.columns:
                    st.error("❌ Cannot save: Test Case ID column is missing")
                    return False
                
                # Remove rows with empty Test Case IDs
                clean_df = edited_df.dropna(subset=['Test Case ID'])
                if clean_df.empty:
                    st.error("❌ Cannot save: No valid test cases found (all Test Case IDs are empty)")
                    return False
                
                # Determine the test type from the data or use 'all' as default
                test_type = "all"  # Default for unified interface
                if 'Test Type' in clean_df.columns:
                    unique_types = clean_df['Test Type'].dropna().unique()
                    if len(unique_types) == 1:
                        test_type = unique_types[0]
                
                # Save to database with is_edited=True to mark as manually edited
                test_run_id = save_test_cases_to_database(
                    DATABASE_PATH, 
                    jira_id, 
                    clean_df, 
                    test_type, 
                    user_name=current_user,
                    is_edited=True
                )
                
                if test_run_id:
                    st.success(f"✅ Successfully saved {len(clean_df)} test cases to database")
                    
                    # Clear any cached data to force refresh on next render
                    if "ai_modified_data" in st.session_state:
                        del st.session_state["ai_modified_data"]
                    if "ai_modification_timestamp" in st.session_state:
                        del st.session_state["ai_modification_timestamp"]
                    
                    return True
                else:
                    st.error("❌ Failed to save test cases to database")
                    return False
                    
        except Exception as e:
            st.error(f"❌ Error saving to database: {str(e)}")
            return False

def render_data_table(filtered_data: pd.DataFrame) -> pd.DataFrame:
    """
    Render the editable data table with professional styling.

    Args:
        filtered_data (pd.DataFrame): Filtered test case data

    Returns:
        pd.DataFrame: Edited dataframe from user interactions
    """
    if filtered_data.empty:
        st.markdown("""
        <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
            <h3 style="color: #6c757d; margin-bottom: 1rem;">⚠️ No test cases match the current filter criteria</h3>
            <p style="color: #6c757d; margin-bottom: 1.5rem;">Try adjusting your filters or resetting them to see more data.</p>
            <p style="color: #495057; font-size: 0.9rem;">💡 <strong>Tip:</strong> Use broader date ranges or select 'All' for categorical filters</p>
        </div>
        """, unsafe_allow_html=True)
        return pd.DataFrame()

    # Display count information with AI modification stats
    unique_count = count_unique_test_cases(filtered_data)
    total_rows = len(filtered_data)

    # Count AI-modified test cases if the column exists
    ai_modified_count = 0
    if 'ai_modified' in filtered_data.columns:
        ai_modified_count = len(filtered_data[filtered_data['ai_modified'] == 1]['Test Case ID'].dropna().unique())

    # Display header with AI indicator
    header_text = "📊 Test Cases"
    if ai_modified_count > 0:
        header_text += f" (🤖 {ai_modified_count} AI-modified)"

    st.subheader(header_text)

    # Display detailed counts
    count_text = f"**Showing {unique_count} unique test cases ({total_rows} total rows)**"
    if ai_modified_count > 0:
        count_text += f" • 🤖 {ai_modified_count} AI-modified"
    st.markdown(count_text)

    # Configure editable columns using enhanced configuration
    from .unified_interface_config import EDITABLE_COLUMNS, READ_ONLY_COLUMNS
    editable_columns = EDITABLE_COLUMNS

    # Add AI modification indicator column for display
    display_data = filtered_data.copy()
    if 'ai_modified' in display_data.columns and 'modification_source' in display_data.columns:
        display_data['AI Status'] = display_data.apply(lambda row:
            "🤖 AI Modified" if row.get('ai_modified') == 1
            else "✏️ Manual" if row.get('modification_source') == 'manual'
            else "📥 Imported", axis=1)
    elif 'ai_modified' in display_data.columns:
        display_data['AI Status'] = display_data.apply(lambda row:
            "🤖 AI Modified" if row.get('ai_modified') == 1
            else "✏️ Manual", axis=1)

    # Hide Test Group column from display
    if 'Test Group' in display_data.columns:
        display_data = display_data.drop('Test Group', axis=1)

    column_config = {}
    for col in display_data.columns:
        if col in editable_columns:
            if col == "Test Status":
                # Special dropdown configuration for Test Status - 4 options only
                column_config[col] = st.column_config.SelectboxColumn(
                    "Test Status",
                    options=["Not Run", "Pass", "Fail", "Blocked"],
                    disabled=False,
                    help="Select test execution status",
                    width="medium"
                )
            elif col == "Priority":
                # Special dropdown configuration for Priority
                column_config[col] = st.column_config.SelectboxColumn(
                    "Priority",
                    options=["High", "Medium", "Low"],
                    disabled=False,
                    help="Select test case priority",
                    width="small"
                )
            elif col == "Test Type":
                # Special dropdown configuration for Test Type
                column_config[col] = st.column_config.SelectboxColumn(
                    "Test Type",
                    options=["positive", "negative", "security", "performance", "all"],
                    disabled=False,
                    help="Select test type classification",
                    width="medium"
                )
            elif col in ["Test Steps", "Expected Result", "Test Case Objective", "Prerequisite", "Comments"]:
                # Multi-line text areas for longer content
                column_config[col] = st.column_config.TextColumn(
                    col,
                    disabled=False,
                    help=f"Click to edit {col.lower()}",
                    width="large"
                )
            else:
                # Standard text columns
                column_config[col] = st.column_config.TextColumn(
                    col,
                    disabled=False,
                    help=f"Click to edit {col.lower()}"
                )
        elif col == "AI Status":
            column_config[col] = st.column_config.TextColumn(
                "AI Status",
                disabled=True,
                help="Shows if test case was modified by AI",
                width="small"
            )
        elif col in READ_ONLY_COLUMNS:
            # Explicitly read-only columns with specific help text
            column_config[col] = st.column_config.TextColumn(
                col,
                disabled=True,
                help=f"Read-only: {col} (system-generated or protected field)"
            )
        else:
            # Default read-only for other columns
            column_config[col] = st.column_config.TextColumn(
                col,
                disabled=True,
                help=f"Read-only: {col}"
            )

    # Display editable table with professional styling
    with st.container():
        # Use the original column order from the two-tab system
        from .unified_interface_config import COLUMN_ORDER

        # Filter column order to only include columns that exist in the data
        available_columns = [col for col in COLUMN_ORDER if col in display_data.columns]

        # Add AI Status column to the end if it exists
        if 'AI Status' in display_data.columns and 'AI Status' not in available_columns:
            available_columns.append('AI Status')

        # Hide internal AI tracking columns from display
        columns_to_hide = ['ai_modified', 'modification_source', 'ai_model_used',
                          'ai_modification_timestamp', 'ai_modification_user', 'ai_user_query',
                          'Test Group']  # Hide Test Group column as requested

        display_columns = [col for col in available_columns if col not in columns_to_hide]
        display_data_filtered = display_data[display_columns]

        # Generate a unique key for the data editor that changes when AI updates occur
        # This ensures the data editor refreshes with new data
        ai_update_counter = st.session_state.get("unified_ai_update_counter", 0)
        editor_key = f"unified_test_cases_editor_{ai_update_counter}"

        edited_df = st.data_editor(
            display_data_filtered,
            use_container_width=True,
            num_rows="dynamic",
            column_config=column_config,
            hide_index=True,
            height=500,
            key=editor_key,
            column_order=display_columns
        )

    # Return the original structure (without AI Status column) for consistency
    if 'AI Status' in edited_df.columns:
        edited_df = edited_df.drop('AI Status', axis=1)

    # Merge back the hidden columns if they existed
    for col in columns_to_hide:
        if col in filtered_data.columns:
            edited_df[col] = filtered_data[col]

    return edited_df

def validate_ai_modification_output(original_df, modified_df):
    """
    Enhanced validation logic for AI modification output with read-only field protection.
    Updated to properly handle the new AI modification workflow.
    """
    # Define read-only fields that should be protected from AI modification
    READ_ONLY_FIELDS = [
        'User Story ID',      # Primary protected field (reported issue)
        'Test Case ID',       # Should never change
        'Project',            # System-assigned
        'Timestamp',          # System-generated
        'User Name',          # System-assigned
        'Username',           # Alternative field name
        'Test Run ID',        # System-generated identifier
        'Actual Result',      # Test execution result
        'Defect ID',          # Defect tracking field
        'Test Type',          # System classification
        'Step No',            # System-generated sequence
        # AI metadata fields (should never be modified by AI)
        'ai_modified',
        'modification_source',
        'ai_model_used',
        'ai_modification_timestamp',
        'ai_modification_user',
        'ai_user_query'
    ]

    # Define explicitly editable fields (consistent with manual editing)
    EDITABLE_FIELDS = [
        'Test Case Objective',
        'Prerequisite',
        'Test Steps',
        'Expected Result',
        'Test Status',
        'Priority',
        'Feature',
        'Comments'
    ]

    # 1. Check if essential columns are present
    essential_columns = ['Test Case ID', 'Test Case Objective', 'Test Steps', 'Expected Result']
    missing_essential = [col for col in essential_columns if col not in modified_df.columns]

    if missing_essential:
        return False, f"Missing essential columns: {missing_essential}"

    # 2. Check if all original columns are preserved
    missing_columns = set(original_df.columns) - set(modified_df.columns)
    if missing_columns:
        return False, f"Missing original columns: {list(missing_columns)}"

    # 3. Validate read-only fields are not modified (check AI metadata fields specifically)
    ai_metadata_fields = ['ai_modified', 'modification_source', 'ai_user_query']
    for field in ai_metadata_fields:
        if field in original_df.columns and field in modified_df.columns:
            # These fields should remain exactly the same as original
            # Use more robust comparison that handles dtype differences
            try:
                # Convert to string and fill NaN values for comparison
                orig_values = original_df[field].fillna('').astype(str)
                mod_values = modified_df[field].fillna('').astype(str)
                
                if not orig_values.equals(mod_values):
                    # Double-check with value-by-value comparison for edge cases
                    if not all(orig_values == mod_values):
                        return False, f"AI metadata field '{field}' was incorrectly modified. AI should not change tracking fields."
            except Exception as e:
                print(f"⚠️ Warning: Could not validate AI metadata field '{field}': {e}")
                # If comparison fails, assume it's unchanged to avoid false positives
                continue

    # 4. Validate other read-only fields are not modified
    other_readonly_fields = [f for f in READ_ONLY_FIELDS if f not in ai_metadata_fields]
    modified_readonly_fields = []
    
    for field in other_readonly_fields:
        if field in original_df.columns and field in modified_df.columns:
            try:
                # Compare field values with robust handling of dtypes and NaN values
                orig_filled = original_df[field].fillna('').astype(str)
                mod_filled = modified_df[field].fillna('').astype(str)
                
                if not orig_filled.equals(mod_filled):
                    # Double-check with element-wise comparison
                    if not all(orig_filled == mod_filled):
                        modified_readonly_fields.append(field)
            except Exception as e:
                print(f"⚠️ Warning: Could not validate read-only field '{field}': {e}")
                # If comparison fails, skip this field to avoid false positives
                continue

    if modified_readonly_fields:
        return False, f"Read-only fields were modified: {', '.join(modified_readonly_fields)}. Only these fields can be modified: {', '.join(EDITABLE_FIELDS)}"

    # 5. Validate Test Case IDs are preserved
    if 'Test Case ID' in original_df.columns and 'Test Case ID' in modified_df.columns:
        try:
            # Convert to string and handle NaN values for robust comparison
            original_test_ids = set(original_df['Test Case ID'].fillna('').astype(str))
            modified_test_ids = set(modified_df['Test Case ID'].fillna('').astype(str))
            
            # Remove empty strings from the sets
            original_test_ids.discard('')
            modified_test_ids.discard('')

            if original_test_ids and original_test_ids != modified_test_ids:
                return False, f"Test Case IDs were modified: {original_test_ids} → {modified_test_ids}"
        except Exception as e:
            print(f"⚠️ Warning: Could not validate Test Case IDs: {e}")
            # If comparison fails, skip this validation to avoid false positives

    # 6. Check for reasonable row count changes
    if len(modified_df) != len(original_df):
        return False, f"Row count changed: {len(original_df)} → {len(modified_df)}. AI should not add or remove rows."

    return True, "Validation passed - only editable fields were modified"

def validate_user_query_for_readonly_fields(user_query: str) -> tuple[bool, str]:
    """
    Pre-validate user query to detect attempts to modify read-only fields.
    Provides early warning before sending to AI.

    Args:
        user_query (str): User's modification request

    Returns:
        tuple: (is_valid, warning_message)
    """
    # Define read-only field patterns to detect in user queries
    readonly_field_patterns = {
        'User Story ID': ['user story id', 'story id', 'user story', 'jira id', 'jira story'],
        'Test Case ID': ['test case id', 'tc id', 'test id', 'case id'],
        'Project': ['project name', 'project'],
        'Timestamp': ['timestamp', 'date created', 'creation date'],
        'Test Type': ['test type', 'type of test'],
        'Defect ID': ['defect id', 'bug id', 'defect number'],
        'Actual Result': ['actual result', 'actual outcome']
    }

    # Define action patterns that suggest modification attempts
    modification_patterns = [
        'replace', 'change', 'update', 'modify', 'set', 'assign',
        'rename', 'alter', 'switch', 'convert', 'transform'
    ]

    user_query_lower = user_query.lower()
    detected_issues = []

    # Check for modification attempts on read-only fields
    for field_name, field_patterns in readonly_field_patterns.items():
        for field_pattern in field_patterns:
            if field_pattern in user_query_lower:
                # Check if there's a modification action nearby
                for action in modification_patterns:
                    if action in user_query_lower:
                        # Check proximity (within 20 characters)
                        field_pos = user_query_lower.find(field_pattern)
                        action_pos = user_query_lower.find(action)
                        if abs(field_pos - action_pos) <= 20:
                            detected_issues.append(field_name)
                            break
                break

    if detected_issues:
        editable_fields = [
            'Test Case Objective', 'Prerequisite', 'Test Steps',
            'Expected Result', 'Test Status', 'Priority', 'Feature'
        ]

        warning_msg = f"""
⚠️ **Read-Only Field Protection Warning**

Your request appears to attempt modifying these read-only fields: **{', '.join(detected_issues)}**

**Read-only fields cannot be modified** to maintain data integrity.

**You can only modify these fields:**
• Test Case Objective
• Prerequisite
• Test Steps
• Expected Result
• Test Status
• Priority
• Feature

Please rephrase your request to focus on modifying only the editable fields listed above.
        """.strip()

        return False, warning_msg

    return True, ""

def render_ai_modification_section(filtered_data: pd.DataFrame) -> pd.DataFrame:
    """
    Render the AI-powered test case modification interface for the unified interface.

    Args:
        filtered_data (pd.DataFrame): Current filtered test case data

    Returns:
        pd.DataFrame: Modified test cases dataframe (same as input if no changes)
    """
    if filtered_data.empty:
        return filtered_data

    # Import required functions
    from helpers.ai.llm_providers import modify_test_cases_with_ai

    # Get AI settings from session state (same pattern as test_analysis.py)
    selected_model = st.session_state.get("selected_model_persistent", "gemini-2.0-flash")
    google_api_key = st.session_state.get("google_api_key", "")
    ai_provider = st.session_state.get("ai_provider_persistent", "Cloud")

    # Fallback: Load API key from config if not in session state
    if not google_api_key:
        try:
            import os
            import json
            config_path = os.path.join('..', 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    google_api_key = config.get('google_api_key', '')
                    # Store in session state for future use
                    st.session_state["google_api_key"] = google_api_key
                    print(f"🔧 DEBUG: Loaded API key from config fallback (length: {len(google_api_key)})")
            else:
                print(f"🔧 DEBUG: Config file not found at {config_path}")
        except Exception as e:
            st.error(f"Error loading API key from config: {e}")
            print(f"🔧 DEBUG: Error loading API key: {e}")
            google_api_key = ""
    else:
        print(f"🔧 DEBUG: API key found in session state (length: {len(google_api_key)})")

    # Get JIRA issue from session state if available
    jira_issue = st.session_state.get("jira_issue")

    st.markdown("---")
    st.subheader("🤖 AI Test Case Modification")

    # Check if AI modifications are pending
    ai_modifications_ready = st.session_state.get("ai_modifications_ready", False)
    pending_modifications = st.session_state.get("pending_ai_modifications")

    # Create an expander for the modification interface (collapsed by default to reduce clutter)
    with st.expander("✨ Modify Test Cases with AI", expanded=False):
        st.markdown("""
        **Use AI to modify your test cases** - Describe what changes you want to make
        and the AI will update your test cases accordingly.

        **Example queries:**
        - "make test steps simple and easy"
        - "Make the test steps more detailed"
        - "Add edge cases for input validation"
        - "Include performance considerations"
        - "Add negative test scenarios"
        """)

        # User query input
        user_query = st.text_area(
            "Describe the changes you want to make:",
            placeholder="e.g., make test steps simple and easy...",
            key="unified_modification_query",
            height=100
        )

        # Modification options
        col1, col2, col3 = st.columns([1, 1, 1])

        with col1:
            # AI provider info (simplified - details available in tooltips)
            st.markdown(f"**AI Provider:** {ai_provider} ({selected_model})")

        with col2:
            # Show current test case count
            test_case_count = count_unique_test_cases(filtered_data)
            st.markdown(f"**Test Cases:** {test_case_count}")

        with col3:
            # Show filtered status
            if len(filtered_data) > 0:
                if ai_modifications_ready:
                    st.markdown(f"**Status:** ✅ AI modifications ready")
                else:
                    st.markdown(f"**Status:** Ready to modify")
            else:
                st.warning("⚠️ No data to modify")

        # Check API key availability for Cloud provider
        api_key_available = True
        if ai_provider == "Cloud" and not google_api_key:
            api_key_available = False
            st.error("🔑 Google API key is required for Cloud AI provider.")

        # Apply AI Modifications button (first stage)
        if st.button(
            "🚀 Apply AI Modifications",
            key="unified_apply_modifications",
            disabled=not user_query.strip() or filtered_data.empty or not api_key_available or ai_modifications_ready,
            help="Generate AI modifications (does not update table yet)" if api_key_available else "API key required for AI modifications",
            use_container_width=True
        ):
            if not user_query.strip():
                st.warning("Please enter a modification request.")
                return filtered_data

            # Pre-validate user query for read-only field modification attempts
            query_valid, warning_message = validate_user_query_for_readonly_fields(user_query)
            if not query_valid:
                st.error("🚫 **Read-Only Field Protection**")
                st.markdown(warning_message)
                return filtered_data

            # Get JIRA description for context
            jira_description = ""
            if jira_issue:
                jira_description = f"Summary: {jira_issue.fields.summary}\n"
                if hasattr(jira_issue.fields, 'description') and jira_issue.fields.description:
                    jira_description += f"Description: {jira_issue.fields.description}"

            # Show progress
            with st.spinner("🤖 AI is analyzing and modifying your test cases..."):
                try:
                    # Validate API key before making the call
                    if ai_provider == "Cloud" and not google_api_key:
                        st.error("🔑 Google API key is missing. Please check your configuration.")
                        return filtered_data

                    # Debug logging before AI call
                    print(f"🔧 DEBUG: About to call AI modification with:")
                    print(f"   - AI Provider: {ai_provider}")
                    print(f"   - Model: {selected_model}")
                    print(f"   - API Key Length: {len(google_api_key) if google_api_key else 0}")

                    # Call AI modification function
                    success, modified_df, error_message = modify_test_cases_with_ai(
                        test_cases_df=filtered_data,
                        jira_description=jira_description,
                        user_query=user_query,
                        model=selected_model,
                        google_api_key=google_api_key
                    )

                    if success:
                        # Validate AI output structure before accepting changes
                        try:
                            is_valid, validation_message = validate_ai_modification_output(filtered_data, modified_df)
                            if not is_valid:
                                st.error(f"❌ AI output validation failed: {validation_message}")
                                st.info("💡 The AI modification was rejected to maintain data integrity. Please try rephrasing your request.")

                                # Provide helpful debugging information
                                with st.expander("🔍 Debug Information", expanded=False):
                                    st.write("**Original Data Shape:**", filtered_data.shape)
                                    st.write("**Modified Data Shape:**", modified_df.shape)
                                    
                                    # Show columns with indexes for easier debugging
                                    st.write("**Original Columns:**")
                                    for i, col in enumerate(filtered_data.columns):
                                        st.write(f"{i}:\"{col}\"")
                                    
                                    st.write("**Modified Columns:**")
                                    for i, col in enumerate(modified_df.columns):
                                        st.write(f"{i}:\"{col}\"")
                                    
                                    # Show AI metadata field values for debugging
                                    ai_fields = ['ai_modified', 'modification_source', 'ai_user_query']
                                    for field in ai_fields:
                                        if field in filtered_data.columns and field in modified_df.columns:
                                            st.write(f"**{field} Comparison:**")
                                            st.write(f"Original: {filtered_data[field].tolist()}")
                                            st.write(f"Modified: {modified_df[field].tolist()}")
                                            st.write(f"Types - Original: {filtered_data[field].dtype}, Modified: {modified_df[field].dtype}")

                                return filtered_data
                        except Exception as validation_error:
                            st.error(f"❌ Validation error: {str(validation_error)}")
                            st.info("💡 There was an issue validating the AI output. Please try again.")

                            # Provide debugging information for validation errors
                            with st.expander("🔍 Validation Error Details", expanded=False):
                                st.write("**Error:**", str(validation_error))
                                st.write("**Original Data Shape:**", filtered_data.shape)
                                st.write("**Modified Data Shape:**", modified_df.shape if 'modified_df' in locals() else "N/A")

                            return filtered_data

                        # Store pending modifications in session state (don't update table yet)
                        st.session_state["pending_ai_modifications"] = modified_df.copy()
                        st.session_state["ai_modifications_ready"] = True
                        st.session_state["original_data_before_ai"] = filtered_data.copy()
                        st.session_state["ai_modification_query"] = user_query
                        st.session_state["ai_modification_metadata"] = {
                            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "ai_provider": ai_provider,
                            "model": selected_model,
                            "original_count": len(filtered_data),
                            "modified_count": len(modified_df)
                        }

                        # Show success message but don't update table
                        st.success("✅ AI modifications are ready!")
                        st.info("💡 Click 'Update Table with AI Output' below to apply the modifications to the table.")

                        # Force rerun to show the new buttons
                        st.rerun()

                    else:
                        # Check if error is related to API key
                        if "API_KEY" in error_message or "api_key" in error_message.lower():
                            st.error("🔑 **API Key Configuration Error**")
                            st.error(f"❌ {error_message}")
                        else:
                            st.error(f"❌ Modification failed: {error_message}")
                        return filtered_data

                except Exception as e:
                    error_str = str(e)
                    if "API_KEY" in error_str or "api_key" in error_str.lower():
                        st.error("🔑 **API Key Configuration Error**")
                        st.error(f"❌ {error_str}")
                    else:
                        st.error(f"❌ Error during modification: {error_str}")
                    return filtered_data

        # Show AI modifications ready section if modifications are pending
        if ai_modifications_ready and pending_modifications is not None:
            st.markdown("---")
            st.markdown("### 🎯 AI Modifications Ready")
            
            # Show modification summary
            original_data = st.session_state.get("original_data_before_ai")
            modification_query = st.session_state.get("ai_modification_query", "")
            modification_metadata = st.session_state.get("ai_modification_metadata", {})
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.info(f"🤖 **Query:** {modification_query}")
                st.write(f"**Original:** {modification_metadata.get('original_count', 0)} test case rows")
                st.write(f"**Modified:** {modification_metadata.get('modified_count', 0)} test case rows")
                st.write(f"**AI Model:** {modification_metadata.get('ai_provider', '')} - {modification_metadata.get('model', '')}")
            
            with col2:
                # Show preview of changes (first few modified test cases)
                if 'Test Case Objective' in pending_modifications.columns:
                    st.write("**Preview of Changes:**")
                    preview_count = min(3, len(pending_modifications))
                    for i in range(preview_count):
                        if i < len(pending_modifications):
                            objective = pending_modifications.iloc[i]['Test Case Objective']
                            if len(objective) > 50:
                                objective = objective[:50] + "..."
                            st.caption(f"• {objective}")

            # Two-button layout for update/undo
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button(
                    "🔄 Update Table with AI Output",
                    key="update_table_with_ai",
                    type="primary",
                    use_container_width=True,
                    help="Apply the AI modifications to the table"
                ):
                    # Apply modifications to the table and save to database
                    try:
                        # Add AI tracking metadata to the pending modifications
                        modified_with_ai_tracking = pending_modifications.copy()
                        
                        # Set AI tracking columns for all rows
                        modified_with_ai_tracking['ai_modified'] = 1
                        modified_with_ai_tracking['modification_source'] = 'ai'
                        modified_with_ai_tracking['ai_model_used'] = modification_metadata.get('model', selected_model)
                        modified_with_ai_tracking['ai_modification_timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        modified_with_ai_tracking['ai_modification_user'] = st.session_state.get("admin_username", "anonymous")
                        modified_with_ai_tracking['ai_user_query'] = modification_query
                        
                        # Store the modified data for immediate display
                        st.session_state["ai_modified_data"] = modified_with_ai_tracking
                        st.session_state["ai_modification_timestamp"] = datetime.now().isoformat()

                        # Save modifications to database with AI tracking
                        from db_helper import update_test_cases_in_database, DATABASE_PATH
                        import json

                        # Get current user
                        current_user = st.session_state.get("admin_username", "anonymous")

                        # Get JIRA ID and test type from current filters or session state
                        current_jira_id = st.session_state.get(get_session_key('filters', 'jira_id'))
                        current_test_type = st.session_state.get(get_session_key('filters', 'dashboard_test_type'))

                        # Prepare AI metadata for database tracking
                        ai_metadata = {
                            'ai_model': modification_metadata.get('model', selected_model),
                            'user_query': modification_query,
                            'original_data': json.dumps(original_data.to_dict(orient='records')),
                            'modified_data': json.dumps(pending_modifications.to_dict(orient='records')),
                            'modification_type': 'content',
                            'processing_time': 0.0,
                            'tokens_used': 0
                        }

                        # If we have the necessary information, save to database
                        if current_jira_id and current_test_type and current_test_type != 'All':
                            try:
                                # Save to database with AI tracking
                                success, message = update_test_cases_in_database(
                                    DATABASE_PATH,
                                    modified_with_ai_tracking,
                                    current_jira_id,
                                    current_test_type,
                                    current_user,
                                    is_edited=True,
                                    ai_metadata=ai_metadata
                                )
                                if success:
                                    st.info(f"✅ {message}")
                                else:
                                    st.warning(f"⚠️ Database update: {message}")
                            except Exception as db_error:
                                st.warning(f"⚠️ Database update failed: {str(db_error)}")

                        # Clear pending modifications
                        st.session_state["ai_modifications_ready"] = False
                        st.session_state["pending_ai_modifications"] = None

                        # Increment the AI update counter to force data editor refresh
                        counter_key = "unified_ai_update_counter"
                        st.session_state[counter_key] = st.session_state.get(counter_key, 0) + 1

                        st.success("✅ Table updated with AI modifications!")
                        
                        # Store modification history
                        if "unified_modification_history" not in st.session_state:
                            st.session_state["unified_modification_history"] = []

                        modification_metadata = st.session_state.get("ai_modification_metadata", {})
                        modification_query = st.session_state.get("ai_modification_query", "")
                        
                        st.session_state["unified_modification_history"].append({
                            "timestamp": modification_metadata.get("timestamp"),
                            "query": modification_query,
                            "ai_provider": modification_metadata.get("ai_provider"),
                            "model": modification_metadata.get("model"),
                            "original_count": modification_metadata.get("original_count"),
                            "modified_count": modification_metadata.get("modified_count"),
                            "status": "applied"
                        })

                        st.rerun()

                    except Exception as db_error:
                        st.error(f"❌ Error updating table: {str(db_error)}")
            
            with col2:
                if st.button(
                    "↩️ Undo All Modifications",
                    key="undo_ai_modifications",
                    use_container_width=True,
                    help="Discard AI modifications and keep original data"
                ):
                    # Clear pending modifications and revert to original
                    st.session_state["ai_modifications_ready"] = False
                    st.session_state["pending_ai_modifications"] = None
                    st.session_state["original_data_before_ai"] = None
                    
                    # Clear any existing AI modified data in display
                    if "ai_modified_data" in st.session_state:
                        del st.session_state["ai_modified_data"]
                    if "ai_modification_timestamp" in st.session_state:
                        del st.session_state["ai_modification_timestamp"]
                    
                    # Increment the AI update counter to force data editor refresh
                    counter_key = "unified_ai_update_counter"
                    st.session_state[counter_key] = st.session_state.get(counter_key, 0) + 1
                    
                    st.info("↩️ AI modifications discarded. Table remains unchanged.")
                    
                    # Store in history as discarded
                    if "unified_modification_history" not in st.session_state:
                        st.session_state["unified_modification_history"] = []

                    modification_metadata = st.session_state.get("ai_modification_metadata", {})
                    modification_query = st.session_state.get("ai_modification_query", "")
                    
                    st.session_state["unified_modification_history"].append({
                        "timestamp": modification_metadata.get("timestamp"),
                        "query": modification_query,
                        "ai_provider": modification_metadata.get("ai_provider"),
                        "model": modification_metadata.get("model"),
                        "original_count": modification_metadata.get("original_count"),
                        "modified_count": modification_metadata.get("modified_count"),
                        "status": "discarded"
                    })
                    
                    st.rerun()

        # Show modification history if available
        if "unified_modification_history" in st.session_state and st.session_state["unified_modification_history"]:
            st.markdown("---")
            st.markdown("**📜 Recent Modifications**")
            history = st.session_state["unified_modification_history"]
            for i, mod in enumerate(reversed(history[-3:])):  # Show last 3 modifications
                status_icon = "✅" if mod.get('status') == 'applied' else "↩️" if mod.get('status') == 'discarded' else "🔄"
                st.markdown(f"**{mod['timestamp']}** ({mod['ai_provider']} - {mod['model']}) {status_icon}")
                st.markdown(f"*{mod['query']}*")
                if mod['original_count'] != mod['modified_count']:
                    st.caption(f"Modified {mod['original_count']} → {mod['modified_count']} rows")
                if i < len(history) - 1:
                    st.markdown("---")

    return filtered_data
__all__ = [
    'render_unified_test_case_interface',
    'should_show_unified_interface',
    'mark_test_cases_generated',
    'reset_test_case_generation_state',
    'initialize_unified_session_state',
    'get_smart_default_value',
    'get_current_generation_run_info',
    'track_test_case_id_before_generation',
    'track_test_case_id_after_generation',
    'get_current_generation_test_case_id_range',
    'clear_all_cache_for_fresh_data',
    'has_valid_test_case_id_range_data',
    'ensure_test_case_id_range_filtering_applied',
    'render_ai_modification_section',
    'render_data_table'  # Export for enhanced functionality
]

def render_export_section(edited_df: pd.DataFrame, filtered_count: int):
    """
    Render the export section with format selection and download functionality.
    Uses chronological sorting (oldest first) for export data.

    Args:
        edited_df (pd.DataFrame): Edited test case data (for display reference)
        filtered_count (int): Number of filtered test cases
    """
    st.markdown("---")
    st.subheader("📤 Export Filtered Data")

    with st.container():
        col1, col2 = st.columns(2)

        with col1:
            # Export format selection - use a different key to avoid conflicts
            current_export_format = st.selectbox(
                "Export Format:",
                options=["Excel", "CSV"],
                key="unified_current_export_format",
                help="Choose the format for exporting filtered test cases"
            )

            # Prepare export button
            if st.button("📤 Prepare Export", key="unified_prepare_export", use_container_width=True):
                if not edited_df.empty:
                    try:
                        # Get export data with chronological sorting (oldest first)
                        from db_helper import get_test_cases_for_export

                        # Get current filters from session state
                        current_filters = get_current_applied_filters()

                        # Fetch data specifically for export with ascending chronological order
                        export_df = get_test_cases_for_export(DATABASE_PATH, current_filters)

                        if export_df.empty:
                            st.warning("⚠️ No data available for export with current filters")
                            return

                        st.info(f"📊 Preparing export with {len(export_df)} records in chronological order (oldest first)")

                        # Generate filename using configuration
                        filename = get_export_filename(current_export_format, include_timestamp=True)

                        # Prepare export data based on format
                        if current_export_format == "Excel":
                            # Use existing Excel export functionality
                            from pathlib import Path
                            temp_dir = Path("Test_cases") / "unified_exports"
                            temp_dir.mkdir(parents=True, exist_ok=True)
                            temp_path = temp_dir / filename

                            # Create Excel file using existing helper with chronologically sorted data
                            create_formatted_excel_from_scenarios(
                                export_df,
                                str(temp_path),
                                is_dataframe=True,
                                save_to_db=False,
                                test_type="filtered",
                                create_excel=True
                            )

                            # Read the created file for download
                            with open(temp_path, "rb") as f:
                                file_content = f.read()

                        else:  # CSV format
                            # Format DataFrame for CSV export using existing helper with chronologically sorted data
                            csv_df = format_csv_for_external_tools(export_df)
                            file_content = csv_df.to_csv(index=False).encode('utf-8')

                        # Store export data in session state with proper validation
                        st.session_state[get_session_key('export', 'ready')] = True
                        st.session_state[get_session_key('export', 'filename')] = filename
                        st.session_state[get_session_key('export', 'format')] = current_export_format
                        st.session_state[get_session_key('export', 'data')] = file_content

                        st.success(f"✅ {current_export_format} file prepared: {filename}")

                    except Exception as e:
                        st.error(f"❌ Error preparing {current_export_format} file: {str(e)}")
                        # Clear export state on error
                        st.session_state[get_session_key('export', 'ready')] = False
                        st.session_state[get_session_key('export', 'data')] = None
                else:
                    st.error("❌ No data available to export")

        with col2:
            # Download button
            export_ready = st.session_state.get(get_session_key('export', 'ready'), False)
            stored_format = st.session_state.get(get_session_key('export', 'format'), 'CSV')
            stored_filename = st.session_state.get(get_session_key('export', 'filename'), 'test_cases.csv')
            stored_data = st.session_state.get(get_session_key('export', 'data'))

            if export_ready and not edited_df.empty and stored_data is not None:
                # Get MIME type and button label using configuration
                mime_type = get_mime_type(stored_format)
                button_label = f"📥 Download {stored_format} File"

                st.download_button(
                    label=button_label,
                    data=stored_data,
                    file_name=stored_filename,
                    mime=mime_type,
                    key="unified_download_export",
                    use_container_width=True,
                    help=f"Download {filtered_count:,} filtered test cases as {stored_format}"
                )
            else:
                # Disabled download button with proper default data
                st.download_button(
                    label="📥 Download File",
                    data=b"",  # Empty bytes, not None
                    file_name="test_cases.csv",
                    mime="text/csv",
                    disabled=True,
                    help="Click 'Prepare Export' first to generate the file",
                    key="unified_download_disabled",
                    use_container_width=True
                )

def should_show_unified_interface() -> bool:
    """
    Determine if the unified interface should be displayed.

    Returns:
        bool: True if interface should be shown, False otherwise
    """
    # Check if test cases have been generated in this session
    test_cases_generated = st.session_state.get("test_cases_generated", False)

    # Check if there's scenario data (indicating successful generation)
    has_scenario_data = (
        "scenario_data" in st.session_state and
        st.session_state.scenario_data and
        st.session_state.scenario_data.get("output_file") is not None
    )

    # Check if user is on the generator page
    on_generator_page = st.session_state.get("current_page") == "generator"

    # Check if user is logged in
    is_logged_in = st.session_state.get("is_admin_logged_in", False)

    return test_cases_generated and has_scenario_data and on_generator_page and is_logged_in

def mark_test_cases_generated():
    """
    Mark that test cases have been successfully generated in this session.
    This should be called after successful test case generation.
    """
    st.session_state["test_cases_generated"] = True
    # Reset the interface initialization flag so smart defaults can be applied
    st.session_state["unified_interface_initialized"] = False

    # Track test case ID after generation for range detection
    track_test_case_id_after_generation()

    # Store smart defaults immediately after generation
    store_smart_defaults_from_generation()
    
    # Mark that filters need to be auto-applied on next render
    st.session_state["auto_apply_filters_on_next_render"] = True

def reset_test_case_generation_state():
    """
    Reset the test case generation state.
    This can be called when starting a new generation or resetting the session.
    """
    st.session_state["test_cases_generated"] = False
    st.session_state["unified_interface_initialized"] = False
    st.session_state["auto_apply_filters_on_next_render"] = False

def load_filter_options() -> Dict:
    """
    Load filter options from database with caching.

    Returns:
        Dict: Available filter options for dropdowns
    """
    # Force refresh of filter options by clearing cache if needed
    # This ensures we always get the latest database values
    try:
        print("🔄 Loading filter options from database...")
        filter_options = get_filter_options(DATABASE_PATH)
        st.session_state.unified_filter_options = filter_options
        print(f"✅ Filter options loaded successfully: {filter_options.get('statistics', {})}")
        return filter_options
    except Exception as e:
        print(f"❌ Error loading filter options: {str(e)}")
        st.error(f"Error loading filter options: {str(e)}")
        # Comprehensive fallback with all expected fields
        fallback_options = {
            'jira_ids': [],
            'test_types': ['All Types','Positive Type','Negative Type','Security Type','Performance Type'],
            'dashboard_test_types': ['positive', 'negative', 'security', 'performance'],
            'priorities': ['High', 'Medium', 'Low'],
            'test_groups': [],
            'features': [],
            'projects': [],
            'users': [],
            'date_range': {'min_date': None, 'max_date': None},
            'statistics': {}
        }
        st.session_state.unified_filter_options = fallback_options
        return fallback_options

def render_unified_test_case_interface(apply_smart_defaults=False):
    """
    Main function to render the complete unified test case interface with manual filter updates.

    Args:
        apply_smart_defaults (bool): If True, apply smart defaults for newly generated test cases

    This function orchestrates all components of the unified interface including
    filters, data table, export functionality, and professional styling.
    Uses manual filter update system where filters only update the table when explicitly applied.
    """
    # Apply custom CSS
    st.markdown(create_unified_interface_css(), unsafe_allow_html=True)
    st.markdown(create_jira_details_css(), unsafe_allow_html=True)

    # Initialize session state with smart defaults if requested
    initialize_unified_session_state(apply_smart_defaults=apply_smart_defaults)

    # Check if we need to auto-apply filters for newly generated test cases
    auto_apply_needed = st.session_state.get("auto_apply_filters_on_next_render", False)
    if auto_apply_needed:
        # Reset filters to smart defaults and apply them automatically
        reset_filters_to_smart_defaults()
        apply_filters_to_database()
        # Clear the flag so this doesn't happen again until next generation
        st.session_state["auto_apply_filters_on_next_render"] = False
        print("🎯 Auto-applied filters for newly generated test cases")

    # Load filter options
    filter_options = load_filter_options()

    # Render unified filter section (UI state only - doesn't trigger database queries)
    with st.container():
        render_unified_filters(filter_options)

    # Render Apply/Reset filter buttons and handle actions
    apply_clicked, reset_clicked = render_filter_action_buttons()

    # Handle filter actions
    if apply_clicked:
        apply_filters_to_database()
        st.rerun()

    if reset_clicked:
        reset_filters_to_smart_defaults()
        st.rerun()

    # Get current applied filters for database query
    current_applied_filters = get_current_applied_filters()

    # Clear export state when applied filters change (to prevent stale export data)
    current_filter_hash = str(sorted(current_applied_filters.items()))
    if 'last_applied_filter_hash' not in st.session_state:
        st.session_state.last_applied_filter_hash = current_filter_hash
    elif st.session_state.last_applied_filter_hash != current_filter_hash:
        clear_export_state()
        st.session_state.last_applied_filter_hash = current_filter_hash

    try:
        # Force fresh database calls every time - no caching
        # Add timestamp to ensure fresh data retrieval
        from datetime import datetime
        refresh_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")

        print(f"🔄 [{refresh_timestamp}] Making fresh database call for test cases table")
        print(f"📊 Applied filters: {current_applied_filters}")

        # Check if we have recent AI-modified data in session state
        ai_modified_data = st.session_state.get("ai_modified_data")
        ai_modification_timestamp = st.session_state.get("ai_modification_timestamp")

        # Use AI-modified data if it's recent (within last 30 seconds) and matches current filters
        use_ai_modified_data = False
        if ai_modified_data is not None and ai_modification_timestamp:
            try:
                mod_time = datetime.fromisoformat(ai_modification_timestamp)
                time_diff = (datetime.now() - mod_time).total_seconds()
                if time_diff < 30:  # Use AI data if modified within last 30 seconds
                    use_ai_modified_data = True
                    print(f"🤖 [{refresh_timestamp}] Using recent AI-modified data from session state")
            except:
                pass

        if use_ai_modified_data:
            # Use AI-modified data for display
            filtered_data = ai_modified_data.copy()
            filtered_unique_count = count_unique_test_cases(filtered_data)
            print(f"✅ [{refresh_timestamp}] Using AI-modified data: {len(filtered_data)} rows ({filtered_unique_count} unique test cases)")

            # Still get total count from database for comparison
            total_data = get_unified_filtered_test_cases(DATABASE_PATH, {})
            total_unique_count = count_unique_test_cases(total_data)
            print(f"📈 [{refresh_timestamp}] Total in database: {len(total_data)} rows ({total_unique_count} unique test cases)")
        else:
            # Fetch filtered data from database using applied filters (always fresh)
            filtered_data = get_unified_filtered_test_cases(DATABASE_PATH, current_applied_filters)
            filtered_unique_count = count_unique_test_cases(filtered_data)
            print(f"✅ [{refresh_timestamp}] Retrieved {len(filtered_data)} filtered rows ({filtered_unique_count} unique test cases)")

            # Get total count for comparison (fetch without filters, always fresh)
            total_data = get_unified_filtered_test_cases(DATABASE_PATH, {})
            total_unique_count = count_unique_test_cases(total_data)
            print(f"📈 [{refresh_timestamp}] Total in database: {len(total_data)} rows ({total_unique_count} unique test cases)")

    except Exception as e:
        st.error(f"Error loading test case data: {str(e)}")
        filtered_data = pd.DataFrame()
        total_data = pd.DataFrame()

    # Render count indicator with refresh button (now uses DataFrames for accurate counting)
    render_count_indicator_with_refresh(filtered_data, total_data, refresh_timestamp)

    # Render data table
    with st.container():
        edited_df = render_data_table(filtered_data)

    # Render save changes button right after the data table
    if not filtered_data.empty:
        with st.container():
            save_successful = render_save_changes_button(edited_df)
            # If save was successful, refresh the interface to show updated data
            if save_successful:
                st.rerun()

    # Render action buttons section for CRUD operations
    action_performed = False
    if not filtered_data.empty or True:  # Always show action buttons (Add Test Case works without existing data)
        try:
            from .unified_action_buttons import render_action_buttons_section
            action_performed = render_action_buttons_section(filtered_data)

            # If an action was performed, refresh the data
            if action_performed:
                st.rerun()
        except ImportError as e:
            st.error(f"❌ Error loading action buttons: {str(e)}")
        except Exception as e:
            st.error(f"❌ Error rendering action buttons: {str(e)}")

    # Render AI modification section
    if not filtered_data.empty:
        with st.container():
            modified_data = render_ai_modification_section(filtered_data)
            # If modifications were applied, use the modified data for export
            if not modified_data.equals(filtered_data):
                export_data = modified_data
            else:
                export_data = edited_df
    else:
        export_data = edited_df

    # Render export section
    if not export_data.empty:
        with st.container():
            # Use unique test case count for export
            filtered_unique_count = count_unique_test_cases(export_data)
            render_export_section(export_data, filtered_unique_count)
__all__ = [
    'render_unified_test_case_interface',
    'should_show_unified_interface',
    'mark_test_cases_generated',
    'reset_test_case_generation_state',
    'initialize_unified_session_state',
    'get_smart_default_value',
    'get_current_generation_run_info',
    'track_test_case_id_before_generation',
    'track_test_case_id_after_generation',
    'get_current_generation_test_case_id_range',
    'clear_all_cache_for_fresh_data',
    'has_valid_test_case_id_range_data',
    'ensure_test_case_id_range_filtering_applied',
    'render_ai_modification_section',
    'render_data_table',  # Export for enhanced functionality
    'render_save_changes_button'  # Export save button function
]
