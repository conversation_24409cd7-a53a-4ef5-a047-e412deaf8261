# Database Schema Update

This document explains the changes made to the database schema to make the `test_runs` table the primary table with the actual JIRA ID.

## Changes Made

1. Modified the `test_runs` table to store the actual JIRA ID (like "TP-10") directly instead of referencing the `jira_issues` table
2. Updated the `test_cases` table to use the actual JIRA ID instead of a foreign key to the `jira_issues` table
3. Updated all functions that interact with these tables to use the actual JIRA ID

## Why These Changes Were Made

The previous schema had the `jira_issues` table as the primary table, with `test_runs` referencing it through a foreign key. This design had several issues:

1. It required joining tables to get the actual JIRA ID, which was inefficient
2. It didn't match the logical flow of the application, where test runs are the primary entity
3. It made it difficult to track which user generated which test cases
4. It caused confusion when viewing the database directly, as the JIRA IDs were stored as numeric IDs instead of their actual values (e.g., "TP-10")

The new schema addresses these issues by making `test_runs` the primary table and storing the actual JIRA ID directly in both the `test_runs` and `test_cases` tables.

## How to Update Your Database

To update your database to the new schema, follow these steps:

1. Make sure you have a backup of your database (the script will create one automatically)
2. Run the `db_schema_update.py` script:

```
python db_schema_update.py
```

This script will:
- Create a backup of your database
- Update the schema to the new format
- Migrate all existing data to the new schema
- Update all functions to use the new schema

If the update fails for any reason, the script will automatically restore your database from the backup.

## After the Update

After updating the database schema, you should notice the following improvements:

1. The `test_runs` table now stores the actual JIRA ID (like "TP-10") directly
2. The `test_cases` table now also stores the actual JIRA ID
3. All functions that interact with these tables now use the actual JIRA ID
4. The database is more efficient and easier to understand

## Troubleshooting

If you encounter any issues after updating the database schema, you can:

1. Restore from the backup created by the update script
2. Contact the development team for assistance

## Technical Details

The update script performs the following operations:

1. Creates backup tables for `test_runs` and `test_cases`
2. Drops the existing `test_runs` table
3. Creates a new `test_runs` table with the actual JIRA ID
4. Migrates data from the backup table to the new table
5. Updates the `test_cases` table to use the actual JIRA ID
6. Creates new indexes for the updated tables
7. Updates the `test_case_executions` table to reference the new tables
