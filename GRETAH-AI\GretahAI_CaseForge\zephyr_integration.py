import requests
import json
import pandas as pd
from datetime import datetime

def upload_test_cases_to_jira(csv_file_path=None, dataframe=None, jira_url=None, jira_username=None, jira_password=None, user_story_ids=None):
    """
    Upload test cases to Jira Zephyr Scale using REST API according to official documentation.
    
    Args:
        csv_file_path: Path to CSV file (optional if dataframe is provided)
        dataframe: Pandas DataFrame containing test cases (optional if csv_file_path is provided)
        jira_url: Jira URL
        jira_username: Jira username
        jira_password: Jira password/token
        user_story_ids: List of user story IDs to link test cases to
        
    Returns:
        Tuple of (success, message)
    """
    try:
        # Load data from CSV if dataframe not provided
        if dataframe is None and csv_file_path:
            dataframe = pd.read_csv(csv_file_path)
        
        if dataframe is None:
            return False, "No test case data provided"
            
        # Authentication for Jira
        auth = (jira_username, jira_password)
        
        # Validate user story IDs
        if not user_story_ids or len(user_story_ids) == 0:
            return False, "No user story IDs provided"
        
        # Step 1: Get the Test issue type ID
        test_type_url = f"{jira_url}/rest/zapi/latest/util/zephyrTestIssueType"
        test_type_response = requests.get(test_type_url, auth=auth)
        
        if test_type_response.status_code != 200:
            return False, f"Failed to get Test issue type ID: {test_type_response.text}"
        
        test_type_data = test_type_response.json()
        if "errorDesc" in test_type_data:
            return False, f"Authentication error: {test_type_data['errorDesc']}"
            
        test_issue_type_id = test_type_data.get("testcaseIssueTypeId")
        if not test_issue_type_id:
            return False, "Failed to get Test issue type ID from response"
            
        print(f"Retrieved Test issue type ID: {test_issue_type_id}")
        
        # Get project ID from the first user story
        user_story_id = user_story_ids[0]
        issue_url = f"{jira_url}/rest/api/2/issue/{user_story_id}"
        issue_response = requests.get(issue_url, auth=auth)
        
        if issue_response.status_code != 200:
            return False, f"Failed to get issue details: {issue_response.text}"
            
        issue_data = issue_response.json()
        project_id = issue_data['fields']['project']['id']
        project_key = issue_data['fields']['project']['key']
        
        print(f"Retrieved project info: ID={project_id}, Key={project_key}")
        
        # Process each unique test case
        successful_uploads = 0
        failed_uploads = 0
        
        # Get unique test case IDs
        test_case_ids = dataframe["Test Case ID"].dropna().unique()
        
        for tc_id in test_case_ids:
            # Get rows for this test case
            tc_rows = dataframe[dataframe["Test Case ID"] == tc_id]
            
            # Get test case details from first row
            first_row = tc_rows.iloc[0]
            test_case_name = first_row["Test Case Objective"]
            description = first_row.get("Prerequisite", "")
            
            # Step 2: Create a new Test item using Jira API
            create_issue_url = f"{jira_url}/rest/api/2/issue"
            issue_payload = {
                "fields": {
                    "project": {
                        "id": project_id
                    },
                    "summary": test_case_name,
                    "description": description,
                    "issuetype": {
                        "id": test_issue_type_id
                    }
                }
            }
            
            create_response = requests.post(
                create_issue_url, 
                json=issue_payload, 
                auth=auth,
                headers={"Content-Type": "application/json"}
            )
            
            if create_response.status_code not in [200, 201]:
                failed_uploads += 1
                print(f"Failed to create test case: {create_response.text}")
                continue
                
            # Get created test case ID and key
            test_case_data = create_response.json()
            created_tc_id = test_case_data.get("id")
            created_tc_key = test_case_data.get("key")
            
            if not created_tc_id:
                failed_uploads += 1
                continue
                
            print(f"Created test case with ID: {created_tc_id}, Key: {created_tc_key}")
            
            # Step 3: Add test steps
            step_count = 0
            step_failures = 0
            
            # Filter rows that have step numbers for this test case
            step_rows = tc_rows[pd.notna(tc_rows.get("Step No"))]
            
            for _, step_row in step_rows.iterrows():
                step_number = step_row.get("Step No")
                test_step = step_row.get("Test Steps", "")
                test_data = step_row.get("Test Data", "")  # Assuming there's a Test Data column
                expected_result = step_row.get("Expected Result", "")
                
                # Create test step using Zephyr API
                step_url = f"{jira_url}/rest/zapi/latest/teststep/{created_tc_id}"
                step_payload = {
                    "step": test_step,
                    "data": test_data,
                    "result": expected_result
                }
                
                step_response = requests.post(
                    step_url,
                    json=step_payload,
                    auth=auth,
                    headers={"Content-Type": "application/json"}
                )
                
                if step_response.status_code == 200:
                    step_count += 1
                    print(f"Added step {step_number} to test case {created_tc_key}")
                else:
                    step_failures += 1
                    print(f"Failed to add step {step_number} to test case {created_tc_key}: {step_response.text}")
            
            print(f"Added {step_count} steps to test case {created_tc_key}. Failed: {step_failures}")
            
            # Link test case to user story
            for story_id in user_story_ids:
                link_url = f"{jira_url}/rest/api/2/issueLink"
                link_payload = {
                    "type": {
                        "name": "Relates"  # This may need to be adjusted based on your Jira configuration
                    },
                    "inwardIssue": {
                        "id": created_tc_id
                    },
                    "outwardIssue": {
                        "id": story_id
                    }
                }
                
                link_response = requests.post(
                    link_url,
                    json=link_payload,
                    auth=auth,
                    headers={"Content-Type": "application/json"}
                )
                
                if link_response.status_code in [200, 201, 204]:
                    print(f"Linked test case {created_tc_key} to user story {story_id}")
                else:
                    print(f"Failed to link test case {created_tc_key} to user story {story_id}: {link_response.text}")
            
            successful_uploads += 1
        
        if successful_uploads > 0:
            return True, f"Successfully uploaded {successful_uploads} test cases to Jira Zephyr Scale. Failed: {failed_uploads}"
        else:
            return False, f"Failed to upload test cases to Jira Zephyr Scale. Please check your credentials and permissions."
            
    except Exception as e:
        return False, f"Error uploading test cases to Jira Zephyr Scale: {str(e)}"


