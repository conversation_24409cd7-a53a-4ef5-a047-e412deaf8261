Test Document for File Upload Feature

This is a sample text document that can be used for testing the file upload functionality in GretahAI ScriptWeaver.

Document Details:
- File Type: Plain Text (.txt)
- Purpose: Testing document upload scenarios
- Content: Sample text for validation
- Size: Small file for quick testing

Test Scenarios:
1. Basic document upload
2. File preview functionality
3. Metadata extraction
4. Security validation
5. Script generation integration

Sample Content:
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Technical Information:
- Character encoding: UTF-8
- Line endings: CRLF (Windows)
- File size: Approximately 1KB
- Created: 2025-01-21
- Purpose: Automated testing

This document contains no malicious content and is safe for testing purposes.

End of test document.
