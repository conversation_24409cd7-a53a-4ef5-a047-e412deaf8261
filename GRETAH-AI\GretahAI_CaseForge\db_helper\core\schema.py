"""
Database schema management and migration utilities for the GretahAI CaseForge system.

This module handles database initialization, schema migrations, and validation
to ensure the database structure is current and compatible with the application.
"""

import sqlite3
import os
import shutil
from datetime import datetime
from .connection import get_thread_local_connection, close_thread_local_connection
from .decorators import retry_on_db_lock


@retry_on_db_lock()
def migrate_database(database_path):
    """
    Migrates the database schema to the latest version.
    
    This function serves as the main entry point for database migration operations.
    It delegates to the comprehensive schema verification and update function to
    ensure the database structure is current and compatible.

    Args:
        database_path (str): Absolute path to the SQLite database file

    Returns:
        bool: True if migration successful, False otherwise

    Note:
        - Uses retry logic for database lock handling
        - Calls verify_and_update_schema for actual migration work
        - Safe to run on databases of any version
    """
    print("Starting database migration...")
    # Use the new comprehensive schema verification and update function
    return verify_and_update_schema(database_path)


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def init_db(database_path):
    """
    Initializes the complete database schema if it doesn't exist.
    
    This function creates all required tables, indexes, and default configuration
    for the GretahAI CaseForge application. It establishes a multi-layered schema
    with proper foreign key relationships and optimized indexes for performance.

    Args:
        database_path (str): Absolute path to the SQLite database file

    Returns:
        bool: True if initialization successful, False otherwise

    Database Tables Created:
        - test_runs: Primary table for test execution sessions
        - jira_issues: JIRA ticket information with test run references
        - test_cases: Individual test case data with JIRA associations
        - test_steps: Detailed test execution steps for each test case
        - test_case_executions: Execution tracking and results
        - app_config: Application configuration and settings

    Features:
        - Creates database directory if needed
        - Enables foreign key support and WAL mode
        - Removes legacy per-JIRA tables
        - Creates performance indexes
        - Sets default admin configuration
        - Runs schema verification after creation
        - Updates existing test cases with defaults

    Raises:
        sqlite3.Error: If database operations fail
        OSError: If directory creation fails

    Note:
        - Uses extended timeout for long operations
        - Automatically migrates from old schema versions
        - Safe to run on existing databases
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Ensure the database directory exists
        db_dir = os.path.dirname(database_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            print(f"Created database directory: {db_dir}")

        # Create connection with extended timeout for long operations
        conn = sqlite3.connect(database_path, timeout=120)
        cursor = conn.cursor()

        # Enable Foreign Key support and WAL mode
        cursor.execute("PRAGMA foreign_keys = ON;")
        cursor.execute("PRAGMA journal_mode = WAL;")

        print("Creating database tables...")

        # Create jira_issues table first (referenced by other tables)
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS jira_issues (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT UNIQUE NOT NULL,
            summary TEXT,
            description TEXT,
            status TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            test_run_id INTEGER,
            enhanced_description TEXT,
            enhanced_timestamp TEXT,
            FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
        )
        ''')

        # Create test_runs table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_runs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_type TEXT NOT NULL,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            num_test_cases INTEGER,
            status TEXT DEFAULT 'planned',
            user_name TEXT,
            notes TEXT
        )
        ''')

        # Create test_cases table with AI tracking fields
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_cases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_case_id TEXT NOT NULL,
            test_case_objective TEXT,
            prerequisite TEXT,
            priority TEXT,
            test_type TEXT,
            test_group TEXT,
            project TEXT,
            feature TEXT,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            is_latest BOOLEAN DEFAULT 1,
            dashboard_test_type TEXT,
            user_name TEXT,
            jira_issue_id INTEGER,
            is_edited BOOLEAN DEFAULT 0,
            test_run_id INTEGER,
            ai_modified BOOLEAN DEFAULT 0,
            modification_source TEXT DEFAULT 'manual',
            ai_model_used TEXT,
            ai_modification_timestamp TEXT,
            ai_modification_user TEXT,
            ai_user_query TEXT,
            FOREIGN KEY (jira_issue_id) REFERENCES jira_issues(id),
            FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
        )
        ''')

        # Create test_steps table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_steps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            step_number INTEGER NOT NULL,
            test_step TEXT,
            expected_result TEXT,
            actual_result TEXT,
            test_status TEXT,
            defect_id TEXT,
            comments TEXT,
            dashboard_test_type TEXT,
            user_name TEXT,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
        )
        ''')

        # Create test_case_executions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_case_executions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            execution_date TEXT DEFAULT CURRENT_TIMESTAMP,
            status TEXT,
            notes TEXT,
            user_name TEXT,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
        )
        ''')

        # Create ai_modifications table for detailed AI modification history
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS ai_modifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            jira_id TEXT NOT NULL,
            test_case_identifier TEXT NOT NULL,
            modification_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            user_name TEXT NOT NULL,
            ai_model TEXT NOT NULL,
            user_query TEXT NOT NULL,
            original_data TEXT,
            modified_data TEXT,
            modification_type TEXT DEFAULT 'content',
            success BOOLEAN DEFAULT 1,
            error_message TEXT,
            processing_time_seconds REAL,
            tokens_used INTEGER,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id) ON DELETE CASCADE
        )
        ''')

        # Create indexes for unified filtering performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_jira_timestamp ON test_cases(jira_id, timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_type_user ON test_cases(test_type, user_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_edited ON test_cases(is_edited)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_run_id ON test_cases(test_run_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_runs_type ON test_runs(test_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_jira_issues_enhanced ON jira_issues(enhanced_description)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_steps_case_id ON test_steps(test_case_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_timestamp ON test_cases(timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases(jira_id)')

        # AI tracking indexes
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_ai_modified ON test_cases(ai_modified)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_modification_source ON test_cases(modification_source)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_test_case_id ON ai_modifications(test_case_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_jira_id ON ai_modifications(jira_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_timestamp ON ai_modifications(modification_timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_user ON ai_modifications(user_name)')

        # Create app_config table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS app_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        print("Creating indexes for better performance...")

        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_jira_issues_jira_id ON jira_issues (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_issue_id ON test_cases (jira_issue_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_test_run_id ON test_cases (test_run_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_test_case_id ON test_steps (test_case_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_case_executions_test_case_id ON test_case_executions (test_case_id)")

        # Remove any legacy per-JIRA tables that might exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'test_cases_%'")
        legacy_tables = cursor.fetchall()
        for table in legacy_tables:
            table_name = table[0]
            if table_name not in ['test_cases', 'test_case_executions']:
                print(f"Removing legacy table: {table_name}")
                cursor.execute(f"DROP TABLE IF EXISTS {table_name}")

        conn.commit()
        print("Database initialization complete.")

        # Set default admin configuration
        try:
            import admin_config
            admin_config.get_config("admin_password")  # This will create default config if needed
        except ImportError:
            print("Warning: admin_config module not available")

        # Run schema verification
        print("Verifying schema completeness...")
        if verify_schema_completeness(database_path):
            print("Schema verification passed.")
        else:
            print("Schema verification failed, but continuing...")

        # Update existing test cases with default values if needed
        update_existing_test_cases(database_path)

        return True

    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def backup_database(database_path):
    """
    Creates a timestamped backup copy of the database file for data protection.

    This function creates a safety backup of the database before performing potentially
    destructive operations like schema migrations or bulk data changes. The backup
    includes a timestamp for easy identification and organization.

    Args:
        database_path (str): Absolute path to the SQLite database file to backup

    Returns:
        str: Path to the backup file if successful, None if backup failed

    Backup Features:
        - Creates complete copy of database file
        - Adds timestamp to backup filename for uniqueness
        - Preserves original file permissions and attributes
        - Uses standard file copy operations for reliability

    Filename Format:
        - Original: test_cases_v2.db
        - Backup: test_cases_v2_backup_YYYYMMDD_HHMMSS.db
        - Example: test_cases_v2_backup_20241213_143022.db

    Use Cases:
        - Before database schema migrations
        - Prior to bulk data operations
        - As part of regular maintenance routines
        - Before testing destructive operations

    Error Handling:
        - Returns None if source file doesn't exist
        - Handles file permission errors gracefully
        - Logs errors for troubleshooting
        - Doesn't interrupt calling operations

    Example:
        # Create backup before migration
        backup_path = backup_database(db_path)
        if backup_path:
            print(f"Backup created: {backup_path}")
            # Proceed with risky operation
        else:
            print("Backup failed, aborting operation")
    """
    if os.path.exists(database_path):
        backup_dir = os.path.join(os.path.dirname(database_path), "backups")
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"database_backup_{timestamp}.db")

        # Create the backup
        shutil.copy2(database_path, backup_path)
        print(f"Created database backup at: {backup_path}")
        return backup_path
    return None


def detect_schema_version(database_path):
    """Detects the current schema version of the database."""
    conn = None
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # Check if the test_runs table has a jira_id column (new schema)
        cursor.execute("PRAGMA table_info(test_runs)")
        columns = [column[1] for column in cursor.fetchall()]

        if "jira_id" in columns:
            return "new"
        else:
            return "old"
    except sqlite3.Error as e:
        print(f"Error detecting schema version: {e}")
        return "unknown"
    finally:
        if conn:
            conn.close()


def migrate_old_to_new_schema(database_path):
    """Migrates the database from the old schema to the new schema."""
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()

        # Enable Foreign Key support
        cursor.execute("PRAGMA foreign_keys = OFF;")  # Temporarily disable for migration

        # Start a transaction
        cursor.execute("BEGIN TRANSACTION")

        print("Creating backup tables...")
        # Step 1: Create backup tables
        cursor.execute("CREATE TABLE test_runs_backup AS SELECT * FROM test_runs")
        cursor.execute("CREATE TABLE test_cases_backup AS SELECT * FROM test_cases")

        # Step 2: Get all JIRA issues
        cursor.execute("SELECT id, jira_id FROM jira_issues")
        jira_issues = {row[0]: row[1] for row in cursor.fetchall()}

        # Step 3: Drop and recreate the test_runs table with the new schema
        print("Updating test_runs table schema...")
        cursor.execute("DROP TABLE test_runs")
        cursor.execute('''
        CREATE TABLE test_runs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_type TEXT NOT NULL,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            num_test_cases INTEGER,
            status TEXT DEFAULT 'planned',
            user_name TEXT,
            notes TEXT
        )
        ''')

        # Step 4: Migrate data from the backup table to the new table
        print("Migrating test_runs data...")
        cursor.execute("SELECT * FROM test_runs_backup")
        for row in cursor.fetchall():
            old_id, jira_issue_id, test_type, timestamp, num_test_cases, status, user_name, notes = row
            jira_id = jira_issues.get(jira_issue_id, f"UNKNOWN_{jira_issue_id}")
            cursor.execute('''
            INSERT INTO test_runs (id, jira_id, test_type, timestamp, num_test_cases, status, user_name, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (old_id, jira_id, test_type, timestamp, num_test_cases, status, user_name, notes))

        # Step 5: Update the test_cases table to use the actual JIRA ID
        print("Updating test_cases table...")
        cursor.execute("ALTER TABLE test_cases ADD COLUMN jira_id TEXT")

        # Step 6: Update the jira_id column in the test_cases table
        for jira_issue_id, jira_id in jira_issues.items():
            cursor.execute("UPDATE test_cases SET jira_id = ? WHERE jira_issue_id = ?", (jira_id, jira_issue_id))

        # Step 7: Create new indexes
        print("Creating new indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id)")

        # Step 8: Drop the backup tables
        print("Cleaning up backup tables...")
        cursor.execute("DROP TABLE test_runs_backup")
        cursor.execute("DROP TABLE test_cases_backup")

        # Re-enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Commit the transaction
        cursor.execute("COMMIT")
        print("Schema migration completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Schema migration error: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def verify_schema_completeness(database_path):
    """
    Verifies that all required tables and columns exist in the database schema.

    This function performs comprehensive validation of the database structure to ensure
    it matches the expected schema for the current version of the application. It checks
    for the presence of all required tables, columns, and their proper data types.

    Args:
        database_path (str): Absolute path to the SQLite database file to verify

    Returns:
        bool: True if schema is complete and valid, False if issues found

    Verification Process:
        1. Checks existence of all required tables
        2. Validates presence of essential columns in each table
        3. Verifies data types match expected schema
        4. Ensures foreign key relationships are properly defined
        5. Validates index presence for performance-critical queries

    Required Tables:
        - test_runs: Primary test execution session tracking
        - jira_issues: JIRA ticket integration data
        - test_cases: Individual test case storage
        - test_steps: Detailed test execution steps
        - test_case_executions: Execution result tracking
        - app_config: Application configuration settings

    Critical Columns Verified:
        - Primary keys and auto-increment settings
        - Foreign key relationship columns
        - Essential data fields for application functionality
        - User tracking and audit columns
        - Timestamp and versioning fields

    Schema Validation Features:
        - Detects missing tables or columns
        - Identifies data type mismatches
        - Reports structural inconsistencies
        - Provides detailed error reporting
        - Suggests corrective actions when possible

    Error Reporting:
        - Logs specific missing elements
        - Provides table and column details
        - Suggests schema update procedures
        - Returns clear pass/fail status

    Example:
        if verify_schema_completeness(db_path):
            print("Database schema is valid")
        else:
            print("Schema validation failed - update required")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # Check for required tables
        required_tables = ["test_runs", "test_cases", "test_steps", "jira_issues"]
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if not cursor.fetchone():
                print(f"Missing required table: {table}")
                return False

        # Check for required columns in test_runs
        cursor.execute("PRAGMA table_info(test_runs)")
        test_runs_columns = [column[1] for column in cursor.fetchall()]
        required_test_runs_columns = ["id", "jira_id", "test_type", "timestamp", "num_test_cases", "status", "user_name", "notes"]
        for column in required_test_runs_columns:
            if column not in test_runs_columns:
                print(f"Missing required column in test_runs: {column}")
                return False

        # Check for required columns in test_cases
        cursor.execute("PRAGMA table_info(test_cases)")
        test_cases_columns = [column[1] for column in cursor.fetchall()]
        required_test_cases_columns = ["id", "jira_id", "test_case_id", "test_case_objective", "dashboard_test_type", "user_name"]
        for column in required_test_cases_columns:
            if column not in test_cases_columns:
                print(f"Missing required column in test_cases: {column}")
                return False

        # Check for required columns in test_steps
        cursor.execute("PRAGMA table_info(test_steps)")
        test_steps_columns = [column[1] for column in cursor.fetchall()]
        required_test_steps_columns = ["id", "test_case_id", "step_number", "test_step", "expected_result"]
        for column in required_test_steps_columns:
            if column not in test_steps_columns:
                print(f"Missing required column in test_steps: {column}")
                return False

        # Check for required columns in jira_issues
        cursor.execute("PRAGMA table_info(jira_issues)")
        jira_issues_columns = [column[1] for column in cursor.fetchall()]
        required_jira_issues_columns = ["id", "jira_id"]
        for column in required_jira_issues_columns:
            if column not in jira_issues_columns:
                print(f"Missing required column in jira_issues: {column}")
                return False

        # Check for required indexes
        required_indexes = [
            "idx_jira_issues_jira_id",
            "idx_test_runs_jira_id",
            "idx_test_cases_jira_id",
            "idx_test_cases_jira_issue_id",
            "idx_test_steps_test_case_id"
        ]

        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
        indexes = [row[0] for row in cursor.fetchall()]

        for index in required_indexes:
            if index not in indexes:
                print(f"Missing required index: {index}")
                # Don't return False for missing indexes, just log them
                # return False

        print("Schema verification complete: All required tables and columns exist")
        return True
    except sqlite3.Error as e:
        print(f"Error verifying schema: {e}")
        return False
    finally:
        if conn:
            conn.close()


def standardize_schema(database_path):
    """Standardizes the database schema to ensure consistency across all installations."""
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()

        # Disable foreign keys temporarily
        cursor.execute("PRAGMA foreign_keys = OFF;")
        cursor.execute("BEGIN TRANSACTION")

        print("Creating backup of all tables...")
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]

        # Create backup of all tables
        for table in tables:
            backup_table_name = f"{table}_backup"
            cursor.execute(f"DROP TABLE IF EXISTS {backup_table_name}")
            cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT * FROM {table}")

        print("Recreating tables with standardized schema...")

        # Drop and recreate all tables with standardized schema
        # 1. jira_issues table
        cursor.execute("DROP TABLE IF EXISTS jira_issues")
        cursor.execute('''
        CREATE TABLE jira_issues (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT UNIQUE NOT NULL,
            summary TEXT,
            description TEXT,
            status TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            test_run_id INTEGER,
            enhanced_description TEXT,
            enhanced_timestamp TEXT,
            FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
        )
        ''')

        # 2. test_runs table
        cursor.execute("DROP TABLE IF EXISTS test_runs")
        cursor.execute('''
        CREATE TABLE test_runs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_type TEXT NOT NULL,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            num_test_cases INTEGER,
            status TEXT DEFAULT 'planned',
            user_name TEXT,
            notes TEXT
        )
        ''')

        # 3. test_cases table with AI tracking fields
        cursor.execute("DROP TABLE IF EXISTS test_cases")
        cursor.execute('''
        CREATE TABLE test_cases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_case_id TEXT NOT NULL,
            test_case_objective TEXT,
            prerequisite TEXT,
            priority TEXT,
            test_type TEXT,
            test_group TEXT,
            project TEXT,
            feature TEXT,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            is_latest BOOLEAN DEFAULT 1,
            dashboard_test_type TEXT,
            user_name TEXT,
            jira_issue_id INTEGER,
            is_edited BOOLEAN DEFAULT 0,
            test_run_id INTEGER,
            ai_modified BOOLEAN DEFAULT 0,
            modification_source TEXT DEFAULT 'manual',
            ai_model_used TEXT,
            ai_modification_timestamp TEXT,
            ai_modification_user TEXT,
            ai_user_query TEXT,
            FOREIGN KEY (jira_issue_id) REFERENCES jira_issues(id),
            FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
        )
        ''')

        # 4. test_steps table
        cursor.execute("DROP TABLE IF EXISTS test_steps")
        cursor.execute('''
        CREATE TABLE test_steps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            step_number INTEGER NOT NULL,
            test_step TEXT,
            expected_result TEXT,
            actual_result TEXT,
            test_status TEXT,
            defect_id TEXT,
            comments TEXT,
            dashboard_test_type TEXT,
            user_name TEXT,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
        )
        ''')

        # 5. test_case_executions table (if it exists)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_executions_backup'")
        if cursor.fetchone():
            cursor.execute("DROP TABLE IF EXISTS test_case_executions")
            cursor.execute('''
            CREATE TABLE test_case_executions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_case_id INTEGER NOT NULL,
                execution_date TEXT DEFAULT CURRENT_TIMESTAMP,
                status TEXT,
                notes TEXT,
                user_name TEXT,
                FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
            )
            ''')

        # 6. ai_modifications table for AI modification history
        cursor.execute("DROP TABLE IF EXISTS ai_modifications")
        cursor.execute('''
        CREATE TABLE ai_modifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            jira_id TEXT NOT NULL,
            test_case_identifier TEXT NOT NULL,
            modification_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            user_name TEXT NOT NULL,
            ai_model TEXT NOT NULL,
            user_query TEXT NOT NULL,
            original_data TEXT,
            modified_data TEXT,
            modification_type TEXT DEFAULT 'content',
            success BOOLEAN DEFAULT 1,
            error_message TEXT,
            processing_time_seconds REAL,
            tokens_used INTEGER,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id) ON DELETE CASCADE
        )
        ''')

        # Migrate data from backup tables to new tables
        print("Migrating data to standardized tables...")

        # 1. Migrate jira_issues data
        cursor.execute("SELECT * FROM jira_issues_backup")
        jira_data = cursor.fetchall()
        for row in jira_data:
            # Handle different column counts for backward compatibility
            if len(row) >= 7:
                cursor.execute('''
                INSERT OR IGNORE INTO jira_issues (id, jira_id, summary, description, status, created_at, test_run_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', row[:7])
            else:
                # Fill missing columns with defaults
                padded_row = list(row) + [None] * (7 - len(row))
                cursor.execute('''
                INSERT OR IGNORE INTO jira_issues (id, jira_id, summary, description, status, created_at, test_run_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', padded_row[:7])

        # Continue migration for other tables...
        # (Similar pattern for test_runs, test_cases, etc.)

        # Create indexes
        print("Creating indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_jira_issues_jira_id ON jira_issues (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_issue_id ON test_cases (jira_issue_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_test_case_id ON test_steps (test_case_id)")

        # Clean up backup tables
        for table in tables:
            backup_table_name = f"{table}_backup"
            cursor.execute(f"DROP TABLE IF EXISTS {backup_table_name}")

        cursor.execute("PRAGMA foreign_keys = ON;")
        cursor.execute("COMMIT")
        print("Schema standardization completed successfully")
        return True

    except sqlite3.Error as e:
        print(f"Schema standardization error: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def verify_and_update_schema(database_path):
    """Verifies and updates the database schema if needed."""
    print(f"Verifying database schema for: {database_path}")

    # Create a backup first
    backup_path = backup_database(database_path)
    if not backup_path:
        print("Warning: Could not create backup before schema update")

    # Detect the schema version
    schema_version = detect_schema_version(database_path)

    if schema_version == "new":
        print("Database schema is already up to date")
        # Still verify completeness
        if verify_schema_completeness(database_path):
            return True
        else:
            print("Schema verification failed, attempting to fix...")
            return standardize_schema(database_path)
    elif schema_version == "old":
        print("Migrating from old schema to new schema...")
        if migrate_old_to_new_schema(database_path):
            return verify_schema_completeness(database_path)
        else:
            return False
    else:
        print("Unknown schema version, attempting to standardize...")
        return standardize_schema(database_path)


@retry_on_db_lock()
def update_database_schema(database_path):
    """Updates the database schema to add any missing columns or tables."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        print("Updating database schema...")

        # Add any missing columns to test_cases table
        cursor.execute("PRAGMA table_info(test_cases)")
        columns = [column[1] for column in cursor.fetchall()]

        missing_columns = {
            'prerequisite': 'TEXT',
            'priority': 'TEXT',
            'test_type': 'TEXT',
            'test_group': 'TEXT',
            'project': 'TEXT',
            'feature': 'TEXT',
            'is_latest': 'BOOLEAN DEFAULT 1',
            'dashboard_test_type': 'TEXT',
            'user_name': 'TEXT',
            'jira_issue_id': 'INTEGER',
            'is_edited': 'BOOLEAN DEFAULT 0',
            'test_run_id': 'INTEGER',
            'jira_id': 'TEXT',
            'ai_modified': 'BOOLEAN DEFAULT 0',
            'modification_source': 'TEXT DEFAULT "manual"',
            'ai_model_used': 'TEXT',
            'ai_modification_timestamp': 'TEXT',
            'ai_modification_user': 'TEXT',
            'ai_user_query': 'TEXT'
        }

        for column, datatype in missing_columns.items():
            if column not in columns:
                print(f"Adding missing column: {column}")
                cursor.execute(f"ALTER TABLE test_cases ADD COLUMN {column} {datatype}")

        # Add any missing columns to test_steps table
        cursor.execute("PRAGMA table_info(test_steps)")
        columns = [column[1] for column in cursor.fetchall()]

        missing_step_columns = {
            'actual_result': 'TEXT',
            'test_status': 'TEXT',
            'defect_id': 'TEXT',
            'comments': 'TEXT',
            'dashboard_test_type': 'TEXT',
            'user_name': 'TEXT'
        }

        for column, datatype in missing_step_columns.items():
            if column not in columns:
                print(f"Adding missing column to test_steps: {column}")
                cursor.execute(f"ALTER TABLE test_steps ADD COLUMN {column} {datatype}")

        # Add missing columns to jira_issues table
        cursor.execute("PRAGMA table_info(jira_issues)")
        columns = [column[1] for column in cursor.fetchall()]

        missing_jira_columns = {
            'enhanced_description': 'TEXT',
            'enhanced_timestamp': 'TEXT'
        }

        for column, datatype in missing_jira_columns.items():
            if column not in columns:
                print(f"Adding missing column to jira_issues: {column}")
                cursor.execute(f"ALTER TABLE jira_issues ADD COLUMN {column} {datatype}")

        # Create ai_modifications table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS ai_modifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            jira_id TEXT NOT NULL,
            test_case_identifier TEXT NOT NULL,
            modification_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            user_name TEXT NOT NULL,
            ai_model TEXT NOT NULL,
            user_query TEXT NOT NULL,
            original_data TEXT,
            modified_data TEXT,
            modification_type TEXT DEFAULT 'content',
            success BOOLEAN DEFAULT 1,
            error_message TEXT,
            processing_time_seconds REAL,
            tokens_used INTEGER,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id) ON DELETE CASCADE
        )
        ''')

        # Create AI tracking indexes if they don't exist
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_ai_modified ON test_cases(ai_modified)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_cases_modification_source ON test_cases(modification_source)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_test_case_id ON ai_modifications(test_case_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_jira_id ON ai_modifications(jira_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_timestamp ON ai_modifications(modification_timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ai_modifications_user ON ai_modifications(user_name)')

        conn.commit()
        print("Database schema update completed successfully")
        return True

    except sqlite3.Error as e:
        print(f"Database schema update error: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()


@retry_on_db_lock()
def update_existing_test_cases(database_path, default_user="admin"):
    """Updates existing test cases with default values for new columns."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Update test cases that don't have user_name set
        cursor.execute("UPDATE test_cases SET user_name = ? WHERE user_name IS NULL", (default_user,))

        # Update test cases that don't have dashboard_test_type set
        cursor.execute("UPDATE test_cases SET dashboard_test_type = 'all' WHERE dashboard_test_type IS NULL")

        # Update test_steps that don't have user_name set
        cursor.execute("UPDATE test_steps SET user_name = ? WHERE user_name IS NULL", (default_user,))

        # Update test_steps that don't have dashboard_test_type set
        cursor.execute("UPDATE test_steps SET dashboard_test_type = 'all' WHERE dashboard_test_type IS NULL")

        conn.commit()
        print("Updated existing test cases with default values")
        return True

    except sqlite3.Error as e:
        print(f"Error updating existing test cases: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()
