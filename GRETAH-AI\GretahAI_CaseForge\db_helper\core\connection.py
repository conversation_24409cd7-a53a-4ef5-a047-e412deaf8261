"""
Database connection management for the GretahAI CaseForge system.

This module handles thread-safe database connections using thread-local storage
and provides connection lifecycle management for SQLite operations.
"""

import sqlite3
import os
import threading
from pathlib import Path

# Define the database path with absolute path
# Use the CaseForge root directory as the base for the database (same as original)
DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "test_cases_v2.db")

# Use thread-local storage for database connections
thread_local = threading.local()


def get_thread_local_connection(database_path, timeout=30):
    """
    Gets or creates a thread-local database connection for safe concurrent access.
    
    This function implements thread-local storage for database connections to ensure
    thread safety in multi-threaded environments. Each thread gets its own connection
    instance with optimized SQLite settings including Write-Ahead Logging (WAL) mode.

    Args:
        database_path (str): Absolute path to the SQLite database file
        timeout (int, optional): Database timeout in seconds. Defaults to 30.

    Returns:
        sqlite3.Connection: Thread-local database connection with optimized settings

    Raises:
        sqlite3.Error: If connection creation fails
        OSError: If database directory creation fails

    Note:
        - Creates database directory if it doesn't exist
        - Enables WAL mode for better concurrency
        - Sets busy timeout for lock handling
        - Disables foreign key constraints for flexibility
    """
    if not hasattr(thread_local, 'connection') or thread_local.connection is None:
        try:
            # Ensure the database directory exists
            db_dir = os.path.dirname(database_path)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
              # Create the connection
            thread_local.connection = sqlite3.connect(database_path, timeout=timeout, check_same_thread=False)
            thread_local.connection.row_factory = sqlite3.Row
            
            # Enable WAL mode for better concurrency
            thread_local.connection.execute("PRAGMA journal_mode=WAL;")
            
            # Set busy timeout for handling locks
            thread_local.connection.execute(f"PRAGMA busy_timeout={timeout * 1000};")
            
            # Disable foreign key constraints (as per original design)
            thread_local.connection.execute("PRAGMA foreign_keys=OFF;")
            
            print(f"Created new database connection for thread: {threading.current_thread().name}")
            
        except sqlite3.Error as e:
            print(f"Error creating database connection: {e}")
            thread_local.connection = None
            raise
            
    return thread_local.connection


def close_thread_local_connection():
    """
    Closes the database connection for the current thread if it exists.
    
    This function safely closes the thread-local database connection and handles
    any pending transactions by committing or rolling back as appropriate.
    It ensures proper cleanup of database resources in multi-threaded environments.

    Raises:
        Exception: Logs any errors during connection closure but doesn't re-raise

    Note:
        - Commits pending transactions before closing
        - Rolls back on transaction errors
        - Always sets connection to None for cleanup
        - Safe to call even if no connection exists
    """
    if hasattr(thread_local, 'connection') and thread_local.connection:
        try:
            # Try to commit any pending transactions
            thread_local.connection.commit()
            print(f"Closing database connection for thread: {threading.current_thread().name}")
        except Exception as e:
            print(f"Error committing before close: {e}")
            try:
                thread_local.connection.rollback()
            except Exception as rollback_error:
                print(f"Error during rollback: {rollback_error}")
        finally:
            thread_local.connection.close()
            thread_local.connection = None


def close_connection():
    """
    Closes the database connection for the current thread to free resources.
    
    This function provides a convenient wrapper around the thread-local connection
    cleanup functionality. It ensures proper cleanup of database resources and
    is safe to call from any thread at any time.

    Features:
        - Thread-safe operation
        - Safe to call multiple times
        - Handles cases where no connection exists
        - Properly commits or rolls back pending transactions
        - Frees thread-local storage resources

    Use Cases:
        - Application shutdown procedures
        - Thread cleanup in multi-threaded environments
        - Resource management in long-running processes
        - Error recovery scenarios
        - Manual connection lifecycle management

    Note:
        This is a convenience wrapper around close_thread_local_connection()
        for easier access and more intuitive naming in application code.

    Example:
        # Clean up before thread termination
        close_connection()
        
        # Resource cleanup in error handling
        try:
            # Database operations
            pass
        except Exception:
            close_connection()
            raise
    """
    close_thread_local_connection()
    print("Database connection closed successfully")
