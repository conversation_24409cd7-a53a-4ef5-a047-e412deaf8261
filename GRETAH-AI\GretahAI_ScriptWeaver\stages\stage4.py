"""
Stage 4: UI Element Detection and Test Case Step Selection

This module handles UI element detection, interactive element selection,
and element matching for test case steps.
Maintains the StateManager pattern and follows the established architectural patterns.

Phase 3c Enhancement: Standardized logging with centralized infrastructure
"""

import os
import logging
import streamlit as st
import time
from datetime import datetime

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
logger = get_stage_logger("stage4")

# Import helper functions from other modules
from core.element_detection import filter_qa_relevant_elements
from core.locator_resolver import resolve_locator_conflicts
from helpers_pure import analyze_step_for_test_data
from state_manager import StateStage
from debug_utils import debug
from ui_components.locator_resolution_display import (
    render_locator_resolution_display,
    render_locator_resolution_summary
)


def stage4_ui_detection_and_matching(state):
    """Phase 4: UI Element Detection and Test Case Step Selection."""
    st.markdown("<h2 class='stage-header'>Phase 4: UI Element Detection</h2>", unsafe_allow_html=True)

    # CRITICAL FIX: File dependency validation
    from ui_components.file_dependency_validator import validate_stage_dependencies
    if not validate_stage_dependencies(state, "Stage 4: UI Element Detection", required_stage_number=2):
        return  # Block access if file dependencies not satisfied

    # Check if we have a stage progression message to display
    if 'stage_progression_message' in st.session_state:
        st.success(st.session_state['stage_progression_message'])
        # Remove the message so it doesn't show up again
        del st.session_state['stage_progression_message']

    # Check if we're coming from Stage 7 (automatic advancement)
    if 'coming_from_stage7' in st.session_state:
        # Get information about the advancement
        if 'force_refresh_after_advance' in st.session_state:
            from_step = st.session_state['force_refresh_after_advance'].get('from_step')
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')

            # Display a compact banner
            st.success(f"✅ Advanced from Step {from_step} to Step {target_step}")

        # Clear the flag so it doesn't show up again
        del st.session_state['coming_from_stage7']

    # Check if we need to force a refresh after automatic advancement
    if 'force_refresh_after_advance' in st.session_state:
        # Get the timestamp to see if this is a recent advancement
        advance_time = st.session_state['force_refresh_after_advance'].get('timestamp')
        from datetime import datetime, timedelta
        now = datetime.now()

        # If the advancement was within the last 5 seconds, force a refresh
        if advance_time and (now - datetime.strptime(advance_time, "%H:%M:%S.%f")) < timedelta(seconds=5):
            # Clear the flag so we don't keep refreshing
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Forcing refresh to ensure UI shows step {target_step}")
            del st.session_state['force_refresh_after_advance']

            # Force a rerun to refresh the UI
            time.sleep(0.5)
            st.rerun()

    # Check if prerequisites are met - JSON-ONLY mode
    try:
        # Load step data from JSON storage (single source of truth)
        step_table_json = state.get_effective_step_table()
        logger.info(f"✅ Loaded {len(step_table_json)} steps from JSON storage")
    except ValueError as e:
        st.error("❌ **Step Data Not Available**")
        st.error(f"Error: {e}")

        with st.expander("🔧 How to Fix This", expanded=True):
            st.markdown("""
            **This error occurs because no step data was found in persistent storage.**

            **To resolve this issue:**
            1. 📋 Go back to **Stage 3** (Test Case Analysis)
            2. 🔄 Select your test case and click **"Convert Test Case"**
            3. ✅ Wait for the conversion to complete
            4. 🔄 Return to this stage

            **Why this happens:**
            - The test case hasn't been converted to automation format yet
            - The JSON step data file is missing or corrupted
            - You may have selected a different test case
            """)

            if st.button("🔄 Go to Stage 3", key="goto_stage3_btn"):
                state.advance_to(StateStage.STAGE3_CONVERT, "User requested to fix missing step data")
                st.rerun()
        return

    # Step selection section in a collapsible section
    with st.expander("Step Selection", expanded=True):
        # Add a reset button to start over with step 1
        reset_cols = st.columns([3, 1])
        with reset_cols[0]:
            if st.button("Reset to Step 1", key="reset_step_btn", help="Reset to the first step of the test case"):
                # Check if we have progress to confirm reset
                has_progress = (hasattr(state, 'current_step_index') and state.current_step_index > 0) or state.all_steps_done

                if has_progress:
                    st.warning("⚠️ Resetting will clear all progress.")
                    confirm_reset = st.button("Confirm Reset", key="confirm_reset_step")

                    if not confirm_reset:
                        st.info("Reset cancelled.")
                        return

                # Use the state manager's update method to reset step progress
                state.update_step_progress(
                    current_step_index=0,
                    all_steps_done=False,
                    step_ready_for_script=False,
                    script_just_generated=False
                )

                # Reset step-specific state
                state.reset_step_state(confirm=True, reason="User requested reset to Step 1")

                st.success("✅ Reset complete.")
                st.rerun()

    # Get total steps if not already set (use effective step count for hybrid editing)
    if state.total_steps == 0:
        if hasattr(state, 'get_effective_total_steps'):
            state.total_steps = state.get_effective_total_steps()
        elif state.step_table_json:
            state.total_steps = len(state.step_table_json)

    # Show completion message if all steps are processed
    if state.all_steps_done:
        st.success("✅ All test case steps have been processed!")
        return

    # WORKFLOW FIX: Direct step progression removed to prevent execution failures
    # Users must follow proper workflow: Stage 4 → Stage 6 → Stage 7 → auto-advance to next step
    # This ensures all script dependencies are properly generated before step advancement.

    # Show workflow guidance when step is ready for script generation
    if state.step_ready_for_script:
        st.markdown("---")
        st.info("🔄 **Next Steps in Workflow**")

        with st.expander("📋 Proper Workflow Sequence", expanded=True):
            st.markdown("""
            **To proceed to the next test step, follow this sequence:**

            1. **Stage 6** - Generate test script for current step
            2. **Stage 7** - Execute and validate the script
            3. **Return to Stage 4** - The next step will be automatically loaded

            **Why this workflow is required:**
            - Each step's script must be generated before advancing
            - Script merging requires all previous step scripts to exist
            - This prevents execution failures in later stages

            **Current Status:** ✅ Element detection completed for this step
            """)

            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 Go to Stage 6", key="goto_stage6_workflow", use_container_width=True, type="primary"):
                    state.advance_to(StateStage.STAGE6_GENERATE, "User proceeding to Stage 6 for script generation")
                    st.rerun()

            with col2:
                if st.button("📖 View Workflow Guide", key="view_workflow_guide", use_container_width=True):
                    st.info("""
                    **GRETAH ScriptWeaver Workflow:**

                    **Stage 4:** UI Element Detection
                    **Stage 5:** Test Data Configuration
                    **Stage 6:** Script Generation ← **Go here next**
                    **Stage 7:** Script Execution & Validation

                    After Stage 7 execution, the system automatically advances to the next step.
                    """)
        return

    # Enhanced logging for step table data analysis
    debug("Stage 4: Using JSON-only step data",
          stage="stage4",
          operation="step_data_analysis",
          context={
              'step_count': len(step_table_json),
              'data_source': 'json_storage'
          })

    if step_table_json:
        first_step = step_table_json[0]
        debug("Stage 4: First step analysis",
              stage="stage4",
              operation="step_data_analysis",
              context={
                  'step_no': first_step.get('step_no'),
                  'action': first_step.get('action'),
                  'is_manual': first_step.get('_is_manual', False)
              })

        if len(step_table_json) > 1:
            second_step = step_table_json[1]
            debug("Stage 4: Second step analysis",
                  stage="stage4",
                  operation="step_data_analysis",
                  context={
                      'step_no': second_step.get('step_no'),
                      'action': second_step.get('action'),
                      'is_manual': second_step.get('_is_manual', False)
                  })

    if not isinstance(step_table_json, list):
        st.error("❌ Step table data is not in the correct format")
        st.error("Please re-convert the test case in Stage 3")
        return

    # Create step selection dropdown from the step table
    step_options = []
    for step in step_table_json:
        if isinstance(step, dict):
            step_no = step.get('step_no', 'N/A')
            action = step.get('action', 'N/A')
            # Add visual indicator for manual steps
            step_indicator = "✏️" if step.get('_is_manual', False) else "🤖"
            step_options.append(f"{step_indicator} Step {step_no} - {action}")

    if not step_options:
        st.warning("No steps found in the step table")
        return

    # Initialize to the first step if needed
    if state.current_step_index < 0 or state.current_step_index >= len(step_options):
        old_index = state.current_step_index
        state.current_step_index = 0
        debug("Initializing current_step_index to 0 (was out of bounds)",
              stage="stage4",
              operation="step_index_initialization",
              context={
                  'old_index': old_index,
                  'new_index': 0,
                  'total_steps': len(step_options)
              })

    # Enhanced logging for current step index
    debug("Current step index status",
          stage="stage4",
          operation="step_index_tracking",
          context={
              'current_index': state.current_step_index,
              'total_steps': len(step_options)
          })

    # Get the current step option
    try:
        # Check if we're coming from Stage 7 with a specific step
        if 'coming_from_stage7' in st.session_state and 'force_refresh_after_advance' in st.session_state:
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Coming from Stage 7, looking for step {target_step}")

            # Find the step option that matches the target step
            matching_options = [opt for opt in step_options if f"Step {target_step}" in opt]
            if matching_options:
                # Find the index of the matching option
                target_index = step_options.index(matching_options[0])

                # If the current index doesn't match the target, update it
                if state.current_step_index != target_index:
                    logger.info(f"Updating current_step_index from {state.current_step_index} to {target_index} to match target step {target_step}")
                    state.current_step_index = target_index

        # Get the current step option based on the (possibly updated) index
        current_step_option = step_options[state.current_step_index]
        logger.info(f"Selected step option: {current_step_option}")
    except IndexError:
        # Handle index error gracefully
        logger.error(f"Index error: current_step_index={state.current_step_index}, len(step_options)={len(step_options)}")
        st.error(f"Error: Step index {state.current_step_index} is out of range (0-{len(step_options)-1})")
        # Reset to a valid index
        state.current_step_index = 0
        current_step_option = step_options[0]
        logger.info(f"Reset to step option: {current_step_option}")

    # Create step selection UI in a collapsible section
    with st.expander("Step Navigation", expanded=True):
        # Create columns for step selection and navigation
        step_col1, step_col2 = st.columns([3, 1])

        with step_col1:
            # Allow direct selection of steps with a selectbox
            selected_index = step_options.index(current_step_option)
            selected_step_option = st.selectbox(
                "Select Test Case Step",
                step_options,
                index=selected_index,
                help="Select a specific test case step to process"
            )

            # If user selected a different step, update the current step index
            if selected_step_option != current_step_option:
                new_index = step_options.index(selected_step_option)

                # Show a warning about changing steps
                st.warning("⚠️ Switching steps will reset current progress.")

                # Add a confirmation button
                if st.button("Confirm Step Change", key="confirm_step_change"):
                    # Check if the current step has any progress
                    has_step_progress = (
                        (hasattr(state, 'step_elements') and state.step_elements) or
                        (hasattr(state, 'step_matches') and state.step_matches) or
                        (hasattr(state, 'test_data') and state.test_data) or
                        (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or
                        (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or
                        (hasattr(state, 'script_just_generated') and state.script_just_generated)
                    )

                    # Log the step change with progress information
                    logger.info(f"Changing step from {state.current_step_index} to {new_index} (has_progress={has_step_progress})")

                    # Use the state manager's update method to update step index
                    state.update_step_progress(current_step_index=new_index)

                    # Reset step-specific state
                    state.reset_step_state(confirm=True, reason=f"User changed from step {state.current_step_index + 1} to step {new_index + 1}")

                    st.success(f"✅ Changed to Step {new_index + 1}")
                    st.rerun()

                # If not confirmed, revert to the current step
                selected_step_option = current_step_option

        with step_col2:
            # Add step completion tracking
            if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
                completed_count = len(state.completed_steps)
                st.metric("Steps Completed", f"{completed_count} of {state.total_steps}")
            else:
                # Initialize completed_steps if not present
                state.completed_steps = []
                st.metric("Steps Completed", f"0 of {state.total_steps}")

    # Set the selected step option for the rest of the code to use
    # This will be the current step unless the user confirmed a change

    if selected_step_option != "Select a step...":
        # Extract step number more robustly to handle hybrid editing formats
        step_no_raw = selected_step_option.split(" - ")[0]
        # Remove emoji indicators and "Step " prefix
        step_no = step_no_raw.replace("✏️", "").replace("🤖", "").replace("Step ", "").strip()
        logger.info(f"Processing step_no: {step_no} from selected_step_option: {selected_step_option}")

        # Find the selected step in the step table (from effective step table)
        logger.info(f"=== Stage 4: Finding step table entry for step {step_no} ===")
        logger.info(f"Stage 4: Searching in step_table_json with {len(step_table_json)} steps")

        selected_step_table_entry = next(
            (step for step in step_table_json if str(step.get('step_no')) == step_no),
            None
        )

        if selected_step_table_entry:
            logger.info(f"Stage 4: ✓ Found step table entry for step {step_no}")
            logger.info(f"  - action: '{selected_step_table_entry.get('action')}'")
            logger.info(f"  - locator: '{selected_step_table_entry.get('locator')}'")
            logger.info(f"  - expected_result: '{selected_step_table_entry.get('expected_result')}'")
            logger.info(f"  - is_manual: {selected_step_table_entry.get('_is_manual', False)}")
            logger.info(f"  - is_ai_generated: {selected_step_table_entry.get('_is_ai_generated', False)}")
        else:
            logger.error(f"Stage 4: ✗ Could not find step table entry for step {step_no}")
            logger.error("Stage 4: Available steps in step_table_json:")
            for i, step in enumerate(step_table_json):
                logger.error(f"  [{i}] step_no: '{step.get('step_no')}' - action: '{step.get('action')}' - is_manual: {step.get('_is_manual', False)}")

        # For hybrid editing, we need to handle the case where the step might be manually added
        # and not exist in the original test case
        original_steps = state.selected_test_case.get('Steps', [])
        selected_original_step = next(
            (step for step in original_steps if str(step.get('Step No')) == step_no),
            None
        )

        # If no original step found but we have a step table entry, create a synthetic original step
        # This handles manually added steps in hybrid editing
        # Use step table entry as authoritative source to avoid action mismatches
        if not selected_original_step and selected_step_table_entry:
            logger.info(f"No original step found for step {step_no}, creating synthetic step from step table entry")
            selected_original_step = {
                'Step No': step_no,
                'Test Steps': selected_step_table_entry.get('action', 'Manual step'),  # Use step table action as authoritative
                'Expected Result': selected_step_table_entry.get('expected_result', 'Step completed successfully'),
                '_is_synthetic': True,  # Mark as synthetic for debugging
                '_source': 'step_table_authoritative'  # Indicate step table is the source of truth
            }
            logger.info(f"Created synthetic original step for step {step_no}: {selected_original_step.get('Test Steps')}")
        elif selected_original_step:
            logger.info(f"Found original step for step {step_no}: {selected_original_step.get('Test Steps')}")
        else:
            logger.error(f"Could not find or create original step for step {step_no}")
            # Log all available original steps for debugging
            for step in original_steps:
                logger.info(f"Available original step: {step.get('Step No')} - {step.get('Test Steps')}")

        if selected_step_table_entry and selected_original_step:
            # Store both versions in state manager
            logger.info(f"=== Stage 4: Storing step data in state manager ===")
            logger.info(f"Stage 4: About to store data for step {step_no}")

            # Store the step table entry (automation-ready format)
            old_step_table_entry = getattr(state, 'selected_step_table_entry', None)
            state.selected_step_table_entry = selected_step_table_entry
            logger.info(f"Stage 4: ✓ Stored selected_step_table_entry")
            logger.info(f"  - action: '{selected_step_table_entry.get('action')}'")
            logger.info(f"  - locator: '{selected_step_table_entry.get('locator')}'")
            logger.info(f"  - expected_result: '{selected_step_table_entry.get('expected_result')}'")
            logger.info(f"  - is_manual: {selected_step_table_entry.get('_is_manual', False)}")
            logger.info(f"  - is_ai_generated: {selected_step_table_entry.get('_is_ai_generated', False)}")

            # Store the original step (original format) - synchronized with step table entry
            old_step = getattr(state, 'selected_step', None)

            # Ensure selected_step is synchronized with step_table_entry to eliminate action mismatches
            # Step table entry is the single source of truth for script generation
            if selected_original_step.get('_source') != 'step_table_authoritative':
                # Synchronize original step with step table entry to maintain consistency
                selected_original_step['Test Steps'] = selected_step_table_entry.get('action', selected_original_step.get('Test Steps', ''))
                selected_original_step['Expected Result'] = selected_step_table_entry.get('expected_result', selected_original_step.get('Expected Result', ''))
                logger.info(f"Stage 4: Synchronized selected_step with step_table_entry to ensure consistency")

            state.selected_step = selected_original_step
            logger.info(f"Stage 4: ✓ Stored selected_step (synchronized with step table)")
            logger.info(f"  - Test Steps: '{selected_original_step.get('Test Steps')}'")
            logger.info(f"  - Expected Result: '{selected_original_step.get('Expected Result')}'")
            logger.info(f"  - Is synthetic: {selected_original_step.get('_is_synthetic', False)}")
            logger.info(f"  - Source: {selected_original_step.get('_source', 'original')}")

            # Data consistency verification (should always match now due to synchronization)
            step_table_action = selected_step_table_entry.get('action', '')
            original_action = selected_original_step.get('Test Steps', '')
            actions_match = step_table_action.lower() == original_action.lower()

            logger.info(f"Stage 4: Data consistency verification:")
            logger.info(f"  - Step table action: '{step_table_action}'")
            logger.info(f"  - Synchronized action: '{original_action}'")
            logger.info(f"  - Actions match: {actions_match}")
            logger.info(f"  - Step table entry is authoritative source for script generation")

            logger.info(f"=== Stage 4: Step data storage completed ===")

            # Validate step data consistency for debugging
            if hasattr(state, 'validate_step_data_consistency'):
                validation_results = state.validate_step_data_consistency("Stage 4")
                if not validation_results["data_sources_consistent"]:
                    logger.error(f"Stage 4: Data consistency issues detected!")
                    for issue in validation_results["issues"]:
                        logger.error(f"  - {issue}")

            # CRITICAL FIX: Restore step context from JSON storage if not already loaded
            try:
                if hasattr(state, 'restore_step_context_from_json'):
                    # Only restore if we don't have step context or completed steps
                    needs_restoration = (
                        not hasattr(state, 'step_context') or not state.step_context or
                        not hasattr(state, 'completed_steps') or not state.completed_steps
                    )

                    if needs_restoration:
                        context_restored = state.restore_step_context_from_json()
                        if context_restored:
                            logger.info("Successfully restored step context from JSON storage")
                        else:
                            logger.info("No step context found in JSON storage to restore")
                    else:
                        logger.info("Step context already available, skipping restoration")
                else:
                    logger.warning("restore_step_context_from_json method not available on state manager")
            except Exception as e:
                logger.error(f"Error restoring step context from JSON storage: {e}")

            # Show context from previous steps if available
            _display_previous_step_context(state, selected_original_step)

            # Display step details in a collapsible section
            with st.expander("Step Details", expanded=True):
                # Create tabs for different views of the step
                step_tab1, step_tab2 = st.tabs(["Automation-Ready Format", "Original Format"])

                with step_tab1:
                    # Display the automation-ready step details
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown(f"**Step Number:** {selected_step_table_entry.get('step_no')}")
                        st.markdown(f"**Action:** {selected_step_table_entry.get('action')}")
                        st.markdown(f"**Locator Strategy:** {selected_step_table_entry.get('locator_strategy')}")
                        st.markdown(f"**Locator:** {selected_step_table_entry.get('locator')}")
                    with col2:
                        st.markdown(f"**Test Data Parameter:** {selected_step_table_entry.get('test_data_param')}")
                        st.markdown(f"**Expected Result:** {selected_step_table_entry.get('expected_result')}")
                        st.markdown(f"**Assertion Type:** {selected_step_table_entry.get('assertion_type')}")
                        st.markdown(f"**Timeout:** {selected_step_table_entry.get('timeout')} seconds")

                with step_tab2:
                    # Display the original step details
                    st.markdown(f"**Step Number:** {selected_original_step.get('Step No')}")
                    st.markdown(f"**Test Steps:** {selected_original_step.get('Test Steps')}")
                    st.markdown(f"**Expected Result:** {selected_original_step.get('Expected Result')}")

            # Check if this step should trigger automatic stage progression
            progression_triggered = _check_automatic_stage_progression(state, selected_step_table_entry, selected_original_step)

            # If automatic progression was triggered, return early to avoid rendering UI elements
            if progression_triggered:
                logger.info(f"Automatic stage progression triggered for step {selected_original_step.get('Step No')} - stopping UI rendering")
                return

            # Stage 4b: UI Element Detection
            _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step)

            # Stage 4c: Element Matching
            _handle_element_matching(state, selected_step_table_entry, selected_original_step)


def _check_automatic_stage_progression(state, selected_step_table_entry, selected_original_step):
    """
    Check if the selected step should trigger automatic stage progression.

    This function analyzes the step to determine if it can be processed automatically
    without user interaction, and if so, advances to the appropriate next stage.

    Args:
        state (StateManager): The application state manager instance
        selected_step_table_entry (dict): The automation-ready step table entry
        selected_original_step (dict): The original test case step
    """
    try:
        # Check if this is a navigation step that doesn't require UI elements
        locator_strategy = selected_step_table_entry.get('locator_strategy', '')
        is_navigation_step = locator_strategy in ["", "none", "n/a", "url"]

        # Check if we already have element matches for this step (from previous processing)
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))
        has_existing_matches = (
            hasattr(state, 'element_matches') and
            state.element_matches and
            test_case_id in state.element_matches and
            step_no in state.element_matches[test_case_id]
        )

        # Check if we have manually selected elements for this step
        has_manual_selection = (
            has_existing_matches and
            any(match.get('manually_selected', False)
                for match in state.element_matches[test_case_id][step_no])
        )

        # Enhanced logging for debugging
        logger.info(f"Automatic progression check for step {step_no}:")
        logger.info(f"  - locator_strategy: '{locator_strategy}'")
        logger.info(f"  - is_navigation_step: {is_navigation_step}")
        logger.info(f"  - test_case_id: '{test_case_id}'")
        logger.info(f"  - has_existing_matches: {has_existing_matches}")
        logger.info(f"  - has_manual_selection: {has_manual_selection}")
        logger.info(f"  - current_stage: {state.current_stage}")

        # Debug element matches structure
        if hasattr(state, 'element_matches') and state.element_matches:
            logger.info(f"  - element_matches keys: {list(state.element_matches.keys())}")
            if test_case_id in state.element_matches:
                logger.info(f"  - steps in element_matches[{test_case_id}]: {list(state.element_matches[test_case_id].keys())}")
        else:
            logger.info(f"  - element_matches: None or empty")

        # If this is a navigation step or we already have matches, process automatically
        if is_navigation_step or has_existing_matches:
            logger.info(f"Step {step_no} qualifies for automatic progression - analyzing test data requirements")

            # Analyze if the step requires test data
            try:
                test_data_analysis = analyze_step_for_test_data(
                    selected_step_table_entry,
                    selected_original_step.get('Test Steps')
                )
                logger.info(f"Test data analysis for step {step_no}: requires_test_data={test_data_analysis.get('requires_test_data', 'unknown')}")
            except Exception as e:
                logger.error(f"Error in test data analysis for step {step_no}: {e}")
                # Fallback to requiring test data to be safe
                test_data_analysis = {
                    "requires_test_data": True,
                    "reason": f"Error in analysis: {e}",
                    "data_types": []
                }

            # Create or update element matches structure
            if not hasattr(state, 'element_matches'):
                state.element_matches = {}

            if test_case_id not in state.element_matches:
                state.element_matches[test_case_id] = {}

            # For navigation steps, create empty matches if they don't exist
            if is_navigation_step and step_no not in state.element_matches[test_case_id]:
                state.element_matches[test_case_id][step_no] = []
                logger.info(f"Created empty element matches for navigation step {step_no}")

            # Update state with analysis results
            state.step_matches = state.element_matches
            state.llm_step_analysis = {
                "requires_ui_element": not is_navigation_step,
                "reason": "Navigation step - no UI elements needed" if is_navigation_step else "UI elements already matched",
                "matches": state.element_matches[test_case_id].get(step_no, []),
                "requires_test_data": test_data_analysis["requires_test_data"],
                "test_data_reason": test_data_analysis["reason"],
                "data_types": test_data_analysis["data_types"]
            }

            # Store test data analysis separately for easier access
            state.test_data_analysis = test_data_analysis

            # Determine next stage based on test data requirements
            if not test_data_analysis["requires_test_data"]:
                # No test data needed - advance to Stage 6
                state.test_data_skipped = True
                state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists

                logger.info(f"Step {step_no} requires no test data - attempting to advance to Stage 6")
                logger.info(f"Current stage before advance: {state.current_stage}")

                if state.current_stage == StateStage.STAGE4_DETECT:
                    advance_success = state.advance_to(StateStage.STAGE6_GENERATE, f"Step {step_no} processed automatically, no test data needed - advancing to Stage 6")
                    logger.info(f"Stage advance to Stage 6 success: {advance_success}")
                    logger.info(f"Current stage after advance: {state.current_stage}")

                    if advance_success:
                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"✅ Step {step_no} processed automatically. Proceeding to Stage 6 (no test data needed)."

                        logger.info(f"Calling st.rerun() for stage transition to Stage 6")
                        # Call st.rerun() to immediately refresh the UI
                        st.rerun()
                        return True
                    else:
                        logger.error(f"Failed to advance to Stage 6 for step {step_no}")
                else:
                    logger.warning(f"Not in Stage 4 - current stage is {state.current_stage}, cannot advance to Stage 6")
            else:
                # Test data needed - advance to Stage 5
                logger.info(f"Step {step_no} requires test data - attempting to advance to Stage 5")
                logger.info(f"Current stage before advance: {state.current_stage}")

                if state.current_stage == StateStage.STAGE4_DETECT:
                    advance_success = state.advance_to(StateStage.STAGE5_DATA, f"Step {step_no} processed automatically - advancing to Stage 5 for test data")
                    logger.info(f"Stage advance to Stage 5 success: {advance_success}")
                    logger.info(f"Current stage after advance: {state.current_stage}")

                    if advance_success:
                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"✅ Step {step_no} processed automatically. Proceeding to Stage 5 for test data configuration."

                        logger.info(f"Calling st.rerun() for stage transition to Stage 5")
                        # Call st.rerun() to immediately refresh the UI
                        st.rerun()
                        return True
                    else:
                        logger.error(f"Failed to advance to Stage 5 for step {step_no}")
                else:
                    logger.warning(f"Not in Stage 4 - current stage is {state.current_stage}, cannot advance to Stage 5")

        # If we reach here, no automatic progression was triggered
        logger.info(f"No automatic progression triggered for step {step_no} - user interaction required")
        return False

    except Exception as e:
        logger.error(f"Error in automatic stage progression check: {e}")
        return False








def _ensure_step_table_analysis(state):
    """
    Ensure that step_table_analysis exists and is valid.
    Re-generate it if missing or None.

    Args:
        state: StateManager instance

    Returns:
        dict: Step table analysis results
    """
    # Check if we have valid step table analysis
    if (hasattr(state, 'step_table_analysis') and
        state.step_table_analysis and
        isinstance(state.step_table_analysis, dict)):
        return state.step_table_analysis

    # Re-generate analysis if missing or invalid
    logger.info("Step table analysis missing or invalid, regenerating...")

    try:
        # Import the analysis function
        from core.ai_helpers import analyze_step_table

        # Get the effective step table
        effective_step_table = state.get_effective_step_table() if hasattr(state, 'get_effective_step_table') else state.step_table_json

        if effective_step_table and isinstance(effective_step_table, list):
            # Create markdown representation for analysis
            markdown_table = state.step_table_markdown if hasattr(state, 'step_table_markdown') and state.step_table_markdown else ""

            # Analyze the step table
            step_table_analysis = analyze_step_table((markdown_table, effective_step_table))

            # Store the analysis in state
            state.step_table_analysis = step_table_analysis

            logger.info(f"Regenerated step table analysis: requires_ui_elements={step_table_analysis.get('requires_ui_elements', True)}")
            return step_table_analysis
        else:
            logger.warning("No valid step table found for analysis")

    except Exception as e:
        logger.error(f"Error regenerating step table analysis: {e}")

    # Return default analysis if regeneration fails
    default_analysis = {
        "requires_ui_elements": True,
        "reason": "Default analysis - UI element detection recommended for safety",
        "actions": [],
        "locator_strategies": []
    }

    state.step_table_analysis = default_analysis
    return default_analysis


def _is_navigation_step_or_no_ui_required(state, selected_step_table_entry):
    """
    Check if this is a navigation step or doesn't require UI elements.

    Args:
        state: StateManager instance
        selected_step_table_entry: The selected step table entry

    Returns:
        bool: True if no UI elements are required
    """
    # Ensure we have valid step table analysis
    step_table_analysis = _ensure_step_table_analysis(state)

    # Check if analysis says no UI elements required
    analysis_says_no_ui = not step_table_analysis.get("requires_ui_elements", True)

    # Check if step has navigation-type locator strategy
    locator_strategy = selected_step_table_entry.get('locator_strategy', '')
    is_navigation_locator = locator_strategy in ["", "none", "n/a", "url"]

    return analysis_says_no_ui or is_navigation_locator


def _requires_ui_elements_but_not_detected(state, selected_step_table_entry):
    """
    Check if UI elements are required but not yet detected.

    Args:
        state: StateManager instance
        selected_step_table_entry: The selected step table entry

    Returns:
        bool: True if UI elements are required but not detected
    """
    # Ensure we have valid step table analysis
    step_table_analysis = _ensure_step_table_analysis(state)

    # Check if analysis says UI elements are required
    analysis_requires_ui = step_table_analysis.get("requires_ui_elements", True)

    # Check if step has non-navigation locator strategy
    locator_strategy = selected_step_table_entry.get('locator_strategy', '')
    step_requires_ui = locator_strategy not in ["", "none", "n/a", "url"]

    # Check if elements have been detected
    elements_detected = hasattr(state, 'detected_elements') and state.detected_elements

    return analysis_requires_ui and step_requires_ui and not elements_detected


def _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step):
    """Handle UI element detection for the selected test case step."""
    with st.expander("UI Element Detection", expanded=True):
        # Ensure we have valid step table analysis
        step_table_analysis = _ensure_step_table_analysis(state)

        # Get analysis results with safe defaults
        requires_ui_elements = step_table_analysis.get("requires_ui_elements", True)
        ui_element_reason = step_table_analysis.get("reason", "UI element detection is needed for proper automation.")

        # Also check the specific step's requirements
        step_requires_ui_elements = selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"]

        # Display message about UI element detection requirement
        if requires_ui_elements and step_requires_ui_elements:
            st.info(f"🔍 {ui_element_reason}")

            # Check if enhanced interactive selection is available
            enhanced_available = _check_enhanced_selection_availability(state)

            # Enhanced Interactive Selection button (full width)
            if enhanced_available["available"]:
                enhanced_select_button = st.button(
                    "🚀 Enhanced Element Selection",
                    key="enhanced_select_element_btn",
                    help="Execute all previous test steps first, then select elements in the resulting application state",
                    use_container_width=True,
                    type="primary"
                )
            else:
                st.button(
                    "🚀 Enhanced Element Selection",
                    key="enhanced_select_element_btn_disabled",
                    help=enhanced_available["reason"],
                    use_container_width=True,
                    disabled=True
                )

            # Add debug button for troubleshooting
            if not enhanced_available["available"]:
                debug_col1, debug_col2 = st.columns([3, 1])
                with debug_col2:
                    if st.button("🔍 Debug", key="debug_enhanced_selection", help="Show diagnostic information for enhanced selection"):
                        _show_enhanced_selection_diagnostics(state)

            # Handle button clicks
            if enhanced_available["available"] and 'enhanced_select_button' in locals() and enhanced_select_button:
                _handle_enhanced_interactive_element_selection(state, selected_step_table_entry, selected_original_step)
        else:
            # If UI element detection is not needed, show a message and set empty elements
            st.success(f"✓ Element detection not needed: {ui_element_reason}")

            # Create empty elements for the workflow to continue
            if not hasattr(state, 'detected_elements') or not state.detected_elements:
                state.detected_elements = []
                state.qa_relevant_elements = []



def _perform_locator_resolution(state, element, selected_step_table_entry, selected_original_step):
    """
    Perform locator resolution for the selected element and display results.

    Args:
        state: StateManager instance
        element: Selected element data
        selected_step_table_entry: Step table entry with locator information
        selected_original_step: Original step data

    Returns:
        dict: Resolution result from resolve_locator_conflicts()
    """
    try:
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Prepare step data for resolution
        step_data = {
            'locator_strategy': selected_step_table_entry.get('locator_strategy', ''),
            'locator': selected_step_table_entry.get('locator', ''),
            'action': selected_step_table_entry.get('action', ''),
            'step_no': step_no
        }

        # Create element match data for resolution
        element_matches = [{
            'element': element,
            'score': 1.0,
            'manually_selected': True,
            'locator_strategy': 'css',  # Default, will be resolved
            'locator': element.get('selector', '')
        }]

        # Perform locator conflict resolution
        resolution_result = resolve_locator_conflicts(
            step_data=step_data,
            element_matches=element_matches,
            step_no=step_no,
            test_case_id=test_case_id
        )

        # Store resolution results in state
        if not hasattr(state, 'locator_resolution_results'):
            state.locator_resolution_results = {}

        if test_case_id not in state.locator_resolution_results:
            state.locator_resolution_results[test_case_id] = {}

        state.locator_resolution_results[test_case_id][step_no] = resolution_result

        debug("Locator resolution completed",
              stage="stage4", operation="locator_resolution",
              context={
                  'test_case_id': test_case_id,
                  'step_no': step_no,
                  'resolved_strategy': resolution_result.get('resolved_locator_strategy'),
                  'confidence_score': resolution_result.get('confidence_score'),
                  'conflict_detected': resolution_result.get('conflict_detected')
              })

        return resolution_result

    except Exception as e:
        debug(f"Error during locator resolution: {e}",
              stage="stage4", operation="locator_resolution_error",
              context={
                  'test_case_id': test_case_id,
                  'step_no': step_no,
                  'error_type': type(e).__name__
              })

        # Return a fallback resolution result
        return {
            'resolved_locator_strategy': 'css',
            'resolved_locator': element.get('selector', ''),
            'resolution_reason': f'Error during resolution: {str(e)}',
            'confidence_score': 0.5,
            'original_step_locator': step_data,
            'original_element_matches': element_matches,
            'conflict_detected': False
        }


def _save_element_matches_to_json(state, selected_step_table_entry, selected_original_step):
    """
    Save element matches with viewport and interaction data to JSON storage.
    This ensures the viewport detection data is persisted for script generation.
    """
    try:
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        debug("Saving element matches with viewport data to JSON storage",
              stage="stage4", operation="save_element_matches",
              context={"test_case_id": test_case_id, "step_no": step_no})

        # Load current step data from JSON
        current_step_data = state.load_step_data_from_json()
        if not current_step_data:
            debug("No existing step data found, cannot save element matches",
                  stage="stage4", operation="save_element_matches_error")
            return False

        # Find the step to update
        step_updated = False
        for step in current_step_data:
            if str(step.get('step_no')) == step_no:
                # Add element matches to the step data
                if hasattr(state, 'element_matches') and test_case_id in state.element_matches:
                    if step_no in state.element_matches[test_case_id]:
                        step['element_matches'] = state.element_matches[test_case_id][step_no]
                        step_updated = True

                        debug("Added element matches to step data",
                              stage="stage4", operation="save_element_matches",
                              context={
                                  "step_no": step_no,
                                  "matches_count": len(state.element_matches[test_case_id][step_no]),
                                  "has_viewport_data": any(
                                      'viewport_status' in match.get('element', {})
                                      for match in state.element_matches[test_case_id][step_no]
                                  ),
                                  "has_interaction_data": any(
                                      'interaction_requirements' in match.get('element', {})
                                      for match in state.element_matches[test_case_id][step_no]
                                  )
                              })
                break

        if step_updated:
            # Save the updated step data back to JSON
            success = state.update_step_data_in_json(
                current_step_data,
                f"Added element matches with viewport data for step {step_no}"
            )

            if success:
                debug("Successfully saved element matches with viewport data to JSON",
                      stage="stage4", operation="save_element_matches_success",
                      context={"test_case_id": test_case_id, "step_no": step_no})
            else:
                debug("Failed to save element matches to JSON storage",
                      stage="stage4", operation="save_element_matches_error",
                      context={"test_case_id": test_case_id, "step_no": step_no})

            return success
        else:
            debug("Step not found in current step data, cannot save element matches",
                  stage="stage4", operation="save_element_matches_error",
                  context={"test_case_id": test_case_id, "step_no": step_no})
            return False

    except Exception as e:
        debug(f"Error saving element matches to JSON: {e}",
              stage="stage4", operation="save_element_matches_error",
              context={"error": str(e)})
        return False


def _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step):
    """Create element match for manually selected element for the selected test case step."""
    try:
        # Prepare the test case and step for automatic matching
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Create a match entry for the manually selected element
        # Use the actual action from step table entry instead of hardcoded value
        actual_action = selected_step_table_entry.get('action', 'interact with') if selected_step_table_entry else 'interact with'
        assertion_type = selected_step_table_entry.get('assertion_type', '') if selected_step_table_entry else ''

        element_match = {
            'element': element,
            'action': actual_action,
            'assertion_type': assertion_type,  # Pass through assertion type information
            'score': 1.0,  # Perfect match score
            'reasoning': 'This element was manually selected by the user.',
            'manually_selected': True
        }

        # Create the element matches structure
        if not hasattr(state, 'element_matches'):
            state.element_matches = {}

        if test_case_id not in state.element_matches:
            state.element_matches[test_case_id] = {}

        state.element_matches[test_case_id][step_no] = [element_match]
        state.step_matches = state.element_matches

        # Set the LLM step analysis
        # Analyze if the step requires test data
        test_data_analysis = analyze_step_for_test_data(
            selected_step_table_entry,
            selected_original_step.get('Test Steps')
        )

        # Determine step intent based on action and assertion type
        is_visibility_verification = (
            actual_action.lower() == 'wait_for_element' and
            assertion_type.lower() == 'element_visible'
        )

        step_intent = "visibility verification (no interaction)" if is_visibility_verification else "user interaction"

        # Store the complete analysis in session state
        state.llm_step_analysis = {
            "requires_ui_element": True,
            "reason": f"Manually selected UI element will be used for this {step_intent} step.",
            "matches": [element_match],
            "requires_test_data": test_data_analysis["requires_test_data"],
            "test_data_reason": test_data_analysis["reason"],
            "data_types": test_data_analysis["data_types"],
            "step_intent": step_intent,
            "is_visibility_verification": is_visibility_verification
        }

        # Store test data analysis separately for easier access
        state.test_data_analysis = test_data_analysis

        # Automatically set test_data_skipped flag for navigation steps
        if not test_data_analysis["requires_test_data"]:
            state.test_data_skipped = True
            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
            st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
            st.info("You can proceed directly to Application Stage 6 to generate the test script.")

        # Inform the user that element matching is complete
        if not test_data_analysis["requires_test_data"]:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed directly to Application Stage 6.")

            # Automatically advance to Stage 6 since no test data is needed
            if state.current_stage == StateStage.STAGE4_DETECT:
                state.advance_to(StateStage.STAGE6_GENERATE, "Element matching completed, no test data needed - advancing to Stage 6")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 6 (no test data needed)."

                # Call st.rerun() to immediately refresh the UI
                st.rerun()
                return
        else:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed to Application Stage 5.")

            # Automatically advance to Stage 5 for test data configuration
            if state.current_stage == StateStage.STAGE4_DETECT:
                state.advance_to(StateStage.STAGE5_DATA, "Element matching completed - advancing to Stage 5 for test data")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 5 for test data configuration."

                # Call st.rerun() to immediately refresh the UI
                st.rerun()
                return
    except Exception as e:
        st.warning(f"Could not automatically complete element matching: {e}. Please use interactive element selection to manually select the required UI element.")



def _handle_element_matching(state, selected_step_table_entry, selected_original_step):
    """Handle element matching for the selected test case step."""
    with st.expander("Element Matching", expanded=True):
        # Check if automatic stage progression has already been handled
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Check if we already have element matches for this step
        has_existing_matches = (
            hasattr(state, 'element_matches') and
            state.element_matches and
            test_case_id in state.element_matches and
            step_no in state.element_matches[test_case_id]
        )

        # Check if we already have a manually selected element with matches
        has_manual_selection = False
        if has_existing_matches:
            has_manual_selection = any(match.get('manually_selected', False)
                                     for match in state.element_matches[test_case_id][step_no])

        if has_manual_selection:
            st.success("✓ Element manually selected")

            # Show the manually selected element with locator resolution
            if hasattr(state, 'detected_elements') and state.detected_elements:
                for element in state.detected_elements:
                    if element.get('manually_selected', False):
                        # Check if we have locator resolution results for this step
                        if (hasattr(state, 'locator_resolution_results') and
                            test_case_id in state.locator_resolution_results and
                            step_no in state.locator_resolution_results[test_case_id]):

                            resolution_result = state.locator_resolution_results[test_case_id][step_no]
                            step_info = {
                                'step_no': step_no,
                                'action': selected_step_table_entry.get('action', 'Unknown')
                            }
                            render_locator_resolution_summary(resolution_result, "stage4")
                        else:
                            st.markdown("**Selected Element Details:**")
                            st.json(element)
                        break

        # Check if this is a navigation step that doesn't require UI elements
        elif _is_navigation_step_or_no_ui_required(state, selected_step_table_entry):
            # Check if automatic progression has already been handled
            if has_existing_matches:
                st.success("✓ Navigation step - already processed")

                # Show guidance based on test data requirements
                if hasattr(state, 'test_data_analysis') and state.test_data_analysis:
                    if not state.test_data_analysis["requires_test_data"]:
                        st.info("✓ Ready for Stage 6 (no test data needed)")
                    else:
                        st.info("✓ Ready for Stage 5 (test data configuration)")
            else:
                st.success("✓ Navigation step - no elements needed")
                st.info("✓ Step processed automatically - check stage progression")

        # Check if element detection is required but not done
        elif _requires_ui_elements_but_not_detected(state, selected_step_table_entry):
            st.warning("⚠️ Please select UI elements first using interactive selection")
        else:
            # Prepare for element matching
            if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
                elements_for_matching = state.qa_relevant_elements
                element_count = len(elements_for_matching)
                st.info(f"Found {element_count} QA-relevant elements")
            elif hasattr(state, 'detected_elements') and state.detected_elements:
                elements_for_matching = state.detected_elements
                element_count = len(elements_for_matching)
                st.info(f"Found {element_count} elements")
            else:
                st.warning("⚠️ No elements detected")
                return

            state.step_elements = elements_for_matching

            # Note: Element matching is now handled automatically through enhanced interactive element selection
            # The manual "Match Elements with Step" button has been removed as it's no longer needed
            st.info("💡 Use the 'Enhanced Element Selection' button above to select UI elements for this step. Element matching will be handled automatically.")





def _display_previous_step_context(state, selected_step):
    """Display context from previous steps to maintain continuity in the workflow."""
    # Enhanced debugging for step context display
    logger.info("=== PREVIOUS STEPS CONTEXT DEBUG ===")
    logger.info(f"hasattr(state, 'completed_steps'): {hasattr(state, 'completed_steps')}")
    logger.info(f"state.completed_steps: {getattr(state, 'completed_steps', 'NOT_SET')}")
    logger.info(f"hasattr(state, 'step_context'): {hasattr(state, 'step_context')}")
    logger.info(f"state.step_context: {getattr(state, 'step_context', 'NOT_SET')}")
    logger.info(f"selected_step: {selected_step}")

    # Check if we have any completed steps and step context
    if (hasattr(state, 'completed_steps') and state.completed_steps and
        hasattr(state, 'step_context') and state.step_context):

        # Get the current step number
        current_step_no = str(selected_step.get('Step No'))
        logger.info(f"Current step number: {current_step_no}")

        # Find previous steps that have been completed
        previous_steps = [step for step in state.completed_steps if step != current_step_no]
        logger.info(f"Previous completed steps: {previous_steps}")

        if previous_steps:
            with st.expander("Previous Steps Context", expanded=False):
                st.info(f"Found {len(previous_steps)} completed step(s) with context data")

                # Create tabs for each previous step
                if len(previous_steps) > 1:
                    tabs = st.tabs([f"Step {step}" for step in previous_steps])

                    for i, step_no in enumerate(previous_steps):
                        with tabs[i]:
                            _display_step_context_content(state, step_no, use_expanders=False)
                else:
                    # Just one previous step
                    _display_step_context_content(state, previous_steps[0], use_expanders=False)
        else:
            logger.info("No previous steps found to display context for")
            # Show a message that no previous steps are available
            with st.expander("Previous Steps Context", expanded=False):
                st.info("No previous steps have been completed yet.")
    else:
        logger.info("No completed steps or step context available")
        # Show debugging information to help diagnose the issue
        with st.expander("Previous Steps Context", expanded=False):
            st.info("No previous step context available.")

            # Add debugging information for developers
            if st.checkbox("Show Debug Information", key="debug_step_context"):
                st.write("**Debug Information:**")
                st.write(f"- Has completed_steps: {hasattr(state, 'completed_steps')}")
                st.write(f"- Completed steps: {getattr(state, 'completed_steps', 'Not set')}")
                st.write(f"- Has step_context: {hasattr(state, 'step_context')}")
                st.write(f"- Step context keys: {list(getattr(state, 'step_context', {}).keys())}")
                st.write(f"- Current step: {selected_step.get('Step No') if selected_step else 'None'}")

    logger.info("=== END PREVIOUS STEPS CONTEXT DEBUG ===")


def _display_step_context_content(state, step_no, use_expanders=True):
    """
    Helper function to display the content of a step context.

    Args:
        state: StateManager instance
        step_no: Step number to display context for
        use_expanders: Whether to use expanders (False when already inside an expander)
    """
    if step_no in state.step_context:
        step_ctx = state.step_context[step_no]

        # Show step execution summary
        st.markdown(f"### Step {step_no} Execution Summary")

        # Show execution timestamp if available
        if step_ctx.get("execution_timestamp"):
            st.markdown(f"**Executed:** {step_ctx['execution_timestamp']}")

        # Show step action and description if available
        if step_ctx.get("step_action"):
            st.markdown(f"**Action:** {step_ctx['step_action']}")

        if step_ctx.get("step_description"):
            st.markdown(f"**Description:** {step_ctx['step_description']}")

        # Show elements if available
        if step_ctx.get("elements"):
            st.markdown(f"**UI Elements:** {len(step_ctx['elements'])} elements detected")

            # Use dual display mode pattern to avoid nested expanders
            if use_expanders:
                with st.expander(f"View Element Details ({len(step_ctx['elements'])} elements)", expanded=False):
                    _display_element_details(step_ctx['elements'])
            else:
                # Use container when already inside an expander
                with st.container():
                    st.markdown("**Element Details:**")
                    _display_element_details(step_ctx['elements'])

        # Show test data if available
        if step_ctx.get("test_data"):
            st.markdown("**Test Data:**")
            for key, value in step_ctx["test_data"].items():
                st.markdown(f"- {key}: `{value}`")

        # Show element matches if available
        if step_ctx.get("matches"):
            matches_count = len(step_ctx["matches"]) if isinstance(step_ctx["matches"], (list, dict)) else 0
            if matches_count > 0:
                st.markdown(f"**Element Matches:** {matches_count} matches found")

        # Show script path if available
        if step_ctx.get("script_path"):
            script_name = os.path.basename(step_ctx['script_path'])
            st.markdown(f"**Generated Script:** `{script_name}`")

            # Show script file status
            if os.path.exists(step_ctx['script_path']):
                st.success("✅ Script file exists")
            else:
                st.warning("⚠️ Script file not found")

        # Show a summary of what was accomplished
        accomplishments = []
        if step_ctx.get("elements"):
            accomplishments.append(f"{len(step_ctx['elements'])} UI elements detected")
        if step_ctx.get("test_data"):
            accomplishments.append(f"{len(step_ctx['test_data'])} test data parameters")
        if step_ctx.get("script_path"):
            accomplishments.append("Test script generated")

        if accomplishments:
            st.markdown("**Accomplishments:**")
            for accomplishment in accomplishments:
                st.markdown(f"- ✅ {accomplishment}")
    else:
        st.info(f"No context available for Step {step_no}")
        st.markdown("This step may not have been executed yet or context data was not saved.")


def _display_element_details(elements):
    """Helper function to display element details with ancestor information."""
    for i, element in enumerate(elements[:5]):  # Show first 5 elements
        st.markdown(f"**Element {i+1}:**")
        st.markdown(f"- Tag: `{element.get('tag', 'N/A')}`")
        st.markdown(f"- Selector: `{element.get('selector', 'N/A')}`")

        # Show enhanced selectors if available
        if element.get('enhanced_css_selector'):
            st.markdown(f"- Enhanced CSS: `{element.get('enhanced_css_selector', 'N/A')}`")
        if element.get('enhanced_xpath'):
            st.markdown(f"- Enhanced XPath: `{element.get('enhanced_xpath', 'N/A')}`")

        # Show viewport status if available
        viewport_status = element.get('viewport_status', {})
        if viewport_status:
            visibility_icon = "✅" if viewport_status.get('is_visible', False) else "❌"
            visibility_pct = viewport_status.get('visibility_percentage', 0)
            st.markdown(f"- **Viewport:** {visibility_icon} {visibility_pct}% visible")

            if viewport_status.get('requires_scroll', False):
                scroll_direction = viewport_status.get('scroll_direction', 'unknown')
                scroll_icon = {'up': '⬆️', 'down': '⬇️', 'left': '⬅️', 'right': '➡️', 'center': '🎯'}.get(scroll_direction, '📍')
                st.markdown(f"- **Scroll:** {scroll_icon} {scroll_direction} required")

        # Show interaction requirements if available
        interaction_req = element.get('interaction_requirements', {})
        if interaction_req and interaction_req.get('requires_hover', False):
            st.markdown(f"- **Interaction:** ⚠️ Hover required")

        # Show ancestor information if available
        ancestors = element.get('ancestors', [])
        if ancestors:
            st.markdown(f"- **Ancestors with IDs ({len(ancestors)}):**")
            for ancestor in ancestors[:3]:  # Show first 3 ancestors
                ancestor_info = f"Level {ancestor.get('level', '?')}: {ancestor.get('tagName', 'unknown')}#{ancestor.get('id', 'unknown')}"
                if ancestor.get('className'):
                    ancestor_info += f" .{ancestor.get('className', '').split()[0]}"
                st.markdown(f"  - {ancestor_info}")
            if len(ancestors) > 3:
                st.markdown(f"  - ... and {len(ancestors) - 3} more ancestors")

        if element.get('attributes', {}).get('text'):
            st.markdown(f"- Text: `{element['attributes']['text'][:50]}...`")
    if len(elements) > 5:
        st.markdown(f"... and {len(elements) - 5} more elements")


def _check_enhanced_selection_availability(state):
    """
    Check if enhanced interactive element selection is available with comprehensive diagnostic logging.

    Args:
        state: StateManager instance

    Returns:
        dict: Availability status with 'available' boolean and 'reason' string
    """
    debug("=== ENHANCED SELECTION AVAILABILITY CHECK START ===",
          stage="stage4", operation="enhanced_selection_availability",
          context={"function": "_check_enhanced_selection_availability"})

    # Log current state information
    debug("Current state summary:",
          stage="stage4", operation="state_summary",
          context={
              "current_stage": getattr(state, 'current_stage', None),
              "current_step_index": getattr(state, 'current_step_index', None),
              "total_steps": getattr(state, 'total_steps', None),
              "has_selected_test_case": hasattr(state, 'selected_test_case') and bool(state.selected_test_case),
              "test_case_id": state.selected_test_case.get('Test Case ID') if hasattr(state, 'selected_test_case') and state.selected_test_case else None
          })

    # Log script-related state
    combined_script_content = getattr(state, 'combined_script_content', '')
    previous_scripts = getattr(state, 'previous_scripts', {})
    debug("Script-related state:",
          stage="stage4", operation="script_state_summary",
          context={
              "combined_script_path": getattr(state, 'combined_script_path', None),
              "combined_script_content_length": len(combined_script_content) if combined_script_content else 0,
              "last_script_file": getattr(state, 'last_script_file', None),
              "generated_script_path": getattr(state, 'generated_script_path', None),
              "previous_scripts_count": len(previous_scripts) if previous_scripts else 0,
              "previous_scripts_keys": list(previous_scripts.keys()) if previous_scripts else []
          })

    # Import here to avoid circular imports
    from core.interactive_selector import _validate_enhanced_selection_prerequisites

    debug("Calling prerequisites validation...",
          stage="stage4", operation="prerequisites_validation_call")

    validation_result = _validate_enhanced_selection_prerequisites(state)

    debug("Prerequisites validation result:",
          stage="stage4", operation="prerequisites_validation_result",
          context={
              "valid": validation_result.get("valid", False),
              "reason": validation_result.get("reason", "Unknown"),
              "details": validation_result.get("details", "No details")
          })

    if validation_result["valid"]:
        debug("✅ Enhanced selection is AVAILABLE",
              stage="stage4", operation="enhanced_selection_availability",
              context={"availability_result": "available"})
        return {
            "available": True,
            "reason": "Enhanced selection available - will execute previous steps first"
        }
    else:
        debug("❌ Enhanced selection is NOT AVAILABLE",
              stage="stage4", operation="enhanced_selection_availability",
              context={
                  "availability_result": "not_available",
                  "validation_reason": validation_result["reason"],
                  "validation_details": validation_result.get("details", "No details")
              })
        return {
            "available": False,
            "reason": validation_result["reason"]
        }


def _handle_enhanced_interactive_element_selection(state, selected_step_table_entry, selected_original_step):
    """Handle enhanced interactive element selection that executes previous steps first."""
    from core.interactive_selector import select_element_with_script_execution

    with st.spinner("🚀 Executing previous test steps and opening browser for enhanced element selection..."):
        try:

            # Get the merged script path - try multiple sources for robustness
            merged_script_path = None

            # Primary source: combined_script_path from state
            if hasattr(state, 'combined_script_path') and state.combined_script_path:
                merged_script_path = state.combined_script_path
                debug(f"Using combined_script_path: {merged_script_path}",
                      stage="stage4", operation="enhanced_selection_script_path",
                      context={"source": "combined_script_path"})

            # Fallback sources if primary is not available
            elif hasattr(state, 'last_script_file') and state.last_script_file:
                merged_script_path = state.last_script_file
                debug(f"Using last_script_file as fallback: {merged_script_path}",
                      stage="stage4", operation="enhanced_selection_script_path",
                      context={"source": "last_script_file"})

            elif hasattr(state, 'optimized_script_path') and state.optimized_script_path:
                merged_script_path = state.optimized_script_path
                debug(f"Using optimized_script_path as fallback: {merged_script_path}",
                      stage="stage4", operation="enhanced_selection_script_path",
                      context={"source": "optimized_script_path"})

            # Log the final script path decision
            debug(f"Final merged script path for enhanced selection: {merged_script_path}",
                  stage="stage4", operation="enhanced_selection_script_path",
                  context={"final_path": merged_script_path, "path_exists": merged_script_path and os.path.exists(merged_script_path) if merged_script_path else False})

            # Validate that we have a script path before proceeding
            if not merged_script_path:
                st.error("❌ **No Script Available**: Cannot perform enhanced element selection without a merged script.")
                st.info("💡 Complete previous test steps and generate scripts in Stage 6 first.")
                return

            # Use the enhanced interactive selector with explicit script path
            selected_element = select_element_with_script_execution(state, merged_script_path)

            # Check if we got an error response
            if isinstance(selected_element, dict) and "error" in selected_element:
                error_info = selected_element
                st.error(f"❌ **Enhanced Selection Failed**: {error_info['message']}")

                if "details" in error_info:
                    st.error(f"**Details**: {error_info['details']}")

                if "troubleshooting" in error_info:
                    st.warning("🔧 **Specific Troubleshooting Steps:**")
                    for tip in error_info["troubleshooting"]:
                        st.warning(f"• {tip}")

                # Show debug option for detailed troubleshooting
                st.info("🔍 Use the 'Debug' button above for detailed diagnostic information.")

            elif selected_element:
                # Process the selected element from enhanced selection
                _process_selected_element(state, selected_element, selected_step_table_entry, selected_original_step, enhanced=True)
            else:
                st.warning("⚠️ No element was selected. Use the Debug button for troubleshooting.")

        except Exception as e:
            st.error(f"❌ Error during enhanced interactive element selection: {e}")
            import traceback
            st.error(traceback.format_exc())
            st.info("🔍 Use the Debug button above for detailed diagnostic information.")


def _generate_enhanced_element_name(selected_element):
    """
    Generate an enhanced, human-readable name for a selected element.

    Args:
        selected_element: Element information from JavaScript selector

    Returns:
        str: Enhanced element name with meaningful context
    """
    parts = []

    # Start with element type
    tag_name = selected_element.get('tagName', 'element')
    element_type = selected_element.get('type', '')
    role = selected_element.get('role', '')

    if element_type:
        parts.append(f"{tag_name} ({element_type})")
    elif role:
        parts.append(f"{tag_name} ({role})")
    else:
        parts.append(tag_name)

    # Add identifying information in order of preference
    element_id = selected_element.get('id', '')
    name = selected_element.get('name', '')
    aria_label = selected_element.get('ariaLabel', '')
    text = selected_element.get('text', '')
    placeholder = selected_element.get('placeholder', '')
    href = selected_element.get('href', '')

    if element_id:
        parts.append(f"#{element_id}")
    elif name:
        parts.append(f"[name='{name}']")
    elif aria_label:
        parts.append(f"[aria-label='{aria_label[:30]}...']" if len(aria_label) > 30 else f"[aria-label='{aria_label}']")
    elif text and len(text.strip()) > 0 and len(text) <= 50:
        parts.append(f"'{text.strip()}'")
    elif placeholder:
        parts.append(f"[placeholder='{placeholder[:30]}...']" if len(placeholder) > 30 else f"[placeholder='{placeholder}']")
    elif href:
        parts.append(f"[href='{href[:30]}...']" if len(href) > 30 else f"[href='{href}']")

    # Add ancestor context if available
    ancestors = selected_element.get('ancestors', [])
    if ancestors and len(ancestors) > 0:
        nearest_ancestor = ancestors[0]
        ancestor_id = nearest_ancestor.get('id', '')
        if ancestor_id:
            parts.append(f"in {nearest_ancestor.get('tagName', 'element')}#{ancestor_id}")

    return ' '.join(parts)


def _process_selected_element(state, selected_element, selected_step_table_entry, selected_original_step, enhanced=False):
    """
    Process a selected element from enhanced interactive selection.

    Args:
        state: StateManager instance
        selected_element: Selected element information
        selected_step_table_entry: Step table entry
        selected_original_step: Original step data
        enhanced: Boolean indicating if this was from enhanced selection
    """
    # Convert the selected element to the format expected by the application
    element = {
        'name': _generate_enhanced_element_name(selected_element),
        'tag': selected_element.get('tagName', ''),
        'selector': selected_element.get('cssSelector', ''),
        'xpath': selected_element.get('xpath', ''),
        'enhanced_css_selector': selected_element.get('enhancedCssSelector', ''),
        'enhanced_xpath': selected_element.get('enhancedXPath', ''),
        'ancestors': selected_element.get('ancestors', []),
        'attributes': {
            'id': selected_element.get('id', ''),
            'name': selected_element.get('name', ''),
            'class': selected_element.get('className', ''),
            'type': selected_element.get('type', ''),
            'value': selected_element.get('value', ''),
            'placeholder': selected_element.get('placeholder', ''),
            'href': selected_element.get('href', ''),
            'role': selected_element.get('role', ''),
            'aria-label': selected_element.get('ariaLabel', ''),
            'text': selected_element.get('text', '')
        },
        'interactive': True,
        'visible': True,
        'manually_selected': True,
        'enhanced_selection': enhanced,  # Mark if this was enhanced selection
        'score': 100,  # Give manually selected elements the highest score
        # NEW: Viewport visibility and interaction data
        'viewport_status': selected_element.get('viewport_status', {}),
        'interaction_requirements': selected_element.get('interaction_requirements', {})
    }

    # Store the element in state manager
    if not hasattr(state, 'detected_elements'):
        state.detected_elements = []

    # Add the manually selected element to the beginning of the list
    state.detected_elements.insert(0, element)

    # Apply QA-specific filtering (though we'll keep the manually selected element)
    qa_elements = [element]  # Start with the manually selected element

    # Add any other elements that match the filtering criteria
    if hasattr(state, 'detected_elements') and len(state.detected_elements) > 1:
        locator_strategy = selected_step_table_entry.get('locator_strategy') if selected_step_table_entry else None
        locator_value = selected_step_table_entry.get('locator') if selected_step_table_entry else None

        other_qa_elements = filter_qa_relevant_elements(
            state.detected_elements[1:],  # Skip the first element which is our manually selected one
            locator_strategy=locator_strategy,
            locator_value=locator_value
        )
        qa_elements.extend(other_qa_elements)

    state.qa_relevant_elements = qa_elements

    # Show success message with element information
    selection_type = "Enhanced" if enhanced else "Regular"
    st.success(f"✓ {selection_type} element selection completed: {element['name']} ({element['selector']})")

    # Perform locator resolution and get results
    resolution_result = _perform_locator_resolution(state, element, selected_step_table_entry, selected_original_step)

    # Display locator resolution results (nested mode to avoid expander conflicts)
    step_info = {
        'step_no': str(selected_original_step.get('Step No')),
        'action': selected_step_table_entry.get('action', 'Unknown') if selected_step_table_entry else 'Unknown'
    }
    render_locator_resolution_display(resolution_result, element, step_info, use_expanders=False)

    # Show the selected element details in a container for technical details
    st.markdown("---")
    st.markdown("#### 🔧 Technical Element Details")

    # Display enhanced element information with ancestor context
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**🎯 Selected Element:**")
        selected_element_info = {
            'name': element.get('name'),
            'tag': element.get('tag'),
            'selector': element.get('selector'),
            'enhanced_css_selector': element.get('enhanced_css_selector'),
            'xpath': element.get('xpath'),
            'enhanced_xpath': element.get('enhanced_xpath'),
            'attributes': element.get('attributes', {})
        }
        st.json(selected_element_info)

        # Display viewport status information
        viewport_status = element.get('viewport_status', {})
        if viewport_status:
            st.markdown("**👁️ Viewport Status:**")
            visibility_icon = "✅" if viewport_status.get('is_visible', False) else "❌"
            visibility_pct = viewport_status.get('visibility_percentage', 0)
            st.markdown(f"- **Visible:** {visibility_icon} {visibility_pct}%")

            if viewport_status.get('requires_scroll', False):
                scroll_direction = viewport_status.get('scroll_direction', 'unknown')
                scroll_distance = viewport_status.get('scroll_distance', 0)
                scroll_icon = {'up': '⬆️', 'down': '⬇️', 'left': '⬅️', 'right': '➡️', 'center': '🎯'}.get(scroll_direction, '📍')
                st.markdown(f"- **Scroll Required:** {scroll_icon} {scroll_direction}")
                if scroll_distance > 0:
                    st.markdown(f"- **Scroll Distance:** {scroll_distance}px")
            else:
                st.markdown("- **Scroll Required:** ✅ No")

        # Display interaction requirements
        interaction_req = element.get('interaction_requirements', {})
        if interaction_req:
            st.markdown("**🖱️ Interaction Requirements:**")
            if interaction_req.get('requires_hover', False):
                st.markdown("- **Hover Required:** ⚠️ Yes")
                hover_target = interaction_req.get('hover_target_selector')
                if hover_target:
                    st.markdown(f"- **Hover Target:** `{hover_target}`")
                hover_chain = interaction_req.get('hover_parent_chain', [])
                if hover_chain:
                    chain_text = " → ".join([f"{p.get('tagName', 'unknown')}" + (f"#{p.get('id')}" if p.get('id') else "") for p in hover_chain])
                    st.markdown(f"- **Hover Chain:** {chain_text}")
            else:
                st.markdown("- **Hover Required:** ✅ No")

    with col2:
        ancestors = element.get('ancestors', [])
        if ancestors:
            st.markdown(f"**🔗 Ancestors with IDs ({len(ancestors)}):**")
            # Use containers instead of expanders to avoid nested expander error
            for i, ancestor in enumerate(ancestors):
                # Create a container with a clear header for each ancestor
                with st.container():
                    ancestor_header = f"**Level {ancestor.get('level', i+1)}:** `{ancestor.get('tagName', 'unknown')}#{ancestor.get('id', 'unknown')}`"
                    st.markdown(ancestor_header)

                    # Display ancestor details in a more compact format
                    ancestor_details = []
                    if ancestor.get('tagName'):
                        ancestor_details.append(f"**Tag:** `{ancestor.get('tagName')}`")
                    if ancestor.get('id'):
                        ancestor_details.append(f"**ID:** `{ancestor.get('id')}`")
                    if ancestor.get('className'):
                        ancestor_details.append(f"**Class:** `{ancestor.get('className')}`")
                    if ancestor.get('cssSelector'):
                        ancestor_details.append(f"**CSS:** `{ancestor.get('cssSelector')}`")
                    if ancestor.get('xpath'):
                        ancestor_details.append(f"**XPath:** `{ancestor.get('xpath')}`")

                    # Display details in a compact format
                    for detail in ancestor_details:
                        st.markdown(f"  - {detail}")

                    # Add separator between ancestors (except for the last one)
                    if i < len(ancestors) - 1:
                        st.markdown("---")
        else:
            st.markdown("**🔗 Ancestors with IDs:**")
            st.info("No ancestor elements with ID attributes found")

    # Automatically create element matches for the manually selected element
    # This allows skipping the element matching step (4c)
    _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step)

    # CRITICAL: Save element matches with viewport and interaction data to JSON storage
    _save_element_matches_to_json(state, selected_step_table_entry, selected_original_step)


def _show_enhanced_selection_diagnostics(state):
    """
    Display comprehensive diagnostic information for enhanced selection troubleshooting.

    Args:
        state: StateManager instance
    """
    from core.interactive_selector import generate_enhanced_selection_diagnostic_report

    st.markdown("### 🔍 Enhanced Selection Diagnostic Report")
    st.info("This diagnostic report helps troubleshoot why Enhanced Element Selection is not available.")

    with st.spinner("Generating diagnostic report..."):
        diagnostic_report = generate_enhanced_selection_diagnostic_report(state)

    # Display basic information
    with st.expander("📋 Basic State Information", expanded=True):
        st.json(diagnostic_report["basic_info"])

    # Display test case information
    with st.expander("🎯 Test Case Information"):
        st.json(diagnostic_report["test_case_info"])

    # Display script paths
    with st.expander("📁 Script Path Configuration", expanded=True):
        st.json(diagnostic_report["script_paths"])

    # Display file existence checks
    with st.expander("✅ File Existence Checks", expanded=True):
        for path_name, file_info in diagnostic_report["file_existence"].items():
            if file_info["path"]:
                status = "✅ EXISTS" if file_info["exists"] else "❌ NOT FOUND"
                st.markdown(f"**{path_name}**: {status}")
                st.code(f"Path: {file_info['path']}\nAbsolute: {file_info['absolute_path']}\nSize: {file_info.get('size_bytes', 0)} bytes")
            else:
                st.markdown(f"**{path_name}**: ⚪ NOT SET")

    # Display previous scripts information
    with st.expander("📜 Previous Scripts Information"):
        st.json(diagnostic_report["previous_scripts_info"])

    # Display found scripts
    with st.expander("🔍 Script File Search Results"):
        for pattern, results in diagnostic_report["found_scripts"].items():
            st.markdown(f"**Pattern**: `{pattern}`")
            if isinstance(results, list):
                if results:
                    for script in results:
                        st.markdown(f"- Found: `{script['path']}` ({script['size_bytes']} bytes)")
                else:
                    st.markdown("- No files found")
            else:
                st.markdown(f"- {results}")

    # Display combined script information
    with st.expander("📝 Combined Script Content"):
        st.json(diagnostic_report["combined_script_info"])

    # Display validation result
    with st.expander("🔬 Validation Result", expanded=True):
        validation = diagnostic_report["validation_result"]
        status = "✅ VALID" if validation["valid"] else "❌ INVALID"
        st.markdown(f"**Status**: {status}")
        st.markdown(f"**Reason**: {validation['reason']}")
        if "details" in validation:
            st.markdown(f"**Details**: {validation['details']}")

    # Provide troubleshooting suggestions
    st.markdown("### 💡 Troubleshooting Suggestions")

    validation = diagnostic_report["validation_result"]
    if not validation["valid"]:
        reason = validation["reason"]

        if "No test case selected" in reason:
            st.warning("🎯 **Action Required**: Please select a test case in Stage 1 first.")

        elif "No merged script available" in reason:
            st.warning("📁 **Action Required**: Please complete previous test steps and generate scripts in Stage 6.")
            st.info("💡 **Tip**: Go to Stage 6 and click 'Generate Combined Script' to create the merged script.")

        elif "Merged script file not found" in reason:
            st.error("🔍 **File Missing**: The merged script path is set but the file doesn't exist.")
            st.info("💡 **Solutions**:")
            st.info("1. Re-generate the script in Stage 6")
            st.info("2. Check if the file was moved or deleted")
            st.info("3. Verify the file path is correct")

        elif "No previous steps completed" in reason:
            st.warning("📋 **Action Required**: Complete at least one previous test step before using enhanced selection.")

        elif "Cannot use enhanced selection on first step" in reason:
            st.info("ℹ️ **Information**: Enhanced selection is not available for the first test step. Complete additional test steps to enable enhanced selection.")

    # Raw diagnostic data for advanced troubleshooting
    with st.expander("🔧 Raw Diagnostic Data (Advanced)"):
        st.json(diagnostic_report)



