"""
Element detection module for GretahAI ScriptWeaver.

This module provides functions for detecting UI elements on a webpage.
It implements the detect_elements and detect_elements_advanced functions
for the modular architecture.
"""

import os
import sys
import time
import json
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, StaleElementReferenceException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.detect")

def setup_driver(headless=False):
    """
    Set up a Chrome WebDriver with anti-detection measures.

    Args:
        headless (bool): Whether to run in headless mode (default: False for better debugging)

    Returns:
        WebDriver: A configured WebDriver instance
    """
    chrome_options = Options()

    if headless:
        chrome_options.add_argument("--headless")

    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")

    # Enhanced anti-detection measures
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Add a realistic user agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    try:
        # Create the driver with ChromeDriverManager
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Additional anti-detection measures using JavaScript
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set CDP to disable automation flags
        driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        # Set navigator properties to make detection harder
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
            '''
        })

        return driver
    except WebDriverException as e:
        logger.error(f"Error setting up WebDriver: {e}")
        return None

def generate_element_name(element):
    """
    Generate a descriptive name for an element based on its attributes.

    Args:
        element: WebElement or dictionary of element attributes

    Returns:
        str: A descriptive name for the element
    """
    # Extract attributes from WebElement or use provided dictionary
    if not isinstance(element, dict):
        attrs = {
            'id': element.get_attribute('id') or '',
            'name': element.get_attribute('name') or '',
            'class': element.get_attribute('class') or '',
            'type': element.get_attribute('type') or '',
            'tag': element.tag_name,
            'text': element.text.strip() if element.text else '',
            'placeholder': element.get_attribute('placeholder') or '',
            'value': element.get_attribute('value') or '',
            'role': element.get_attribute('role') or ''
        }
    else:
        attrs = element

    # Clean text function to create valid variable names
    def clean_text(text, max_length=30):
        if not text:
            return ""
        # Keep only alphanumeric and spaces
        cleaned = ''.join(c for c in text if c.isalnum() or c.isspace())
        # Truncate to max length and remove trailing spaces
        cleaned = cleaned[:max_length].strip()
        # Replace spaces with underscores and convert to lowercase
        return cleaned.lower().replace(' ', '_')

    # Try different attributes to generate a name
    name_parts = []

    # Use id as primary identifier
    if attrs.get('id'):
        name_parts.append(clean_text(attrs['id']))

    # Use name as secondary identifier
    elif attrs.get('name'):
        name_parts.append(clean_text(attrs['name']))

    # Use text content for buttons, links, etc.
    elif attrs.get('text') and len(attrs.get('text', '')) < 50:
        name_parts.append(clean_text(attrs['text']))

    # Use placeholder for input fields
    elif attrs.get('placeholder'):
        name_parts.append(clean_text(attrs['placeholder']))

    # Use value for inputs with values
    elif attrs.get('value') and attrs.get('tag') == 'input':
        name_parts.append(clean_text(attrs['value']))

    # If no good identifier, use tag and role
    if not name_parts:
        if attrs.get('tag'):
            name_parts.append(attrs.get('tag').lower())
        if attrs.get('role'):
            name_parts.append(attrs.get('role').lower())

    # Add type suffix for inputs
    if attrs.get('tag') == 'input' and attrs.get('type') and attrs.get('type') not in ''.join(name_parts).lower():
        name_parts.append(attrs.get('type').lower())

    # If we have a name, use it
    if name_parts:
        return '_'.join(name_parts)

    # Last resort: use tag with a hash
    tag = attrs.get('tag', 'element').lower()
    return f"{tag}_{hash(str(attrs)) % 10000}"

def generate_unique_selector(driver, element):
    """
    Generate a unique CSS or XPath selector for an element.

    Args:
        driver: WebDriver instance
        element: WebElement

    Returns:
        dict: {'type': 'css|xpath', 'selector': selector_string}
    """
    # Try ID selector (most reliable)
    element_id = element.get_attribute('id')
    if element_id:
        selector = f"#{element_id}"
        if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
            return {'type': 'css', 'selector': selector}

    # Try name attribute
    element_name = element.get_attribute('name')
    if element_name:
        selector = f"[name='{element_name}']"
        if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
            return {'type': 'css', 'selector': selector}

    # Try data-testid or other data attributes
    for attr in ['data-testid', 'data-test', 'data-automation', 'data-cy', 'data-qa']:
        attr_value = element.get_attribute(attr)
        if attr_value:
            selector = f"[{attr}='{attr_value}']"
            if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                return {'type': 'css', 'selector': selector}

    # Try tag with class (filtered to exclude GRETAH internal classes)
    element_class = element.get_attribute('class')
    tag_name = element.tag_name
    if element_class:
        # Filter out GRETAH artificial classes
        classes = element_class.split()
        natural_classes = [
            cls for cls in classes
            if cls and not cls.startswith('gretah-')
            and not cls.startswith('highlight-')
            and not cls.startswith('selected-')
            and not cls.isspace()
        ]

        for cls in natural_classes:
            selector = f"{tag_name}.{cls}"
            if len(driver.find_elements(By.CSS_SELECTOR, selector)) == 1:
                return {'type': 'css', 'selector': selector}

    # Try XPath with text content
    element_text = element.text.strip()
    if element_text:
        # Escape quotes in text
        text = element_text.replace("'", "\\'")
        selector = f"//{tag_name}[contains(text(), '{text}')]"
        if len(driver.find_elements(By.XPATH, selector)) == 1:
            return {'type': 'xpath', 'selector': selector}

    # Generate a full CSS path as last resort
    try:
        js_path = driver.execute_script("""
            function getPathTo(element) {
                if (element.id !== '')
                    return '#' + element.id;
                if (element === document.body)
                    return 'body';

                var ix = 0;
                var siblings = element.parentNode.childNodes;
                for (var i = 0; i < siblings.length; i++) {
                    var sibling = siblings[i];
                    if (sibling === element)
                        return getPathTo(element.parentNode) + ' > ' + element.tagName.toLowerCase() + ':nth-child(' + (ix + 1) + ')';
                    if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                        ix++;
                }
            }
            return getPathTo(arguments[0]);
        """, element)

        return {'type': 'css', 'selector': js_path}
    except:
        # Absolute fallback: XPath with position
        try:
            js_xpath = driver.execute_script("""
                function getXPathTo(element) {
                    if (element.id !== '')
                        return "//*[@id='" + element.id + "']";
                    if (element === document.body)
                        return "/html/body";

                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element)
                            return getXPathTo(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                            ix++;
                    }
                }
                return getXPathTo(arguments[0]);
            """, element)

            return {'type': 'xpath', 'selector': js_xpath}
        except:
            # Last resort
            return {'type': 'css', 'selector': tag_name}

def find_elements_by_css(driver):
    """
    Find important interactive elements using focused CSS selectors.

    Args:
        driver: WebDriver instance

    Returns:
        list: List of detected elements
    """
    elements = []

    # Focused list of selectors for key interactive elements
    selectors = [
        # Primary interactive elements
        "input[type='text']", "input[type='email']", "input[type='password']",
        "input[type='submit']", "input[type='button']", "input[type='checkbox']",
        "input[type='radio']",

        # Essential UI controls
        "button", "a[href]", "select", "textarea",

        # Interactive elements with role attributes
        "[role]", "[role='button']", "[role='link']", "[role='checkbox']", "[role='radio']",
        "[role='tab']", "[role='menuitem']", "[role='combobox']", "[role='listbox']",
        "[role='menu']", "[role='menubar']", "[role='dialog']", "[role='alert']",
        "[role='search']", "[role='form']", "[role='navigation']", "[role='banner']",

        # Elements with event handlers
        "[onclick]", "[onchange]", "[onsubmit]", "[onmouseover]", "[onmouseout]",
        "[onkeydown]", "[onkeyup]", "[onfocus]", "[onblur]", "[oninput]",

        # Form elements
        "form", "label", "fieldset", "legend", "optgroup", "datalist",

        # Elements that might be clickable
        ".btn", ".button", "[class*='btn']", "[class*='button']",
        "[id*='btn']", "[id*='button']", "[class*='link']", "[id*='link']",
        "[class*='submit']", "[id*='submit']", "[class*='cancel']", "[id*='cancel']",

        # Common UI elements
        "header", "footer", "nav", "aside", "main", "section", "article",
        "div[id]", "div[class]", "span[id]", "span[class]", "p[id]", "p[class]",

        # Common UI components
        "table", "tr", "td", "th", "thead", "tbody", "tfoot",
        "ul", "ol", "li", "dl", "dt", "dd",
        "img", "iframe", "video", "audio", "canvas",
        "h1", "h2", "h3", "h4", "h5", "h6",

        # Elements with common attributes
        "[id]", "[name]", "[title]", "[aria-label]", "[aria-labelledby]",
        "[placeholder]", "[value]", "[for]", "[href]", "[src]",

        # Elements with tabindex
        "[tabindex]", "[tabindex='0']",

        # Generic elements that might be interactive
        "div", "span"
    ]

    # Process each selector
    for selector in selectors:
        try:
            found_elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for element in found_elements:
                try:
                    # Check if element is displayed
                    if element.is_displayed():
                        # Generate a unique selector
                        selector_info = generate_unique_selector(driver, element)

                        # Generate a name
                        name = generate_element_name(element)

                        # Get element position and size
                        rect = element.rect

                        # Create element object
                        element_obj = {
                            'name': name,
                            'selector_type': selector_info['type'],
                            'selector': selector_info['selector'],
                            'attributes': {
                                'id': element.get_attribute('id') or '',
                                'name': element.get_attribute('name') or '',
                                'class': element.get_attribute('class') or '',
                                'type': element.get_attribute('type') or '',
                                'tag': element.tag_name,
                                'text': element.text.strip() if element.text else '',
                                'placeholder': element.get_attribute('placeholder') or '',
                                'value': element.get_attribute('value') or '',
                                'role': element.get_attribute('role') or '',
                                'visible': element.is_displayed(),
                                'x': rect['x'],
                                'y': rect['y'],
                                'width': rect['width'],
                                'height': rect['height']
                            }
                        }

                        elements.append(element_obj)
                except StaleElementReferenceException:
                    continue
                except Exception as e:
                    logger.warning(f"Error processing element with selector {selector}: {e}")
        except Exception as e:
            logger.warning(f"Error finding elements with selector {selector}: {e}")

    return elements

def find_elements_by_js(driver):
    """
    Find elements using JavaScript to detect all interactive elements.

    Args:
        driver: WebDriver instance

    Returns:
        list: List of detected elements
    """
    script = """
    function getAllInteractiveElements() {
        // Get all elements in the document
        const allElements = document.querySelectorAll('*');
        const interactiveElements = [];

        // Check each element
        for (let element of allElements) {
            // Skip hidden elements but include those that might be conditionally visible
            if (element.offsetParent === null && element.tagName !== 'BODY' &&
                getComputedStyle(element).display === 'none' &&
                getComputedStyle(element).visibility === 'hidden') continue;

            const tagName = element.tagName.toLowerCase();
            const hasClickHandler = element.onclick || element.getAttribute('onclick');
            const hasChangeHandler = element.onchange || element.getAttribute('onchange');
            const hasInputHandler = element.oninput || element.getAttribute('oninput');
            const hasKeyHandler = element.onkeydown || element.getAttribute('onkeydown') ||
                                element.onkeyup || element.getAttribute('onkeyup');
            const hasMouseHandler = element.onmouseover || element.getAttribute('onmouseover') ||
                                  element.onmouseout || element.getAttribute('onmouseout');
            const role = element.getAttribute('role');
            const id = element.id || '';
            const className = element.className || '';
            const name = element.name || '';
            const type = element.type || '';

            // Check if element has any attributes that suggest interactivity
            const hasInteractiveAttributes = element.hasAttribute('tabindex') ||
                                           element.hasAttribute('aria-label') ||
                                           element.hasAttribute('aria-labelledby') ||
                                           element.hasAttribute('aria-describedby') ||
                                           element.hasAttribute('aria-controls') ||
                                           element.hasAttribute('aria-expanded') ||
                                           element.hasAttribute('aria-haspopup') ||
                                           element.hasAttribute('aria-selected') ||
                                           element.hasAttribute('aria-checked');

            // Check if element has classes or IDs suggesting interactivity
            const hasInteractiveClassOrId =
                /btn|button|link|submit|cancel|menu|tab|dropdown|select|checkbox|radio|toggle|switch|slider|scroll|drag|click|tap|swipe/i.test(id) ||
                /btn|button|link|submit|cancel|menu|tab|dropdown|select|checkbox|radio|toggle|switch|slider|scroll|drag|click|tap|swipe/i.test(className);

            const isInteractive =
                // Standard interactive elements
                tagName === 'a' ||
                tagName === 'button' ||
                tagName === 'input' ||
                tagName === 'select' ||
                tagName === 'textarea' ||
                tagName === 'label' ||
                tagName === 'option' ||
                tagName === 'form' ||
                tagName === 'fieldset' ||
                tagName === 'legend' ||
                tagName === 'datalist' ||
                tagName === 'optgroup' ||
                // Elements with interactive roles
                role === 'button' ||
                role === 'link' ||
                role === 'checkbox' ||
                role === 'radio' ||
                role === 'tab' ||
                role === 'menuitem' ||
                role === 'menu' ||
                role === 'menubar' ||
                role === 'listbox' ||
                role === 'option' ||
                role === 'switch' ||
                role === 'combobox' ||
                role === 'slider' ||
                role === 'spinbutton' ||
                role === 'textbox' ||
                role === 'searchbox' ||
                // Elements with event handlers
                hasClickHandler ||
                hasChangeHandler ||
                hasInputHandler ||
                hasKeyHandler ||
                hasMouseHandler ||
                // Elements with interactive attributes
                hasInteractiveAttributes ||
                // Elements with interactive classes or IDs
                hasInteractiveClassOrId ||
                // Special cases for common UI elements
                (tagName === 'div' && (id || className)) ||
                (tagName === 'span' && (id || className)) ||
                (tagName === 'img' && (element.hasAttribute('onclick') || element.hasAttribute('alt'))) ||
                (tagName === 'svg' && (element.hasAttribute('onclick') || element.parentElement.hasAttribute('onclick')));

            // Include all elements with id or name attributes as they might be important
            const isIdentifiable = id || name;

            if (isInteractive || isIdentifiable) {
                // Get element info
                const rect = element.getBoundingClientRect();

                // Include elements that might be conditionally visible
                if (rect.width > 0 || rect.height > 0 ||
                    getComputedStyle(element).display !== 'none' ||
                    getComputedStyle(element).visibility !== 'hidden') {

                    // Get computed style for more accurate visibility check
                    const style = getComputedStyle(element);
                    const isVisible = style.display !== 'none' && style.visibility !== 'hidden' &&
                                    style.opacity !== '0' && rect.width > 0 && rect.height > 0;

                    interactiveElements.push({
                        element: element,
                        tagName: tagName,
                        id: id,
                        name: name,
                        className: className,
                        type: type,
                        value: element.value || '',
                        text: element.textContent.trim() || '',
                        placeholder: element.placeholder || '',
                        role: role || '',
                        x: rect.x,
                        y: rect.y,
                        width: rect.width,
                        height: rect.height,
                        isVisible: isVisible
                    });
                }
            }
        }

        return interactiveElements;
    }

    return getAllInteractiveElements();
    """

    js_elements = []

    try:
        # Execute JavaScript to find interactive elements
        elements_data = driver.execute_script(script)

        # Process each element
        for data in elements_data:
            try:
                # Get the actual WebElement
                element = data['element']

                # Generate a unique selector
                selector_info = generate_unique_selector(driver, element)

                # Generate a name
                name = generate_element_name({
                    'id': data['id'],
                    'name': data['name'],
                    'tag': data['tagName'],
                    'text': data['text'],
                    'placeholder': data['placeholder'],
                    'value': data['value'],
                    'role': data['role']
                })

                # Create element object
                element_obj = {
                    'name': name,
                    'selector_type': selector_info['type'],
                    'selector': selector_info['selector'],
                    'attributes': {
                        'id': data['id'],
                        'name': data['name'],
                        'class': data['className'],
                        'type': data['type'],
                        'tag': data['tagName'],
                        'text': data['text'],
                        'placeholder': data['placeholder'],
                        'value': data['value'],
                        'role': data['role'],
                        'visible': data.get('isVisible', True),
                        'x': data.get('x', 0),
                        'y': data.get('y', 0),
                        'width': data.get('width', 0),
                        'height': data.get('height', 0)
                    }
                }

                js_elements.append(element_obj)
            except StaleElementReferenceException:
                continue
            except Exception as e:
                logger.warning(f"Error processing JS element: {e}")
    except Exception as e:
        logger.error(f"Error executing JavaScript to find elements: {e}")

    return js_elements

def filter_important_elements(elements):
    """
    Filter and prioritize important UI elements while removing duplicates.

    Args:
        elements (list): List of detected elements

    Returns:
        list: Filtered list of important elements
    """
    # Define high-priority interactive elements only
    important_tags = {
        'input': 10,
        'button': 10,
        'a': 9,
        'select': 9,
        'textarea': 8
    }

    # Focus on key identifying attributes only
    important_attributes = [
        'id', 'name', 'type', 'role',
        'placeholder', 'value', 'href'
    ]

    filtered_elements = []
    seen_selectors = set()

    for element in elements:
        # Skip if we've seen this selector before
        if element['selector'] in seen_selectors:
            continue

        attrs = element['attributes']
        tag = attrs.get('tag', '').lower()

        # Calculate element importance score
        score = 0

        # Base score from tag type
        score += important_tags.get(tag, 0)

        # Boost score for interactive elements
        if attrs.get('type') in ['submit', 'button', 'text', 'password', 'email', 'checkbox', 'radio']:
            score += 5

        # Boost score for elements with important attributes
        for attr in important_attributes:
            if attrs.get(attr):
                score += 2

        # Boost score for visible elements
        if attrs.get('visible', False):
            score += 3

        # Boost score for elements with text content
        if attrs.get('text'):
            score += 2

        # Only include elements with a minimum importance score
        if score >= 5:
            element['importance_score'] = score
            filtered_elements.append(element)
            seen_selectors.add(element['selector'])

    # Sort by importance score
    filtered_elements.sort(key=lambda x: x.get('importance_score', 0), reverse=True)

    return filtered_elements

def detect_elements(url, browser=None, headless=False, save_to_file=True, elements_dir="detected_elements"):
    """
    Optimized function to detect important interactive UI elements on a webpage.

    Args:
        url (str): URL of the webpage to analyze
        browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
        headless (bool, optional): Whether to run in headless mode. Defaults to False for better debugging
        save_to_file (bool, optional): Whether to save the detected elements to a file. Defaults to True
        elements_dir (str, optional): Directory to save the detected elements to. Defaults to "detected_elements"

    Returns:
        list: List of detected elements
    """
    logger.info(f"Starting element detection for: {url}")

    # Setup driver with consistent default behavior
    if browser is None:
        driver = setup_driver(headless=headless)
        if not driver:
            logger.error("Failed to set up WebDriver")
            return []
    else:
        driver = browser

    elements = []
    try:
        # Navigate to URL with reduced wait time
        logger.info(f"Navigating to {url}")
        driver.get(url)

        # Use WebDriverWait instead of sleep for better performance
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # Single smooth scroll to handle dynamic loading
        logger.info("Quick scroll to load dynamic content...")
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(1)  # Brief wait for dynamic content

        # Scroll back to top
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)

        # Try multiple methods to find elements
        logger.info("Finding elements using CSS selectors...")
        css_elements = find_elements_by_css(driver)

        logger.info("Finding elements using JavaScript...")
        js_elements = find_elements_by_js(driver)

        # Combine and deduplicate elements
        all_elements = css_elements + js_elements

        # Filter and deduplicate
        logger.info("Filtering and deduplicating elements...")
        filtered_elements = filter_important_elements(all_elements)

        logger.info(f"Found {len(filtered_elements)} important unique elements out of {len(all_elements)} total elements")

        # Save to file if requested
        if save_to_file:
            try:
                # Create directory if it doesn't exist
                if not os.path.exists(elements_dir):
                    os.makedirs(elements_dir)

                # Generate filename based on URL
                filename = url.replace("://", "_").replace("/", "_").replace(".", "_")
                if len(filename) > 100:
                    filename = filename[:100]
                filename = f"{filename}_{int(time.time())}.json"
                filepath = os.path.join(elements_dir, filename)

                # Save elements to file
                with open(filepath, 'w') as f:
                    json.dump(filtered_elements, f, indent=2)
                logger.info(f"Saved {len(filtered_elements)} elements to {filepath}")
            except Exception as e:
                logger.error(f"Error saving elements to file: {e}")

        return filtered_elements

    except Exception as e:
        logger.error(f"Error during element detection: {e}")
        return []

    finally:
        # Only quit the driver if we created it
        if browser is None and driver:
            try:
                driver.quit()
            except:
                pass

def detect_elements_advanced(url, browser=None, headless=False, save_to_file=True, elements_dir="detected_elements"):
    """
    Advanced version of detect_elements with additional options.
    This is a wrapper around detect_elements with the same functionality for now.

    Args:
        url (str): URL of the webpage to analyze
        browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
        headless (bool, optional): Whether to run in headless mode. Defaults to False for better debugging
        save_to_file (bool, optional): Whether to save the detected elements to a file. Defaults to True
        elements_dir (str, optional): Directory to save the detected elements to. Defaults to "detected_elements"

    Returns:
        list: List of detected elements
    """
    logger.info(f"Running advanced element detection for: {url}")
    return detect_elements(url, browser, headless, save_to_file, elements_dir)
