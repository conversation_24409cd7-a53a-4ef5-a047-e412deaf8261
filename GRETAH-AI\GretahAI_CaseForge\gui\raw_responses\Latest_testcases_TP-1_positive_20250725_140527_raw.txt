```json
[
  {
    "scenario_name": "Successful Login After Valid Credentials Entry",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify successful login with valid username and password.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "Username should be successfully entered in the username field."},
      {"action": "Verify if user is able to enter valid password in the password field", "expected_result": "Password should be successfully entered in the password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be redirected to the home page."},
      {"action": "Verify if user is able to view the homepage after successful login", "expected_result": "Homepage should be displayed correctly."}
    ]
  },
  {
    "scenario_name": "Username Field Presence",
    "type": "positive",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the username field is present on the login page.",
    "steps": [
      {"action": "Verify if user is able to navigate to the login page", "expected_result": "Login page should be displayed."},
      {"action": "Verify if user is able to locate the username field on the login page", "expected_result": "Username field should be visible."}
    ]
  },
  {
    "scenario_name": "Password Field Presence",
    "type": "positive",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the password field is present on the login page.",
    "steps": [
      {"action": "Verify if user is able to navigate to the login page", "expected_result": "Login page should be displayed."},
      {"action": "Verify if user is able to locate the password field on the login page", "expected_result": "Password field should be visible."}
    ]
  },
  {
    "scenario_name": "Login Button Functionality",
    "type": "positive",
    "prerequisites": "User should have access to the login page and valid credentials.",
    "Test Case Objective": "Verify that the login button redirects to the homepage with valid credentials",
    "steps": [
      {"action": "Verify if user is able to navigate to the login page", "expected_result": "Login page should be displayed."},
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "Username should be successfully entered in the username field."},
      {"action": "Verify if user is able to enter valid password in the password field", "expected_result": "Password should be successfully entered in the password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be redirected to the home page."}
    ]
  },
  {
    "scenario_name": "Successful Logout",
    "type": "positive",
    "prerequisites": "User should be logged in.",
    "Test Case Objective": "Verify that the user can successfully log out of the application.",
    "steps": [
      {"action": "Verify if user is able to log in with valid credentials", "expected_result": "User should be logged in successfully."},
      {"action": "Verify if user is able to locate and click the 'Logout' button/link", "expected_result": "User should be logged out of the application."},
      {"action": "Verify if user is able to be redirected to the login page after logging out", "expected_result": "Login page should be displayed."}
    ]
  }
]
```