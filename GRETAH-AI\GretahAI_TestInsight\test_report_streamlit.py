"""
Streamlit app to test the report generator with performance metrics.
"""

import streamlit as st
import os
from pathlib import Path
from report_generator import generate_report
from sql_lite_db_helpers import load_test_run_history, close_thread_local_connection

# Set up Streamlit session state
if 'model' not in st.session_state:
    st.session_state.model = 'gemini-1.5-flash-latest'
if 'api_key' not in st.session_state:
    st.session_state.api_key = ''
if 'model_type' not in st.session_state:
    st.session_state.model_type = 'Online'
if 'google_request_timestamps' not in st.session_state:
    st.session_state.google_request_timestamps = []
if 'google_token_usage' not in st.session_state:
    st.session_state.google_token_usage = []

# Define paths
BASE_DIR = Path(__file__).resolve().parent
DB_PATH = BASE_DIR / "test_results.db"
REPORTS_DIR = BASE_DIR.parent / "reports"
REPORTS_DIR.mkdir(parents=True, exist_ok=True)

st.title("Test Report Generator")

# Get test run history
run_history = load_test_run_history(str(DB_PATH), limit=10)
close_thread_local_connection()

if not run_history:
    st.error("No test runs found in the database. Please run tests first.")
else:
    # Create options for the selectbox
    run_options = {
        f"{run['timestamp']} (Total: {run.get('total_tests', 0)}, Failed: {run.get('failed_tests', 0)})": run['timestamp']
        for run in run_history
    }
    
    selected_run_display = st.selectbox(
        "Select run for report generation:",
        options=list(run_options.keys())
    )
    
    if selected_run_display:
        selected_run_ts = run_options[selected_run_display]
        
        if st.button("Generate Report"):
            with st.spinner("Generating report..."):
                report_path = generate_report(selected_run_ts, str(DB_PATH), str(REPORTS_DIR))
                
                if report_path:
                    st.success(f"Report generated successfully!")
                    
                    # Create a download button for the report
                    with open(report_path, "rb") as file:
                        st.download_button(
                            label="Download Report",
                            data=file,
                            file_name=report_path.name,
                            mime="application/pdf"
                        )
                else:
                    st.error("Report generation failed.")
