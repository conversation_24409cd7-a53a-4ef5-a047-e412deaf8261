import os
import time
import json
import pytest
import requests
import random
import string
import csv
import yaml
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    StaleElementReferenceException,
    ElementNotInteractableException,
    WebDriverException
)
from selenium.webdriver.common.action_chains import ActionChains

# Constants
BASE_URL = "https://the-internet.herokuapp.com"
TEST_DATA_DIR = Path("test_data")
TEST_DATA_DIR.mkdir(exist_ok=True)

# Create a test file for upload tests
def create_test_file(filename="test_upload.txt", content=None):
    """Create a test file with random content if not provided"""
    filepath = TEST_DATA_DIR / filename
    if content is None:
        content = ''.join(random.choices(string.ascii_letters + string.digits, k=100))
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    return str(filepath.absolute())

# Test utilities class for legacy code support
class TestUtils:
    @staticmethod
    def wait_for_page_load(driver, timeout=10):
        """Wait for page to finish loading completely"""
        try:
            WebDriverWait(driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
        except TimeoutException:
            pass
    
    @staticmethod
    def highlight_element(driver, element, duration=0.5):
        """Highlight an element temporarily for visual debugging"""
        original_style = element.get_attribute("style")
        driver.execute_script(
            "arguments[0].setAttribute('style', arguments[1]);", 
            element, 
            "border: 2px solid red; background: yellow;"
        )
        time.sleep(duration)
        driver.execute_script(
            "arguments[0].setAttribute('style', arguments[1]);", 
            element, 
            original_style
        )

    @staticmethod
    def is_element_in_viewport(driver, element):
        """Check if element is fully visible in viewport using JavaScript"""
        return driver.execute_script("""
            var rect = arguments[0].getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        """, element)

# Performance metrics collection
class PerformanceMetrics:
    @staticmethod
    def get_page_load_metrics(driver):
        """Extract detailed page load timing metrics"""
        try:
            timing = driver.execute_script("return window.performance.timing")
            navigation_start = timing["navigationStart"]
            
            metrics = {
                "total_page_load": timing["loadEventEnd"] - navigation_start,
                "dns_time": timing["domainLookupEnd"] - timing["domainLookupStart"],
                "connection_time": timing["connectEnd"] - timing["connectStart"],
                "server_response_time": timing["responseStart"] - timing["requestStart"],
                "page_download_time": timing["responseEnd"] - timing["responseStart"],
                "dom_interactive_time": timing["domInteractive"] - navigation_start,
                "dom_complete_time": timing["domComplete"] - navigation_start
            }
            
            return metrics
        except Exception:
            return {}

# 1. Complex login test with credential management and session validation
def test_login_with_session_validation(browser):
    """Test login functionality with session validation and security checks"""
    try:
        # Navigate to login page
        browser.get(f"{BASE_URL}/login")
        TestUtils.wait_for_page_load(browser)
        
        # Check initial page state
        username_field = WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.ID, "username"))
        )
        password_field = browser.find_element(By.ID, "password")
        login_button = browser.find_element(By.CSS_SELECTOR, "button[type='submit']")
        
        # Verify form security attributes
        assert password_field.get_attribute("type") == "password", "Password field not secure"
        
        # Enter credentials
        username_field.send_keys("tomsmith")
        password_field.send_keys("SuperSecretPassword!")
        
        # Submit form
        login_button.click()
        
        # Wait for successful login
        WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.success"))
        )
        
        # Verify successful login elements
        success_message = browser.find_element(By.CLASS_NAME, "flash.success")
        assert "You logged into a secure area" in success_message.text
        
        # Check for secure session cookie
        cookies = browser.get_cookies()
        
        # Verify logout button exists
        logout_button = browser.find_element(By.CSS_SELECTOR, "a.button")
        assert "Logout" in logout_button.text
        
        # Test logout
        logout_button.click()
        
        # Verify logout success
        WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.success"))
        )
        logout_message = browser.find_element(By.CLASS_NAME, "flash.success")
        assert "You logged out of the secure area" in logout_message.text
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 2. Dynamic content test with AJAX refreshes and content verification
def test_dynamic_content_with_refreshes(browser):
    """Test dynamic content with multiple refreshes and content verification"""
    try:
        # Navigate to dynamic content page
        browser.get(f"{BASE_URL}/dynamic_content")
        TestUtils.wait_for_page_load(browser)
        
        # Track content changes across refreshes
        content_snapshots = []
        
        # Function to capture current content state
        def capture_content_state():
            elements = browser.find_elements(By.CSS_SELECTOR, ".row .large-10.columns")
            return [element.text for element in elements]
        
        # Initial content state
        initial_content = capture_content_state()
        content_snapshots.append(initial_content)
        
        # Perform multiple refreshes to verify content changes
        refresh_count = 3
        for i in range(refresh_count):
            # Click the "click here" link to refresh content
            refresh_link = browser.find_element(By.CSS_SELECTOR, "a[href='/dynamic_content?with_content=static']")
            refresh_link.click()
            
            # Wait for page to reload
            TestUtils.wait_for_page_load(browser)
            
            # Capture new content state
            new_content = capture_content_state()
            content_snapshots.append(new_content)
            
            # Verify content changed
            assert len(new_content) == len(initial_content), "Content element count changed"
            
            # Check if at least one content element changed
            changes_found = False
            for j, (old, new) in enumerate(zip(content_snapshots[-2], new_content)):
                if old != new:
                    changes_found = True
            
            assert changes_found, f"No content changes detected after refresh {i+1}"
        
        # Verify all content snapshots are different
        for i, snapshot1 in enumerate(content_snapshots[:-1]):
            for j, snapshot2 in enumerate(content_snapshots[i+1:], i+1):
                differences = sum(1 for a, b in zip(snapshot1, snapshot2) if a != b)
                assert differences > 0, f"No differences between snapshots {i} and {j}"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 3. Drag and drop test with element position verification
def test_drag_and_drop_with_position_tracking(browser):
    """Test drag and drop functionality with detailed position tracking"""
    try:
        # Navigate to drag and drop page
        browser.get(f"{BASE_URL}/drag_and_drop")
        TestUtils.wait_for_page_load(browser)
        
        # Get the drag and drop elements
        source = browser.find_element(By.ID, "column-a")
        target = browser.find_element(By.ID, "column-b")
        
        # Record initial positions and text
        def get_element_info(element):
            location = element.location
            size = element.size
            text = element.text
            return {
                "location": location,
                "size": size,
                "text": text,
                "center_x": location["x"] + size["width"] / 2,
                "center_y": location["y"] + size["height"] / 2
            }
        
        source_before = get_element_info(source)
        target_before = get_element_info(target)
        
        # Perform drag and drop using JavaScript (more reliable than ActionChains)
        browser.execute_script("""
            function createEvent(typeOfEvent) {
                var event = document.createEvent("CustomEvent");
                event.initCustomEvent(typeOfEvent, true, true, null);
                event.dataTransfer = {
                    data: {},
                    setData: function (key, value) {
                        this.data[key] = value;
                    },
                    getData: function (key) {
                        return this.data[key];
                    }
                };
                return event;
            }
            
            function dispatchEvent(element, event, transferData) {
                if (transferData !== undefined) {
                    event.dataTransfer = transferData;
                }
                if (element.dispatchEvent) {
                    element.dispatchEvent(event);
                } else if (element.fireEvent) {
                    element.fireEvent("on" + event.type, event);
                }
            }
            
            function simulateHTML5DragAndDrop(element, target) {
                var dragStartEvent = createEvent('dragstart');
                dispatchEvent(element, dragStartEvent);
                
                var dropEvent = createEvent('drop');
                dispatchEvent(target, dropEvent, dragStartEvent.dataTransfer);
                
                var dragEndEvent = createEvent('dragend');
                dispatchEvent(element, dragEndEvent, dropEvent.dataTransfer);
            }
            
            simulateHTML5DragAndDrop(arguments[0], arguments[1]);
        """, source, target)
        
        # Allow time for animation to complete
        time.sleep(1)
        
        # Verify the elements have swapped
        source_after = get_element_info(source)
        target_after = get_element_info(target)
        
        # Check if texts have been swapped
        assert source_after["text"] == target_before["text"], "Source element text didn't change as expected"
        assert target_after["text"] == source_before["text"], "Target element text didn't change as expected"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 4. File upload with content verification and progress tracking
def test_file_upload_with_verification(browser):
    """Test file upload with content verification and progress tracking"""
    try:
        # Create test file with unique content
        unique_content = f"Test file content {datetime.now().isoformat()}"
        test_file_path = create_test_file(content=unique_content)
        test_filename = os.path.basename(test_file_path)
        
        # Navigate to upload page
        browser.get(f"{BASE_URL}/upload")
        TestUtils.wait_for_page_load(browser)
        
        # Get the file input element
        file_input = browser.find_element(By.ID, "file-upload")
        
        # Verify file input is enabled
        assert file_input.is_enabled(), "File input is not enabled"
        
        # Set file path
        file_input.send_keys(test_file_path)
        
        # Click upload button
        upload_button = browser.find_element(By.ID, "file-submit")
        upload_button.click()
        
        # Wait for upload to complete
        WebDriverWait(browser, 15).until(
            EC.presence_of_element_located((By.ID, "uploaded-files"))
        )
        
        # Verify upload success message
        success_header = browser.find_element(By.CSS_SELECTOR, "h3")
        assert "File Uploaded!" in success_header.text, "Upload success message not found"
        
        # Verify uploaded filename
        uploaded_files = browser.find_element(By.ID, "uploaded-files")
        assert test_filename in uploaded_files.text, f"Uploaded filename not found. Expected: {test_filename}, Got: {uploaded_files.text}"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 5. JavaScript alerts, confirms and prompts with multiple interaction patterns
def test_javascript_alerts_with_interactions(browser):
    """Test JavaScript alerts, confirms and prompts with various interaction patterns"""
    try:
        # Navigate to JavaScript alerts page
        browser.get(f"{BASE_URL}/javascript_alerts")
        TestUtils.wait_for_page_load(browser)
        
        # Test JS Alert
        js_alert_button = browser.find_element(By.XPATH, "//button[contains(text(),'Click for JS Alert')]")
        js_alert_button.click()
        
        # Switch to alert and accept
        alert = WebDriverWait(browser, 5).until(EC.alert_is_present())
        alert.accept()
        
        # Verify result
        result = browser.find_element(By.ID, "result")
        assert "You successfully clicked an alert" in result.text
        
        # Test JS Confirm - Accept
        js_confirm_button = browser.find_element(By.XPATH, "//button[contains(text(),'Click for JS Confirm')]")
        js_confirm_button.click()
        
        # Switch to confirm and accept
        confirm = WebDriverWait(browser, 5).until(EC.alert_is_present())
        confirm.accept()
        
        # Verify result for accept
        result = browser.find_element(By.ID, "result")
        assert "You clicked: Ok" in result.text
        
        # Test JS Confirm - Dismiss
        js_confirm_button.click()
        
        # Switch to confirm and dismiss
        confirm = WebDriverWait(browser, 5).until(EC.alert_is_present())
        confirm.dismiss()
        
        # Verify result for dismiss
        result = browser.find_element(By.ID, "result")
        assert "You clicked: Cancel" in result.text
        
        # Test JS Prompt - Enter text
        js_prompt_button = browser.find_element(By.XPATH, "//button[contains(text(),'Click for JS Prompt')]")
        js_prompt_button.click()
        
        # Switch to prompt, enter text and accept
        prompt = WebDriverWait(browser, 5).until(EC.alert_is_present())
        test_input = f"Test input {datetime.now().strftime('%H:%M:%S')}"
        prompt.send_keys(test_input)
        prompt.accept()
        
        # Verify result with entered text
        result = browser.find_element(By.ID, "result")
        assert f"You entered: {test_input}" in result.text
        
        # Test JS Prompt - Cancel
        js_prompt_button.click()
        
        # Switch to prompt and dismiss
        prompt = WebDriverWait(browser, 5).until(EC.alert_is_present())
        prompt.dismiss()
        
        # Verify result for cancel
        result = browser.find_element(By.ID, "result")
        assert "You entered: null" in result.text
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 6. Key presses test with complex keyboard interactions
def test_key_presses_with_combinations(browser):
    """Test key presses with various key combinations and special keys"""
    try:
        # Navigate to key presses page
        browser.get(f"{BASE_URL}/key_presses")
        TestUtils.wait_for_page_load(browser)
        
        # Get the input field
        input_field = browser.find_element(By.ID, "target")
        
        # Define key combinations to test
        test_keys = [
            (Keys.SPACE, "SPACE"),
            (Keys.ARROW_UP, "UP"),
            (Keys.ARROW_DOWN, "DOWN"),
            (Keys.ARROW_LEFT, "LEFT"),
            (Keys.ARROW_RIGHT, "RIGHT"),
            (Keys.ESCAPE, "ESCAPE"),
            (Keys.BACK_SPACE, "BACK_SPACE"),
            (Keys.TAB, "TAB"),
            (Keys.SHIFT, "SHIFT"),
            (Keys.CONTROL, "CONTROL"),
            (Keys.ALT, "ALT"),
            (Keys.ENTER, "ENTER")
        ]
        
        # Test individual keys
        for key, expected_text in test_keys:
            # Clear field and press key
            input_field.clear()
            input_field.send_keys(key)
            
            # Wait for result to update
            WebDriverWait(browser, 5).until(
                lambda d: expected_text in d.find_element(By.ID, "result").text
            )
            
            # Verify result
            result = browser.find_element(By.ID, "result")
            assert f"You entered: {expected_text}" in result.text
        
        # Test key combinations (e.g., SHIFT+A)
        # Clear field and press combination
        input_field.clear()
        ActionChains(browser).key_down(Keys.SHIFT).send_keys('a').key_up(Keys.SHIFT).perform()
        
        # Verify result
        result = browser.find_element(By.ID, "result")
        # Either "A" or "SHIFT" might be registered as the last key pressed
        assert "You entered: SHIFT" in result.text or "You entered: A" in result.text
        
        # Test typing sequence
        # Clear field and type sequence
        input_field.clear()
        sequence = "Hello123"
        input_field.send_keys(sequence)
        
        # Verify result (last character)
        result = browser.find_element(By.ID, "result")
        assert f"You entered: {sequence[-1].upper()}" in result.text
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 7. Horizontal slider with precise movements and value validation
def test_horizontal_slider_with_precision(browser):
    """Test horizontal slider with precise movements and value validation"""
    try:
        # Navigate to horizontal slider page
        browser.get(f"{BASE_URL}/horizontal_slider")
        TestUtils.wait_for_page_load(browser)
        
        # Get the slider and value elements
        slider = browser.find_element(By.CSS_SELECTOR, "input[type='range']")
        value_display = browser.find_element(By.ID, "range")
        
        # Get slider attributes
        min_value = float(slider.get_attribute("min") or "0")
        max_value = float(slider.get_attribute("max") or "100")
        step = float(slider.get_attribute("step") or "1")
        
        # Move slider to specific positions and verify values
        test_positions = [0, 0.5, 1, 2, 2.5, 3, 3.5, 4, 4.5, 5]
        
        for position in test_positions:
            # Calculate the exact percentage for the position
            percentage = (position - min_value) / (max_value - min_value)
            
            # Move slider using ActionChains and JavaScript for precision
            # First reset to 0
            browser.execute_script("arguments[0].value = arguments[1]", slider, min_value)
            browser.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }))", slider)
            
            # Then set to exact value
            browser.execute_script("arguments[0].value = arguments[1]", slider, position)
            browser.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }))", slider)
            
            # Wait for value to update
            WebDriverWait(browser, 5).until(
                lambda d: d.find_element(By.ID, "range").text == str(position)
            )
            
            # Verify displayed value
            displayed_value = float(value_display.text)
            assert abs(displayed_value - position) < 0.1, f"Expected value {position}, got {displayed_value}"
        
        # Test random position
        random_position = round(random.uniform(min_value, max_value) * 2) / 2  # Round to nearest 0.5
        
        browser.execute_script("arguments[0].value = arguments[1]", slider, random_position)
        browser.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }))", slider)
        
        # Wait for value to update
        WebDriverWait(browser, 5).until(
            lambda d: abs(float(d.find_element(By.ID, "range").text) - random_position) < 0.1
        )
        
        # Verify displayed value
        displayed_value = float(value_display.text)
        assert abs(displayed_value - random_position) < 0.1, f"Expected value {random_position}, got {displayed_value}"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 8. Form validation with comprehensive field testing
def test_form_validation_comprehensive(browser):
    """Test form validation with comprehensive field testing"""
    try:
        # Navigate to form page
        browser.get(f"{BASE_URL}/login")
        TestUtils.wait_for_page_load(browser)
        
        # Test empty submission
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Check if we're still on the login page (no redirection)
        assert "login" in browser.current_url, "Form submitted with empty fields"
        
        # Test invalid username
        username_field = browser.find_element(By.ID, "username")
        password_field = browser.find_element(By.ID, "password")
        
        username_field.send_keys("invaliduser")
        password_field.send_keys("SuperSecretPassword!")
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Check for error message
        WebDriverWait(browser, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.error"))
        )
        error_message = browser.find_element(By.CLASS_NAME, "flash.error")
        assert "Your username is invalid" in error_message.text
        
        # Test invalid password
        username_field.clear()
        password_field.clear()
        username_field.send_keys("tomsmith")
        password_field.send_keys("invalidpassword")
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Check for error message
        WebDriverWait(browser, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.error"))
        )
        error_message = browser.find_element(By.CLASS_NAME, "flash.error")
        assert "Your password is invalid" in error_message.text
        
        # Test SQL injection attempt
        username_field.clear()
        password_field.clear()
        username_field.send_keys("' OR 1=1 --")
        password_field.send_keys("' OR 1=1 --")
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Check for error message (should not be successful)
        WebDriverWait(browser, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.error"))
        )
        error_message = browser.find_element(By.CLASS_NAME, "flash.error")
        assert "Your username is invalid" in error_message.text
        
        # Test XSS attempt
        username_field.clear()
        password_field.clear()
        username_field.send_keys("<script>alert('XSS')</script>")
        password_field.send_keys("SuperSecretPassword!")
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Check for error message
        WebDriverWait(browser, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.error"))
        )
        
        # Test valid credentials
        username_field.clear()
        password_field.clear()
        username_field.send_keys("tomsmith")
        password_field.send_keys("SuperSecretPassword!")
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Check for success message
        WebDriverWait(browser, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.success"))
        )
        success_message = browser.find_element(By.CLASS_NAME, "flash.success")
        assert "You logged into a secure area" in success_message.text
        
        # Verify we're on the secure page
        assert "secure" in browser.current_url
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 9. Nested frames with content validation
def test_nested_frames_content(browser):
    """Test nested frames with content validation"""
    try:
        # Navigate to nested frames page
        browser.get(f"{BASE_URL}/nested_frames")
        TestUtils.wait_for_page_load(browser)
        
        # Check top level frames
        browser.switch_to.default_content()
        top_frame = browser.find_element(By.NAME, "frame-top")
        
        # Switch to top frame
        browser.switch_to.frame(top_frame)
        
        # Verify left frame
        browser.switch_to.frame(browser.find_element(By.NAME, "frame-left"))
        body_text = browser.find_element(By.TAG_NAME, "body").text
        assert "LEFT" in body_text
        
        # Return to top frame and verify middle frame
        browser.switch_to.default_content()
        browser.switch_to.frame(top_frame)
        browser.switch_to.frame(browser.find_element(By.NAME, "frame-middle"))
        body_text = browser.find_element(By.TAG_NAME, "body").text
        assert "MIDDLE" in body_text
        
        # Return to top frame and verify right frame
        browser.switch_to.default_content()
        browser.switch_to.frame(top_frame)
        browser.switch_to.frame(browser.find_element(By.NAME, "frame-right"))
        body_text = browser.find_element(By.TAG_NAME, "body").text
        assert "RIGHT" in body_text
        
        # Return to default and verify bottom frame
        browser.switch_to.default_content()
        browser.switch_to.frame(browser.find_element(By.NAME, "frame-bottom"))
        body_text = browser.find_element(By.TAG_NAME, "body").text
        assert "BOTTOM" in body_text
        
        # Return to default content
        browser.switch_to.default_content()
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 10. Hovers test with dynamic content loading
def test_hovers_with_dynamic_content(browser):
    """Test hovers with dynamic content loading"""
    try:
        # Navigate to hovers page
        browser.get(f"{BASE_URL}/hovers")
        TestUtils.wait_for_page_load(browser)
        
        # Find all user figures
        figures = browser.find_elements(By.CSS_SELECTOR, ".figure")
        assert len(figures) >= 3, f"Expected at least 3 figures, found {len(figures)}"
        
        # Test each figure
        for i, figure in enumerate(figures):
            # Perform hover action
            ActionChains(browser).move_to_element(figure).perform()
            
            # Wait for hover details to appear
            try:
                WebDriverWait(browser, 5).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, f".figure:nth-of-type({i+1}) .figcaption"))
                )
            except TimeoutException:
                # Try again with longer hover
                ActionChains(browser).move_to_element(figure).pause(1).perform()
                WebDriverWait(browser, 5).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, f".figure:nth-of-type({i+1}) .figcaption"))
                )
            
            # Verify caption text
            caption = browser.find_element(By.CSS_SELECTOR, f".figure:nth-of-type({i+1}) .figcaption h5")
            assert f"user{i+1}" in caption.text.lower(), f"Expected user{i+1} in caption, got {caption.text}"
            
            # Verify view profile link
            profile_link = browser.find_element(By.CSS_SELECTOR, f".figure:nth-of-type({i+1}) .figcaption a")
            assert "View profile" in profile_link.text
            
            # Get link URL
            link_url = profile_link.get_attribute("href")
            assert f"/users/{i+1}" in link_url, f"Expected /users/{i+1} in URL, got {link_url}"
            
            # Move away to reset hover state
            ActionChains(browser).move_to_element(browser.find_element(By.TAG_NAME, "h3")).perform()
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 11. Checkboxes test with state verification
def test_checkboxes_with_state_verification(browser):
    """Test checkboxes with state verification"""
    try:
        # Navigate to checkboxes page
        browser.get(f"{BASE_URL}/checkboxes")
        TestUtils.wait_for_page_load(browser)
        
        # Get all checkboxes
        checkboxes = browser.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
        assert len(checkboxes) >= 2, f"Expected at least 2 checkboxes, found {len(checkboxes)}"
        
        # Check initial states
        initial_states = []
        for i, checkbox in enumerate(checkboxes):
            is_checked = checkbox.is_selected()
            initial_states.append(is_checked)
        
        # Toggle each checkbox and verify state changes
        for i, checkbox in enumerate(checkboxes):
            # Toggle checkbox
            checkbox.click()
            
            # Verify state changed
            new_state = checkbox.is_selected()
            assert new_state != initial_states[i], f"Checkbox {i+1} state did not change"
            
        
        # Toggle all checkboxes back to initial states
        for i, checkbox in enumerate(checkboxes):
            current_state = checkbox.is_selected()
            if current_state != initial_states[i]:
                checkbox.click()
                assert checkbox.is_selected() == initial_states[i], f"Failed to reset checkbox {i+1}"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 12. Context menu test with right-click handling
def test_context_menu_with_right_click(browser):
    """Test context menu with right-click handling"""
    try:
        # Navigate to context menu page
        browser.get(f"{BASE_URL}/context_menu")
        TestUtils.wait_for_page_load(browser)
        
        # Find the context menu area
        context_area = browser.find_element(By.ID, "hot-spot")
        
        
        # Perform right-click action
        ActionChains(browser).context_click(context_area).perform()
        
        # Wait for alert to appear
        alert = WebDriverWait(browser, 5).until(EC.alert_is_present())
        
        # Verify alert text
        assert "You selected a context menu" in alert.text
        
        # Dismiss the alert
        alert.accept()
        
        # Verify alert is gone
        try:
            WebDriverWait(browser, 1).until(EC.alert_is_present())
            assert False, "Alert still present after accepting"
        except TimeoutException:
            pass
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 13. Dropdown test with all selection methods
def test_dropdown_with_all_selection_methods(browser):
    """Test dropdown with all selection methods"""
    try:
        # Navigate to dropdown page
        browser.get(f"{BASE_URL}/dropdown")
        TestUtils.wait_for_page_load(browser)
        
        # Find the dropdown element
        dropdown_element = browser.find_element(By.ID, "dropdown")
        select = Select(dropdown_element)
        
        # Get all options
        options = select.options
        
        # Verify initial state
        initial_selected = select.first_selected_option.text
        assert "Please select an option" in initial_selected
        
        # Method 1: Select by visible text
        select.select_by_visible_text("Option 1")
        
        # Verify selection
        selected = select.first_selected_option.text
        assert selected == "Option 1", f"Expected 'Option 1', got '{selected}'"
        
        # Method 2: Select by value
        select.select_by_value("2")
        
        # Verify selection
        selected = select.first_selected_option.text
        assert selected == "Option 2", f"Expected 'Option 2', got '{selected}'"
        
        # Method 3: Select by index
        select.select_by_index(1)  # Index 1 should be "Option 1"
        
        # Verify selection
        selected = select.first_selected_option.text
        assert selected == "Option 1", f"Expected 'Option 1', got '{selected}'"
        
        # Method 4: Direct JavaScript selection
        browser.execute_script("arguments[0].value = '2'", dropdown_element)
        browser.execute_script("arguments[0].dispatchEvent(new Event('change'))", dropdown_element)
        
        # Verify selection after JavaScript
        selected = Select(dropdown_element).first_selected_option.text
        assert selected == "Option 2", f"Expected 'Option 2', got '{selected}'"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 14. Dynamic controls test with AJAX waits
def test_dynamic_controls_with_ajax_waits(browser):
    """Test dynamic controls with AJAX waits"""
    try:
        # Navigate to dynamic controls page
        browser.get(f"{BASE_URL}/dynamic_controls")
        TestUtils.wait_for_page_load(browser)
        
        # Test checkbox removal
        checkbox = browser.find_element(By.CSS_SELECTOR, "input[type=checkbox]")
        assert checkbox.is_displayed(), "Checkbox not initially displayed"
        
        # Click the remove button
        remove_button = browser.find_element(By.CSS_SELECTOR, "button:contains('Remove')")
        remove_button.click()
        
        # Wait for loading indicator and then for it to disappear
        WebDriverWait(browser, 5).until(
            EC.visibility_of_element_located((By.ID, "loading"))
        )
        
        WebDriverWait(browser, 10).until(
            EC.invisibility_of_element_located((By.ID, "loading"))
        )
        
        # Verify checkbox is gone
        message = browser.find_element(By.ID, "message")
        assert "gone" in message.text.lower()
        
        # Verify checkbox is no longer in DOM or not visible
        try:
            checkbox = browser.find_element(By.CSS_SELECTOR, "input[type=checkbox]")
            assert not checkbox.is_displayed(), "Checkbox still visible after removal"
        except NoSuchElementException:
            pass
        
        # Test checkbox re-addition
        
        # Click the add button
        add_button = browser.find_element(By.CSS_SELECTOR, "button:contains('Add')")
        add_button.click()
        
        # Wait for loading indicator and then for it to disappear
        WebDriverWait(browser, 5).until(
            EC.visibility_of_element_located((By.ID, "loading"))
        )
        
        WebDriverWait(browser, 10).until(
            EC.invisibility_of_element_located((By.ID, "loading"))
        )
        
        # Verify checkbox is back
        WebDriverWait(browser, 5).until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "input[type=checkbox]"))
        )
        checkbox = browser.find_element(By.CSS_SELECTOR, "input[type=checkbox]")
        assert checkbox.is_displayed(), "Checkbox not displayed after re-addition"
        
        message = browser.find_element(By.ID, "message")
        assert "back" in message.text.lower()
        
        # Test enable/disable input
        
        # Verify input is initially disabled
        input_field = browser.find_element(By.CSS_SELECTOR, "input[type=text]")
        assert not input_field.is_enabled(), "Input should be initially disabled"
        
        # Click the enable button
        enable_button = browser.find_element(By.CSS_SELECTOR, "button:contains('Enable')")
        enable_button.click()
        
        # Wait for loading and then for it to disappear
        WebDriverWait(browser, 5).until(
            EC.visibility_of_element_located((By.ID, "loading"))
        )
        WebDriverWait(browser, 10).until(
            EC.invisibility_of_element_located((By.ID, "loading"))
        )
        
        # Verify input is now enabled
        WebDriverWait(browser, 5).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type=text]"))
        )
        input_field = browser.find_element(By.CSS_SELECTOR, "input[type=text]")
        assert input_field.is_enabled(), "Input should be enabled after clicking Enable"
        
        message = browser.find_element(By.ID, "message")
        assert "enabled" in message.text.lower()
        
        # Test the input by typing
        test_text = "Testing dynamic input"
        input_field.clear()
        input_field.send_keys(test_text)
        assert input_field.get_attribute("value") == test_text
        
        # Disable the input again
        disable_button = browser.find_element(By.CSS_SELECTOR, "button:contains('Disable')")
        disable_button.click()
        
        # Wait for loading and then for it to disappear
        WebDriverWait(browser, 5).until(
            EC.visibility_of_element_located((By.ID, "loading"))
        )
        WebDriverWait(browser, 10).until(
            EC.invisibility_of_element_located((By.ID, "loading"))
        )
        
        # Verify input is now disabled again
        input_field = browser.find_element(By.CSS_SELECTOR, "input[type=text]")
        assert not input_field.is_enabled(), "Input should be disabled after clicking Disable"
        
        message = browser.find_element(By.ID, "message")
        assert "disabled" in message.text.lower()
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise
