"""
Script Editor UI Component for GretahAI ScriptWeaver

This module provides UI components for manual script editing functionality in Stage 6.
It allows users to edit generated scripts directly within the interface, save changes,
and revert to original AI-generated versions.

Key Features:
- Editable text area with syntax highlighting
- Save/Cancel functionality for script edits
- Visual indicators for AI-generated vs manually edited scripts
- Revert to original AI script capability
- Integration with StateManager for persistent state
"""

import streamlit as st
from datetime import datetime
from typing import Optional

# Import GRETAH standardized logging
from debug_utils import debug


def render_script_status_badge(script_status: dict) -> None:
    """
    Render status badge showing whether script is AI-generated or manually edited.
    
    Args:
        script_status: Dictionary containing script status information
    """
    if script_status['is_manually_edited']:
        # Show manually edited badge
        edit_time = script_status['edit_timestamp']
        if edit_time:
            time_str = edit_time.strftime("%Y-%m-%d %H:%M:%S")
            st.success(f"✏️ **Manually Edited** - {time_str}")
        else:
            st.success("✏️ **Manually Edited**")
    else:
        # Show AI-generated badge
        st.info("🤖 **AI Generated**")


def render_script_editor(state, script_content: str, script_file: str) -> bool:
    """
    Render the script editor component with edit/view modes.
    
    Args:
        state: StateManager instance
        script_content: Current script content to display/edit
        script_file: Path to the script file
        
    Returns:
        bool: True if script was modified, False otherwise
    """
    script_modified = False
    script_status = state.get_script_status_info()
    
    # Header with status badge
    col1, col2 = st.columns([3, 1])
    with col1:
        st.subheader("Generated Script")
    with col2:
        render_script_status_badge(script_status)
    
    # Edit mode toggle and utility buttons
    col1, col2, col3, col4 = st.columns([1, 1, 1, 1])
    
    with col1:
        # Edit mode toggle
        if script_status['in_edit_mode']:
            if st.button("👁️ View Mode", key="toggle_view_mode", help="Switch to view-only mode"):
                state.toggle_script_edit_mode()
                st.session_state['state'] = state
                st.rerun()
        else:
            if st.button("✏️ Edit Script", key="toggle_edit_mode", help="Switch to edit mode"):
                state.toggle_script_edit_mode()
                st.session_state['state'] = state
                st.rerun()
    
    with col2:
        # Copy button
        if st.button("📋 Copy Script", key="copy_script_editor"):
            st.session_state['clipboard_content'] = script_content
            st.success("✅ Script copied!")
    
    with col3:
        # Download button
        if st.download_button(
            label="💾 Download",
            data=script_content,
            file_name=f"script_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py",
            mime="text/plain",
            key="download_script_editor"
        ):
            st.success("✅ Script downloaded!")
    
    with col4:
        # Revert button (only show if script was manually edited)
        if script_status['can_revert']:
            if st.button("🔄 Revert to AI", key="revert_to_ai", help="Restore original AI-generated script"):
                if state.revert_to_ai_script():
                    st.session_state['state'] = state
                    st.success("✅ Reverted to original AI-generated script!")
                    st.rerun()
                else:
                    st.error("❌ Could not revert script")
    
    # Script content display/editor
    if script_status['in_edit_mode']:
        # Edit mode - show editable text area
        st.markdown("**✏️ Edit Mode - Make your changes below:**")
        
        edited_content = st.text_area(
            "Script Content:",
            value=script_content,
            height=400,
            key="script_editor_textarea",
            help="Edit the script content directly. Use Ctrl+A to select all, Ctrl+C to copy, etc."
        )
        
        # Edit mode action buttons
        col1, col2 = st.columns([1, 1])
        
        with col1:
            if st.button("💾 Save Changes", key="save_script_changes", type="primary", use_container_width=True):
                if edited_content != script_content:
                    # Content was modified
                    try:
                        state.save_manual_script_edit(edited_content, "User manual edit")
                        st.session_state['state'] = state
                        st.success("✅ Script changes saved successfully!")
                        script_modified = True
                        st.rerun()
                    except Exception as e:
                        st.error(f"❌ Error saving script: {str(e)}")
                        debug(f"Error saving manual script edit: {str(e)}",
                              stage="script_editor", operation="save_error",
                              context={'error_message': str(e), 'error_type': type(e).__name__})
                else:
                    st.info("ℹ️ No changes detected")
        
        with col2:
            if st.button("❌ Cancel", key="cancel_script_edit", use_container_width=True):
                state.toggle_script_edit_mode()  # Exit edit mode without saving
                st.session_state['state'] = state
                st.info("✅ Edit cancelled")
                st.rerun()
        
        # Show diff if content changed
        if edited_content != script_content:
            st.markdown("---")
            st.markdown("**📝 Changes Preview:**")
            
            # Simple change indicator
            original_lines = len(script_content.splitlines())
            edited_lines = len(edited_content.splitlines())
            char_diff = len(edited_content) - len(script_content)
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Lines", edited_lines, edited_lines - original_lines)
            with col2:
                st.metric("Characters", len(edited_content), char_diff)
            with col3:
                if char_diff > 0:
                    st.success(f"+{char_diff} chars")
                elif char_diff < 0:
                    st.error(f"{char_diff} chars")
                else:
                    st.info("No change")
    
    else:
        # View mode - show read-only script with syntax highlighting
        st.code(script_content, language="python")
        
        # Show edit hint
        if not script_status['is_manually_edited']:
            st.info("💡 **Tip:** Click 'Edit Script' above to modify the generated script directly.")
    
    return script_modified


def render_script_editor_help() -> None:
    """
    Render help information for the script editor.
    """
    with st.expander("ℹ️ Script Editor Help", expanded=False):
        st.markdown("""
        **Script Editor Features:**
        
        - **✏️ Edit Mode**: Switch to edit mode to modify the script directly
        - **👁️ View Mode**: Switch back to read-only view with syntax highlighting  
        - **💾 Save Changes**: Save your modifications and create a new script version
        - **❌ Cancel**: Discard changes and exit edit mode
        - **🔄 Revert to AI**: Restore the original AI-generated script
        - **📋 Copy**: Copy script content to clipboard
        - **💾 Download**: Download script as a .py file
        
        **Status Indicators:**
        - **🤖 AI Generated**: Script was created by AI and hasn't been modified
        - **✏️ Manually Edited**: Script has been modified by user
        
        **Tips:**
        - Your changes are automatically saved to a new timestamped file
        - The original AI-generated script is preserved for revert capability
        - Edited scripts will be used in Stage 7 (Script Execution) and beyond
        - All script versions are tracked in the Script Browser (Stage 9)
        """)
