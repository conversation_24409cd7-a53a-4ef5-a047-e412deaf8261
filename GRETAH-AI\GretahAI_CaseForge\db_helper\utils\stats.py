"""
Statistics utilities for the GretahAI CaseForge database system.

This module provides statistical analysis and reporting functions for
test cases, test runs, and database metrics.
"""

import sqlite3
from datetime import datetime, timedelta
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_database_stats(database_path):
    """
    Retrieves comprehensive database statistics.
    
    This function provides a complete overview of the database contents including
    counts of records, user activity, JIRA coverage, and temporal distribution
    of test case creation. It's essential for monitoring system usage and health.

    Args:
        database_path (str): Absolute path to the SQLite database file

    Returns:
        dict: Dictionary containing comprehensive statistics:
            - total_jira_issues: Total number of JIRA issues
            - total_test_runs: Total number of test runs
            - total_test_cases: Total number of test cases
            - total_test_steps: Total number of test steps
            - unique_users: List of unique users
            - test_types_distribution: Count by test type
            - recent_activity: Recent test creation activity
            - jira_coverage: Test case counts per JIRA ID
        Returns empty dict on error.

    Example:
        stats = get_database_stats(db_path)
        print(f"Total test cases: {stats.get('total_test_cases', 0)}")
        print(f"Active users: {len(stats.get('unique_users', []))}")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        stats = {}
        
        # Basic counts
        cursor.execute("SELECT COUNT(*) FROM jira_issues")
        stats['total_jira_issues'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM test_runs")
        stats['total_test_runs'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM test_cases")
        stats['total_test_cases'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM test_steps")
        stats['total_test_steps'] = cursor.fetchone()[0]
        
        # Unique users
        cursor.execute("SELECT DISTINCT user_name FROM test_cases WHERE user_name IS NOT NULL")
        stats['unique_users'] = [row[0] for row in cursor.fetchall()]
        
        # Test types distribution
        cursor.execute("""
            SELECT dashboard_test_type, COUNT(*) as count
            FROM test_cases
            GROUP BY dashboard_test_type
        """)
        stats['test_types_distribution'] = {row[0]: row[1] for row in cursor.fetchall()}
        
        # Recent activity (last 7 days)
        seven_days_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
        cursor.execute("""
            SELECT DATE(timestamp) as date, COUNT(*) as count
            FROM test_cases
            WHERE DATE(timestamp) >= ?
            GROUP BY DATE(timestamp)
            ORDER BY date
        """, (seven_days_ago,))
        stats['recent_activity'] = {row[0]: row[1] for row in cursor.fetchall()}
        
        # JIRA coverage
        cursor.execute("""
            SELECT jira_id, COUNT(DISTINCT test_case_id) as test_case_count
            FROM test_cases
            GROUP BY jira_id
            ORDER BY test_case_count DESC
        """)
        stats['jira_coverage'] = {row[0]: row[1] for row in cursor.fetchall()}
        
        # Status distribution
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM test_runs
            GROUP BY status
        """)
        stats['test_run_status_distribution'] = {row[0] or 'unknown': row[1] for row in cursor.fetchall()}
        
        # Average test cases per run
        cursor.execute("""
            SELECT AVG(CAST(num_test_cases AS FLOAT)) as avg_test_cases
            FROM test_runs
            WHERE num_test_cases > 0
        """)
        result = cursor.fetchone()
        stats['avg_test_cases_per_run'] = round(result[0], 2) if result[0] else 0
        
        return stats
        
    except sqlite3.Error as e:
        print(f"Error getting database stats: {e}")
        return {}
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def count_test_cases_for_test_run(database_path, test_run_id):
    """
    Counts the number of test cases associated with a specific test run.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        test_run_id (int): ID of the test run

    Returns:
        int: Number of test cases in the test run, 0 if not found or on error

    Example:
        count = count_test_cases_for_test_run(db_path, 123)
        print(f"Test run 123 has {count} test cases")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        cursor.execute(
            "SELECT COUNT(DISTINCT test_case_id) FROM test_cases WHERE test_run_id = ?",
            (test_run_id,)
        )
        
        result = cursor.fetchone()
        return result[0] if result else 0
        
    except sqlite3.Error as e:
        print(f"Error counting test cases for test run: {e}")
        return 0
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_user_statistics(database_path, user_name):
    """
    Retrieves statistics for a specific user.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        user_name (str): Name of the user

    Returns:
        dict: Dictionary containing user statistics:
            - total_test_cases: Total test cases created by user
            - total_test_runs: Total test runs created by user
            - jira_ids_worked_on: List of JIRA IDs the user has worked on
            - test_types_used: Distribution of test types used
            - first_activity: Date of first test case creation
            - last_activity: Date of most recent test case creation
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        stats = {}
        
        # Total test cases by user
        cursor.execute("SELECT COUNT(*) FROM test_cases WHERE user_name = ?", (user_name,))
        stats['total_test_cases'] = cursor.fetchone()[0]
        
        # Total test runs by user
        cursor.execute("SELECT COUNT(*) FROM test_runs WHERE user_name = ?", (user_name,))
        stats['total_test_runs'] = cursor.fetchone()[0]
        
        # JIRA IDs worked on
        cursor.execute("SELECT DISTINCT jira_id FROM test_cases WHERE user_name = ?", (user_name,))
        stats['jira_ids_worked_on'] = [row[0] for row in cursor.fetchall()]
        
        # Test types distribution
        cursor.execute("""
            SELECT dashboard_test_type, COUNT(*) as count
            FROM test_cases
            WHERE user_name = ?
            GROUP BY dashboard_test_type
        """, (user_name,))
        stats['test_types_used'] = {row[0]: row[1] for row in cursor.fetchall()}
        
        # Activity timeline
        cursor.execute("""
            SELECT MIN(timestamp) as first_activity, MAX(timestamp) as last_activity
            FROM test_cases
            WHERE user_name = ?
        """, (user_name,))
        result = cursor.fetchone()
        stats['first_activity'] = result[0] if result[0] else None
        stats['last_activity'] = result[1] if result[1] else None
        
        return stats
        
    except sqlite3.Error as e:
        print(f"Error getting user statistics: {e}")
        return {}
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_jira_statistics(database_path, jira_id):
    """
    Retrieves statistics for a specific JIRA ID.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier

    Returns:
        dict: Dictionary containing JIRA statistics:
            - total_test_cases: Total test cases for this JIRA
            - total_test_runs: Total test runs for this JIRA
            - total_test_steps: Total test steps for this JIRA
            - contributors: List of users who worked on this JIRA
            - test_types_coverage: Distribution of test types
            - latest_update: Most recent activity timestamp
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        stats = {}
        
        # Total test cases
        cursor.execute("SELECT COUNT(*) FROM test_cases WHERE jira_id = ?", (jira_id,))
        stats['total_test_cases'] = cursor.fetchone()[0]
        
        # Total test runs
        cursor.execute("SELECT COUNT(*) FROM test_runs WHERE jira_id = ?", (jira_id,))
        stats['total_test_runs'] = cursor.fetchone()[0]
        
        # Total test steps
        cursor.execute("""
            SELECT COUNT(ts.id)
            FROM test_steps ts
            JOIN test_cases tc ON ts.test_case_id = tc.id
            WHERE tc.jira_id = ?
        """, (jira_id,))
        stats['total_test_steps'] = cursor.fetchone()[0]
        
        # Contributors
        cursor.execute("SELECT DISTINCT user_name FROM test_cases WHERE jira_id = ? AND user_name IS NOT NULL", (jira_id,))
        stats['contributors'] = [row[0] for row in cursor.fetchall()]
        
        # Test types coverage
        cursor.execute("""
            SELECT dashboard_test_type, COUNT(*) as count
            FROM test_cases
            WHERE jira_id = ?
            GROUP BY dashboard_test_type
        """, (jira_id,))
        stats['test_types_coverage'] = {row[0]: row[1] for row in cursor.fetchall()}
        
        # Latest update
        cursor.execute("SELECT MAX(timestamp) FROM test_cases WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()
        stats['latest_update'] = result[0] if result[0] else None
        
        return stats
        
    except sqlite3.Error as e:
        print(f"Error getting JIRA statistics: {e}")
        return {}
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


def format_stats_report(stats):
    """
    Formats statistics into a readable report string.
    
    Args:
        stats (dict): Statistics dictionary from get_database_stats()
        
    Returns:
        str: Formatted report string
    """
    if not stats:
        return "No statistics available."
        
    report = []
    report.append("=== GretahAI CaseForge Database Statistics ===")
    report.append("")
    
    # Basic counts
    report.append(f"Total JIRA Issues: {stats.get('total_jira_issues', 0)}")
    report.append(f"Total Test Runs: {stats.get('total_test_runs', 0)}")
    report.append(f"Total Test Cases: {stats.get('total_test_cases', 0)}")
    report.append(f"Total Test Steps: {stats.get('total_test_steps', 0)}")
    report.append("")
    
    # Users
    users = stats.get('unique_users', [])
    report.append(f"Active Users ({len(users)}): {', '.join(users) if users else 'None'}")
    report.append("")
    
    # Test types
    test_types = stats.get('test_types_distribution', {})
    if test_types:
        report.append("Test Types Distribution:")
        for test_type, count in test_types.items():
            report.append(f"  {test_type}: {count}")
        report.append("")
    
    # JIRA coverage
    jira_coverage = stats.get('jira_coverage', {})
    if jira_coverage:
        report.append("JIRA Coverage (Top 10):")
        sorted_jira = sorted(jira_coverage.items(), key=lambda x: x[1], reverse=True)[:10]
        for jira_id, count in sorted_jira:
            report.append(f"  {jira_id}: {count} test cases")
        report.append("")
    
    # Recent activity
    recent = stats.get('recent_activity', {})
    if recent:
        report.append("Recent Activity (Last 7 days):")
        for date, count in sorted(recent.items()):
            report.append(f"  {date}: {count} test cases")
        report.append("")
    
    # Average test cases per run
    avg_cases = stats.get('avg_test_cases_per_run', 0)
    report.append(f"Average Test Cases per Run: {avg_cases}")
    
    return "\n".join(report)
