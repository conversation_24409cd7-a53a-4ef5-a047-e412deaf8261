"""
Stage 6B: Test Case-Level Script Generation for GretahAI ScriptWeaver

This module handles the test case-level script generation phase of the application workflow.
It provides functionality for:
- Generating complete test scripts for entire test cases in one operation
- Enhanced input collection for locators, URLs, and test data
- Bypassing the step-by-step workflow for rapid script generation
- Integration with manual testers who have pre-defined information
- Direct transition to Stage 7 (Test Execution) after generation

This is an alternative workflow to the traditional Stage 4-6 step-by-step process.
"""

import streamlit as st
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional, List

from debug_utils import debug

# Import state management
from state_manager import StateStage

# AI generation functions are imported dynamically within functions to avoid circular imports


def stage6b_generate_test_case_script(state):
    """
    Stage 6B: Generate Complete Test Case Script.

    This stage allows users to generate a complete test script for the entire test case
    in one operation, bypassing the step-by-step workflow. It's designed for scenarios
    where manual testers have pre-defined locators, URLs, and test data.

    Args:
        state (StateManager): The application state manager instance
    """
    debug("Stage 6B: Function entry", stage="stage6b",
          operation="function_entry",
          context={
              'current_stage': state.current_stage.get_display_name(),
              'current_stage_num': state.current_stage.get_stage_number(),
              'function_call_timestamp': datetime.now().isoformat(),
              'generated_script_path': getattr(state, 'generated_script_path', None),
              'all_steps_done': getattr(state, 'all_steps_done', None),
              'selected_test_case_id': state.selected_test_case.get('Test Case ID', 'Unknown') if hasattr(state, 'selected_test_case') and state.selected_test_case else None
          })

    st.markdown("<h2 class='stage-header'>Stage 6B: Test Case-Level Script Generation</h2>", unsafe_allow_html=True)
    
    debug("Stage 6B: Test Case-Level Script Generation accessed",
          stage="stage6b",
          operation="stage_entry",
          context={
              'current_stage': state.current_stage.name if hasattr(state, 'current_stage') else 'unknown',
              'has_selected_test_case': hasattr(state, 'selected_test_case') and bool(state.selected_test_case),
              'has_step_table': hasattr(state, 'step_table_json') and bool(state.step_table_json)
          })
    
    # Validate prerequisites
    if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
        st.error("❌ No test case selected. Please return to Stage 3 to select a test case.")
        return
    
    if not hasattr(state, 'step_table_json') or not state.step_table_json:
        st.error("❌ No step table available. Please return to Stage 3 to convert the test case.")
        return
    
    # Display test case information
    _display_test_case_summary(state)
    
    # Enhanced input collection interface
    st.markdown("---")
    st.markdown("### 📝 Enhanced Input Collection")
    st.info("💡 **Tip:** Provide as much information as possible upfront to generate a complete, ready-to-run test script.")
    
    # Collect enhanced inputs
    enhanced_inputs = _collect_enhanced_inputs(state)
    
    # Script generation section
    st.markdown("---")
    st.markdown("### 🚀 Generate Complete Test Script")
    
    if enhanced_inputs:
        _display_generation_section(state, enhanced_inputs)
    else:
        st.warning("⚠️ Please provide the required information above to enable script generation.")


def _display_test_case_summary(state):
    """Display a summary of the selected test case and its steps."""
    test_case = state.selected_test_case
    step_table = state.step_table_json
    
    st.markdown("### 📋 Test Case Summary")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown(f"**Test Case ID:** {test_case.get('Test Case ID', 'Unknown')}")
        st.markdown(f"**Objective:** {test_case.get('Test Case Objective', 'Not specified')}")
        
    with col2:
        st.metric("Total Steps", len(step_table))
    
    # Display step overview
    with st.expander("📝 Step Overview", expanded=False):
        for i, step in enumerate(step_table, 1):
            st.markdown(f"**Step {i}:** {step.get('action', 'Unknown action')}")
            if step.get('expected_result'):
                st.markdown(f"   *Expected:* {step.get('expected_result')}")


def _collect_enhanced_inputs(state) -> Optional[Dict[str, Any]]:
    """
    Collect enhanced inputs from the user for test case-level generation.
    
    Returns:
        Dict containing all collected inputs, or None if incomplete
    """
    step_table = state.step_table_json
    
    # Initialize session state for inputs if not exists
    if 'stage6b_inputs' not in st.session_state:
        st.session_state.stage6b_inputs = {}
    
    inputs = st.session_state.stage6b_inputs
    
    # Website URL (should already be available from state)
    website_url = getattr(state, 'website_url', '')
    st.text_input("🌐 Website URL", value=website_url, key="stage6b_website_url", disabled=True)
    
    # Collect inputs for each step
    tabs = st.tabs([f"Step {i+1}" for i in range(len(step_table))])
    
    all_inputs_provided = True
    
    for i, (tab, step) in enumerate(zip(tabs, step_table)):
        with tab:
            step_key = f"step_{i+1}"
            if step_key not in inputs:
                inputs[step_key] = {}
            
            step_inputs = inputs[step_key]
            
            # Display step information
            st.markdown(f"**Action:** {step.get('action', 'Unknown')}")
            st.markdown(f"**Expected Result:** {step.get('expected_result', 'Not specified')}")
            
            # Collect step-specific inputs based on action type
            step_complete = _collect_step_inputs(step, step_inputs, i+1)
            
            if not step_complete:
                all_inputs_provided = False
    
    # Save inputs to session state
    st.session_state.stage6b_inputs = inputs
    
    return inputs if all_inputs_provided else None


def _collect_step_inputs(step: Dict[str, Any], step_inputs: Dict[str, Any], step_number: int) -> bool:
    """
    Collect inputs for a specific step based on its action type.

    Returns:
        bool: True if all required inputs are provided
    """
    action = step.get('action', '').lower()
    locator_strategy = step.get('locator_strategy', '')
    assertion_type = step.get('assertion_type', '').lower()

    # Determine what inputs are needed based on action type
    # CRITICAL FIX: URL verification actions don't need locators
    if action in ['verify_url'] or (action in ['verify', 'verification', 'assert', 'check'] and
                                   assertion_type in ['url_contains', 'page_title', 'url_equals', 'url_matches']):
        needs_locator = False
    else:
        needs_locator = any(keyword in action for keyword in ['click', 'type', 'select', 'wait', 'verify', 'assert'])

    needs_text_input = any(keyword in action for keyword in ['type', 'input', 'enter'])
    needs_url = 'navigate' in action or 'go to' in action

    # URL verification steps need expected URL input
    needs_expected_url = (action in ['verify_url'] or
                         (action in ['verify', 'verification', 'assert', 'check'] and
                          assertion_type in ['url_contains', 'page_title', 'url_equals', 'url_matches']))
    
    inputs_complete = True

    # Debug: Log the field requirements for this step
    debug(f"Stage 6B: Step {step_number} field requirements", stage="stage6b",
          operation="field_requirements_analysis",
          context={
              'action': action,
              'assertion_type': assertion_type,
              'needs_locator': needs_locator,
              'needs_text_input': needs_text_input,
              'needs_url': needs_url,
              'needs_expected_url': needs_expected_url,
              'locator_strategy': locator_strategy
          })
    
    # Locator input
    if needs_locator:
        locator_value = st.text_input(
            f"🎯 Element Locator ({locator_strategy or 'XPath/CSS'})",
            value=step_inputs.get('locator', ''),
            key=f"step_{step_number}_locator",
            help="Provide the XPath, CSS selector, or other locator for the UI element"
        )
        step_inputs['locator'] = locator_value
        if not locator_value.strip():
            inputs_complete = False
    
    # Text input for typing actions
    if needs_text_input:
        text_value = st.text_input(
            "✏️ Text to Input",
            value=step_inputs.get('text_input', ''),
            key=f"step_{step_number}_text",
            help="Provide the text that should be entered into the field"
        )
        step_inputs['text_input'] = text_value
        if not text_value.strip():
            inputs_complete = False
    
    # URL input for navigation actions
    if needs_url:
        url_value = st.text_input(
            "🔗 Navigation URL",
            value=step_inputs.get('url', ''),
            key=f"step_{step_number}_url",
            help="Provide the specific URL to navigate to"
        )
        step_inputs['url'] = url_value
        if not url_value.strip():
            inputs_complete = False

    # Expected URL input for URL verification actions
    if needs_expected_url:
        expected_url_value = st.text_input(
            "🎯 Expected URL",
            value=step_inputs.get('expected_url', ''),
            key=f"step_{step_number}_expected_url",
            help="Provide the expected URL or URL pattern to verify against"
        )
        step_inputs['expected_url'] = expected_url_value
        if not expected_url_value.strip():
            inputs_complete = False
    
    # Additional notes/instructions
    notes = st.text_area(
        "📝 Additional Notes",
        value=step_inputs.get('notes', ''),
        key=f"step_{step_number}_notes",
        help="Any additional instructions or context for this step"
    )
    step_inputs['notes'] = notes
    
    # Show completion status
    if inputs_complete:
        st.success("✅ Step inputs complete")
    else:
        st.warning("⚠️ Please provide all required inputs")
    
    return inputs_complete


def _display_generation_section(state, enhanced_inputs: Dict[str, Any]):
    """Display the script generation section with options and controls."""

    # Custom instructions
    custom_instructions = st.text_area(
        "🎯 Custom Instructions (Optional)",
        value=getattr(state, 'user_generation_comment_6b', ''),
        height=100,
        help="Provide any specific requirements or preferences for the generated script"
    )

    # Store user comment in state
    if custom_instructions != getattr(state, 'user_generation_comment_6b', ''):
        state.user_generation_comment_6b = custom_instructions
        setattr(state, 'generation_comment_enhancement_done_6b', False)  # Reset enhancement flag when comment changes
        setattr(state, 'ai_enhanced_generation_comment_6b', getattr(state, 'ai_enhanced_generation_comment_6b', ""))
        setattr(state, 'use_enhanced_generation_comment_6b', getattr(state, 'use_enhanced_generation_comment_6b', False))
        st.session_state['state'] = state

    # AI Enhancement section
    if custom_instructions.strip():
        st.markdown("---")
        st.markdown("**✨ AI Enhancement (Optional)**")

        # Enhancement button
        if st.button("✨ Enhance Comment with AI",
                   use_container_width=True,
                   key="enhance_generation_comment_ai_6b",
                   help="Let AI enhance your feedback into detailed technical instructions"):
            logger.info("Stage 6B: User clicked 'Enhance Comment with AI' button")

            with st.spinner("Enhancing your feedback with AI..."):
                try:
                    from core.ai_enhancement import enhance_generation_comment_with_ai
                    from core.logging_config import get_logger  # Ensure this import remains
                    enhanced_comment = enhance_generation_comment_with_ai(
                        user_comment=custom_instructions,
                        test_case=state.selected_test_case,
                        step_table_entry=None,  # Stage 6B works with complete test case
                        api_key=state.google_api_key,
                        context={
                            'test_case_id': state.selected_test_case.get('Test Case ID', 'unknown'),
                            'generation_stage': 'stage6b_ai_enhancement'
                        }
                    )

                    # Store enhanced comment in state
                    state.ai_enhanced_generation_comment_6b = enhanced_comment
                    state.generation_comment_enhancement_done_6b = True
                    st.session_state['state'] = state

                    logger.info(f"Stage 6B: AI comment enhancement completed, enhanced length: {len(enhanced_comment)}")
                    st.success("✅ Comment enhanced successfully!")
                    st.rerun()

                except Exception as e:
                    error_msg = f"Error enhancing comment: {str(e)}"
                    st.error(error_msg)
                    logger.error(f"Stage 6B: {error_msg}")

        # Show enhanced comment if available
        if (hasattr(state, 'generation_comment_enhancement_done_6b') and state.generation_comment_enhancement_done_6b and
            hasattr(state, 'ai_enhanced_generation_comment_6b') and state.ai_enhanced_generation_comment_6b):

            st.markdown("**📝 Enhanced Instructions:**")

            # Show both versions for comparison
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Your Original Feedback:**")
                st.info(custom_instructions)

            with col2:
                st.markdown("**AI-Enhanced Instructions:**")
                st.success(state.ai_enhanced_generation_comment_6b)

            # User choice between original and enhanced
            st.markdown("**Choose which version to use for regeneration:**")

            comment_choice = st.radio(
                "Select instructions to use:",
                options=["Use Original Feedback", "Use AI-Enhanced Instructions"],
                index=1 if getattr(state, 'use_enhanced_generation_comment_6b', False) else 0,
                key="generation_comment_choice_radio_6b",
                horizontal=True
            )

            # Update state based on user choice
            use_enhanced = comment_choice == "Use AI-Enhanced Instructions"
            if use_enhanced != getattr(state, 'use_enhanced_generation_comment_6b', False):
                state.use_enhanced_generation_comment_6b = use_enhanced
                st.session_state['state'] = state
    
    # Generation options
    col1, col2 = st.columns(2)
    
    with col1:
        include_comments = st.checkbox("📝 Include detailed comments", value=True)
        include_assertions = st.checkbox("✅ Include comprehensive assertions", value=True)
    
    with col2:
        include_error_handling = st.checkbox("🛡️ Include error handling", value=True)
        include_screenshots = st.checkbox("📸 Include screenshot capture", value=False)
    
    # Check if script has already been generated
    script_already_generated = (hasattr(state, 'generated_script_path') and
                                state.generated_script_path and
                                hasattr(state, 'all_steps_done') and
                                state.all_steps_done)

    if script_already_generated:
        st.success("✅ **Script already generated!** You should be automatically redirected to Stage 7B.")
        st.info("💡 If you're not in Stage 7B, please use the sidebar navigation to go to **Stage 7B: Test Case-Level Script Execution**")

        # Show manual navigation button as backup
        if st.button("🔄 Go to Stage 7B Now", type="primary", key="goto_stage7b_manual"):
            success = state.advance_to(StateStage.STAGE7B_EXECUTE_TESTCASE, "Manual navigation to Stage 7B from Stage 6B")
            if success:
                st.session_state['state'] = state
                st.rerun()
            else:
                st.error("❌ Failed to navigate to Stage 7B. Please use sidebar navigation.")
    else:
        # Generate button (only show if script hasn't been generated yet)
        if st.button("🚀 Generate Complete Test Script", type="primary", key="generate_complete_script"):
            # Determine which instructions to use
            final_instructions = custom_instructions
            if (hasattr(state, 'use_enhanced_generation_comment_6b') and state.use_enhanced_generation_comment_6b and
                hasattr(state, 'ai_enhanced_generation_comment_6b') and state.ai_enhanced_generation_comment_6b):
                final_instructions = state.ai_enhanced_generation_comment_6b

            _generate_complete_script(state, enhanced_inputs, {
                'custom_instructions': final_instructions,
                'include_comments': include_comments,
                'include_assertions': include_assertions,
                'include_error_handling': include_error_handling,
                'include_screenshots': include_screenshots
            })


def _generate_complete_script(state, enhanced_inputs: Dict[str, Any], generation_options: Dict[str, Any]):
    """Generate the complete test case script using AI."""

    with st.spinner("🤖 Generating complete test script..."):
        try:
            # Import AI generation function
            from core.ai_generation import generate_complete_test_case_script

            # Generate the complete script using AI
            complete_script = generate_complete_test_case_script(
                test_case=state.selected_test_case,
                step_table=state.step_table_json,
                enhanced_inputs=enhanced_inputs,
                website_url=getattr(state, 'website_url', ''),
                generation_options=generation_options,
                api_key=getattr(state, 'google_api_key', None),
                state=state
            )

            # Clean up the response to extract code if needed
            if "```python" in complete_script:
                code_start = complete_script.find("```python") + 10
                code_end = complete_script.rfind("```")
                complete_script = complete_script[code_start:code_end].strip()
            elif "```" in complete_script:
                code_start = complete_script.find("```") + 3
                code_end = complete_script.rfind("```")
                complete_script = complete_script[code_start:code_end].strip()

            # Display generation success
            st.success("✅ Complete test script generated successfully!")

            # Show generation summary
            st.info("📋 **Generation Summary:**")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Test Case", state.selected_test_case.get('Test Case ID', 'Unknown'))
            with col2:
                st.metric("Total Steps", len(state.step_table_json))
            with col3:
                st.metric("Script Length", f"{len(complete_script)} chars")

            # Display the generated script
            st.markdown("### 📄 Generated Test Script")
            st.code(complete_script, language="python")

            # Save the script to a file
            script_filepath = _save_generated_script(state, complete_script, generation_options)

            # Set state variables for Stage 7 and Stage 8 compatibility
            state.generated_script_path = script_filepath
            state.combined_script_path = script_filepath  # For Stage 8 compatibility
            state.combined_script_content = complete_script  # For Stage 8 compatibility

            # Mark all steps as done since we generated the complete script
            state.all_steps_done = True

            debug("Stage 6B: Set compatibility variables for Stage 7 and 8", stage="stage6b",
                  operation="compatibility_setup",
                  context={
                      'generated_script_path': script_filepath,
                      'combined_script_path': script_filepath,
                      'script_length': len(complete_script),
                      'all_steps_done': True
                  })

            # CRITICAL FIX: Auto-navigate to Stage 7 after successful script generation
            st.markdown("---")
            st.success("🚀 **Script generation complete!** Automatically proceeding to Stage 7: Test Script Execution...")

            debug("Stage 6B: Auto-navigating to Stage 7 after successful script generation", stage="stage6b",
                  operation="auto_navigation_to_stage7",
                  context={
                      'script_path': script_filepath,
                      'test_case_id': state.selected_test_case.get('Test Case ID', 'Unknown'),
                      'auto_navigation_timestamp': datetime.now().isoformat()
                  })

            # Auto-advance to Stage 7B without user interaction
            transition_reason = f"Stage 6B → Stage 7B: Auto-navigation after successful script generation for {state.selected_test_case.get('Test Case ID', 'Unknown')}"
            success = state.advance_to(StateStage.STAGE7B_EXECUTE_TESTCASE, transition_reason)

            if success:
                debug("Stage 6B: Auto-navigation to Stage 7B successful", stage="stage6b",
                      operation="auto_navigation_success",
                      context={
                          'new_stage': state.current_stage.get_display_name(),
                          'transition_reason': transition_reason
                      })

                # Force state update and trigger rerun
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Automatically advanced to Stage 7B: Test Case-Level Script Execution"

                # Add a small delay to show the success message before rerun
                time.sleep(1)
                st.rerun()
            else:
                debug("Stage 6B: Auto-navigation to Stage 7B failed", stage="stage6b",
                      operation="auto_navigation_failed",
                      context={'reason': 'advance_to returned False'})
                st.error("❌ Failed to automatically advance to Stage 7B. You can manually navigate using the sidebar.")

                # Fallback: Show manual navigation option
                if st.button("🔄 Manually Go to Stage 7B", type="primary", key="manual_stage7b_fallback"):
                    debug("Stage 6B: Manual fallback navigation to Stage 7B", stage="stage6b",
                          operation="manual_fallback_navigation",
                          context={
                              'fallback_reason': 'Auto-navigation failed, user clicked manual fallback',
                              'script_path': getattr(state, 'generated_script_path', None)
                          })

                    # Try manual transition
                    manual_transition_reason = f"Stage 6B → Stage 7B: Manual fallback navigation for {state.selected_test_case.get('Test Case ID', 'Unknown')}"
                    success = state.advance_to(StateStage.STAGE7B_EXECUTE_TESTCASE, manual_transition_reason)

                    if success:
                        st.session_state['state'] = state
                        st.rerun()
                    else:
                        st.error("❌ Manual navigation also failed. Please use the sidebar navigation to go to Stage 7B.")

        except Exception as e:
            st.error(f"❌ Error generating script: {str(e)}")
            debug("Script generation error", stage="stage6b", operation="generation_error",
                  context={'error': str(e), 'test_case_id': state.selected_test_case.get('Test Case ID', 'Unknown')})

            # Show error details in expander
            with st.expander("🔍 Error Details", expanded=False):
                st.code(str(e))


def _save_generated_script(state, script_content: str, generation_options: Dict[str, Any]) -> str:
    """Save the generated script to a file.

    Returns:
        str: The filepath where the script was saved
    """
    try:
        # Create script directory if it doesn't exist
        script_dir = "generated_tests"
        os.makedirs(script_dir, exist_ok=True)

        # Generate filename
        test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown').replace(' ', '_').replace('-', '_')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_{test_case_id}_complete_{timestamp}.py"
        filepath = os.path.join(script_dir, filename)

        # Add header comment to the script
        header = f"""# Complete Test Script for Test Case: {state.selected_test_case.get('Test Case ID', 'Unknown')}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Generated via Stage 6B (Test Case-Level Generation)
# Total steps: {len(state.step_table_json)}
# Generation options: {generation_options}

"""
        final_script_content = header + script_content

        # Save the script
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(final_script_content)

        # Store the file path in state
        state.generated_script_path = filepath

        st.success(f"💾 Script saved to: `{filepath}`")
        debug("Complete script saved", stage="stage6b", operation="script_save",
              context={'filepath': filepath, 'script_length': len(final_script_content)})

        return filepath

    except Exception as e:
        st.warning(f"⚠️ Could not save script to file: {str(e)}")
        debug("Script save error", stage="stage6b", operation="script_save_error",
              context={'error': str(e)})
        return ""


def _get_script_file_path(state) -> str:
    """Get the file path for the generated script."""
    test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown').replace(' ', '_').replace('-', '_')
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"test_{test_case_id}_complete_{timestamp}.py"
    return os.path.join("generated_tests", filename)
