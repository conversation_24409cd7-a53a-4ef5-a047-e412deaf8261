```json
[
  {
    "scenario_name": "Successful Login After Account Creation",
    "type": "positive",
    "prerequisites": "User should have a valid and active account in the system with correct credentials.",
    "Test Case Objective": "Verify that a user with valid credentials can successfully log in to the system.",
    "steps": [
      {"action": "Verify if user is able to enter their valid username in the 'User ID' field", "expected_result": "The username should be successfully entered in the 'User ID' field."},
      {"action": "Verify if user is able to enter their valid password in the 'Password' field", "expected_result": "The password should be successfully entered in the 'Password' field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "The user should be redirected to the home page or dashboard."},
      {"action": "Verify if user is able to see the user's name displayed on the welcome page", "expected_result": "The user's name should be displayed on the welcome page, confirming successful login."}
    ]
  },
  {
    "scenario_name": "Login with correct credentials after multiple failed attempts on other accounts",
    "type": "positive",
    "prerequisites": "User should have a valid account with correct credentials. The lockout mechanism should not affect other valid user accounts.",
    "Test Case Objective": "Verify that a user with valid credentials can log in successfully even after failed login attempts on different user accounts.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the 'User ID' field", "expected_result": "The username should be accepted and displayed in the field."},
      {"action": "Verify if user is able to enter the correct password in the 'Password' field", "expected_result": "The password should be accepted and masked in the field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "The user should be successfully logged in and redirected to their profile page."},
      {"action": "Verify if user is able to see the home page successfully", "expected_result": "The user should be able to access the home page without any issues"}
    ]
  },
  {
    "scenario_name": "Successful Logout",
    "type": "positive",
    "prerequisites": "User should be successfully logged in to the system.",
    "Test Case Objective": "Verify that a logged-in user can successfully log out of the system.",
    "steps": [
      {"action": "Verify if user is able to locate and click the 'Logout' button or link", "expected_result": "The 'Logout' button or link should be clickable."},
      {"action": "Verify if user is able to click the 'Logout' button or link", "expected_result": "The user should be logged out of the system."},
      {"action": "Verify if user is able to see the login page after clicking the logout button", "expected_result": "The login page should be displayed, indicating successful logout."}
    ]
  },
  {
    "scenario_name": "Persistent Login After Browser Refresh",
    "type": "positive",
    "prerequisites": "User should be successfully logged in to the system and the system should support session persistence.",
    "Test Case Objective": "Verify that a user's login session persists after refreshing the browser.",
    "steps": [
      {"action": "Verify if user is able to log in to the system with valid credentials", "expected_result": "The user should be successfully logged in and redirected to the home page or dashboard."},
      {"action": "Verify if user is able to refresh the browser window", "expected_result": "The browser window should be refreshed without logging the user out."},
      {"action": "Verify if user is able to remain logged in and access the system after the browser refresh", "expected_result": "The user should still be logged in and able to access the system's features."}
    ]
  },
  {
    "scenario_name": "Password change after successful login",
    "type": "positive",
    "prerequisites": "User should be successfully logged in to the system with valid credentials.",
    "Test Case Objective": "Verify that a logged-in user can successfully change their password.",
    "steps": [
      {"action": "Verify if user is able to navigate to the 'Change Password' section in their profile settings", "expected_result": "The user should be able to access the password change form."},
      {"action": "Verify if user is able to enter the current password correctly", "expected_result": "The current password should be validated."},
      {"action": "Verify if user is able to enter a new, valid password and confirm it", "expected_result": "The new password and confirmation should match and meet complexity requirements."},
      {"action": "Verify if user is able to save the new password", "expected_result": "The password should be successfully changed and a confirmation message should be displayed."}
    ]
  }
]
```