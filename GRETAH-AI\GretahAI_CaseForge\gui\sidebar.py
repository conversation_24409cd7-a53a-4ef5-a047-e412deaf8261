"""
Sidebar functionality for the GUI application.
"""

import streamlit as st
import os
import pandas as pd
from pathlib import Path
from helpers import is_ollama_running, upload_edited_excel

def render_sidebar(jira_connected, config):
    """
    Render the sidebar for the application.

    Args:
        jira_connected (bool): Whether the JIRA connection is established
        config (dict): Configuration dictionary

    Returns:
        tuple: (ai_provider, google_api_key, selected_model)
    """
    # Initialize persistent session state for AI settings
    if "ai_provider_persistent" not in st.session_state:
        st.session_state["ai_provider_persistent"] = "Cloud"  # Default value

    if "selected_model_persistent" not in st.session_state:
        st.session_state["selected_model_persistent"] = "gemini-2.0-flash"  # Default value

    # Initialize variables that need to be available globally
    uploaded_file = None
    google_api_key = ""  # Default value

    with st.sidebar:
        # CaseForge-specific branding
        st.image("../assets/images/Cogniron_logo.png", width=300)
        
        # Replace hard-coded colors with classes that will adapt to the theme
        st.markdown("""
        <div style="text-align: center; margin-bottom: 20px;">
            <h2 class="sidebar-header" style="margin: 0; padding: 0; font-size: 1.5rem;">GretahAI CaseForge</h2>
            <p class="sidebar-subheader" style="margin: 0; padding: 0; font-size: 0.9rem;">Test Case Management</p>
        </div>
        """, unsafe_allow_html=True)

        # Initialize the page state if it doesn't exist
        if "current_page" not in st.session_state:
            st.session_state["current_page"] = "generator"

        # Navigation section at the top
        st.markdown("### Navigation")

        # Create radio buttons for navigation that look like the other inputs
        current_page = st.radio(
            "Select Section",
            options=["Test Case Generator", "Test Analysis"],
            index=0 if st.session_state["current_page"] == "generator" else 1,
            key="navigation_radio",
            horizontal=True
        )

        # Update the session state based on selection
        if current_page == "Test Case Generator" and st.session_state["current_page"] != "generator":
            st.session_state["current_page"] = "generator"
            st.rerun()
        elif current_page == "Test Analysis" and st.session_state["current_page"] != "analysis":
            st.session_state["current_page"] = "analysis"
            st.rerun()

        # Only show AI settings if we're on the Test Case Generator page
        if st.session_state["current_page"] == "generator":
            st.markdown("---")
            # Add AI Provider selection with persistent state
            provider_options = ["Local", "Cloud"]
            current_provider_index = 1  # Default to Cloud (index 1)
            try:
                current_provider_index = provider_options.index(st.session_state["ai_provider_persistent"])
            except (ValueError, KeyError):
                current_provider_index = 1  # Default to Cloud if not found

            ai_provider = st.radio(
                "Select AI Provider",
                options=provider_options,
                index=current_provider_index,
                key="ai_provider_radio" # Add a key for stability
            )

            # Update persistent session state when provider changes
            if ai_provider != st.session_state["ai_provider_persistent"]:
                st.session_state["ai_provider_persistent"] = ai_provider
                # Reset model selection when provider changes to avoid invalid combinations
                if ai_provider == "Local":
                    st.session_state["selected_model_persistent"] = "gemma:2b"
                else:  # Cloud
                    st.session_state["selected_model_persistent"] = "gemini-2.0-flash"

            # Get Google API key from config but don't show the input field
            try:
                google_api_key = config.get("google_api_key", "")
                # Store API key in session state for use by AI modification functionality
                st.session_state["google_api_key"] = google_api_key
            except:
                google_api_key = ""
                st.session_state["google_api_key"] = ""

            st.markdown("### Model Settings")
            # Adjust model options based on provider (example)
            if ai_provider == "Local":
                # Example local models - adjust based on what 'ollama list' shows
                model_options = ["gemma:2b", "gemma:7b", "mistral"] # Replace with actual available local models
                provider_tag = "📍 **LOCAL**"
                # Try to get actual local models if Ollama is running
                try:
                    ollama_running, ollama_version = is_ollama_running()
                    if not ollama_running:
                        st.warning("⚠️ Ollama not detected. Local AI provider may not work.")
                except Exception:
                    pass
            else: # Cloud
                model_options = ["gemini-2.0-flash", "gemini-1.5-flash", "gemini-1.5-flash-8b"]
                provider_tag = "☁️ **CLOUD**"

            # Determine the current model index based on persistent session state
            current_model_index = 0
            current_model = st.session_state["selected_model_persistent"]

            # Ensure the current model is valid for the selected provider
            if current_model in model_options:
                current_model_index = model_options.index(current_model)
            else:
                # If current model is not valid for this provider, use default
                current_model_index = 0
                st.session_state["selected_model_persistent"] = model_options[0]

            col1, col2 = st.columns([3, 1])
            with col1:
                # Use a consistent key for the selectbox to maintain state
                selected_model = st.selectbox(
                    "Select AI Model",
                    model_options,
                    index=current_model_index,
                    key="model_select_persistent"
                )

                # Update persistent session state when model changes
                if selected_model != st.session_state["selected_model_persistent"]:
                    st.session_state["selected_model_persistent"] = selected_model

            with col2:
                st.markdown(provider_tag)

            st.markdown("---")
            st.markdown("### Context Settings")
            
            # Define clear function for context
            def clear_context():
                st.session_state["context_input_area"] = ""
            
            # Initialize the session state for context_prompt if it doesn't exist
            if "context_input_area" not in st.session_state:
                st.session_state["context_input_area"] = ""
            
            # Add a text area for additional context
            context_value = st.text_area(
                "Additional Context",
                help="Additional context or instructions that will be automatically used when generating test cases",
                placeholder="Enter additional context or instructions here...",
                height=100,
                key="context_input_area"
            )
            
            # Update session state with current value for backward compatibility
            st.session_state["context_prompt"] = context_value
            
            # Add clear button with on_click callback
            st.button("🗑️ Clear Context", key="clear_context_btn", on_click=clear_context)

            st.markdown("---")
            st.markdown("### Upload Edited Test Cases")
            uploaded_file = st.file_uploader("Upload edited Excel file", type=["xlsx"], help="Upload an edited test case Excel file to store it in the system")
        else:
            # If we're on the Test Analysis page, show login status and options
            st.markdown("---")

            # Check if the user is logged in
            is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
            current_user = st.session_state.get("admin_username", "")

            if is_admin_logged_in and current_user:
                st.success(f"Logged in as: {current_user}")

                # Add logout button
                if st.button("Logout", key="sidebar_analysis_logout"):
                    st.session_state["admin_username"] = ""
                    st.session_state["is_admin_logged_in"] = False
                    st.session_state["admin_user"] = ""
                    st.rerun()  # Keep rerun only for critical state changes
            else:
                st.info("Please log in through the Admin Panel to view your test runs.")

        # Only show upload options if we're on the Generator page and have an uploaded file
        if st.session_state["current_page"] == "generator" and "uploaded_file" in locals() and uploaded_file is not None:
            # Add information about the upload options
            with st.expander("About Upload Options"):
                st.markdown("### Update Database")
                st.info("This option will save the edited test cases to the database with the 'is_edited' flag set to True. When viewing test cases, edited versions will be prioritized over original versions.")

            # Store the uploaded file in session state for later use
            st.session_state["uploaded_excel_file"] = uploaded_file
            st.success(f"Excel file '{uploaded_file.name}' uploaded successfully. Choose an action below:")

            # Center the Update Database button since Jira Integration is removed
            if st.button("Update Database", use_container_width=True):
                with st.spinner("Updating database..."):
                    # Get the current logged-in user
                    current_user = st.session_state.get("admin_username", "admin")
                    success, message, file_path = upload_edited_excel(st.session_state["uploaded_excel_file"], current_user)
                    if success:
                        st.success(message)
                    else:
                        st.error(message)

        st.markdown("---")

        # Add Cloud Usage section to sidebar (always show it)
        # Calculate usage metrics
        from datetime import datetime, timedelta
        from gui.utils import save_usage_data

        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        one_day_ago = now - timedelta(days=1)

        # Filter timestamps for the relevant periods
        requests_last_minute = [ts for ts in st.session_state.google_request_timestamps if ts > one_minute_ago]
        requests_last_day = [ts for ts in st.session_state.google_request_timestamps if ts > one_day_ago]

        # Filter token usage for the last minute
        tokens_last_minute_data = [tup for tup in st.session_state.google_token_usage if tup[0] > one_minute_ago]
        tokens_last_minute_sum = sum(count for _, count in tokens_last_minute_data)  # Use _ for unused variable

        # Calculate metrics
        current_rpm = len(requests_last_minute)
        current_tpm = tokens_last_minute_sum
        current_rpd = len(requests_last_day)

        # Display metrics in a more compact format for the sidebar
        with st.expander("☁️ Cloud Usage", expanded=False):
            st.metric("TPM (Tokens/Minute)", f"{current_tpm:,}")
            st.metric("RPM (Requests/Minute)", f"{current_rpm}")
            st.metric("RPD (Requests/Day)", f"{current_rpd}")

            # Add a button to clear session usage stats
            if st.button("Clear Usage Stats", key="clear_google_usage_stats"):
                st.session_state.google_request_timestamps = []
                st.session_state.google_token_usage = []
                save_usage_data() # Clear the file
                st.success("Usage stats cleared")
                # Remove unnecessary rerun - let natural flow handle update

        # Add Admin Panel
        # Check if we should automatically open the Admin Panel
        auto_open_admin = st.session_state.get("open_admin_panel", False)
        if auto_open_admin:
            # Reset the flag
            st.session_state["open_admin_panel"] = False        # Import admin configuration and database helper
        import admin_config
        import db_helper as db
        # Reload the module to ensure we have the latest version with our new function
        import importlib
        importlib.reload(db)

        with st.expander("🔐 Admin Panel", expanded=auto_open_admin):
            st.warning("⚠️ This panel provides administrative functions that can delete data.")

            # Create tabs for different admin functions
            admin_tabs = st.tabs(["Login", "User Management", "Settings", "Database Operations"])

            # Login Tab
            with admin_tabs[0]:
                st.subheader("Admin Login")

                # Username and password inputs
                admin_username = st.text_input("Username", key="admin_username_input")
                admin_password = st.text_input("Password", type="password", key="admin_password_input")

                # Login button with better styling
                col1, col2 = st.columns([1, 3])
                with col1:
                    login_button = st.button("🔐 Login", key="admin_login_button")

                # Handle login button
                if login_button:
                    if admin_username and admin_password:
                        with st.spinner("Logging in..."):
                            if admin_config.verify_user(admin_username, admin_password):
                                if admin_config.is_admin_user(admin_username):
                                    st.session_state["admin_logged_in"] = True
                                    st.session_state["admin_username"] = admin_username
                                    # Set is_admin_logged_in flag to share with test runs tab
                                    st.session_state["is_admin_logged_in"] = True
                                    # Store the admin user in a separate variable for the test runs tab
                                    st.session_state["admin_user"] = admin_username
                                    st.success(f"Logged in as {admin_username}")
                                    st.rerun()  # Keep rerun only for authentication
                                else:
                                    st.error("You do not have admin privileges")
                            else:
                                st.error("Invalid username or password")
                    else:
                        st.error("Username and password are required")

                # Check if a user is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if is_logged_in:
                    st.success(f"Logged in as {logged_in_username}")

                    # Logout button with better styling
                    col1, col2 = st.columns([1, 3])
                    with col1:
                        logout_button = st.button("🚪 Logout", key="admin_logout_button")

                    # Handle logout button
                    if logout_button:
                        with st.spinner("Logging out..."):
                            st.session_state["admin_logged_in"] = False
                            st.session_state["admin_username"] = ""
                            # Clear is_admin_logged_in flag
                            st.session_state["is_admin_logged_in"] = False
                            # Clear admin_user
                            st.session_state["admin_user"] = ""
                            st.rerun()  # Keep rerun only for authentication

            # User Management Tab
            with admin_tabs[1]:
                st.subheader("User Management")

                # Check if admin is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if not is_logged_in:
                    st.warning("Please login as an admin to manage users")
                else:
                    # Display existing users
                    users = admin_config.get_config("users")
                    if users:
                        st.write("Existing Users:")
                        user_data = []
                        for user in users:
                            user_data.append({
                                "Username": user.get("username"),
                                "Role": user.get("role"),
                                "Created": user.get("created_at", "").split("T")[0] if "T" in user.get("created_at", "") else user.get("created_at", "")
                            })
                        st.dataframe(user_data)

                    # Add new user section
                    st.write("---")
                    st.subheader("Add New User")
                    new_username = st.text_input("New Username", key="new_username_input")
                    new_password = st.text_input("New Password", type="password", key="new_password_input")
                    new_role = st.selectbox("Role", options=["user", "admin"], key="new_role_select")

                    # Use columns for better button layout
                    col1, col2 = st.columns([1, 3])
                    with col1:
                        add_user_button = st.button("➕ Add User", key="add_user_button")

                    # Handle add user button
                    if add_user_button:
                        if new_username and new_password:
                            with st.spinner("Adding user..."):
                                success = admin_config.add_user(
                                    new_username,
                                    new_password,
                                    new_role,
                                    logged_in_username,
                                    admin_password
                                )
                                if success:
                                    st.success(f"User '{new_username}' added successfully")
                                    # Remove unnecessary rerun - let natural flow handle update
                                else:
                                    st.error("Failed to add user. Make sure you have admin privileges and the username is unique.")
                        else:
                            st.error("Username and password are required")

                    # Delete user section
                    st.write("---")
                    st.subheader("Delete User")

                    # Get usernames for selection
                    usernames = [user.get("username") for user in users if user.get("username") != logged_in_username]
                    if usernames:
                        delete_username = st.selectbox("Select User to Delete", options=usernames, key="delete_username_select")

                        # Use columns for better button layout
                        col1, col2 = st.columns([1, 3])
                        with col1:
                            delete_user_button = st.button("🗑️ Delete User", key="delete_user_button")

                        # Add confirmation checkbox
                        with col2:
                            if delete_user_button:
                                confirm_delete = st.checkbox("Confirm deletion", key="delete_user_confirm")

                        # Handle delete user button
                        if delete_user_button:
                            if delete_username:
                                if 'confirm_delete' in locals() and confirm_delete:
                                    with st.spinner("Deleting user..."):
                                        success = admin_config.delete_user(
                                            delete_username,
                                            logged_in_username,
                                            admin_password
                                        )
                                        if success:
                                            st.success(f"User '{delete_username}' deleted successfully")
                                            # Remove unnecessary rerun - let natural flow handle update
                                        else:
                                            st.error("Failed to delete user. Make sure you have admin privileges.")
                                else:
                                    st.error("Please confirm deletion by checking the box")
                    else:
                        st.info("No other users to delete")

                    # Change own password section
                    st.write("---")
                    st.subheader("Change Your Password")
                    current_password = st.text_input("Current Password", type="password", key="current_password_input")
                    new_password = st.text_input("New Password", type="password", key="change_new_password_input")
                    confirm_password = st.text_input("Confirm New Password", type="password", key="confirm_new_password_input")

                    # Use columns for better button layout
                    col1, col2 = st.columns([1, 3])
                    with col1:
                        change_pwd_button = st.button("🔄 Change Password", key="change_own_password_button")

                    # Handle change password button
                    if change_pwd_button:
                        if current_password and new_password and confirm_password:
                            if new_password == confirm_password:
                                with st.spinner("Changing password..."):
                                    success = admin_config.change_user_password(
                                        logged_in_username,
                                        current_password,
                                        new_password
                                    )
                                    if success:
                                        st.success("Password changed successfully")
                                    else:
                                        st.error("Failed to change password. Current password may be incorrect.")
                            else:
                                st.error("New passwords do not match")
                        else:
                            st.error("All fields are required")

            # Settings Tab
            with admin_tabs[2]:
                st.subheader("Settings")

                # Check if admin is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if not is_logged_in:
                    st.warning("Please login as an admin to change settings")
                else:
                    # Get current settings
                    allow_delete = admin_config.get_config("allow_delete_test_cases")
                    allow_clear = admin_config.get_config("allow_clear_database")

                    # Display current settings
                    st.info(f"Delete test cases without password: {'Allowed' if allow_delete else 'Requires Admin Password'}")
                    st.info(f"Clear entire database without password: {'Allowed' if allow_clear else 'Requires Admin Password'}")

                    col1, col2 = st.columns(2)

                    with col1:
                        if st.button("Allow Delete Test Cases", key="allow_delete_button", help="Allow deleting test cases without admin password"):
                            admin_config.update_config("allow_delete_test_cases", True)
                            st.success("Setting updated successfully")
                            # Remove unnecessary rerun - let natural flow handle update

                        if st.button("Allow Clear Database", key="allow_clear_button", help="Allow clearing the entire database without admin password"):
                            admin_config.update_config("allow_clear_database", True)
                            st.success("Setting updated successfully")
                            # Remove unnecessary rerun - let natural flow handle update

                    with col2:
                        if st.button("Require Password for Delete", key="require_password_delete_button", help="Require admin password to delete test cases"):
                            admin_config.update_config("allow_delete_test_cases", False)
                            st.success("Setting updated successfully")
                            # Remove unnecessary rerun - let natural flow handle update

                        if st.button("Require Password for Clear", key="require_password_clear_button", help="Require admin password to clear the database"):
                            admin_config.update_config("allow_clear_database", False)
                            st.success("Setting updated successfully")
                            # Remove unnecessary rerun - let natural flow handle update

            # Database Operations Tab
            with admin_tabs[3]:
                st.subheader("Database Operations")

                # Check if admin is logged in
                is_logged_in = st.session_state.get("admin_logged_in", False)
                logged_in_username = st.session_state.get("admin_username", "")

                if not is_logged_in:
                    st.warning("Please login as an admin to perform database operations")
                else:
                    # Create tabs for different deletion operations
                    delete_tabs = st.tabs(["Delete by JIRA ID", "Delete Edited Test Cases", "Delete by Time Range"])

                    # Tab 1: Delete by JIRA ID
                    with delete_tabs[0]:
                        # JIRA ID and test type for deletion
                        delete_jira_id = st.text_input("JIRA ID", key="delete_jira_id_input")
                        delete_test_type = st.selectbox(
                            "Test Type to Delete",
                            options=["all", "positive", "negative", "security", "performance"],
                            key="delete_test_type_select"
                        )

                        # Add some spacing
                        st.write("")

                        # Delete button
                        if st.button("🗑️ Delete Test Cases",
                                    key="delete_test_cases_button",
                                    help="Delete test cases for the specified JIRA ID and test type"):
                            if delete_jira_id:
                                with st.spinner("Deleting test cases..."):
                                    success, message, deletion_counts = db.clear_database_for_jira(db.DATABASE_PATH, delete_jira_id, delete_test_type, admin_password)
                                    if success:
                                        if deletion_counts and any(count > 0 for count in deletion_counts.values()):
                                            st.success(message)
                                        else:
                                            st.info(message)
                                    else:
                                        st.error(message)
                            else:
                                st.error("Please enter a JIRA ID")

                    # Tab 2: Delete Edited Test Cases
                    with delete_tabs[1]:
                        st.info("This will delete test cases that are marked as edited (is_edited = 1). These are typically test cases that have been uploaded through the 'Upload Edited Excel' feature.")

                        # JIRA ID for deletion (optional)
                        delete_edited_jira_id = st.text_input("JIRA ID (optional, leave empty to delete all edited test cases)", key="delete_edited_jira_id_input")

                        # Add some spacing
                        st.write("")

                        # Delete button - using the updated approach with better return handling
                        if st.button("🗑️ Delete Edited Test Cases",
                                   key="delete_edited_test_cases_button",
                                   help="Delete test cases marked as edited (is_edited = 1)"):
                            if delete_edited_jira_id:
                                # Delete edited test cases for specific JIRA ID
                                with st.spinner("Deleting edited test cases..."):
                                    success, message, deleted_count = db.delete_edited_test_cases(db.DATABASE_PATH, delete_edited_jira_id, admin_password)
                                    if success:
                                        if deleted_count > 0:
                                            st.success(message)
                                        else:
                                            st.info(message)
                                    else:
                                        st.error(message)
                            else:
                                # Delete all edited test cases
                                with st.spinner("Deleting all edited test cases..."):
                                    success, message, deleted_count = db.delete_all_edited_test_cases(db.DATABASE_PATH, admin_password)
                                    if success:
                                        if deleted_count > 0:
                                            st.success(message)
                                        else:
                                            st.info(message)
                                    else:
                                        st.error(message)

                    # Tab 3: Delete by Time Range
                    with delete_tabs[2]:
                        st.info("Delete test cases created within a specific time range.")

                        # Date range selection
                        col1, col2 = st.columns(2)
                        with col1:
                            start_date = st.date_input("Start Date", key="delete_start_date")
                        with col2:
                            end_date = st.date_input("End Date", key="delete_end_date")

                        # Confirmation checkbox
                        confirm_delete_by_date = st.checkbox("I confirm that I want to delete test cases in this date range", key="delete_by_date_confirm")

                        # Add some spacing
                        st.write("")

                        # Delete button
                        delete_by_date_button = st.button("🗑️ Delete Test Cases by Date",
                                                    key="delete_by_date_button",
                                                    help="Delete test cases created within the specified date range")

                        # Handle delete by date button
                        if delete_by_date_button:
                            if not start_date or not end_date:
                                st.error("Please select both start and end dates")
                            elif start_date > end_date:
                                st.error("Start date must be before or equal to end date")
                            elif not confirm_delete_by_date:
                                st.error("Please check the confirmation checkbox to delete test cases")
                            else:
                                with st.spinner("Deleting test cases by date..."):
                                    # Convert dates to strings in the format expected by the database function
                                    start_datetime_str = f"{start_date.strftime('%Y-%m-%d')} 00:00:00"
                                    end_datetime_str = f"{end_date.strftime('%Y-%m-%d')} 23:59:59"

                                    # Get admin password if needed
                                    admin_password = st.session_state.get("admin_password_input", "")

                                    # Call the database function to delete test cases by date range
                                    success, message, deletion_counts = db.delete_test_cases_by_time_range(
                                        db.DATABASE_PATH, 
                                        start_datetime_str, 
                                        end_datetime_str,
                                        admin_password if admin_password else None
                                    )

                                    if success:
                                        if deletion_counts and any(count > 0 for count in deletion_counts.values()):
                                            st.success(message)
                                        else:
                                            st.info(message)
                                    else:
                                        st.error(message)

                    # Add a separator
                    st.markdown("---")

                    # Clear Entire Database section
                    st.subheader("Clear Entire Database")
                    st.warning("⚠️ This will delete ALL test cases, test runs, and JIRA issues in the database. This action cannot be undone.")

                    # Simplified single-click approach with confirmation checkbox
                    confirm_clear_database = st.checkbox(
                        "I understand that this action will permanently delete ALL data and cannot be undone",
                        key="confirm_clear_entire_database",
                        value=False
                    )

                    # Single clear button that works immediately when confirmed
                    if st.button("⚠️ Clear Entire Database", 
                               key="clear_entire_database_button",
                               disabled=not confirm_clear_database,
                               help="Check the confirmation box above to enable this button"):
                        
                        # Get admin password if needed
                        admin_password = st.session_state.get("admin_password_input", "")
                        
                        with st.spinner("Clearing entire database..."):
                            try:
                                # Use the direct database clearing function
                                from db_helper.operations.admin_operations import clear_database
                                
                                success = clear_database(db.DATABASE_PATH, admin_password if admin_password else None)
                                
                                if success:
                                    st.success("✅ Database cleared successfully!")
                                    st.info("🔄 Please refresh the page or navigate to see the updated state.")
                                else:
                                    st.error("❌ Failed to clear database. Please check the logs for details.")
                                    st.info("💡 Make sure you have admin privileges or try providing the admin password.")
                                    
                            except Exception as e:
                                st.error(f"❌ Error clearing database: {str(e)}")
                                st.info("💡 Please check the logs for more detailed error information.")

    # Return persistent session state values to ensure consistency across modules
    return st.session_state["ai_provider_persistent"], google_api_key, st.session_state["selected_model_persistent"]