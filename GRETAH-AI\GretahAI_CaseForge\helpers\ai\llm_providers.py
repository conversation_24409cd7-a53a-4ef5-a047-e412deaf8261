"""
LLM Providers Module for GretahAI CaseForge

This module handles integration with multiple Large Language Model providers
including Ollama (local) and Cloud (cloud-based) for test case generation.

Functions:
- run_ollama_with_chat: Execute prompts using local Ollama models
- run_google_ai_studio: Execute prompts using Cloud API
- is_ollama_running: Check Ollama service availability
- modify_test_cases_with_ai: Modify existing test cases based on user queries

© 2025 GretahAI Team
"""

import requests
import json
import pandas as pd
from ollama import chat, ChatResponse
import google.generativeai as genai


def run_ollama_with_chat(prompt: str, model: str = "mistral", temperature: float = None) -> str:
    """
    Executes AI prompt using Ollama local LLM with configurable parameters.
    
    This function provides interface to Ollama's local language models for test case
    generation. It handles model communication, response processing, and error management
    for local AI inference without external API dependencies.

    Args:
        prompt (str): The input prompt for the AI model containing test requirements
        model (str, optional): Ollama model name to use. Defaults to "mistral".
                              Common options: "mistral", "llama2", "codellama"
        temperature (float, optional): Sampling temperature for response variability.
                                     Range: 0.0 (deterministic) to 1.0 (creative).
                                     Defaults to None (uses model default).

    Returns:
        str: Generated response from the AI model, or error message if generation fails

    Model Configuration:
        - Uses Ollama's chat interface for conversational context
        - Supports temperature control for output creativity
        - Handles model-specific configurations
        - Manages context and conversation flow

    Response Processing:
        - Extracts content from ChatResponse object
        - Handles different response formats
        - Manages streaming and non-streaming responses
        - Processes model-specific output structures

    Error Handling:
        - Catches and handles Ollama connection errors
        - Returns descriptive error messages
        - Manages model loading failures
        - Handles timeout and response issues

    Performance Considerations:
        - Local inference - no external API calls
        - Model loading time on first use
        - Memory requirements for larger models
        - CPU/GPU utilization optimization

    Example:
        # Basic usage with default model
        response = run_ollama_with_chat("Generate test cases for login")
        
        # With specific model and temperature
        response = run_ollama_with_chat(
            prompt="Generate negative test cases", 
            model="llama2", 
            temperature=0.7
        )
        
        if "Error" not in response:
            print("Generated test cases:", response)
    """
    try:        
        # If temperature is provided, try to use it with the API
        if temperature is not None:
            try:
                # Try with temperature parameter
                response: ChatResponse = chat(
                    model=model,
                    messages=[{'role': 'user', 'content': prompt}],
                    temperature=temperature
                )
                return response.message.content
            except TypeError:
                # If temperature parameter not supported, just run without it silently
                response: ChatResponse = chat(
                    model=model,
                    messages=[{'role': 'user', 'content': prompt}]
                )
                return response.message.content
        else:
            # If no temperature is provided, just use the standard API call
            response: ChatResponse = chat(
                model=model,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt,
                    },
                ]
            )
            return response.message.content
    except Exception as e:
        error_msg = f"Error from Ollama: {str(e)}"
        print(error_msg)
        return error_msg


def run_google_ai_studio(prompt, api_key: str, model: str = "gemini-2.0-flash", temperature: float = None):
    """
    Executes AI prompt using Cloud API with advanced model capabilities.

    This function provides interface to Google's Gemini models through the Cloud API,
    offering state-of-the-art language understanding and generation capabilities for
    sophisticated test case creation with superior context awareness.

    Args:
        prompt (str): Input prompt containing test case generation requirements
        api_key (str): Cloud API key for authentication
        model (str, optional): Gemini model variant to use. Defaults to "gemini-2.0-flash".
                              Options: "gemini-2.0-flash", "gemini-pro", "gemini-pro-vision"
        temperature (float, optional): Controls response creativity and randomness.
                                     Range: 0.0 (deterministic) to 1.0 (creative).
                                     Defaults to None (uses API default).

    Returns:
        str: Generated AI response content, or detailed error message if request fails

    API Configuration:
        - Configures Google Generative AI client with provided API key
        - Sets up model instance with specified parameters
        - Handles authentication and connection setup
        - Manages request headers and API versioning

    Model Features:
        - Advanced language understanding and reasoning
        - Superior context retention and coherence
        - Multimodal capabilities (text, images) depending on model
        - High-quality structured output generation

    Response Processing:
        - Extracts text content from API response
        - Handles structured response formats
        - Manages response validation and cleanup
        - Processes potential multipart responses

    Error Handling:
        - Comprehensive API error catching and reporting
        - Authentication failure management
        - Rate limiting and quota handling
        - Network connectivity error management
        - Invalid API key detection

    Safety and Compliance:
        - Handles Google's safety filtering
        - Manages content policy restrictions
        - Processes blocked or filtered responses
        - Maintains compliance with usage policies

    Performance Optimization:
        - Efficient API request structuring
        - Response caching where appropriate
        - Connection pooling and reuse
        - Timeout and retry management

    Example:
        # Basic usage with API key
        api_key = "your-google-ai-studio-key"
        response = run_google_ai_studio(
            "Generate comprehensive test cases for user authentication",
            api_key
        )
        
        # With specific model and creativity setting
        response = run_google_ai_studio(
            prompt="Create security test scenarios",
            api_key=api_key,
            model="gemini-pro",
            temperature=0.3
        )
        
        if not response.startswith("Error"):
            print("Generated test cases:", response)
    """
    try:
        # Debug logging for API key
        print(f"🔧 DEBUG: run_google_ai_studio called with:")
        print(f"   - API Key Length: {len(api_key) if api_key else 0}")
        print(f"   - API Key Present: {'Yes' if api_key else 'No'}")
        if not api_key:
            print("   - API Key is None or empty!")

        # Configure the API key
        genai.configure(api_key=api_key)

        # Create the model instance
        model_instance = genai.GenerativeModel(model)

        # Only create config with temperature for JIRA enhancement prompts
        # Check specifically for the JIRA enhancement identifier
        generation_config = None
        if temperature is not None and prompt and isinstance(prompt, str) and "Role: You are an expert technical writer" in prompt:
            generation_config = genai.GenerationConfig(temperature=temperature)

        # Check if prompt is a tuple containing an image and text
        if isinstance(prompt, tuple) and len(prompt) == 2:
            image, text_prompt = prompt
            # Use multimodal input (image + text) WITHOUT temperature
            response = model_instance.generate_content(
                contents=prompt,
                generation_config=None  # No temperature for test case generation
            )
        else:
            # Regular text generation - only use temperature config for JIRA enhancement
            response = model_instance.generate_content(
                contents=prompt,
                generation_config=generation_config  # Will be None for test case generation
            )

        # Get token count if available
        token_count = None
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            token_count = response.usage_metadata.total_token_count

        return response.text, token_count

    except Exception as e:
        error_msg = f"Error from Cloud: {str(e)}"
        print(error_msg)
        return error_msg, 0


def is_ollama_running():
    """
    Checks if Ollama service is running and accessible for local AI inference.
    
    This function performs a health check on the Ollama service to determine if it's
    available for processing AI requests. It's used to validate the local AI setup
    before attempting test case generation with Ollama models.

    Returns:
        bool: True if Ollama is running and responsive, False otherwise

    Health Check Process:
        - Attempts HTTP connection to Ollama API endpoint
        - Verifies service responsiveness
        - Validates basic API functionality
        - Handles connection timeouts gracefully

    Network Configuration:
        - Uses default Ollama port and endpoint
        - Supports local and network Ollama instances
        - Handles firewall and proxy configurations
        - Manages connection timeouts appropriately

    Error Handling:
        - Returns False for any connection issues
        - Handles network timeouts gracefully
        - Manages service unavailability
        - Logs errors for troubleshooting

    Use Cases:
        - Pre-validation before AI generation
        - Service health monitoring
        - Provider selection logic
        - User interface status indication

    Example:
        if is_ollama_running():
            print("Ollama service available")
            # Proceed with Ollama-based generation
        else:
            print("Ollama service unavailable")
            # Use alternative AI provider
    """
    try:
        response = requests.get("http://localhost:11434/api/version", timeout=2)
        return response.status_code == 200, response.json().get("version", "unknown")
    except:
        return False, "not available"


def modify_test_cases_with_ai(
    test_cases_df: pd.DataFrame, 
    jira_description: str, 
    user_query: str, 
    model: str = "gemini-2.0-flash", 
    google_api_key: str = None
) -> tuple:
    """
    Modify existing test cases using AI based on user queries.
    This function now uses the unified prompt from generate_test_case_modification_prompt.
    Args:
        test_cases_df (pd.DataFrame): Existing test cases dataframe
        jira_description (str): Original JIRA issue description/summary
        user_query (str): User's request for modifications
        model (str): Google Gemini model name to use
        google_api_key (str): Google AI API key (required)
    Returns:
        tuple: (success: bool, modified_df: pd.DataFrame, error_message: str)
    """
    try:
        from helpers.ai.prompt_generation import generate_test_case_modification_prompt
        # Generate the prompt using the new function
        prompt = generate_test_case_modification_prompt(test_cases_df, jira_description, user_query)
        # Always use Cloud
        response, _ = run_google_ai_studio(prompt, google_api_key, model)
        # Parse the AI response
        try:
            response_clean = response.strip()
            start_idx = response_clean.find('[')
            end_idx = response_clean.rfind(']') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_clean[start_idx:end_idx]
                modified_data = json.loads(json_str)
                modified_df = pd.DataFrame(modified_data)
                
                # Preserve original column order and data types
                for col in test_cases_df.columns:
                    if col not in modified_df.columns:
                        # Add missing columns with the same data type as original
                        if col in test_cases_df.columns:
                            # Use the original column's data to preserve dtype
                            original_series = test_cases_df[col]
                            if original_series.dtype == 'object':
                                modified_df[col] = ""
                            elif original_series.dtype in ['int64', 'int32']:
                                modified_df[col] = 0
                            elif original_series.dtype in ['float64', 'float32']:
                                modified_df[col] = 0.0
                            else:
                                modified_df[col] = ""
                        else:
                            modified_df[col] = ""
                
                # Reorder columns to match original DataFrame
                modified_df = modified_df[test_cases_df.columns]
                
                # Preserve data types for read-only fields that shouldn't change
                readonly_fields = ['ai_modified', 'modification_source', 'ai_model_used', 
                                 'ai_modification_timestamp', 'ai_modification_user', 'ai_user_query']
                
                for field in readonly_fields:
                    if field in test_cases_df.columns and field in modified_df.columns:
                        # Copy the original values exactly to preserve data type and content
                        modified_df[field] = test_cases_df[field].copy()
                
                return True, modified_df, None
            else:
                return False, test_cases_df, "AI response did not contain valid JSON array"
        except json.JSONDecodeError as e:
            return False, test_cases_df, f"Failed to parse AI response as JSON: {str(e)}"
    except Exception as e:
        return False, test_cases_df, f"Error during AI modification: {str(e)}"
