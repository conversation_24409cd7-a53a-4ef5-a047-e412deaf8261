"""
File Security Validator for GretahAI ScriptWeaver

This module provides comprehensive security validation for uploaded files,
including malware detection, content analysis, and security policy enforcement.

Key Features:
- Advanced file type validation beyond MIME types
- Content-based security scanning
- File size and structure validation
- Malicious content detection
- Security policy enforcement

© 2025 Cogniron All Rights Reserved.
"""

import os
import hashlib
import mimetypes
import zipfile
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import re

# Import GRETAH standardized logging
from debug_utils import debug


class FileSecurityValidator:
    """
    Advanced security validator for uploaded files.
    
    Provides comprehensive security checks including content analysis,
    malware detection patterns, and security policy enforcement.
    """
    
    # Maximum file sizes by category (in bytes)
    MAX_FILE_SIZES = {
        'images': 10 * 1024 * 1024,      # 10MB
        'documents': 25 * 1024 * 1024,   # 25MB
        'data_files': 50 * 1024 * 1024,  # 50MB
        'archives': 100 * 1024 * 1024    # 100MB
    }
    
    # Suspicious file signatures (magic bytes)
    MALICIOUS_SIGNATURES = [
        b'\x4d\x5a',                    # PE executable
        b'\x7f\x45\x4c\x46',           # ELF executable
        b'\xfe\xed\xfa\xce',           # Mach-O executable (32-bit)
        b'\xfe\xed\xfa\xcf',           # Mach-O executable (64-bit)
        b'\xca\xfe\xba\xbe',           # Java class file
        b'\x50\x4b\x03\x04',           # ZIP (check contents separately)
    ]
    
    # Suspicious content patterns
    MALICIOUS_PATTERNS = [
        # Script injection patterns
        rb'<script[^>]*>.*?</script>',
        rb'javascript:',
        rb'vbscript:',
        rb'data:text/html',
        
        # Command injection patterns
        rb'cmd\.exe',
        rb'powershell',
        rb'/bin/sh',
        rb'/bin/bash',
        rb'system\(',
        rb'exec\(',
        rb'eval\(',
        
        # SQL injection patterns
        rb'union\s+select',
        rb'drop\s+table',
        rb'delete\s+from',
        rb'insert\s+into',
        
        # Path traversal patterns
        rb'\.\./\.\.',
        rb'\.\.\\\.\.\\',
        rb'%2e%2e%2f',
        rb'%2e%2e%5c',
    ]
    
    # Forbidden file extensions
    FORBIDDEN_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js', '.jar',
        '.app', '.deb', '.pkg', '.dmg', '.run', '.msi', '.ps1', '.sh', '.bash',
        '.php', '.asp', '.aspx', '.jsp', '.cgi', '.pl', '.py', '.rb'
    }
    
    def __init__(self):
        """Initialize the FileSecurityValidator."""
        self.quarantine_dir = Path.cwd() / "quarantine"
        self.quarantine_dir.mkdir(exist_ok=True)
        
    def validate_file_security(self, file_content: bytes, filename: str, 
                             file_category: str) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        Perform comprehensive security validation on a file.
        
        Args:
            file_content: File content as bytes
            filename: Original filename
            file_category: File category (images, documents, etc.)
            
        Returns:
            Tuple of (is_safe, security_warnings, security_report)
        """
        security_warnings = []
        security_report = {
            'filename': filename,
            'file_size': len(file_content),
            'file_category': file_category,
            'checks_performed': [],
            'threats_detected': [],
            'risk_level': 'low'
        }
        
        try:
            debug(f"Starting security validation for file: {filename}",
                  stage="file_security", operation="validation_start",
                  context={'filename': filename, 'file_size': len(file_content)})
            
            # 1. File extension validation
            is_safe, warnings = self._validate_file_extension(filename)
            security_warnings.extend(warnings)
            security_report['checks_performed'].append('file_extension')
            if not is_safe:
                security_report['threats_detected'].append('forbidden_extension')
                security_report['risk_level'] = 'high'
            
            # 2. File size validation
            is_safe, warnings = self._validate_file_size(file_content, file_category)
            security_warnings.extend(warnings)
            security_report['checks_performed'].append('file_size')
            if not is_safe:
                security_report['threats_detected'].append('oversized_file')
                security_report['risk_level'] = 'medium'
            
            # 3. Magic byte validation
            is_safe, warnings = self._validate_magic_bytes(file_content, filename)
            security_warnings.extend(warnings)
            security_report['checks_performed'].append('magic_bytes')
            if not is_safe:
                security_report['threats_detected'].append('suspicious_signature')
                security_report['risk_level'] = 'high'
            
            # 4. Content pattern analysis
            is_safe, warnings = self._analyze_content_patterns(file_content)
            security_warnings.extend(warnings)
            security_report['checks_performed'].append('content_patterns')
            if not is_safe:
                security_report['threats_detected'].append('malicious_content')
                security_report['risk_level'] = 'high'
            
            # 5. Archive content validation (if applicable)
            if filename.lower().endswith('.zip'):
                is_safe, warnings = self._validate_archive_content(file_content)
                security_warnings.extend(warnings)
                security_report['checks_performed'].append('archive_content')
                if not is_safe:
                    security_report['threats_detected'].append('malicious_archive')
                    security_report['risk_level'] = 'high'
            
            # 6. File structure validation
            is_safe, warnings = self._validate_file_structure(file_content, filename)
            security_warnings.extend(warnings)
            security_report['checks_performed'].append('file_structure')
            if not is_safe:
                security_report['threats_detected'].append('corrupted_structure')
                security_report['risk_level'] = 'medium'
            
            # Determine overall safety
            overall_safe = len(security_report['threats_detected']) == 0
            
            # Log security validation results
            debug(f"Security validation completed for {filename}",
                  stage="file_security", operation="validation_complete",
                  context={
                      'filename': filename,
                      'is_safe': overall_safe,
                      'warnings_count': len(security_warnings),
                      'threats_count': len(security_report['threats_detected']),
                      'risk_level': security_report['risk_level']
                  })
            
            return overall_safe, security_warnings, security_report
            
        except Exception as e:
            error_msg = f"Security validation error: {str(e)}"
            security_warnings.append(error_msg)
            security_report['threats_detected'].append('validation_error')
            security_report['risk_level'] = 'high'
            
            debug(f"Security validation error for {filename}: {e}",
                  stage="file_security", operation="validation_error",
                  context={'filename': filename, 'error': str(e)})
            
            return False, security_warnings, security_report
    
    def _validate_file_extension(self, filename: str) -> Tuple[bool, List[str]]:
        """Validate file extension against forbidden list."""
        file_ext = Path(filename).suffix.lower()
        warnings = []
        
        if file_ext in self.FORBIDDEN_EXTENSIONS:
            warnings.append(f"Forbidden file extension: {file_ext}")
            return False, warnings
        
        # Check for double extensions (e.g., .txt.exe)
        if len(Path(filename).suffixes) > 1:
            all_extensions = [ext.lower() for ext in Path(filename).suffixes]
            for ext in all_extensions:
                if ext in self.FORBIDDEN_EXTENSIONS:
                    warnings.append(f"Hidden forbidden extension detected: {ext}")
                    return False, warnings
        
        return True, warnings
    
    def _validate_file_size(self, file_content: bytes, file_category: str) -> Tuple[bool, List[str]]:
        """Validate file size against category limits."""
        file_size = len(file_content)
        max_size = self.MAX_FILE_SIZES.get(file_category, 10 * 1024 * 1024)  # Default 10MB
        warnings = []
        
        if file_size > max_size:
            warnings.append(f"File too large: {file_size:,} bytes (max: {max_size:,} bytes)")
            return False, warnings
        
        # Check for suspiciously small files that claim to be certain types
        if file_size < 100:  # Less than 100 bytes
            warnings.append("File suspiciously small - may be malformed")
        
        return True, warnings
    
    def _validate_magic_bytes(self, file_content: bytes, filename: str) -> Tuple[bool, List[str]]:
        """Validate file magic bytes against known malicious signatures."""
        warnings = []
        
        # Check against malicious signatures
        for signature in self.MALICIOUS_SIGNATURES:
            if file_content.startswith(signature):
                warnings.append(f"Malicious file signature detected: {signature.hex()}")
                return False, warnings
        
        # Validate that file content matches expected type based on extension
        file_ext = Path(filename).suffix.lower()
        expected_mime, _ = mimetypes.guess_type(filename)
        
        # Basic magic byte validation for common file types
        magic_byte_checks = {
            '.jpg': [b'\xff\xd8\xff'],
            '.jpeg': [b'\xff\xd8\xff'],
            '.png': [b'\x89\x50\x4e\x47'],
            '.gif': [b'\x47\x49\x46\x38'],
            '.pdf': [b'%PDF-'],
            '.zip': [b'\x50\x4b\x03\x04', b'\x50\x4b\x05\x06', b'\x50\x4b\x07\x08']
        }
        
        if file_ext in magic_byte_checks:
            expected_signatures = magic_byte_checks[file_ext]
            if not any(file_content.startswith(sig) for sig in expected_signatures):
                warnings.append(f"File content doesn't match extension {file_ext}")
                # This is a warning, not necessarily malicious
        
        return True, warnings
    
    def _analyze_content_patterns(self, file_content: bytes) -> Tuple[bool, List[str]]:
        """Analyze file content for malicious patterns."""
        warnings = []
        
        # Convert to lowercase for pattern matching
        content_lower = file_content.lower()
        
        for pattern in self.MALICIOUS_PATTERNS:
            if re.search(pattern, content_lower, re.IGNORECASE | re.DOTALL):
                warnings.append(f"Suspicious content pattern detected")
                return False, warnings
        
        # Check for excessive null bytes (potential padding attack)
        null_byte_count = file_content.count(b'\x00')
        if null_byte_count > len(file_content) * 0.5:  # More than 50% null bytes
            warnings.append("Excessive null bytes detected - potential padding attack")
            return False, warnings
        
        return True, warnings
    
    def _validate_archive_content(self, file_content: bytes) -> Tuple[bool, List[str]]:
        """Validate contents of archive files."""
        warnings = []
        
        try:
            # Create a temporary file to analyze the ZIP
            import tempfile
            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(file_content)
                temp_file.flush()
                
                with zipfile.ZipFile(temp_file.name, 'r') as zip_file:
                    # Check for zip bombs (excessive compression ratio)
                    total_uncompressed = sum(info.file_size for info in zip_file.filelist)
                    total_compressed = sum(info.compress_size for info in zip_file.filelist)
                    
                    if total_compressed > 0:
                        compression_ratio = total_uncompressed / total_compressed
                        if compression_ratio > 100:  # Suspicious compression ratio
                            warnings.append(f"Suspicious compression ratio: {compression_ratio:.1f}:1")
                            return False, warnings
                    
                    # Check individual files in the archive
                    for info in zip_file.filelist:
                        # Check for path traversal in filenames
                        if '..' in info.filename or info.filename.startswith('/'):
                            warnings.append(f"Path traversal detected in archive: {info.filename}")
                            return False, warnings
                        
                        # Check for forbidden file types in archive
                        file_ext = Path(info.filename).suffix.lower()
                        if file_ext in self.FORBIDDEN_EXTENSIONS:
                            warnings.append(f"Forbidden file type in archive: {info.filename}")
                            return False, warnings
                        
                        # Check for excessively large files in archive
                        if info.file_size > 500 * 1024 * 1024:  # 500MB per file
                            warnings.append(f"Oversized file in archive: {info.filename}")
                            return False, warnings
        
        except zipfile.BadZipFile:
            warnings.append("Corrupted or invalid ZIP file")
            return False, warnings
        except Exception as e:
            warnings.append(f"Error analyzing archive: {str(e)}")
            return False, warnings
        
        return True, warnings
    
    def _validate_file_structure(self, file_content: bytes, filename: str) -> Tuple[bool, List[str]]:
        """Validate file structure integrity."""
        warnings = []
        file_ext = Path(filename).suffix.lower()
        
        try:
            # Basic structure validation for common file types
            if file_ext in ['.jpg', '.jpeg']:
                # JPEG should end with FFD9
                if not file_content.endswith(b'\xff\xd9'):
                    warnings.append("JPEG file appears to be truncated or corrupted")
            
            elif file_ext == '.png':
                # PNG should end with IEND chunk
                if not file_content.endswith(b'IEND\xaeB`\x82'):
                    warnings.append("PNG file appears to be corrupted")
            
            elif file_ext == '.pdf':
                # PDF should contain %%EOF
                if b'%%EOF' not in file_content:
                    warnings.append("PDF file appears to be corrupted")
            
            # Check for embedded files or suspicious structures
            if b'Content-Type:' in file_content and file_ext not in ['.eml', '.msg']:
                warnings.append("File contains MIME headers - possible email attachment")
            
        except Exception as e:
            warnings.append(f"Error validating file structure: {str(e)}")
        
        return True, warnings  # Structure issues are warnings, not blocking
    
    def quarantine_file(self, file_content: bytes, filename: str, 
                       security_report: Dict[str, Any]) -> str:
        """
        Quarantine a suspicious file for further analysis.
        
        Args:
            file_content: File content as bytes
            filename: Original filename
            security_report: Security validation report
            
        Returns:
            Path to quarantined file
        """
        try:
            # Generate unique quarantine filename
            file_hash = hashlib.sha256(file_content).hexdigest()[:16]
            quarantine_filename = f"quarantine_{file_hash}_{filename}"
            quarantine_path = self.quarantine_dir / quarantine_filename
            
            # Save file to quarantine
            with open(quarantine_path, 'wb') as f:
                f.write(file_content)
            
            # Save security report
            report_path = self.quarantine_dir / f"report_{file_hash}.json"
            with open(report_path, 'w') as f:
                json.dump(security_report, f, indent=2)
            
            debug(f"File quarantined: {filename}",
                  stage="file_security", operation="quarantine",
                  context={
                      'filename': filename,
                      'quarantine_path': str(quarantine_path),
                      'threats': security_report.get('threats_detected', [])
                  })
            
            return str(quarantine_path)
            
        except Exception as e:
            debug(f"Error quarantining file {filename}: {e}",
                  stage="file_security", operation="quarantine_error",
                  context={'filename': filename, 'error': str(e)})
            raise
    
    def get_security_recommendations(self, security_report: Dict[str, Any]) -> List[str]:
        """
        Get security recommendations based on validation report.
        
        Args:
            security_report: Security validation report
            
        Returns:
            List of security recommendations
        """
        recommendations = []
        threats = security_report.get('threats_detected', [])
        risk_level = security_report.get('risk_level', 'low')
        
        if 'forbidden_extension' in threats:
            recommendations.append("Use only approved file types for uploads")
        
        if 'oversized_file' in threats:
            recommendations.append("Reduce file size or split into smaller files")
        
        if 'suspicious_signature' in threats:
            recommendations.append("File appears to be executable - use document or image files only")
        
        if 'malicious_content' in threats:
            recommendations.append("File contains suspicious content - scan with antivirus software")
        
        if 'malicious_archive' in threats:
            recommendations.append("Archive contains unsafe files - extract and validate contents manually")
        
        if risk_level == 'high':
            recommendations.append("⚠️ HIGH RISK: Do not use this file for testing")
        elif risk_level == 'medium':
            recommendations.append("⚠️ MEDIUM RISK: Use with caution")
        
        if not recommendations:
            recommendations.append("✅ File appears safe for testing use")
        
        return recommendations
