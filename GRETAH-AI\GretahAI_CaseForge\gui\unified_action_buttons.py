"""
Action buttons for the unified test cases dashboard.

This module provides UI components for CRUD operations including:
- Add Test Case button with form dialog
- Add Test Step button with selection and form
- Delete Test Case button with confirmation dialog
- Delete Test Step button with selection and confirmation

All components follow the existing design patterns and integrate with the database-first approach.
"""

import streamlit as st
import pandas as pd
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime

# Import database operations
import db_helper as db
from db_helper.operations.crud_operations import (
    add_new_test_case,
    add_test_step_to_case,
    delete_test_step,
    delete_test_case_with_confirmation,
    get_test_case_info,
    validate_test_case_data,
    validate_test_step_data,
    get_test_case_for_cloning,
    clone_test_case
)

# Import configuration for session state keys
from .unified_interface_config import get_ui_filter_key, get_applied_filter_key


def get_smart_defaults_for_new_test_case(filtered_data: pd.DataFrame) -> Dict[str, str]:
    """
    Extract smart defaults for new test case creation from filtered data and session state.

    Args:
        filtered_data: Current filtered test case data

    Returns:
        Dict[str, str]: Dictionary of smart default values
    """
    defaults = {}

    # Get current filter values from session state
    current_jira_id = st.session_state.get(get_ui_filter_key("jira_id"), "")
    current_test_type = st.session_state.get(get_ui_filter_key("test_type"), "positive")
    current_user = st.session_state.get("current_user", "test_user")

    # Extract project from filtered data or session state
    project_value = ""
    if not filtered_data.empty and 'Project' in filtered_data.columns:
        # Get most common project from filtered data
        projects = filtered_data['Project'].dropna()
        if not projects.empty:
            project_counts = projects.value_counts()
            if len(project_counts) > 0:
                project_value = project_counts.index[0]  # Most common project

    # Fallback to session state or applied filter
    if not project_value:
        project_value = st.session_state.get(get_ui_filter_key("project"), "")
        if not project_value or project_value == "all":
            project_value = st.session_state.get(get_applied_filter_key("project"), "")

    # Extract feature from filtered data or session state
    feature_value = ""
    if not filtered_data.empty and 'Feature' in filtered_data.columns:
        # Get most common feature from filtered data
        features = filtered_data['Feature'].dropna()
        if not features.empty:
            feature_counts = features.value_counts()
            if len(feature_counts) > 0:
                feature_value = feature_counts.index[0]  # Most common feature

    # Fallback to session state or applied filter
    if not feature_value:
        feature_value = st.session_state.get(get_ui_filter_key("feature"), "")
        if not feature_value or feature_value == "all":
            feature_value = st.session_state.get(get_applied_filter_key("feature"), "")

    defaults = {
        'jira_id': current_jira_id if current_jira_id and current_jira_id != "all" else "",
        'test_type': current_test_type if current_test_type != "all" else "positive",
        'user_name': current_user,
        'project': project_value if project_value and project_value != "all" else "",
        'feature': feature_value if feature_value and feature_value != "all" else ""
    }

    return defaults


def render_action_buttons_section(filtered_data: pd.DataFrame) -> bool:
    """
    Render the action buttons section for CRUD operations.
    
    Args:
        filtered_data: Current filtered test case data
    
    Returns:
        bool: True if any action was performed that requires data refresh
    """
    st.markdown("---")
    st.subheader("🔧 Test Case Management Actions")
    
    # Create columns for action buttons
    col1, col2, col3, col4, col5 = st.columns(5)
    
    action_performed = False
    
    with col1:
        if st.button("➕ Add Test Case",
                    help="Create a new test case with proper ID generation",
                    use_container_width=True,
                    type="primary",
                    key="action_btn_add_test_case"):
            st.session_state["show_add_test_case_dialog"] = True

    with col2:
        # Only enable if we have test cases
        disabled = filtered_data.empty
        if st.button("📝 Add Test Step",
                    help="Add a new test step to an existing test case" if not disabled else "No test cases available",
                    use_container_width=True,
                    disabled=disabled,
                    key="action_btn_add_test_step"):
            st.session_state["show_add_test_step_dialog"] = True

    with col3:
        # Only enable if we have test cases
        disabled = filtered_data.empty
        if st.button("🗑️ Delete Test Case",
                    help="Delete an entire test case and all its steps" if not disabled else "No test cases available",
                    use_container_width=True,
                    disabled=disabled,
                    key="action_btn_delete_test_case"):
            st.session_state["show_delete_test_case_dialog"] = True

    with col4:
        # Only enable if we have test cases
        disabled = filtered_data.empty
        if st.button("❌ Delete Test Step",
                    help="Delete individual test steps from test cases" if not disabled else "No test cases available",
                    use_container_width=True,
                    disabled=disabled,
                    key="action_btn_delete_test_step"):
            st.session_state["show_delete_test_step_dialog"] = True

    with col5:
        # Only enable if we have test cases
        disabled = filtered_data.empty
        if st.button("📋 Clone Test Case",
                    help="Create a copy of an existing test case with new ID" if not disabled else "No test cases available",
                    use_container_width=True,
                    disabled=disabled,
                    key="action_btn_clone_test_case"):
            st.session_state["show_clone_test_case_dialog"] = True
    
    # Handle dialog states
    if st.session_state.get("show_add_test_case_dialog", False):
        action_performed = render_add_test_case_dialog(filtered_data)
    
    if st.session_state.get("show_add_test_step_dialog", False):
        action_performed = render_add_test_step_dialog(filtered_data) or action_performed
    
    if st.session_state.get("show_delete_test_case_dialog", False):
        action_performed = render_delete_test_case_dialog(filtered_data) or action_performed
    
    if st.session_state.get("show_delete_test_step_dialog", False):
        action_performed = render_delete_test_step_dialog(filtered_data) or action_performed

    if st.session_state.get("show_clone_test_case_dialog", False):
        action_performed = render_clone_test_case_dialog(filtered_data) or action_performed

    return action_performed


def render_add_test_case_dialog(filtered_data: pd.DataFrame = None) -> bool:
    """
    Render the Add Test Case dialog with form fields and smart defaults.

    Args:
        filtered_data: Current filtered test case data for smart defaults

    Returns:
        bool: True if test case was added successfully
    """
    with st.expander("➕ Add New Test Case", expanded=True):
        st.markdown("**Create a new test case with proper ID generation**")

        # Get smart defaults from filtered data and session state
        if filtered_data is None:
            filtered_data = pd.DataFrame()

        smart_defaults = get_smart_defaults_for_new_test_case(filtered_data)
        current_jira_id = smart_defaults['jira_id']
        current_test_type = smart_defaults['test_type']
        current_user = smart_defaults['user_name']
        smart_project = smart_defaults['project']
        smart_feature = smart_defaults['feature']
        
        # Form fields
        col1, col2 = st.columns(2)
        
        with col1:
            jira_id = st.text_input(
                "JIRA User Story ID *",
                value=current_jira_id,
                help="JIRA ticket identifier (e.g., TP-1, STORY-123)",
                key="add_tc_jira_id"
            )
            
            test_type = st.selectbox(
                "Test Type *",
                options=["positive", "negative", "security", "performance", "all"],
                index=["positive", "negative", "security", "performance", "all"].index(current_test_type.lower()),
                help="Type of testing for this test case",
                key="add_tc_test_type"
            )
            
            priority = st.selectbox(
                "Priority",
                options=["High", "Medium", "Low"],
                index=1,  # Default to Medium
                help="Test case priority level",
                key="add_tc_priority"
            )
        
        with col2:
            project = st.text_input(
                "Project",
                value=smart_project,
                help="Project name (auto-filled from current data, editable)",
                key="add_tc_project"
            )

            feature = st.text_input(
                "Feature",
                value=smart_feature,
                help="Feature name (auto-filled from current data, editable)",
                key="add_tc_feature"
            )
        
        # Multi-line fields
        test_case_objective = st.text_area(
            "Test Case Objective",
            help="Description of what this test case validates",
            height=100,
            key="add_tc_objective"
        )
        
        prerequisite = st.text_area(
            "Prerequisites",
            help="Prerequisites or setup required before running this test",
            height=80,
            key="add_tc_prerequisite"
        )
        
        comments = st.text_area(
            "Comments",
            help="Additional comments or notes",
            height=80,
            key="add_tc_comments"
        )

        # Enhanced workflow option
        st.markdown("---")
        add_steps_after = st.checkbox(
            "🔄 Add test steps after creation",
            value=True,  # Default to True for better workflow
            help="Automatically open the Add Test Step dialog after creating this test case",
            key="add_tc_add_steps_after"
        )
        
        # Action buttons
        col1, col2, col3 = st.columns([1, 1, 2])
        
        with col1:
            if st.button("✅ Create Test Case", type="primary", use_container_width=True, key="dialog_btn_create_test_case"):
                # Enhanced validation
                errors = []

                # Required field validation
                if not jira_id.strip():
                    errors.append("JIRA User Story ID is required")
                elif not jira_id.strip().replace("-", "").replace("_", "").isalnum():
                    errors.append("JIRA User Story ID must contain only letters, numbers, hyphens, and underscores")

                if not test_type:
                    errors.append("Test Type is required")

                if not current_user.strip():
                    errors.append("User name is required (check session state)")

                # Optional field validation
                if test_case_objective and len(test_case_objective.strip()) > 1000:
                    errors.append("Test Case Objective must be less than 1000 characters")

                if prerequisite and len(prerequisite.strip()) > 1000:
                    errors.append("Prerequisites must be less than 1000 characters")

                # Validate using helper function
                data = {
                    'jira_id': jira_id,
                    'test_type': test_type,
                    'user_name': current_user,
                    'priority': priority
                }

                is_valid, validation_errors = validate_test_case_data(data)
                errors.extend(validation_errors)

                if errors:
                    st.error("❌ **Validation Errors:**")
                    for error in errors:
                        st.error(f"• {error}")
                else:
                    try:
                        # Show progress indicator
                        with st.spinner("Creating test case..."):
                            # Create the test case
                            success, message, test_case_id = add_new_test_case(
                                database_path=db.DATABASE_PATH,
                                jira_id=jira_id.strip(),
                                test_type=test_type,
                                user_name=current_user,
                                project=project.strip(),
                                feature=feature.strip(),
                                test_case_objective=test_case_objective.strip(),
                                prerequisite=prerequisite.strip(),
                                priority=priority,
                                test_group="",  # Empty test group since field was removed
                                comments=comments.strip()
                            )

                        if success:
                            st.success(f"✅ {message}")
                            if test_case_id:
                                st.info(f"🆔 **New Test Case ID**: {test_case_id}")

                            # Enhanced workflow: Check if user wants to add test steps
                            if add_steps_after:
                                st.info("🔄 **Next Step**: Add test steps to your new test case")
                                # Store the new test case ID for the Add Test Step dialog
                                st.session_state["new_test_case_for_steps"] = test_case_id
                                # Clear the current dialog
                                st.session_state["show_add_test_case_dialog"] = False
                                # Open the Add Test Step dialog
                                st.session_state["show_add_test_step_dialog"] = True
                            else:
                                # Standard workflow: just close the dialog
                                st.session_state["show_add_test_case_dialog"] = False

                            # Clear form fields
                            for key in list(st.session_state.keys()):
                                if key.startswith("add_tc_"):
                                    del st.session_state[key]
                            st.rerun()
                            return True
                        else:
                            st.error(f"❌ **Database Error**: {message}")
                            st.error("Please check your database connection and try again.")

                    except Exception as e:
                        st.error(f"❌ **Unexpected Error**: {str(e)}")
                        st.error("Please contact support if this error persists.")
        
        with col2:
            if st.button("❌ Cancel", use_container_width=True, key="dialog_btn_cancel_add_test_case"):
                st.session_state["show_add_test_case_dialog"] = False
                # Clear form fields
                for key in list(st.session_state.keys()):
                    if key.startswith("add_tc_"):
                        del st.session_state[key]
                st.rerun()
    
    return False


def render_add_test_step_dialog(filtered_data: pd.DataFrame) -> bool:
    """
    Render the Add Test Step dialog with test case selection.

    Args:
        filtered_data: Current filtered test case data

    Returns:
        bool: True if test step was added successfully
    """
    with st.expander("📝 Add Test Step to Existing Test Case", expanded=True):
        st.markdown("**Add a new test step to an existing test case**")

        # Get unique test case IDs from filtered data
        if 'Test Case ID' in filtered_data.columns:
            test_case_ids = sorted(filtered_data['Test Case ID'].dropna().unique())
        else:
            st.error("❌ No test case IDs found in current data")
            return False

        if not test_case_ids:
            st.error("❌ No test cases available in current filtered data")
            return False

        # Test case selection with smart default for newly created test cases
        new_test_case_id = st.session_state.get("new_test_case_for_steps")
        default_index = 0

        # If we have a newly created test case, try to select it by default
        if new_test_case_id and new_test_case_id in test_case_ids:
            try:
                default_index = test_case_ids.index(new_test_case_id)
                st.info(f"🎯 **Auto-selected**: {new_test_case_id} (newly created test case)")
                # Clear the session state after using it
                del st.session_state["new_test_case_for_steps"]
            except ValueError:
                pass

        selected_test_case = st.selectbox(
            "Select Test Case *",
            options=test_case_ids,
            index=default_index,
            help="Choose the test case to add a step to",
            key="add_step_test_case_id"
        )

        # Get test case info for context and step position options
        current_step_count = 0
        if selected_test_case:
            test_case_info = get_test_case_info(db.DATABASE_PATH, selected_test_case)
            if test_case_info:
                current_step_count = test_case_info.get('step_count', 0)
                st.info(f"📋 **{selected_test_case}**: {test_case_info.get('test_case_objective', 'No objective')} "
                       f"(Current steps: {current_step_count})")

        # Step position selection
        position_options = ["End (append)", "Beginning (insert at start)"]
        position_values = ["end", "beginning"]

        # Add specific position options if there are existing steps
        if current_step_count > 0:
            for i in range(1, current_step_count + 1):
                position_options.append(f"After step {i}")
                position_values.append(str(i + 1))

        insert_position_display = st.selectbox(
            "Insert Position",
            options=position_options,
            index=0,  # Default to "End"
            help="Choose where to insert the new test step",
            key="add_step_position"
        )

        # Map display option to actual value
        insert_position = position_values[position_options.index(insert_position_display)]

        # Form fields
        col1, col2 = st.columns(2)

        with col1:
            test_step = st.text_area(
                "Test Step Description *",
                help="Describe the action to be performed",
                height=100,
                key="add_step_description"
            )

            test_status = st.selectbox(
                "Test Status",
                options=["Not Run", "Pass", "Fail", "Blocked"],
                index=0,  # Default to "Not Run"
                help="Initial status for this test step",
                key="add_step_status"
            )

        with col2:
            expected_result = st.text_area(
                "Expected Result *",
                help="Describe the expected outcome",
                height=100,
                key="add_step_expected"
            )

            comments = st.text_area(
                "Comments",
                help="Additional notes or comments",
                height=80,
                key="add_step_comments"
            )

        # Action buttons
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("✅ Add Test Step", type="primary", use_container_width=True, key="dialog_btn_add_test_step"):
                current_user = st.session_state.get("current_user", "test_user")

                # Enhanced validation
                errors = []

                # Required field validation
                if not selected_test_case:
                    errors.append("Test Case selection is required")

                if not test_step.strip():
                    errors.append("Test step description is required")
                elif len(test_step.strip()) > 1000:
                    errors.append("Test step description must be less than 1000 characters")

                if not expected_result.strip():
                    errors.append("Expected result is required")
                elif len(expected_result.strip()) > 1000:
                    errors.append("Expected result must be less than 1000 characters")

                if not current_user.strip():
                    errors.append("User name is required (check session state)")

                # Optional field validation
                if comments and len(comments.strip()) > 500:
                    errors.append("Comments must be less than 500 characters")

                # Validate using helper function
                data = {
                    'test_case_id': selected_test_case,
                    'user_name': current_user,
                    'test_status': test_status
                }

                is_valid, validation_errors = validate_test_step_data(data)
                errors.extend(validation_errors)

                if errors:
                    st.error("❌ **Validation Errors:**")
                    for error in errors:
                        st.error(f"• {error}")
                else:
                    try:
                        # Show progress indicator
                        with st.spinner(f"Adding test step to {selected_test_case}..."):
                            # Add the test step
                            success, message = add_test_step_to_case(
                                database_path=db.DATABASE_PATH,
                                test_case_id=selected_test_case,
                                test_step=test_step.strip(),
                                expected_result=expected_result.strip(),
                                user_name=current_user,
                                test_status=test_status,
                                comments=comments.strip(),
                                insert_position=insert_position
                            )

                        if success:
                            st.success(f"✅ {message}")
                            # Clear the dialog
                            st.session_state["show_add_test_step_dialog"] = False
                            # Clear form fields
                            for key in list(st.session_state.keys()):
                                if key.startswith("add_step_"):
                                    del st.session_state[key]
                            st.rerun()
                            return True
                        else:
                            st.error(f"❌ **Database Error**: {message}")
                            st.error("Please check that the test case still exists and try again.")

                    except Exception as e:
                        st.error(f"❌ **Unexpected Error**: {str(e)}")
                        st.error("Please contact support if this error persists.")

        with col2:
            if st.button("❌ Cancel", use_container_width=True, key="dialog_btn_cancel_add_test_step"):
                st.session_state["show_add_test_step_dialog"] = False
                # Clear form fields
                for key in list(st.session_state.keys()):
                    if key.startswith("add_step_"):
                        del st.session_state[key]
                st.rerun()

    return False


def render_delete_test_case_dialog(filtered_data: pd.DataFrame) -> bool:
    """
    Render the Delete Test Case dialog with confirmation.

    Args:
        filtered_data: Current filtered test case data

    Returns:
        bool: True if test case was deleted successfully
    """
    with st.expander("🗑️ Delete Test Case", expanded=True):
        st.markdown("**⚠️ Delete an entire test case and all its test steps**")
        st.warning("This action cannot be undone. All test steps will also be deleted.")

        # Get unique test case IDs from filtered data
        if 'Test Case ID' in filtered_data.columns:
            test_case_ids = sorted(filtered_data['Test Case ID'].dropna().unique())
        else:
            st.error("❌ No test case IDs found in current data")
            return False

        if not test_case_ids:
            st.error("❌ No test cases available in current filtered data")
            return False

        # Test case selection
        selected_test_case = st.selectbox(
            "Select Test Case to Delete *",
            options=test_case_ids,
            help="Choose the test case to delete permanently",
            key="delete_tc_test_case_id"
        )

        # Get test case info for confirmation
        if selected_test_case:
            test_case_info = get_test_case_info(db.DATABASE_PATH, selected_test_case)
            if test_case_info:
                st.error(f"🗑️ **Will delete**: {selected_test_case} - {test_case_info.get('test_case_objective', 'No objective')}")
                st.error(f"📊 **Test steps to be deleted**: {test_case_info.get('step_count', 0)}")

        # Confirmation checkbox
        confirm_deletion = st.checkbox(
            f"I confirm that I want to permanently delete test case {selected_test_case} and all its test steps",
            key="delete_tc_confirm"
        )

        # Action buttons
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("🗑️ Delete Test Case",
                        type="primary",
                        use_container_width=True,
                        disabled=not confirm_deletion,
                        key="dialog_btn_delete_test_case"):
                if confirm_deletion:
                    try:
                        # Show progress indicator
                        with st.spinner(f"Deleting test case {selected_test_case}..."):
                            # Delete the test case
                            success, message, deletion_counts = delete_test_case_with_confirmation(
                                database_path=db.DATABASE_PATH,
                                test_case_id=selected_test_case
                            )

                        if success:
                            st.success(f"✅ {message}")
                            st.info(f"📊 **Deletion Summary**: {deletion_counts['test_cases']} test case(s) and {deletion_counts['test_steps']} test step(s) removed")

                            # Clear the dialog
                            st.session_state["show_delete_test_case_dialog"] = False
                            # Clear form fields
                            for key in list(st.session_state.keys()):
                                if key.startswith("delete_tc_"):
                                    del st.session_state[key]
                            st.rerun()
                            return True
                        else:
                            st.error(f"❌ **Deletion Failed**: {message}")
                            if "not found" in message.lower():
                                st.error("The test case may have been already deleted or moved.")
                            else:
                                st.error("Please check your database connection and try again.")

                    except Exception as e:
                        st.error(f"❌ **Unexpected Error During Deletion**: {str(e)}")
                        st.error("The deletion operation failed. Please contact support if this error persists.")
                else:
                    st.warning("⚠️ Please confirm the deletion by checking the checkbox above.")

        with col2:
            if st.button("❌ Cancel", use_container_width=True, key="dialog_btn_cancel_delete_test_case"):
                st.session_state["show_delete_test_case_dialog"] = False
                # Clear form fields
                for key in list(st.session_state.keys()):
                    if key.startswith("delete_tc_"):
                        del st.session_state[key]
                st.rerun()

    return False


def render_delete_test_step_dialog(filtered_data: pd.DataFrame) -> bool:
    """
    Render the Delete Test Step dialog with test case and step selection.

    Args:
        filtered_data: Current filtered test case data

    Returns:
        bool: True if test step was deleted successfully
    """
    with st.expander("❌ Delete Test Step", expanded=True):
        st.markdown("**Delete individual test steps from test cases**")
        st.warning("This action cannot be undone. Step numbers will be automatically renumbered.")

        # Get unique test case IDs from filtered data
        if 'Test Case ID' in filtered_data.columns:
            test_case_ids = sorted(filtered_data['Test Case ID'].dropna().unique())
        else:
            st.error("❌ No test case IDs found in current data")
            return False

        if not test_case_ids:
            st.error("❌ No test cases available in current filtered data")
            return False

        # Test case selection
        selected_test_case = st.selectbox(
            "Select Test Case *",
            options=test_case_ids,
            help="Choose the test case containing the step to delete",
            key="delete_step_test_case_id"
        )

        # Get available step numbers for the selected test case
        step_numbers = []
        if selected_test_case and 'Step No' in filtered_data.columns:
            case_data = filtered_data[filtered_data['Test Case ID'] == selected_test_case]
            step_numbers = sorted([int(step) for step in case_data['Step No'].dropna().unique() if str(step).isdigit()])

        if not step_numbers:
            st.warning(f"⚠️ No test steps found for test case {selected_test_case}")
            return False

        # Step selection
        selected_step = st.selectbox(
            "Select Step Number to Delete *",
            options=step_numbers,
            help="Choose the step number to delete",
            key="delete_step_step_number"
        )

        # Show step details for confirmation
        if selected_test_case and selected_step:
            case_step_data = filtered_data[
                (filtered_data['Test Case ID'] == selected_test_case) &
                (filtered_data['Step No'] == selected_step)
            ]
            if not case_step_data.empty:
                step_info = case_step_data.iloc[0]
                st.error(f"🗑️ **Will delete Step {selected_step}**:")
                st.error(f"📝 **Test Step**: {step_info.get('Test Steps', 'N/A')}")
                st.error(f"🎯 **Expected Result**: {step_info.get('Expected Result', 'N/A')}")

        # Confirmation checkbox
        confirm_deletion = st.checkbox(
            f"I confirm that I want to permanently delete step {selected_step} from test case {selected_test_case}",
            key="delete_step_confirm"
        )

        # Action buttons
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("❌ Delete Test Step",
                        type="primary",
                        use_container_width=True,
                        disabled=not confirm_deletion,
                        key="dialog_btn_delete_test_step"):
                if confirm_deletion:
                    try:
                        # Additional validation before deletion
                        if not selected_test_case or not selected_step:
                            st.error("❌ **Validation Error**: Test case and step selection are required")
                            return False

                        # Show progress indicator
                        with st.spinner(f"Deleting step {selected_step} from {selected_test_case}..."):
                            # Delete the test step
                            success, message = delete_test_step(
                                database_path=db.DATABASE_PATH,
                                test_case_id=selected_test_case,
                                step_number=selected_step
                            )

                        if success:
                            st.success(f"✅ {message}")
                            st.info("📊 **Note**: Remaining step numbers have been automatically renumbered")

                            # Clear the dialog
                            st.session_state["show_delete_test_step_dialog"] = False
                            # Clear form fields
                            for key in list(st.session_state.keys()):
                                if key.startswith("delete_step_"):
                                    del st.session_state[key]
                            st.rerun()
                            return True
                        else:
                            st.error(f"❌ **Deletion Failed**: {message}")
                            if "not found" in message.lower():
                                st.error("The test step may have been already deleted or the test case may not exist.")
                            else:
                                st.error("Please check your database connection and try again.")

                    except Exception as e:
                        st.error(f"❌ **Unexpected Error During Deletion**: {str(e)}")
                        st.error("The deletion operation failed. Please contact support if this error persists.")
                else:
                    st.warning("⚠️ Please confirm the deletion by checking the checkbox above.")

        with col2:
            if st.button("❌ Cancel", use_container_width=True, key="dialog_btn_cancel_delete_test_step"):
                st.session_state["show_delete_test_step_dialog"] = False
                # Clear form fields
                for key in list(st.session_state.keys()):
                    if key.startswith("delete_step_"):
                        del st.session_state[key]
                st.rerun()

    return False


def render_clone_test_case_dialog(filtered_data: pd.DataFrame) -> bool:
    """
    Render the Clone Test Case dialog with source selection and modification options.

    Args:
        filtered_data: Current filtered test case data

    Returns:
        bool: True if test case was cloned successfully
    """
    with st.expander("📋 Clone Test Case", expanded=True):
        st.markdown("**Create a copy of an existing test case with a new Test Case ID**")
        st.info("💡 All test steps will be copied. Test status and actual results will be reset for the new test case.")

        # Get unique test case IDs from filtered data
        if 'Test Case ID' in filtered_data.columns:
            test_case_ids = sorted(filtered_data['Test Case ID'].dropna().unique())
        else:
            st.error("❌ No test case IDs found in current data")
            return False

        if not test_case_ids:
            st.error("❌ No test cases available in current filtered data")
            return False

        # Source test case selection
        selected_source = st.selectbox(
            "Select Test Case to Clone *",
            options=test_case_ids,
            help="Choose the test case to create a copy of",
            key="clone_source_test_case_id"
        )

        # Get source test case info for preview
        source_data = None
        if selected_source:
            source_data = get_test_case_for_cloning(db.DATABASE_PATH, selected_source)
            if source_data:
                st.success(f"📋 **Source**: {selected_source} - {source_data.get('test_case_objective', 'No objective')}")
                st.info(f"📊 **Will copy**: {len(source_data.get('test_steps', []))} test steps")

        # Get smart defaults for the clone
        smart_defaults = get_smart_defaults_for_new_test_case(filtered_data)

        # Clone destination fields
        st.markdown("---")
        st.markdown("**📝 Modify fields for the cloned test case (optional)**")

        col1, col2 = st.columns(2)

        with col1:
            new_jira_id = st.text_input(
                "JIRA User Story ID *",
                value=smart_defaults['jira_id'],
                help="JIRA ID for the cloned test case",
                key="clone_jira_id"
            )

            new_project = st.text_input(
                "Project",
                value=source_data.get('project', '') if source_data else smart_defaults['project'],
                help="Project name (editable)",
                key="clone_project"
            )

            new_priority = st.selectbox(
                "Priority",
                options=["High", "Medium", "Low"],
                index=["High", "Medium", "Low"].index(source_data.get('priority', 'Medium') if source_data else 'Medium'),
                help="Test case priority level",
                key="clone_priority"
            )

        with col2:
            new_feature = st.text_input(
                "Feature",
                value=source_data.get('feature', '') if source_data else smart_defaults['feature'],
                help="Feature name (editable)",
                key="clone_feature"
            )

            # Test Group input removed as the column is now hidden

        # Multi-line fields
        new_objective = st.text_area(
            "Test Case Objective",
            value=source_data.get('test_case_objective', '') if source_data else "",
            help="Modify the test case objective if needed",
            height=100,
            key="clone_objective"
        )

        new_prerequisite = st.text_area(
            "Prerequisites",
            value=source_data.get('prerequisite', '') if source_data else "",
            help="Modify prerequisites if needed",
            height=80,
            key="clone_prerequisite"
        )

        # Action buttons
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("📋 Clone Test Case", type="primary", use_container_width=True, key="dialog_btn_clone_test_case"):
                current_user = smart_defaults['user_name']

                # Enhanced validation
                errors = []

                if not new_jira_id.strip():
                    errors.append("JIRA User Story ID is required")
                elif not new_jira_id.strip().replace("-", "").replace("_", "").isalnum():
                    errors.append("JIRA User Story ID must contain only letters, numbers, hyphens, and underscores")

                if not selected_source:
                    errors.append("Source test case selection is required")

                if not current_user.strip():
                    errors.append("User name is required (check session state)")

                if errors:
                    st.error("❌ **Validation Errors:**")
                    for error in errors:
                        st.error(f"• {error}")
                else:
                    try:
                        # Show progress indicator
                        with st.spinner(f"Cloning {selected_source} to new test case..."):
                            # Prepare modifications
                            modifications = {
                                'project': new_project.strip(),
                                'feature': new_feature.strip(),
                                'test_case_objective': new_objective.strip(),
                                'prerequisite': new_prerequisite.strip(),
                                'priority': new_priority
                                # 'test_group' field removed as column is now hidden
                            }

                            # Clone the test case
                            success, message, new_test_case_id = clone_test_case(
                                database_path=db.DATABASE_PATH,
                                source_test_case_id=selected_source,
                                new_jira_id=new_jira_id.strip(),
                                user_name=current_user,
                                modifications=modifications
                            )

                        if success:
                            st.success(f"✅ {message}")
                            if new_test_case_id:
                                st.info(f"🆔 **New Test Case ID**: {new_test_case_id}")

                            # Clear the dialog
                            st.session_state["show_clone_test_case_dialog"] = False
                            # Clear form fields
                            for key in list(st.session_state.keys()):
                                if key.startswith("clone_"):
                                    del st.session_state[key]
                            st.rerun()
                            return True
                        else:
                            st.error(f"❌ **Cloning Failed**: {message}")

                    except Exception as e:
                        st.error(f"❌ **Unexpected Error**: {str(e)}")
                        st.error("Please contact support if this error persists.")

        with col2:
            if st.button("❌ Cancel", use_container_width=True, key="dialog_btn_cancel_clone_test_case"):
                st.session_state["show_clone_test_case_dialog"] = False
                # Clear form fields
                for key in list(st.session_state.keys()):
                    if key.startswith("clone_"):
                        del st.session_state[key]
                st.rerun()

    return False
