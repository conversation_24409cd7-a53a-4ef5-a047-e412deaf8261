"""
Responsive Table Display Components for GretahAI ScriptWeaver

This module provides UI components for displaying tables in a responsive manner
that prevents horizontal overflow and maintains readability across different screen sizes.

Key Features:
- Responsive markdown table rendering with proper containment
- Text wrapping for long content in table cells
- Professional styling consistent with application design
- Horizontal scroll containment within container boundaries
- Support for step table display with optimized column widths

Functions:
    render_responsive_markdown_table: Render markdown table with responsive styling
    render_responsive_step_table: Specialized rendering for step tables
"""

import streamlit as st


def render_responsive_markdown_table(markdown_table: str, container_class: str = "responsive-table-container") -> None:
    """
    Render a markdown table with responsive styling to prevent horizontal overflow.

    This function takes a markdown table string and renders it with CSS styling
    that ensures the table stays within container boundaries and provides
    proper text wrapping for long content.

    Args:
        markdown_table (str): The markdown table string to render
        container_class (str): CSS class name for the container (default: "responsive-table-container")
    """
    if not markdown_table or not isinstance(markdown_table, str):
        st.warning("⚠️ No table data available to display")
        return
    
    # CSS for responsive table styling
    responsive_table_css = f"""
    <style>
    .{container_class} {{
        width: 100%;
        overflow-x: auto;
        margin: 1rem 0;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background-color: white;
    }}
    
    .{container_class} table {{
        width: 100%;
        border-collapse: collapse;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 0.9rem;
        background-color: white;
        margin: 0;
    }}
    
    .{container_class} th {{
        background-color: #673AB7;
        color: white;
        font-weight: 600;
        padding: 12px 8px;
        text-align: left;
        border-bottom: 2px solid #5E35B1;
        position: sticky;
        top: 0;
        z-index: 10;
    }}
    
    .{container_class} td {{
        padding: 10px 8px;
        border-bottom: 1px solid #e0e0e0;
        vertical-align: top;
        word-wrap: break-word;
        word-break: break-word;
        max-width: 200px;
        line-height: 1.4;
        color: #333333 !important; /* Dark gray text for high contrast */
        background-color: #ffffff !important; /* White background */
    }}

    .{container_class} tr:nth-child(even) {{
        background-color: #f8f9fa !important; /* Light gray for alternating rows */
    }}

    .{container_class} tr:nth-child(even) td {{
        background-color: #f8f9fa !important; /* Light gray for alternating row cells */
        color: #333333 !important; /* Dark text for contrast */
    }}

    .{container_class} tr:hover {{
        background-color: rgba(103, 58, 183, 0.08) !important; /* Light purple hover */
        transition: background-color 0.2s ease;
    }}

    .{container_class} tr:hover td {{
        background-color: rgba(103, 58, 183, 0.08) !important; /* Hover effect on cells */
        color: #333333 !important; /* Maintain dark text on hover */
    }}
    
    /* Column-specific styling for step tables */
    .{container_class} td:nth-child(1) {{
        /* Step No column */
        min-width: 60px;
        max-width: 80px;
        text-align: center;
        font-weight: 600;
        color: #4527A0 !important; /* Darker purple for better contrast (WCAG AA compliant) */
        background-color: #ffffff !important; /* White background */
    }}

    .{container_class} td:nth-child(2) {{
        /* Action column */
        min-width: 120px;
        max-width: 180px;
        font-weight: 500;
        color: #333333 !important; /* Dark gray text for high contrast */
        background-color: #ffffff !important; /* White background */
    }}

    .{container_class} td:nth-child(3) {{
        /* Locator column */
        min-width: 150px;
        max-width: 250px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        background-color: #f8f9fa !important; /* Light gray background for code-like content */
        border-radius: 4px;
        padding: 8px;
        color: #000000 !important; /* Black text for maximum contrast on light background */
    }}

    .{container_class} td:nth-child(4) {{
        /* Expected Result column */
        min-width: 120px;
        max-width: 200px;
        color: #333333 !important; /* Dark gray text for high contrast */
        background-color: #ffffff !important; /* White background */
    }}
    
    /* Responsive breakpoints */
    @media (max-width: 768px) {{
        .{container_class} {{
            font-size: 0.8rem;
        }}
        
        .{container_class} th,
        .{container_class} td {{
            padding: 8px 6px;
        }}
        
        .{container_class} td:nth-child(3) {{
            max-width: 150px;
            font-size: 0.75rem;
        }}
    }}
    
    @media (max-width: 480px) {{
        .{container_class} {{
            font-size: 0.75rem;
        }}
        
        .{container_class} th,
        .{container_class} td {{
            padding: 6px 4px;
        }}
        
        .{container_class} td:nth-child(3) {{
            max-width: 100px;
        }}
    }}
    </style>
    """
    
    # Inject the CSS
    st.markdown(responsive_table_css, unsafe_allow_html=True)
    
    # Convert markdown table to HTML with responsive container
    html_table = _convert_markdown_to_responsive_html(markdown_table, container_class)

    # Render the responsive table
    st.markdown(html_table, unsafe_allow_html=True)


def render_responsive_step_table(step_table_markdown: str, show_copy_button: bool = True) -> None:
    """
    Specialized rendering for step tables with optimized styling and functionality.

    This function provides enhanced rendering specifically for automation step tables
    with features like copy functionality and step-specific column optimization.

    Args:
        step_table_markdown (str): The step table markdown string to render
        show_copy_button (bool): Whether to show the copy to clipboard button
    """
    if not step_table_markdown or not isinstance(step_table_markdown, str):
        st.warning("⚠️ No step table data available to display")
        return

    # Render the responsive table
    render_responsive_markdown_table(step_table_markdown, "step-table-container")
    
    # Add copy functionality if requested
    if show_copy_button:
        _, col2, _ = st.columns([1, 1, 2])
        with col2:
            if st.button("📋 Copy Table", key="copy_responsive_table_btn", help="Copy table to clipboard"):
                try:
                    import pyperclip
                    pyperclip.copy(step_table_markdown)
                    st.success("✅ Table copied to clipboard!")
                except ImportError:
                    st.warning("⚠️ pyperclip module not installed. Cannot copy to clipboard.")
                except Exception as e:
                    st.error(f"❌ Error copying to clipboard: {e}")


def _convert_markdown_to_responsive_html(markdown_table: str, container_class: str) -> str:
    """
    Convert a markdown table to responsive HTML with proper styling.

    Args:
        markdown_table (str): The markdown table string
        container_class (str): CSS class for the container

    Returns:
        str: HTML representation of the table with responsive styling
    """
    if not markdown_table.strip():
        return f'<div class="{container_class}"><p>No table data available</p></div>'

    # Clean the markdown table first
    cleaned_table = _clean_markdown_table(markdown_table)

    # Try the robust parsing approach first
    try:
        return _parse_markdown_table_robust(cleaned_table, container_class)
    except Exception as e:
        # Fallback to simple parsing if robust parsing fails
        return _parse_markdown_table_simple(cleaned_table, container_class)


def _parse_markdown_table_robust(markdown_table: str, container_class: str) -> str:
    """
    Robust markdown table parsing that handles various formats.
    """
    lines = [line.strip() for line in markdown_table.strip().split('\n') if line.strip()]
    if len(lines) < 2:  # Need at least header and one data row
        return f'<div class="{container_class}"><p>Invalid table format - only {len(lines)} lines found</p></div>'

    # Find the first line that looks like a table header (contains multiple |)
    header_line = None
    header_index = 0

    for i, line in enumerate(lines):
        if line.count('|') >= 3:  # At least 2 columns (3 pipes minimum: |col1|col2|)
            header_line = line.strip()
            header_index = i
            break

    if not header_line:
        return f'<div class="{container_class}"><p>No valid table header found in content</p></div>'

    if not header_line.startswith('|') or not header_line.endswith('|'):
        # Try to fix common formatting issues
        if '|' in header_line:
            # Add missing pipes if needed
            if not header_line.startswith('|'):
                header_line = '|' + header_line
            if not header_line.endswith('|'):
                header_line = header_line + '|'
        else:
            # More detailed error message for debugging
            return f'<div class="{container_class}"><p>Invalid table header format: "{header_line}" (no pipes found)</p></div>'
    
    headers = [cell.strip() for cell in header_line[1:-1].split('|')]

    # Find the start of data rows (skip separator row if it exists)
    data_start_index = header_index + 1
    if data_start_index < len(lines):
        separator_line = lines[data_start_index].strip()
        if separator_line.startswith('|') and '---' in separator_line:
            data_start_index += 1  # Skip separator row

    # Parse data rows - be more flexible about which lines to process
    data_rows = []
    for i, line in enumerate(lines[data_start_index:], start=data_start_index):
        line = line.strip()
        if not line:  # Skip empty lines
            continue

        # Skip lines that don't look like table rows
        if line.count('|') < 2:  # Need at least 2 pipes for a valid row
            continue

        # Fix common formatting issues
        if '|' in line:
            if not line.startswith('|'):
                line = '|' + line
            if not line.endswith('|'):
                line = line + '|'

        if line.startswith('|') and line.endswith('|'):
            cells = [cell.strip() for cell in line[1:-1].split('|')]
            # Ensure we have the same number of cells as headers
            while len(cells) < len(headers):
                cells.append('')
            data_rows.append(cells[:len(headers)])  # Truncate if too many cells
    
    # Build HTML table
    html = f'<div class="{container_class}">\n<table>\n'

    # Add header
    html += '<thead>\n<tr>\n'
    for header in headers:
        html += f'<th>{_escape_html(header)}</th>\n'
    html += '</tr>\n</thead>\n'

    # Add body
    html += '<tbody>\n'
    for row in data_rows:
        html += '<tr>\n'
        for cell in row:
            # Apply special formatting for certain content types
            formatted_cell = _format_table_cell(cell)
            html += f'<td>{formatted_cell}</td>\n'
        html += '</tr>\n'
    html += '</tbody>\n'

    html += '</table>\n</div>'

    return html


def _parse_markdown_table_simple(markdown_table: str, container_class: str) -> str:
    """
    Simple fallback parser for markdown tables that handles basic cases.
    """
    lines = [line.strip() for line in markdown_table.strip().split('\n') if line.strip()]

    if not lines:
        return f'<div class="{container_class}"><p>No table content found</p></div>'

    # Try to find any line that looks like a header (contains multiple |)
    header_line = None
    data_lines = []

    for i, line in enumerate(lines):
        pipe_count = line.count('|')
        if pipe_count >= 3:  # At least 2 columns (3 pipes minimum: |col1|col2|)
            if header_line is None:
                header_line = line
            else:
                # Skip separator lines (contain ---)
                if '---' not in line:
                    data_lines.append(line)

    if not header_line:
        return f'<div class="{container_class}"><p>No valid table header found</p></div>'

    # Parse header
    header_parts = header_line.split('|')
    headers = [part.strip() for part in header_parts if part.strip()]

    # Parse data rows
    data_rows = []
    for line in data_lines:
        parts = line.split('|')
        cells = [part.strip() for part in parts if part.strip() or parts.index(part) < len(headers)]
        # Pad or truncate to match header count
        while len(cells) < len(headers):
            cells.append('')
        data_rows.append(cells[:len(headers)])

    # Build HTML
    html = f'<div class="{container_class}">\n<table>\n'
    html += '<thead>\n<tr>\n'
    for header in headers:
        html += f'<th>{_escape_html(header)}</th>\n'
    html += '</tr>\n</thead>\n'
    html += '<tbody>\n'
    for row in data_rows:
        html += '<tr>\n'
        for cell in row:
            formatted_cell = _format_table_cell(cell)
            html += f'<td>{formatted_cell}</td>\n'
        html += '</tr>\n'
    html += '</tbody>\n'
    html += '</table>\n</div>'

    return html


def _format_table_cell(cell_content: str) -> str:
    """
    Format table cell content with appropriate styling for different content types.
    
    Args:
        cell_content (str): The raw cell content
        
    Returns:
        str: Formatted HTML content for the cell
    """
    if not cell_content:
        return ''
    
    # Escape HTML first
    content = _escape_html(cell_content)
    
    # Apply special formatting for locators (CSS selectors, XPath, etc.)
    if any(indicator in content.lower() for indicator in ['css:', 'xpath:', 'id:', 'name:', 'class:']):
        content = f'<code style="background-color: #f8f9fa; color: #000000; padding: 2px 4px; border-radius: 3px; font-size: 0.9em; border: 1px solid #e0e0e0;">{content}</code>'
    
    # Apply formatting for step numbers
    elif content.isdigit():
        content = f'<strong>{content}</strong>'
    
    return content


def _clean_markdown_table(markdown_table: str) -> str:
    """
    Clean markdown table content to remove common formatting issues.

    Args:
        markdown_table (str): The raw markdown table string

    Returns:
        str: Cleaned markdown table string
    """
    if not markdown_table:
        return ""

    # Split into lines and clean each line
    lines = markdown_table.strip().split('\n')
    cleaned_lines = []

    for line in lines:
        line = line.strip()

        # Skip empty lines
        if not line:
            continue

        # Skip lines that are just single characters (like the "n" issue)
        if len(line) == 1 and line not in ['|']:
            continue

        # Skip lines that don't contain pipes and aren't part of a table
        if '|' not in line and len(cleaned_lines) == 0:
            continue

        cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)



def _escape_html(text: str) -> str:
    """
    Escape HTML special characters in text.

    Args:
        text (str): Text to escape

    Returns:
        str: HTML-escaped text
    """
    if not isinstance(text, str):
        text = str(text)

    return (text.replace('&', '&amp;')
                .replace('<', '&lt;')
                .replace('>', '&gt;')
                .replace('"', '&quot;')
                .replace("'", '&#x27;'))
