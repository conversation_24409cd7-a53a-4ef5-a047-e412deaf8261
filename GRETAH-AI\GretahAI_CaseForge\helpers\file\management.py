"""
File Management Module for GretahAI CaseForge

This module handles file operations, versioning, and test case file management.

Functions:
- get_latest_test_case_file: Find the latest test case file
- get_existing_test_cases: Retrieve existing test cases for a JIRA issue

© 2025 GretahAI Team
"""

import os
import pandas as pd
from datetime import datetime
from typing import Optional, Tuple, List, Dict, Any
import streamlit as st
from pathlib import Path


def get_latest_test_case_file(jira_id=None, test_type=None):
    """
    Finds the latest generated test case Excel file based on timestamp in the filename.
    
    This function implements a dual approach: first attempting to retrieve test cases from
    the database, and if that fails, falling back to Excel file search. It prioritizes
    database data and creates temporary Excel files for download when needed.

    Args:
        jira_id (str, optional): JIRA ticket identifier to filter by (e.g., "TP-1"). 
                                Defaults to None.
        test_type (str, optional): Test type to filter by ("positive", "negative", 
                                  "security", "performance", "all"). Defaults to None.

    Returns:
        tuple: (file_path, file_name) if file found, (None, None) if no files found
            - file_path (str): Full path to the Excel file
            - file_name (str): Just the filename portion

    Search Strategy:
        1. Database First: Attempts to get latest test run from database
        2. Excel Fallback: Searches Test_cases folder for timestamped Excel files
        3. File Filtering: Applies JIRA ID and test type filters
        4. Timestamp Validation: Only returns files with proper timestamp format

    Database Integration:
        - Retrieves test cases using Test_case_db_helper module
        - Creates temporary Excel file from database data
        - Uses timestamp for unique temporary filenames
        - Leverages formatted Excel creation function

    File Pattern Matching:
        - Looks for files starting with "Latest_testcases_"
        - Requires timestamp pattern: YYYYMMDD_HHMMSS
        - Filters by JIRA ID if provided (contains "_JIRA_ID_")
        - Handles "all" test type with "_ALL_" pattern
        - Skips temporary Excel files (starting with "~$")

    Timestamp Format:
        - Date: 8 digits (YYYYMMDD)
        - Time: 6 digits (HHMMSS)
        - Example: Latest_testcases_TP-1_positive_20241213_143022.xlsx

    Error Handling:
        - Returns (None, None) on any errors
        - Logs detailed error messages
        - Gracefully handles missing directories
        - Handles database connection failures

    Example:
        # Get latest test cases for specific JIRA and type
        path, name = get_latest_test_case_file("TP-1", "positive")
        if path:
            print(f"Found latest file: {name}")
        
        # Get latest file regardless of filters
        path, name = get_latest_test_case_file()
    """
    try:        # First, try to get test cases from database
        try:
            import db_helper as db
            
            # Get latest test run from database
            latest_run = db.get_latest_test_run(jira_id, test_type)
            
            if latest_run:
                # Create temporary Excel file from database data
                from helpers.excel import create_formatted_excel_from_scenarios
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                temp_filename = f"Latest_testcases_{jira_id}_{test_type}_{timestamp}.xlsx"
                temp_path = os.path.join("temp_excel", temp_filename)
                
                # Ensure temp directory exists
                os.makedirs("temp_excel", exist_ok=True)
                
                # Create formatted Excel from database data
                create_formatted_excel_from_scenarios(
                    latest_run,
                    temp_path,
                    is_dataframe=True,
                    create_excel=True
                )
                
                return temp_path, temp_filename
                
        except Exception as db_error:
            print(f"Database lookup failed, falling back to file search: {db_error}")
        
        # Fallback to Excel file search
        test_cases_dir = "Test_cases"
        if not os.path.exists(test_cases_dir):
            return None, None
        
        # Get all Excel files in the directory
        files = [f for f in os.listdir(test_cases_dir) 
                if f.endswith(('.xlsx', '.xls')) and not f.startswith('~$')]
        
        # Filter files that start with "Latest_testcases_"
        latest_files = [f for f in files if f.startswith("Latest_testcases_")]
        
        # Apply JIRA ID filter if specified
        if jira_id:
            latest_files = [f for f in latest_files if f"_{jira_id}_" in f]
        
        # Apply test type filter if specified
        if test_type and test_type.lower() != "all":
            latest_files = [f for f in latest_files if f"_{test_type}_" in f]
        elif test_type and test_type.lower() == "all":
            latest_files = [f for f in latest_files if "_ALL_" in f]
        
        if not latest_files:
            return None, None
        
        # Sort by timestamp in filename (assuming format: YYYYMMDD_HHMMSS)
        def extract_timestamp(filename):
            try:
                # Look for timestamp pattern in filename
                import re
                pattern = r'(\d{8}_\d{6})'
                match = re.search(pattern, filename)
                if match:
                    return match.group(1)
                return "00000000_000000"
            except:
                return "00000000_000000"
        
        # Sort files by timestamp (newest first)
        latest_files.sort(key=extract_timestamp, reverse=True)
        
        # Get the most recent file
        latest_file = latest_files[0]
        latest_path = os.path.join(test_cases_dir, latest_file)
        
        return latest_path, latest_file
        
    except Exception as e:
        print(f"Error finding latest test case file: {e}")
        return None, None


def get_existing_test_cases(issue_key):
    """
    Retrieves existing test cases for a given JIRA issue key from database or Excel files.
    
    This function implements a comprehensive approach to finding existing test cases,
    primarily using the database and falling back to Excel files when needed. It's
    designed to prevent duplication when generating new test cases.

    Args:
        issue_key (str): JIRA issue key to search for (e.g., "TP-1", "STORY-123")

    Returns:
        list: List of dictionaries containing existing test case data with structure:
            [
                {
                    "test_case_id": "TC_001",
                    "objective": "Test user login functionality",
                    "steps": [
                        {
                            "action": "Enter valid username",
                            "expected_result": "Username should be accepted"
                        },
                        ...
                    ]
                },
                ...
            ]
            Empty list if no test cases found or on error.

    Data Retrieval Strategy:
        1. Database Primary: Uses Test_case_db_helper to get test cases
        2. Excel Fallback: Searches for main Excel files in Test_cases folder
        3. Legacy Support: Handles old file naming conventions
        4. Data Processing: Extracts objectives and steps from either source

    Database Integration:
        - Uses get_test_cases_from_database() function
        - Processes DataFrame results into structured format
        - Groups test steps by test case ID
        - Handles missing or null data gracefully

    Excel File Handling:
        - Searches for main files: "Complete_testcases_{issue_key}_ALL.xlsx"
        - Falls back to legacy naming: "test_scenarios_{issue_key}_ALL.xlsx"
        - Reads Excel files using pandas
        - Processes similar to database data

    Data Structure Processing:
        - Groups rows by unique Test Case ID
        - Extracts objective from first row of each test case
        - Collects all test steps with actions and expected results
        - Filters out empty or null test steps

    Use Cases:
        - Preventing duplicate test case generation
        - Providing context to AI models about existing tests
        - Validating test coverage completeness
        - Supporting incremental test case creation

    Error Handling:
        - Returns empty list on any errors
        - Logs detailed error messages for debugging
        - Handles missing files gracefully
        - Manages database connection failures

    Example:
        existing = get_existing_test_cases("TP-1")
        if existing:
            print(f"Found {len(existing)} existing test cases")
            for tc in existing:
                print(f"- {tc['test_case_id']}: {tc['objective']}")
        else:
            print("No existing test cases found")
    """
    try:
        # First, try to get test cases from database
        try:
            import db_helper as db
            
            # Get test cases from database for this issue
            test_cases_df = db.get_test_cases_from_database(db.DATABASE_PATH, issue_key, dashboard_test_type=None)
            
            if test_cases_df is not None and not test_cases_df.empty:
                # Process DataFrame into the expected format
                existing_test_cases = []
                
                # Group by Test Case ID
                grouped = test_cases_df.groupby('Test Case ID')
                
                for test_case_id, group in grouped:
                    # Get the objective from the first row
                    objective = group.iloc[0]['Test Case Objective']
                    
                    # Collect all steps
                    steps = []
                    for _, row in group.iterrows():
                        action = row.get('Test Steps', '')
                        expected_result = row.get('Expected Result', '')
                        
                        if action and str(action).strip() and str(action).strip().lower() != 'nan':
                            steps.append({
                                "action": str(action).strip(),
                                "expected_result": str(expected_result).strip() if expected_result else ""
                            })
                    
                    if steps:
                        existing_test_cases.append({
                            "test_case_id": test_case_id,
                            "objective": str(objective).strip() if objective else "",
                            "steps": steps
                        })
                
                return existing_test_cases
                
        except Exception as db_error:
            print(f"Database lookup failed, falling back to Excel files: {db_error}")
        
        # Fallback to Excel file search
        test_cases_dir = "Test_cases"
        if not os.path.exists(test_cases_dir):
            return []
        
        # Look for main test case files
        possible_filenames = [
            f"Complete_testcases_{issue_key}_ALL.xlsx",
            f"test_scenarios_{issue_key}_ALL.xlsx",
            f"Latest_testcases_{issue_key}_ALL.xlsx"
        ]
        
        df = None
        for filename in possible_filenames:
            filepath = os.path.join(test_cases_dir, filename)
            if os.path.exists(filepath):
                try:
                    df = pd.read_excel(filepath)
                    break
                except Exception as e:
                    print(f"Error reading {filename}: {e}")
                    continue
        
        if df is None or df.empty:
            return []
        
        # Process Excel data similar to database processing
        existing_test_cases = []
        
        # Group by Test Case ID if column exists
        if 'Test Case ID' in df.columns:
            grouped = df.groupby('Test Case ID')
            
            for test_case_id, group in grouped:
                # Get the objective from the first row
                objective_col = 'Test Case Objective' if 'Test Case Objective' in df.columns else 'Objective'
                objective = group.iloc[0][objective_col] if objective_col in df.columns else ""
                
                # Collect all steps
                steps = []
                step_col = 'Test Steps' if 'Test Steps' in df.columns else 'Steps'
                result_col = 'Expected Result' if 'Expected Result' in df.columns else 'Expected Results'
                
                if step_col in df.columns:
                    for _, row in group.iterrows():
                        action = row.get(step_col, '')
                        expected_result = row.get(result_col, '') if result_col in df.columns else ""
                        
                        if action and str(action).strip() and str(action).strip().lower() != 'nan':
                            steps.append({
                                "action": str(action).strip(),
                                "expected_result": str(expected_result).strip() if expected_result else ""
                            })
                
                if steps:
                    existing_test_cases.append({
                        "test_case_id": test_case_id,
                        "objective": str(objective).strip() if objective else "",
                        "steps": steps
                    })
        
        return existing_test_cases
        
    except Exception as e:
        print(f"Error getting existing test cases for {issue_key}: {e}")
        return []


def process_attachment_for_display(att, attached_images_dir):
    """
    Process a JIRA attachment for display and download.
    
    FUNCTION TYPE: FILE PROCESSING HELPER
    
    Downloads and processes a JIRA attachment, saving it locally
    and preparing it for display in the UI.

    Args:
        att: JIRA attachment object
        attached_images_dir (Path): Directory to save attachments

    Returns:
        tuple: (success: bool, img_data: bytes, img_path: Path, error: str)
            - success: True if processing successful
            - img_data: Raw image data for display
            - img_path: Local path where file was saved
            - error: Error message if processing failed

    Usage Example:
        success, img_data, img_path, error = process_attachment_for_display(att, images_dir)
        if success:
            # Display the image
        else:
            st.error(error)
    """
    try:
        # Download and save the attachment
        img_data = att.get()
        img_path = attached_images_dir / att.filename
        
        with open(img_path, 'wb') as f:
            f.write(img_data)
        
        return True, img_data, img_path, None
        
    except Exception as e:
        error_msg = f"Error processing attachment {att.filename}: {str(e)}"
        return False, None, None, error_msg


def merge_excel_files(input_files, output_file, creating_new_main_file=False):
    """Merges multiple Excel files into a single Excel file with all Test cases in one sheet."""
    try:
        all_dfs = []
        for file in input_files:
            if os.path.exists(file):
                df = pd.read_excel(file)
                all_dfs.append(df)
        
        if all_dfs:
            combined_df = pd.concat(all_dfs, ignore_index=True)
            combined_df.to_excel(output_file, index=False)
            return True
        return False
    except Exception as e:
        import streamlit as st
        st.error(f"Error merging Excel files: {e}")
        return False
