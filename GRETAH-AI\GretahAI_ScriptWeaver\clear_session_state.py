#!/usr/bin/env python3
"""
Clear Session State Script for GretahAI ScriptWeaver

This script helps clear problematic session state entries that might cause
widget key conflicts, particularly the "action_ai_4_ext" issue.
"""

import streamlit as st
import os
import sys

def clear_problematic_session_keys():
    """Clear session state keys that might cause widget conflicts."""
    
    print("🧹 Clearing problematic session state keys...")
    
    # Keys that might cause widget conflicts
    problematic_patterns = [
        "action_ai_",
        "locator_strategy_ai_",
        "locator_ai_",
        "test_data_param_ai_",
        "expected_result_ai_",
        "timeout_ai_",
        "step_type_ai_",
        "assertion_type_ai_",
        "step_description_ai_",
        "condition_ai_",
        "_ext",
        "form_data_ai_",
        "editing_ai_"
    ]
    
    # Clear Streamlit's internal cache directories
    streamlit_cache_dirs = [
        os.path.expanduser("~/.streamlit"),
        ".streamlit",
        "cache",
        ".cache"
    ]
    
    for cache_dir in streamlit_cache_dirs:
        if os.path.exists(cache_dir):
            try:
                import shutil
                shutil.rmtree(cache_dir)
                print(f"✅ Cleared cache directory: {cache_dir}")
            except Exception as e:
                print(f"⚠️ Could not clear {cache_dir}: {e}")
    
    print("✅ Session state cleanup completed!")
    print("\n🔄 Please restart your Streamlit application now.")
    print("   Run: streamlit run app.py")

def clear_temp_files():
    """Clear temporary files that might contain cached state."""
    
    temp_patterns = [
        "*.tmp",
        "*.temp",
        "*_temp.json",
        "*_cache.json"
    ]
    
    import glob
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"✅ Removed temp file: {file}")
            except Exception as e:
                print(f"⚠️ Could not remove {file}: {e}")

if __name__ == "__main__":
    print("🚀 GretahAI ScriptWeaver Session State Cleaner")
    print("=" * 50)
    
    clear_problematic_session_keys()
    clear_temp_files()
    
    print("\n" + "=" * 50)
    print("✅ Cleanup completed!")
    print("\n📋 Next steps:")
    print("1. Close any running Streamlit applications")
    print("2. Wait 5 seconds")
    print("3. Run: streamlit run app.py")
    print("4. The widget key conflict should be resolved")
