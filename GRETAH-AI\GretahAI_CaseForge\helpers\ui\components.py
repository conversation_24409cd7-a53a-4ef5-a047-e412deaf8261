"""
UI Components for GretahAI CaseForge Test Generator

This module contains UI components, styling, and modal functions
for the test generator interface.

Functions:
- create_attachment_grid_html: Generate CSS styles for attachment display grid
- create_jira_details_css: Generate CSS styles for JIRA details display
- render_image_modal: Render modal dialog for image viewing
- render_pdf_modal: Render modal dialog for PDF viewing
- format_test_type_display_name: Format test type for display in UI
- render_test_case_modification_interface: Render AI-powered test case modification UI
- render_jira_issue_section: Render reusable JIRA issue details section

© 2025 GretahAI Team
"""

import os
import base64
from datetime import datetime
import streamlit as st
import pandas as pd


def create_attachment_grid_html():
    """
    Generate CSS styles for attachment display grid.
    
    FUNCTION TYPE: UI STYLING HELPER
    
    Returns the CSS styles needed for displaying JIRA attachments
    in a grid layout.

    Returns:
        str: CSS style definitions for attachment grid

    Usage Example:
        st.markdown(create_attachment_grid_html(), unsafe_allow_html=True)
    """
    return """
    <style>
    .attachment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
        padding: 20px;
    }
    .attachment-item {
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 10px;
        text-align: center;
        background: white;
    }
    .attachment-item img {
        max-width: 100%;
        height: auto;
        border-radius: 5px;
    }
    .attachment-item p {
        margin: 10px 0;
        font-size: 14px;
        color: #666;
    }
    </style>
    """


def create_jira_details_css():
    """
    Generate CSS styles for JIRA details display.
    
    FUNCTION TYPE: UI STYLING HELPER
    
    Returns comprehensive CSS styles for formatting JIRA issue details
    in a consistent and readable manner, optimized for markdown content.

    Returns:
        str: CSS style definitions for JIRA details

    Usage Example:
        st.markdown(create_jira_details_css(), unsafe_allow_html=True)
    """
    return """
    <style>
    .jira-details {
        font-size: 14px !important;
        line-height: 1.5 !important;
        margin-bottom: 10px !important;
    }
    .jira-details h1, .jira-details h2, .jira-details h3, .jira-details h4, .jira-details h5, .jira-details h6 {
        font-weight: bold !important;
        margin-top: 20px !important;
        margin-bottom: 10px !important;
    }
    .jira-details h2 {
        font-size: 20px !important;
    }
    .jira-details h3 {
        font-size: 16px !important;
    }
    .jira-details .timestamp {
        color: #666;
        font-style: italic;
        font-size: 12px !important;
        margin-top: 5px !important;
        margin-bottom: 15px !important;
    }
    .stToggle {
        margin-top: 5px !important;
    }
    .jira-details p {
        font-size: 14px !important;
        margin-bottom: 10px !important;
    }
    .jira-details ul, .jira-details ol {
        font-size: 14px !important;
        margin-left: 20px !important;
        margin-bottom: 10px !important;
    }
    .jira-details li {
        margin-bottom: 5px !important;
        font-size: 14px !important;
    }
    .jira-details strong {
        font-weight: bold !important;
        font-size: 14px !important;
    }
    .jira-details table {
        border-collapse: collapse;
        width: 100%;
        margin: 10px 0;
    }
    .jira-details th, .jira-details td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
        font-size: 14px !important;
    }
    .jira-details th {
        background-color: #f2f2f2;
        font-weight: bold;
    }
    .jira-details * {
        font-size: 14px !important;
    }
    </style>
    """


def render_image_modal():
    """
    Render modal dialog for image viewing.
    
    Handles the image modal display with CSS styling, close functionality,
    and download capabilities.
    
    Returns:
        None (renders directly to Streamlit UI)
    """
    if "modal_image" in st.session_state and st.session_state.modal_image.get("show", False):
        # Create modal dialog using Streamlit dialog
        @st.dialog(st.session_state.modal_image['filename'])
        def show_image_modal():
            # Add custom CSS for better image display in modal
            st.markdown("""
            <style>
            /* Ensure the modal dialog has proper width and height */
            .stDialog > div {
                max-width: 95vw !important;
                max-height: 95vh !important;
                width: auto !important;
            }
            
            /* Center and properly size the image in the modal */
            .stDialog img {
                max-width: 100% !important;
                max-height: 80vh !important;
                object-fit: contain !important;
                margin: 0 auto !important;
                display: block !important;
                border-radius: 8px !important;
            }
            
            /* Ensure the image container is properly centered */
            .stDialog .stImage > div {
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                min-height: 60vh !important;
            }
            </style>
            """, unsafe_allow_html=True)
            
            # Display the image at full size with proper sizing
            try:
                # Check if file exists before displaying
                image_path = st.session_state.modal_image["path"]
                if os.path.exists(image_path):
                    st.image(
                        image_path,
                        caption=st.session_state.modal_image['filename'],
                        use_container_width=True
                    )
                else:
                    st.error(f"Image file not found: {image_path}")
                    st.info("The image file may have been moved or deleted.")
            except Exception as e:
                st.error(f"Error displaying image: {str(e)}")
                st.info("Please check if the image file exists and is accessible.")
                # Show more details for debugging
                st.code(f"Image path: {st.session_state.modal_image.get('path', 'Not available')}")
                st.code(f"Filename: {st.session_state.modal_image.get('filename', 'Not available')}")
            
            # Add action buttons
            col1, col2, col3 = st.columns([1, 2, 1])
            with col1:
                if st.button("❌ Close", key="close_modal_button", use_container_width=True):
                    st.session_state.modal_image["show"] = False
                    st.rerun()
            
            with col2:
                # Add download button
                try:
                    image_path = st.session_state.modal_image["path"]
                    if os.path.exists(image_path):
                        with open(image_path, "rb") as file:
                            st.download_button(
                                label=f"💾 Download {st.session_state.modal_image['filename']}",
                                data=file,
                                file_name=st.session_state.modal_image["filename"],
                                mime=f"image/{os.path.splitext(st.session_state.modal_image['filename'])[1][1:]}",
                                key=f"download_modal_image_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                                use_container_width=True
                            )
                    else:
                        st.error("File not found for download")
                except Exception as e:
                    st.error(f"Error preparing download: {str(e)}")
            
            with col3:
                # Add a button to open image in new tab (if supported by browser)
                if st.button("🔗 Open in New Tab", key="open_new_tab_button", use_container_width=True, help="Try to open image in a new browser tab"):
                    try:
                        image_path = st.session_state.modal_image["path"]
                        if os.path.exists(image_path):
                            # Create a base64 encoded data URL for the image
                            with open(image_path, "rb") as img_file:
                                img_data = img_file.read()
                                img_base64 = base64.b64encode(img_data).decode()
                                file_ext = os.path.splitext(st.session_state.modal_image['filename'])[1][1:].lower()
                                
                                # Create a download link that opens in new tab
                                st.markdown(f"""
                                <script>
                                    window.open('data:image/{file_ext};base64,{img_base64}', '_blank');
                                </script>
                                """, unsafe_allow_html=True)
                                st.success("Image opened in new tab (if supported by your browser)")
                        else:
                            st.error("File not found for opening in new tab")
                    except Exception as e:
                        st.error(f"Error opening in new tab: {str(e)}")
        
        # Show the modal
        show_image_modal()


def render_pdf_modal():
    """
    Render modal dialog for PDF viewing.
    
    Handles the PDF modal display with CSS styling, iframe rendering,
    close functionality, and download capabilities.
    
    Returns:
        None (renders directly to Streamlit UI)
    """
    if "pdf_preview" in st.session_state and st.session_state.pdf_preview.get("show", False):
        # Add custom CSS for the PDF modal
        st.markdown("""
        <style>
        .pdf-modal-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
            position: relative;
            z-index: 1000;
        }
        .pdf-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        </style>
        <div class="pdf-modal-container">
            <h3>{}</h3>
        </div>
        """.format(st.session_state.pdf_preview['filename']), unsafe_allow_html=True)

        # Create a modal-like display for the PDF
        with st.container():
            # Add a close button at the top
            col1, col2 = st.columns([6, 1])
            with col2:
                if st.button("❌ Close", key="close_pdf_button"):
                    st.session_state.pdf_preview["show"] = False
                    # Remove unnecessary rerun - let natural flow handle update

            # Display the PDF using an iframe
            pdf_path = st.session_state.pdf_preview["path"]

            # Create an iframe to display the PDF
            st.markdown(f"""
            <iframe
                src="{pdf_path}"
                class="pdf-frame"
                title="{st.session_state.pdf_preview['filename']}"
            ></iframe>
            """, unsafe_allow_html=True)

            # Add a download button
            try:
                with open(pdf_path, "rb") as file:
                    st.download_button(
                        label=f"💾 Download {st.session_state.pdf_preview['filename']}",
                        data=file,
                        file_name=st.session_state.pdf_preview['filename'],
                        mime="application/pdf",
                        key=f"download_pdf_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                    )
            except Exception as e:
                st.warning(f"Could not create download button: {str(e)}")
                st.markdown(f"Download the file manually from: {pdf_path}")


def format_test_type_display_name(test_type):
    """
    Format test type for display in UI.
    
    FUNCTION TYPE: UI FORMATTING HELPER
    
    Converts internal test type codes to user-friendly display names.

    Args:
        test_type (str): Internal test type code

    Returns:
        str: Formatted display name

    Usage Example:
        display_name = format_test_type_display_name("positive")
        # Returns: "POSITIVE - Success Cases"
    """
    test_type_labels = {
        "all": "All Test Case Types",
        "positive": "POSITIVE - Success Cases", 
        "negative": "NEGATIVE - Error Cases",
        "security": "SECURITY - Security Tests",
        "performance": "PERFORMANCE - Speed & Load"
    }
    return test_type_labels.get(test_type, test_type.upper())


def render_test_case_modification_interface(
    test_cases_df: pd.DataFrame, 
    jira_issue, 
    selected_model: str, 
    google_api_key: str = None,
    tab_key: str = "general",
    store_in_session: bool = False,
    show_action_buttons: bool = False) -> pd.DataFrame:
    """
    Render the AI-powered test case modification interface.
    
    This function provides a UI for users to request modifications to existing
    test cases using natural language queries, which are then processed by AI
    to generate updated test cases.
    
    Args:
        test_cases_df (pd.DataFrame): Current test cases dataframe
        jira_issue: JIRA issue object containing story details
        selected_model (str): Selected AI model name
        google_api_key (str): Google AI API key if using Google provider
        tab_key (str): Unique key for this tab to avoid Streamlit conflicts
        store_in_session (bool): Allow caller to request storing in session state
        show_action_buttons (bool): Show update table and undo buttons for test analysis
        
    Returns:
        pd.DataFrame: Modified test cases dataframe (same as input if no changes)
    """
    # Import the AI modification function
    from helpers.ai.llm_providers import modify_test_cases_with_ai
    
    # Create an expander for the modification interface
    with st.expander("🤖 AI Test Case Modification", expanded=False):
        # Show action buttons at the top if requested (for test analysis tab and test generator)
        if show_action_buttons:
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("🔄 Update Table with AI Output", key=f"update_table_with_ai_output_{tab_key}"):
                    ai_modified_key = f"ai_modified_df_{tab_key}"
                    table_updated_key = f"{tab_key}_table_updated_with_ai"
                    editor_data_key = f"{tab_key}_editor_data"
                    
                    # Enhanced debugging and validation
                    print(f"DEBUG: Update Table button clicked for tab_key: {tab_key}")
                    print(f"DEBUG: Looking for AI modified data in key: {ai_modified_key}")
                    print(f"DEBUG: Session state keys containing 'ai_modified': {[k for k in st.session_state.keys() if 'ai_modified' in k]}")
                    print(f"DEBUG: Session state keys containing tab_key '{tab_key}': {[k for k in st.session_state.keys() if tab_key in k]}")
                    
                    if ai_modified_key in st.session_state and st.session_state[ai_modified_key] is not None:
                        print(f"DEBUG: Found AI modified data with shape: {st.session_state[ai_modified_key].shape}")
                        
                        # Store the AI modified data in the editor data key
                        st.session_state[editor_data_key] = st.session_state[ai_modified_key].copy()
                        st.session_state[table_updated_key] = True
                        
                        # For test generator tabs, clear ALL related editor keys to force complete refresh
                        if tab_key == "test_generator":
                            keys_to_clear = [
                                "generated_test_cases_editor",
                                "test_generator_editor_data",
                                "test_generator_editor"
                            ]
                        elif tab_key == "most_recent":
                            keys_to_clear = [
                                "most_recent_test_cases_editor", 
                                "most_recent_editor_data",
                                "most_recent_editor"
                            ]
                        else:
                            keys_to_clear = [f"{tab_key}_editor", f"{tab_key}_editor_data"]
                        
                        for key in keys_to_clear:
                            if key in st.session_state:
                                del st.session_state[key]
                                print(f"DEBUG: Cleared key: {key}")
                        
                        print(f"DEBUG: Updated editor data and cleared specific editor keys")
                        st.success("✅ Table updated with latest AI modifications.")
                        st.rerun()
                    else:
                        print(f"DEBUG: No AI modified data found in session state")
                        # Show more detailed error information
                        available_keys = [k for k in st.session_state.keys() if 'ai_modified' in k or tab_key in k]
                        if available_keys:
                            st.warning(f"⚠️ No AI modifications found for key '{ai_modified_key}'. Available related keys: {available_keys}")
                        else:
                            st.warning("⚠️ No AI modifications to apply. Generate modifications first using the '🚀 Apply AI Modifications' button.")

            with col2:
                if st.button("↩️ Undo All Modifications", key=f"undo_all_modifications_{tab_key}"):
                    original_df_key = f"original_{tab_key}_df"
                    table_updated_key = f"{tab_key}_table_updated_with_ai"
                    editor_data_key = f"{tab_key}_editor_data"
                    ai_modified_key = f"ai_modified_df_{tab_key}"
                    
                    print(f"DEBUG: Undo button clicked for tab_key: {tab_key}")
                    print(f"DEBUG: Looking for original data in key: {original_df_key}")
                    
                    if original_df_key in st.session_state:
                        print(f"DEBUG: Found original data with shape: {st.session_state[original_df_key].shape}")
                        
                        # Restore original data
                        st.session_state[editor_data_key] = st.session_state[original_df_key].copy()
                        st.session_state[table_updated_key] = False
                        
                        # Clear AI modified data
                        if ai_modified_key in st.session_state:
                            del st.session_state[ai_modified_key]
                        
                        # For test generator tabs, clear ALL related editor keys to force complete refresh
                        if tab_key == "test_generator":
                            keys_to_clear = [
                                "generated_test_cases_editor",
                                "test_generator_editor_data", 
                                "test_generator_editor"
                            ]
                        elif tab_key == "most_recent":
                            keys_to_clear = [
                                "most_recent_test_cases_editor",
                                "most_recent_editor_data",
                                "most_recent_editor"
                            ]
                        else:
                            keys_to_clear = [f"{tab_key}_editor", f"{tab_key}_editor_data"]
                        
                        for key in keys_to_clear:
                            if key in st.session_state:
                                del st.session_state[key]
                                print(f"DEBUG: Cleared key: {key}")
                        
                        print(f"DEBUG: Restored original data and cleared AI modifications")
                        st.success("↩️ All modifications have been undone. Original data restored.")
                        st.rerun()
                    else:
                        print(f"DEBUG: No original data found in session state")
                        st.warning("⚠️ No original data found to restore.")

        st.markdown("""
        **Use AI to modify your test cases** - Describe what changes you want to make 
        and the AI will update your test cases accordingly.
        
        **Example queries:**
        - "Add more security-focused test cases"
        - "Make the test steps more detailed"
        - "Add edge cases for input validation"
        - "Include performance considerations"
        - "Add negative test scenarios"
        """)
        
        # User query input
        user_query = st.text_area(
            "Describe the changes you want to make:",
            placeholder="e.g., Add more detailed test steps for each scenario...",
            key=f"modification_query_{tab_key}",
            height=100
        )
        
        # Modification options
        col1, col2 = st.columns([1, 1])

        with col1:
            # AI provider info (simplified - details available in tooltips)
            st.markdown(f"**AI Provider:** Cloud ({selected_model})")

        with col2:
            # Show current test case count
            test_case_count = len(test_cases_df["Test Case ID"].dropna().unique()) if "Test Case ID" in test_cases_df.columns else len(test_cases_df)
            st.markdown(f"**Test Cases:** {test_case_count}")
        
        # Modify button
        if st.button(
            "🚀 Apply AI Modifications", 
            key=f"apply_modifications_{tab_key}",
            disabled=not user_query.strip(),
            help="Click to apply the requested modifications using AI"
        ):
            if not user_query.strip():
                st.warning("Please enter a modification request.")
                return test_cases_df
                
            # Get JIRA description for context
            jira_description = ""
            if jira_issue:
                jira_description = f"Summary: {jira_issue.fields.summary}\n"
                if hasattr(jira_issue.fields, 'description') and jira_issue.fields.description:
                    jira_description += f"Description: {jira_issue.fields.description}"
            
            # Show progress
            with st.spinner("🤖 AI is analyzing and modifying your test cases..."):
                try:
                    # Call AI modification function
                    success, modified_df, error_message = modify_test_cases_with_ai(
                        test_cases_df=test_cases_df,
                        jira_description=jira_description,
                        user_query=user_query,
                        model=selected_model,
                        google_api_key=google_api_key
                    )
                    
                    if success:
                        st.success("✅ Test cases modified successfully!")

                        # Show both the JSON sent to the AI and the raw AI output in tabs (always, even if validation fails)
                        import json
                        sent_json = json.dumps(test_cases_df.to_dict(orient="records"), indent=2)
                        raw_json = json.dumps(modified_df.to_dict(orient="records"), indent=2)
                        tabs = st.tabs(["JSON Input Sent to AI", "Raw AI Output"])
                        with tabs[0]:
                            st.markdown(f"""
<details open>
<summary><b>📤 JSON Input Sent to AI (click to collapse)</b></summary>

```json
{sent_json}
```
</details>
""", unsafe_allow_html=True)
                        with tabs[1]:
                            st.markdown(f"""
<details open>
<summary><b>📝 Raw AI Output (click to collapse)</b></summary>

```json
{raw_json}
```
</details>
""", unsafe_allow_html=True)

                        # Validate AI output structure before accepting changes
                        if (
                            len(modified_df) != len(test_cases_df)
                            or set(modified_df.columns) != set(test_cases_df.columns)
                        ):
                            st.error("❌ AI output does not match the original test case structure. No changes applied. Please rephrase your request or try again.")
                            if st.button("Display Raw AI Output in Table (Override)", key=f"display_raw_ai_output_{tab_key}"):
                                st.warning("⚠️ You are overriding validation. Please review the table carefully for missing or extra columns/rows.")
                                # Store the modification even if validation fails (for override)
                                if store_in_session:
                                    st.session_state[f"ai_modified_df_{tab_key}"] = modified_df.copy()
                        print(f"DEBUG: Stored AI modified data for {tab_key} with key '{ai_modified_key}' and shape: {modified_df.shape}")
                        
                        # Debug: Verify the data was stored correctly
                        if ai_modified_key in st.session_state:
                            stored_df = st.session_state[ai_modified_key]
                            print(f"DEBUG: Verification - AI modified data stored successfully: {stored_df.shape}")
                            print(f"DEBUG: First few rows of stored data: {stored_df.head(2).to_dict()}")
                        else:
                            print(f"DEBUG: ERROR - AI modified data was not stored properly!")
                        
                        # Show instruction to user
                        st.info("📋 Click the '🔄 Update Table with AI Output' button above to apply these changes to the table.")
                        
                        return modified_df
                        
                    else:
                        st.error(f"❌ Modification failed: {error_message}")
                        if store_in_session:
                            st.session_state[f"ai_modified_df_{tab_key}"] = None
                        return test_cases_df
                        
                except Exception as e:
                    st.error(f"❌ Error during modification: {str(e)}")
                    if store_in_session:
                        st.session_state[f"ai_modified_df_{tab_key}"] = None
                    return test_cases_df
        
        # Show modification history if available (avoid nested expander)
        if f"modification_history_{tab_key}" in st.session_state and st.session_state[f"modification_history_{tab_key}"]:
            st.markdown("<hr>", unsafe_allow_html=True)
            st.markdown("**📜 Modification History**")
            history = st.session_state[f"modification_history_{tab_key}"]
            for i, mod in enumerate(reversed(history[-5:])):  # Show last 5 modifications
                st.markdown(f"**{mod['timestamp']}** (Cloud - {mod['model']})")
                st.markdown(f"*{mod['query']}*")
                if i < len(history) - 1:
                    st.markdown("---")
    
    return test_cases_df


def render_jira_issue_section(issue, enhanced_desc=None, enhanced_timestamp=None):
    """
    Render a reusable JIRA issue details section.
    
    Args:
        issue: JIRA issue object
        enhanced_desc: Optional enhanced description text
        enhanced_timestamp: Optional timestamp when enhancement was created
    
    Returns:
        None (renders directly to Streamlit UI)
    """
    import streamlit as st
    from datetime import datetime
    import re
    from ..jira.operations import format_jira_description, parse_enhanced_description_json
    # from ..ui.styles import create_jira_details_css
    # Use the local function defined above instead of importing
    
    # Add custom CSS for consistent font sizes
    st.markdown(create_jira_details_css(), unsafe_allow_html=True)
    
    # Add JIRA Details header
    st.markdown("""
    <div class="jira-details">
    <h2>JIRA Details</h2>
    </div>
    """, unsafe_allow_html=True)

    c1, c2 = st.columns(2)
    with c1:
        st.markdown('<div class="jira-details"><p><strong>Issue Key:</strong> ' + issue.key + '</p></div>', unsafe_allow_html=True)
        st.markdown('<div class="jira-details"><p><strong>Summary:</strong> ' + issue.fields.summary + '</p></div>', unsafe_allow_html=True)
    with c2:
        st.markdown('<div class="jira-details"><p><strong>Status:</strong> ' + issue.fields.status.name + '</p></div>', unsafe_allow_html=True)
        created_date = datetime.strptime(issue.fields.created, '%Y-%m-%dT%H:%M:%S.%f%z').strftime('%Y-%m-%d')
        st.markdown('<div class="jira-details"><p><strong>Created:</strong> ' + created_date + '</p></div>', unsafe_allow_html=True)

    # Display Description with enhancement toggle
    description = issue.fields.description or "No description found."
    
    # Initialize session state for toggle if it doesn't exist
    if "use_enhanced_description" not in st.session_state:
        st.session_state["use_enhanced_description"] = False
    
    # Simple toggle display logic
    col1, col2, col3 = st.columns([2, 2, 1])
    with col1:
        st.markdown('<div class="jira-details"><h3>Description</h3></div>', unsafe_allow_html=True)
    with col2:
        if enhanced_desc and enhanced_timestamp:
            st.markdown(f"**Enhanced:** {enhanced_timestamp}")
    with col3:
        if enhanced_desc:
            show_enhanced = st.toggle("Show Enhanced", key="use_enhanced_description",
                                  help="Toggle to view enhanced description")
        else:
            st.markdown("**Enhanced:** Not available")
            show_enhanced = False

    # Determine which description to show based on toggle state
    if show_enhanced and enhanced_desc:
        # Use the helper function to parse and format enhanced description
        formatted_description = parse_enhanced_description_json(enhanced_desc)
        
        # Display the formatted enhanced description as markdown
        st.markdown(formatted_description, unsafe_allow_html=True)
    else:
        # Use the centralized helper function to format the original description as markdown
        formatted_description = format_jira_description(description)
        
        # Display the formatted description as markdown
        st.markdown(formatted_description, unsafe_allow_html=True)

    # Display acceptance criteria if available
    acceptance_criteria = getattr(issue.fields, 'customfield_10005', None)
    if acceptance_criteria:
        st.markdown('<div class="jira-details"><h3>Acceptance Criteria</h3></div>', unsafe_allow_html=True)

        # Format the acceptance criteria for better readability
        formatted_criteria = acceptance_criteria
        # Format bullet points and numbered lists
        formatted_criteria = re.sub(r'^\s*\*\s+(.+?)$', r'<li>\1</li>', formatted_criteria, flags=re.MULTILINE)
        formatted_criteria = re.sub(r'^\s*#\s+(.+?)$', r'<li>\1</li>', formatted_criteria, flags=re.MULTILINE)
        # Wrap lists in ul/ol tags
        if '<li>' in formatted_criteria:
            formatted_criteria = re.sub(r'(<li>.*?</li>)', r'<ul>\1</ul>', formatted_criteria, flags=re.DOTALL)
            # Fix nested lists (remove extra ul tags)
            formatted_criteria = formatted_criteria.replace('</li>\n<ul>', '')
            formatted_criteria = formatted_criteria.replace('</ul>\n<li>', '</li>')

        # Format headings
        formatted_criteria = re.sub(r'^h([1-6])\.\s+(.+?)$', r'<h\1>\2</h\1>', formatted_criteria, flags=re.MULTILINE)

        # Format bold and italic text
        formatted_criteria = re.sub(r'\*([^*]+)\*', r'<strong>\1</strong>', formatted_criteria)
        formatted_criteria = re.sub(r'_([^_]+)_', r'<em>\1</em>', formatted_criteria)

        # Handle paragraphs
        formatted_criteria = '<p>' + formatted_criteria.replace('\n\n', '</p><p>') + '</p>'
        # Clean up any double paragraph tags
        formatted_criteria = formatted_criteria.replace('<p><p>', '<p>')
        formatted_criteria = formatted_criteria.replace('</p></p>', '</p>')

        # Display the formatted acceptance criteria
        st.markdown('<div class="jira-details">' + formatted_criteria + '</div>', unsafe_allow_html=True)


def show_extraction_guidance(extract_button_pressed):
    """
    Show guidance for JIRA extraction when no issue is loaded.

    Args:
        extract_button_pressed (bool): Whether the extract button was pressed
    """
    # Guidance is now provided through tooltips, so this function is simplified
    pass
