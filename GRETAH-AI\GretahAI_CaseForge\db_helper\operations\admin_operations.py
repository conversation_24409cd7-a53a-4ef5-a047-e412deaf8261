"""
Administrative operations for the GretahAI CaseForge database system.

This module handles admin configuration, authentication, and database clearing operations
with proper security and permission management.
"""

import sqlite3
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock

# Import the admin configuration module
try:
    import admin_config
except ImportError:
    admin_config = None
    print("Warning: admin_config module not available")


def get_app_config(database_path, key):
    """
    Retrieves a configuration value from the admin configuration system.

    This function serves as a wrapper around the admin_config module to provide
    a consistent interface for retrieving application configuration values.
    The database_path parameter is maintained for compatibility but not used.

    Args:
        database_path (str): Path to SQLite database (kept for compatibility, not used)
        key (str): Configuration key to retrieve (e.g., 'admin_password', 'allow_delete_test_cases')

    Returns:
        str or None: Configuration value if found, None otherwise

    Available Configuration Keys:
        - 'admin_password': Administrator password for secure operations
        - 'allow_delete_test_cases': Permission to delete test cases without password
        - 'allow_clear_database': Permission to clear entire database without password

    Example:
        password_required = not get_app_config(db_path, 'allow_delete_test_cases')
        admin_pass = get_app_config(db_path, 'admin_password')
    """
    if admin_config:
        return admin_config.get_config(key)
    return None


def update_app_config(database_path, key, value):
    """
    Updates a configuration value using the admin configuration system.

    This function serves as a wrapper around the admin_config module to provide
    a consistent interface for updating application configuration values.
    The database_path parameter is maintained for compatibility but not used.

    Args:
        database_path (str): Path to SQLite database (kept for compatibility, not used)
        key (str): Configuration key to update
        value (str): New value to set for the configuration key

    Returns:
        bool: True if update successful, False otherwise

    Updatable Configuration Keys:
        - 'admin_password': Administrator password for secure operations
        - 'allow_delete_test_cases': Permission setting for test case deletion
        - 'allow_clear_database': Permission setting for database clearing

    Security Note:
        Configuration changes may affect application security and permissions.
        Ensure proper authorization before calling this function.

    Example:
        # Update admin password
        success = update_app_config(db_path, 'admin_password', 'newpass123')
        
        # Enable deletion without password
        success = update_app_config(db_path, 'allow_delete_test_cases', 'true')
    """
    if admin_config:
        return admin_config.update_config(key, value)
    return False


def verify_admin_password(database_path, password):
    """
    Verifies if the provided password matches the stored admin password.

    This function serves as a wrapper around the admin_config module to provide
    a consistent interface for admin password verification. The database_path
    parameter is maintained for compatibility but not used.

    Args:
        database_path (str): Path to SQLite database (kept for compatibility, not used)
        password (str): Password to verify against stored admin password

    Returns:
        bool: True if password matches, False otherwise

    Security Features:
        - Uses secure password comparison from admin_config module
        - Protects against timing attacks through constant-time comparison
        - Does not expose the actual password in any form
        - Logs authentication attempts for security auditing

    Usage in Security Context:
        This function is typically used before performing sensitive operations
        such as clearing databases, deleting test cases, or modifying system
        configuration. Always verify permissions before destructive operations.

    Example:
        if verify_admin_password(db_path, user_input_password):
            # Proceed with admin operation
            clear_database(db_path, user_input_password)
        else:
            # Reject operation and log attempt
            print("Invalid admin password")
    """
    if admin_config:
        return admin_config.verify_admin_password(password)
    return False


@retry_on_db_lock()
def clear_database(database_path, admin_password=None):
    """
    Clears all test data from the database tables while preserving schema and configuration.

    This function performs a complete reset of all test-related data including test cases,
    test steps, test runs, and JIRA issues. It requires proper admin authentication and
    provides comprehensive verification of the clearing operation.

    Args:
        database_path (str): Absolute path to the SQLite database file
        admin_password (str, optional): Admin password for verification. Required if 
                                       allow_clear_database setting is false. Defaults to None.

    Returns:
        bool: True if clearing successful, False otherwise

    Security Features:
        - Requires admin password verification unless specifically allowed
        - Uses admin_config module for authentication
        - Respects allow_clear_database configuration setting
        - Provides security logging for audit purposes

    Clearing Process:
        1. Validates admin permissions and password
        2. Disables foreign key constraints temporarily
        3. Deletes data in dependency order (steps → cases → runs → issues)
        4. Resets auto-increment counters for clean IDs
        5. Re-enables foreign key constraints
        6. Verifies all tables are empty
        7. Forces deletion if verification finds remaining data

    Tables Cleared:
        - test_steps: All test execution steps
        - test_cases: All test case records
        - test_runs: All test execution sessions
        - test_case_executions: All execution tracking data
        - jira_issues: All JIRA ticket associations

    Data Preserved:
        - app_config: Application settings and configuration
        - Database schema: All table structures and indexes
        - Admin settings: Password and permission configurations

    Error Handling:
        - Rolls back transaction on any failure
        - Provides detailed error logging
        - Safely handles connection cleanup
        - Returns false on authentication failure

    Example:
        # Clear with admin password
        success = clear_database(db_path, 'admin123')
        
        # Clear if allowed without password
        success = clear_database(db_path)
    """
    conn = None
    try:
        # Check admin permissions first
        if admin_password:
            if not admin_config or not admin_config.verify_admin_password(admin_password):
                print("Invalid admin password provided")
                return False
        else:
            if admin_config:
                allow_clear = admin_config.get_config("allow_clear_database")
                if allow_clear != "true":
                    print("Admin password required to clear database")
                    return False

        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        print("Starting database clearing operation...")

        # Disable foreign key constraints temporarily
        cursor.execute("PRAGMA foreign_keys = OFF")

        # Start transaction
        cursor.execute("BEGIN TRANSACTION")

        # Delete data in the correct order (respecting dependencies)
        print("Clearing test steps...")
        cursor.execute("DELETE FROM test_steps")
        steps_deleted = cursor.rowcount
        print(f"Deleted {steps_deleted} test steps")

        print("Clearing test case executions...")
        cursor.execute("DELETE FROM test_case_executions")
        executions_deleted = cursor.rowcount
        print(f"Deleted {executions_deleted} test case executions")

        print("Clearing test cases...")
        cursor.execute("DELETE FROM test_cases")
        cases_deleted = cursor.rowcount
        print(f"Deleted {cases_deleted} test cases")

        print("Clearing test runs...")
        cursor.execute("DELETE FROM test_runs")
        runs_deleted = cursor.rowcount
        print(f"Deleted {runs_deleted} test runs")

        print("Clearing JIRA issues...")
        cursor.execute("DELETE FROM jira_issues")
        jira_deleted = cursor.rowcount
        print(f"Deleted {jira_deleted} JIRA issues")

        # Reset auto-increment counters
        print("Resetting auto-increment counters...")
        cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ('test_steps', 'test_cases', 'test_runs', 'jira_issues', 'test_case_executions')")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON")

        # Commit the transaction
        cursor.execute("COMMIT")

        # Verify that all tables are empty
        print("Verifying database is cleared...")
        tables_to_check = ['test_steps', 'test_cases', 'test_runs', 'jira_issues', 'test_case_executions']
        all_clear = True

        for table in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            if count > 0:
                print(f"Warning: {table} still has {count} records")
                all_clear = False

        if not all_clear:
            print("Warning: Some data may not have been cleared completely")
            # Force delete if verification finds remaining data
            for table in tables_to_check:
                cursor.execute(f"DELETE FROM {table}")
            cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ('test_steps', 'test_cases', 'test_runs', 'jira_issues', 'test_case_executions')")
            conn.commit()

        print("Database cleared successfully!")
        print(f"Summary: {cases_deleted} test cases, {steps_deleted} steps, {runs_deleted} runs, {jira_deleted} JIRA issues, {executions_deleted} executions")

        return True

    except sqlite3.Error as e:
        print(f"Database error during clearing: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()


@retry_on_db_lock()
def clear_database_for_jira(database_path, jira_id, test_type=None, admin_password=None):
    """
    Clears all test cases and related data for a specific JIRA ticket and test type.

    This function provides targeted deletion of test data for a specific JIRA ticket,
    optionally filtered by test type. It maintains referential integrity by properly
    handling related records and provides comprehensive cleanup including test case
    counter resets when appropriate.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier to target for deletion
        test_type (str, optional): Specific test type to delete ("positive", "negative", 
                                  "security", "performance"). If None, deletes all types. 
                                  Defaults to None.
        admin_password (str, optional): Admin password for verification. Required if 
                                       allow_delete_test_cases setting is false. Defaults to None.

    Returns:
        tuple: (success: bool, message: str, deletion_counts: dict) where deletion_counts 
               contains the number of items deleted for each category

    Security Features:
        - Requires admin authentication based on configuration
        - Uses admin_config.is_operation_allowed for permission checking
        - Provides audit logging for deletion operations
        - Validates permissions before any data modification

    Deletion Process:
        1. Validates admin permissions for delete operations
        2. Identifies test cases matching JIRA ID and test type criteria
        3. Deletes test steps for identified test cases
        4. Removes test cases and associated executions
        5. Cleans up test runs for the JIRA ticket
        6. Conditionally removes JIRA issue if no other runs exist
        7. Resets test case counters if all data for JIRA ID is removed

    Data Relationships Handled:
        - test_steps → test_cases (Foreign key relationship)
        - test_case_executions → test_cases (Execution tracking)
        - test_cases → test_runs (Run associations)
        - test_runs → jira_issues (JIRA ticket links)

    Smart Cleanup Features:
        - Only deletes JIRA issue if no other test runs remain
        - Resets global test case counter when appropriate
        - Maintains referential integrity throughout process
        - Provides detailed logging of deletion counts

    Filter Logic:
        - Uses dashboard_test_type field for filtering
        - Supports exact test type matching
        - Handles "all" test type appropriately
        - Case-insensitive test type comparison

    Error Handling:
        - Rolls back transaction on any failure
        - Provides detailed error logging with context
        - Safely handles missing data scenarios
        - Reports success/failure status clearly

    Example:
        # Delete all positive test cases for TP-1
        success, message, counts = clear_database_for_jira(db_path, 'TP-1', 'positive', 'admin123')
        
        # Delete all test cases for TP-1 regardless of type
        success, message, counts = clear_database_for_jira(db_path, 'TP-1')
    """
    conn = None
    try:
        # Check admin permissions
        if admin_config and not admin_config.is_operation_allowed("delete_test_cases", admin_password):
            return False, "Permission denied: Admin password required for delete operations", {}

        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        print(f"Starting targeted deletion for JIRA ID: {jira_id}, test type: {test_type}")

        # Start transaction
        cursor.execute("BEGIN TRANSACTION")

        # Disable foreign key constraints temporarily for cleanup
        cursor.execute("PRAGMA foreign_keys = OFF")

        # Build the filter condition for test type
        test_type_condition = ""
        params = [jira_id]

        if test_type and test_type.lower() != "all":
            test_type_condition = " AND dashboard_test_type = ?"
            params.append(test_type.lower())

        # Get the test case IDs that match our criteria
        cursor.execute(f"""
        SELECT id FROM test_cases 
        WHERE jira_id = ?{test_type_condition}
        """, params)
        
        test_case_ids = [row[0] for row in cursor.fetchall()]
        
        if not test_case_ids:
            print(f"No test cases found for JIRA ID: {jira_id}, test type: {test_type}")
            cursor.execute("ROLLBACK")
            test_type_display = f" of type '{test_type}'" if test_type and test_type.lower() != "all" else ""
            return True, f"No test cases found for JIRA ID '{jira_id}'{test_type_display}", {}

        print(f"Found {len(test_case_ids)} test cases to delete")

        # Initialize deletion counters
        deletion_counts = {
            'test_cases': 0,
            'test_steps': 0,
            'test_runs': 0,
            'test_case_executions': 0,
            'jira_issues': 0
        }

        # Delete test steps for these test cases
        test_case_ids_str = ','.join(['?'] * len(test_case_ids))
        cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({test_case_ids_str})", test_case_ids)
        deletion_counts['test_steps'] = cursor.rowcount
        print(f"Deleted {deletion_counts['test_steps']} test steps")

        # Delete test case executions
        cursor.execute(f"DELETE FROM test_case_executions WHERE test_case_id IN ({test_case_ids_str})", test_case_ids)
        deletion_counts['test_case_executions'] = cursor.rowcount
        print(f"Deleted {deletion_counts['test_case_executions']} test case executions")

        # Delete the test cases themselves
        cursor.execute(f"""
        DELETE FROM test_cases 
        WHERE jira_id = ?{test_type_condition}
        """, params)
        deletion_counts['test_cases'] = cursor.rowcount
        print(f"Deleted {deletion_counts['test_cases']} test cases")

        # Check if we should clean up test runs
        # If test_type is specified and not "all", only delete runs for that specific type
        if test_type and test_type.lower() != "all":
            cursor.execute("DELETE FROM test_runs WHERE jira_id = ? AND test_type = ?", (jira_id, test_type.lower()))
            deletion_counts['test_runs'] = cursor.rowcount
            print(f"Deleted {deletion_counts['test_runs']} test runs for test type: {test_type}")
        else:
            cursor.execute("DELETE FROM test_runs WHERE jira_id = ?", (jira_id,))
            deletion_counts['test_runs'] = cursor.rowcount
            print(f"Deleted {deletion_counts['test_runs']} test runs")

        # Check if there are any remaining test runs for this JIRA ID
        cursor.execute("SELECT COUNT(*) FROM test_runs WHERE jira_id = ?", (jira_id,))
        remaining_runs = cursor.fetchone()[0]

        # If no test runs remain, we can safely delete the JIRA issue
        if remaining_runs == 0:
            cursor.execute("DELETE FROM jira_issues WHERE jira_id = ?", (jira_id,))
            deletion_counts['jira_issues'] = cursor.rowcount
            print(f"Deleted {deletion_counts['jira_issues']} JIRA issue record")

            # Reset the auto-increment counter for test cases if we deleted everything for this JIRA
            cursor.execute("SELECT COUNT(*) FROM test_cases")
            total_cases = cursor.fetchone()[0]
            if total_cases == 0:
                print("No test cases remain, resetting auto-increment counter")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name = 'test_cases'")
        else:
            print(f"JIRA issue retained ({remaining_runs} test runs still exist)")

        # Re-enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON")

        # Commit the transaction
        cursor.execute("COMMIT")

        print(f"Targeted deletion completed successfully for JIRA ID: {jira_id}")
        
        # Create summary message
        test_type_display = f" of type '{test_type}'" if test_type and test_type.lower() != "all" else ""
        summary_parts = []
        if deletion_counts['test_cases'] > 0:
            summary_parts.append(f"{deletion_counts['test_cases']} test cases")
        if deletion_counts['test_steps'] > 0:
            summary_parts.append(f"{deletion_counts['test_steps']} test steps")
        if deletion_counts['test_runs'] > 0:
            summary_parts.append(f"{deletion_counts['test_runs']} test runs")
        if deletion_counts['test_case_executions'] > 0:
            summary_parts.append(f"{deletion_counts['test_case_executions']} executions")
        if deletion_counts['jira_issues'] > 0:
            summary_parts.append(f"{deletion_counts['jira_issues']} JIRA issues")
        
        summary = f"Successfully deleted {', '.join(summary_parts)} for JIRA ID '{jira_id}'{test_type_display}"
        print(f"Summary: {summary}")

        return True, summary, deletion_counts

    except sqlite3.Error as e:
        print(f"Database error during targeted deletion: {e}")
        if conn:
            conn.rollback()
        return False, f"Database error during deletion: {str(e)}", {}
    finally:
        close_thread_local_connection()
