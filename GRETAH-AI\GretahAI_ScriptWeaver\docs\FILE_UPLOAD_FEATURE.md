# File Upload Feature Documentation

## Overview

The File Upload Feature in GretahAI ScriptWeaver allows users to upload files (images, documents, data files) as test data for automated testing scenarios. This feature enhances the testing capabilities by enabling real file upload automation in generated PyTest scripts.

## Features

### Supported File Types

#### Images
- **Extensions**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.svg`, `.webp`
- **Max Size**: 10MB
- **Use Cases**: Profile picture uploads, image galleries, avatar testing

#### Documents
- **Extensions**: `.pdf`, `.docx`, `.doc`, `.txt`, `.rtf`
- **Max Size**: 25MB
- **Use Cases**: Document upload testing, file attachment scenarios

#### Data Files
- **Extensions**: `.csv`, `.json`, `.xml`, `.xlsx`, `.xls`
- **Max Size**: 50MB
- **Use Cases**: Data import testing, bulk upload scenarios

#### Archives
- **Extensions**: `.zip`
- **Max Size**: 100MB
- **Use Cases**: Bulk file upload testing, compressed file handling

## How to Use

### Step 1: Navigate to Stage 5 (Test Data Configuration)

1. Complete Stages 1-4 of the ScriptWeaver workflow
2. In Stage 5, you'll see two tabs:
   - **Manual Text Input**: For traditional text-based test data
   - **File Upload**: For uploading files as test data

### Step 2: Upload Files

1. Click on the **File Upload** tab
2. Review the file upload suggestions based on your test step
3. Use the file uploader to select your file
4. The system will automatically:
   - Validate file type and size
   - Perform security checks
   - Generate a preview (for supported file types)

### Step 3: Configure File Metadata

1. Add an optional description for the file's purpose
2. Click **Save File** to store the file for testing
3. The file will be automatically integrated into your test data

### Step 4: Generated Script Integration

The uploaded file will be automatically integrated into your generated PyTest script with:
- Proper file path resolution
- Selenium file upload automation
- Error handling for missing files
- File existence validation

## Security Features

### File Validation
- **Magic Byte Validation**: Verifies file content matches the extension
- **MIME Type Checking**: Ensures file type consistency
- **Size Limits**: Enforces category-specific size restrictions
- **Extension Filtering**: Blocks dangerous file types

### Content Analysis
- **Malicious Pattern Detection**: Scans for suspicious content
- **Archive Validation**: Checks ZIP files for malicious contents
- **Path Traversal Protection**: Prevents directory traversal attacks
- **Executable Detection**: Blocks executable files

### Quarantine System
- Suspicious files are automatically quarantined
- Security reports generated for all uploads
- Detailed threat analysis and recommendations

## File Storage Structure

```
GretahAI_ScriptWeaver/
├── test_data_uploads/
│   ├── images/              # Image files
│   ├── documents/           # Document files
│   ├── data_files/         # Data files
│   ├── archives/           # Archive files
│   └── metadata/           # File metadata
└── quarantine/             # Quarantined files
```

## Generated Script Examples

### Image Upload Example
```python
# File upload for image: profile_picture.jpg
try:
    # Locate the file input element
    file_input = browser.find_element(By.CSS_SELECTOR, "input[type='file']")
    
    # Verify file exists before upload
    upload_file_path = r"test_data_uploads/images/step_3_20250121_143022_a1b2c3d4.jpg"
    if not os.path.exists(upload_file_path):
        raise FileNotFoundError(f"Upload file not found: {upload_file_path}")
    
    # Upload the image file
    file_input.send_keys(upload_file_path)
    
    # Wait for upload to complete
    WebDriverWait(browser, 30).until(
        lambda driver: driver.execute_script("return document.readyState") == "complete"
    )
    
    print(f"Successfully uploaded image: profile_picture.jpg")
    
except FileNotFoundError as e:
    print(f"File not found error: {e}")
    raise
except Exception as e:
    print(f"Error uploading image file: {e}")
    raise
```

### Document Upload Example
```python
# File upload for document: test_document.pdf
try:
    # Locate the file input element
    file_input = browser.find_element(By.CSS_SELECTOR, "input[type='file']")
    
    # Upload the document file
    upload_file_path = r"test_data_uploads/documents/step_2_20250121_143022_e5f6g7h8.pdf"
    file_input.send_keys(upload_file_path)
    
    # Wait for upload and processing to complete
    WebDriverWait(browser, 60).until(
        lambda driver: driver.execute_script("return document.readyState") == "complete"
    )
    
    print(f"Successfully uploaded document: test_document.pdf")
    
except Exception as e:
    print(f"Error uploading document file: {e}")
    raise
```

## Configuration

### File Size Limits
You can modify file size limits in `core/file_upload_manager.py`:

```python
SUPPORTED_FILE_TYPES = {
    'images': {
        'max_size_mb': 10,  # Modify as needed
        # ...
    }
}
```

### Security Settings
Security validation can be configured in `core/file_security_validator.py`:

```python
# Maximum file sizes by category (in bytes)
MAX_FILE_SIZES = {
    'images': 10 * 1024 * 1024,      # 10MB
    'documents': 25 * 1024 * 1024,   # 25MB
    # ...
}
```

## Troubleshooting

### Common Issues

#### File Upload Fails
- **Check file size**: Ensure file is within size limits
- **Verify file type**: Only supported extensions are allowed
- **Security validation**: File may have been quarantined for security reasons

#### Generated Script Errors
- **File path issues**: Ensure uploaded files haven't been moved or deleted
- **Selenium locator**: Verify the file input element selector is correct
- **Timing issues**: Increase wait times for large file uploads

#### Security Warnings
- **False positives**: Some legitimate files may trigger security warnings
- **Quarantined files**: Check the quarantine directory for blocked files
- **Review security report**: Check file metadata for detailed security analysis

### Debug Information

Enable debug logging to troubleshoot issues:

```bash
export SCRIPTWEAVER_DEBUG=true
```

Check log files in:
- `logs/scriptweaver_*.log` - General application logs
- `ai_logs/` - AI interaction logs
- `debug_logs/` - Detailed debug information

## API Reference

### FileUploadManager
Main class for handling file uploads:

```python
from core.file_upload_manager import FileUploadManager

manager = FileUploadManager()
is_valid, error_msg, category = manager.validate_file(uploaded_file, file_content)
metadata = manager.save_uploaded_file(uploaded_file, file_content, step_no, description)
```

### FileSecurityValidator
Security validation for uploaded files:

```python
from core.file_security_validator import FileSecurityValidator

validator = FileSecurityValidator()
is_safe, warnings, report = validator.validate_file_security(file_content, filename, category)
```

### FileUploadComponents
UI components for file upload interface:

```python
from ui_components.file_upload_components import FileUploadComponents

ui = FileUploadComponents()
metadata = ui.render_file_upload_section(step_no, step_action)
```

## Best Practices

### File Organization
- Use descriptive filenames for uploaded test files
- Keep file sizes reasonable for faster test execution
- Organize files by test case or functionality

### Security Considerations
- Only upload files from trusted sources
- Review security warnings before proceeding
- Regularly clean up old test files

### Test Design
- Include file existence checks in your test scenarios
- Handle file upload failures gracefully
- Test with various file types and sizes

### Performance
- Use smaller files for faster test execution
- Consider file upload timeouts in your test design
- Monitor disk space usage for uploaded files

## Migration Guide

### From Manual File Handling
If you were previously handling files manually in your tests:

1. Upload your test files through the ScriptWeaver interface
2. Remove manual file path references from your test data
3. Let ScriptWeaver generate the file upload automation code
4. Update any custom file handling logic

### Upgrading Existing Tests
To add file upload capabilities to existing tests:

1. Navigate to Stage 5 for your existing test case
2. Use the File Upload tab to add file test data
3. Regenerate your test scripts to include file upload code
4. Update any manual file references

## Support

For issues or questions about the file upload feature:

1. Check the troubleshooting section above
2. Review the debug logs for detailed error information
3. Consult the API reference for programmatic usage
4. Contact the development team for advanced support

## Quick Start Guide

### 1. Basic File Upload

1. **Navigate to Stage 5**: Complete stages 1-4 of your test case
2. **Select File Upload Tab**: Click on "📁 File Upload" tab
3. **Choose File**: Click "Browse files" and select your test file
4. **Add Description**: Optionally describe the file's purpose
5. **Save File**: Click "💾 Save File" to store for testing
6. **Generate Script**: Proceed to Stage 6 to generate automation script

### 2. File Type Selection Guide

| Test Scenario | Recommended File Type | Example Files |
|---------------|----------------------|---------------|
| Profile Picture Upload | Images (.jpg, .png) | avatar.jpg, profile.png |
| Document Upload | Documents (.pdf, .docx) | resume.pdf, contract.docx |
| Data Import | Data Files (.csv, .json) | users.csv, config.json |
| Bulk Upload | Archives (.zip) | documents.zip, images.zip |

### 3. Security Best Practices

- ✅ **Use trusted files**: Only upload files from reliable sources
- ✅ **Check file sizes**: Keep files within reasonable limits
- ✅ **Review warnings**: Address any security warnings before proceeding
- ❌ **Avoid executables**: Never upload .exe, .bat, or script files
- ❌ **No sensitive data**: Don't upload files with real personal information

## Changelog

### Version 1.0.0 (2025-01-21)
- Initial release of file upload feature
- Support for images, documents, data files, and archives
- Comprehensive security validation
- Automatic script generation integration
- File management and cleanup capabilities

---

© 2025 Cogniron All Rights Reserved.
