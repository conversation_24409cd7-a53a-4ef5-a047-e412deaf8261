"""
Prompt Generation Module for GretahAI CaseForge

This module handles prompt engineering and content generation for AI models
to create effective test case generation prompts based on JIRA issues.

Functions:
- extract_test_info_from_issue: Extract and process test info from JIRA issues
- generate_gemini_test_gen_prompt: Generate optimized prompts for Gemini models

© 2025 GretahAI Team
"""

import json
import os
import streamlit as st
from PIL import Image
import pandas as pd


def extract_test_info_from_issue(issue, test_type, num_scenarios=5, enhanced_description=None):
    """
    Extracts and processes test information from a JIRA issue for AI prompt generation.
    
    This function prepares comprehensive test context by extracting JIRA issue details,
    gathering existing test cases to prevent duplication, and formatting all information
    for effective AI model consumption. It's a critical component in the test generation pipeline.

    Args:
        issue (jira.Issue): JIRA issue object containing ticket details
        test_type (str): Type of test scenarios to generate. Must be one of:
                        - "positive": Happy path and valid scenarios
                        - "negative": Error handling and invalid inputs  
                        - "security": Security testing scenarios
                        - "performance": Performance and load testing
                        - "mixed": Combination of positive and negative
        num_scenarios (int, optional): Number of test scenarios to generate. Defaults to 5.
        enhanced_description (str, optional): Enhanced description to use instead of 
                                            JIRA description. Defaults to None.    Returns:
        str: Complete formatted prompt text for AI model containing:
            - Test case instructions based on test_type
            - JIRA issue summary and description  
            - Existing test cases summary to prevent duplication
            - JSON template and output requirements

    Validation:
        - Validates test_type against predefined valid types
        - Raises ValueError for invalid test types
        - Ensures issue object has required fields

    Existing Test Case Processing:
        - Retrieves existing test cases using get_existing_test_cases()
        - Formats them into readable summary for AI context
        - Includes test case IDs, objectives, and step details
        - Helps prevent duplication in new test generation

    Test Type Instructions:
        Each test type has specific, detailed instructions:
        - **Positive**: Focus on successful scenarios and valid user paths
        - **Negative**: Emphasize invalid inputs and error conditions
        - **Security**: Target authentication, authorization, and data protection
        - **Performance**: Specify load parameters and response time expectations
        - **Mixed**: Generate combination of positive and negative scenarios

    Enhanced Description Support:
        - Uses enhanced description if provided (AI-improved JIRA description)
        - Falls back to original JIRA description if no enhancement
        - Enables improved test generation with richer context

    Context Formatting:
        - Creates structured summary of existing test cases
        - Formats test steps with clear action → expected result mapping
        - Provides clear separation between different test cases
        - Uses numbering for easy reference

    Error Handling:
        - Raises ValueError for invalid test types
        - Handles missing JIRA fields gracefully
        - Manages empty or null descriptions    Example:
        issue = jira_client.issue("TP-1")
        prompt = extract_test_info_from_issue(issue, "positive", 5, enhanced_desc)
        # Use prompt with AI model to generate test cases
    """
    # Import here to avoid circular imports
    from ..file.management import get_existing_test_cases
    
    # Define valid test types
    valid_test_types = ["positive", "negative", "security", "performance", "mixed"]

    if test_type not in valid_test_types:
        raise ValueError(
            f"Invalid test_type: '{test_type}'. Must be one of: {', '.join(valid_test_types)}"
        )

    test_case = issue.fields.summary.strip()
    # Use enhanced description if provided, otherwise use the description from the issue
    acceptance_criteria = enhanced_description if enhanced_description is not None else issue.fields.description.strip()

    # Get existing test cases to avoid duplication
    existing_test_cases = get_existing_test_cases(issue.key)

    # Create a summary of existing test cases to include in the prompt
    existing_test_cases_summary = ""
    if existing_test_cases:
        existing_test_cases_summary = "\nEXISTING TEST CASES (DO NOT DUPLICATE THESE):\n"
        for i, tc in enumerate(existing_test_cases, 1):
            existing_test_cases_summary += f"{i}. {tc['test_case_id']}: {tc['objective']}\n"
            for j, step in enumerate(tc['steps'], 1):
                existing_test_cases_summary += f"   {j}. {step['action']} -> {step['expected_result']}\n"
            existing_test_cases_summary += "\n"

    # Clearly defined instructions for each test type
    test_instructions = {
         "negative": (
            "- Emphasize invalid inputs, boundary conditions, or error handling.\n"
            "- Do not include any successful or valid user paths.\n"
        ),
        "positive": (
        "Generate only POSITIVE test scenarios demonstrating successful and expected system behavior under ideal conditions.\n"
        "- Do not include any errors, exceptions, or invalid inputs.\n"
        "- All test steps must simulate valid actions that a typical user would perform.\n"
        "- Each scenario should include a clear objective, user-friendly prerequisite, 3–5 test steps, and corresponding expected results using future tense ('should')."
        ),
        "security": (
            "Generate only SECURITY test scenarios focusing explicitly on input validation, authentication, authorization, and data protection. "
            "Scenarios should never include generic functional tests."
        ),
        "performance": (
            "Generate only PERFORMANCE test scenarios clearly specifying load parameters, response times, resource usage, and throughput expectations. "
            "Scenarios should never include generic functionality tests."
        ),
        "mixed": (
            "Generate a MIXED set of POSITIVE and NEGATIVE test scenarios. "
            "Each scenario must explicitly state its type ('positive' or 'negative') in the 'type' field."
        ),
    }

    # JSON scenario structure template (for instructional clarity)
    scenario_template = (
        '{\n'
        '  "scenario_name": "<Short, unique, descriptive scenario name>",\n'
        '  "type": "<positive|negative|security|performance>",\n'
        '  "prerequisites": "<User should have necessary access and permissions for the feature being tested. Write from user perspective.>",\n'
        '  "Test Case Objective": "<The objective should concisely summarize the test steps and expected results. Must start with \'Verify\'>",\n'
        '  "steps": [\n'
        '    {"action": "<Begin with Verify followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"},\n'
        '    {"action": "<Begin with Verify followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"}\n'
        '  ]\n'
        '}'
    )

    # Compose final instruction text
    instruction = (
        "You are an experienced manual QA test engineer tasked with creating detailed test scenarios deligating for Junior QA engineering.\n\n"
        f"Given the following Test Case and Acceptance Criteria, generate exactly {num_scenarios} test scenarios as a JSON array.\n\n"
        f"TEST TYPE INSTRUCTIONS:\n{test_instructions[test_type]}\n\n"
        "EACH SCENARIO MUST FOLLOW THIS EXACT JSON STRUCTURE:\n"
        f"{scenario_template}\n\n"
        "STRICT OUTPUT REQUIREMENTS:\n"
        "- Output must ONLY be a JSON array containing exactly the requested scenarios.\n"
        "- Do not use markdown, code fences, or any additional explanatory text.\n"
        "- Every scenario object must contain all specified keys exactly as provided, no additional keys or comments are allowed.\n"
         "- Include exactly 3 to 5 detailed steps.\n"
         "- test_case_objective must begin with 'Verify', 'Validate', or 'Check'.\n"
         "- Each step must begin with 'Verify if user is able to' and focus on an invalid or error-inducing scenario.\n"
         "- expected_results must be written in future tense using 'should' and clearly indicate the error or system behavior (e.g., 'Error message should be displayed').\n"
         "- Prerequisite must be from the user's perspective (e.g., 'User should have access to the login page').\n"
         "- DO NOT duplicate any of the existing test cases listed below. Create entirely new test scenarios with different objectives and steps.\n"
    )

    combined_text = (
        f"{instruction}\n"
        f"TEST CASE:\n{test_case}\n\n"
        f"ACCEPTANCE CRITERIA:\n{acceptance_criteria}\n"
        f"{existing_test_cases_summary}"
    )

    return combined_text


def generate_gemini_test_gen_prompt(issue, test_type, num_scenarios=5, attachment_path=None, enhanced_description=None):
    """
    Generates a prompt optimized for Gemini to create detailed manual test scenarios
    for execution by junior QA engineers.

    Args:
        issue: An object with 'fields.summary' and 'fields.description' attributes (strings).
        test_type (str): The type of test scenarios to generate.
                         Must be one of: "positive", "negative", "security", "performance", "mixed".
        num_scenarios (int): The exact number of scenarios to generate.
        attachment_path (str, optional): Path to an attachment (screenshot) to include in the prompt.
        enhanced_description (str, optional): Enhanced description to use instead of issue.fields.description.

    Returns:
        str: The generated prompt string, or a tuple of (image, prompt) if attachment_path is provided.

    Raises:
        ValueError: If test_type is invalid or issue structure is incorrect.
    """    # Import here to avoid circular imports
    from ..file.management import get_existing_test_cases
    
    # --- Validation ---
    valid_test_types = ["positive", "negative", "security", "performance", "mixed"]
    if test_type not in valid_test_types:
        raise ValueError(f"Invalid test_type: '{test_type}'. Must be one of: {', '.join(valid_test_types)}")

    # --- Input Data Extraction ---
    try:
        test_case_summary = issue.fields.summary.strip()
        # Use enhanced description if provided, otherwise use the one from the issue
        acceptance_criteria = enhanced_description if enhanced_description is not None else issue.fields.description.strip()
        if not isinstance(test_case_summary, str) or not isinstance(acceptance_criteria, str):
            raise ValueError("issue.fields.summary and issue.fields.description must be strings.")
    except AttributeError:
        raise ValueError("Input 'issue' object must have 'fields.summary' and 'fields.description' attributes.")
    except Exception as e:  # Catch other potential errors during access
        raise ValueError(f"Error accessing issue fields: {e}")

    # Get existing test cases to avoid duplication
    existing_test_cases = get_existing_test_cases(issue.key)

    # Create a summary of existing test cases to include in the prompt
    existing_test_cases_summary = ""
    if existing_test_cases:
        existing_test_cases_summary = "\nEXISTING TEST CASES (DO NOT DUPLICATE THESE):\n"
        for i, tc in enumerate(existing_test_cases, 1):
            existing_test_cases_summary += f"{i}. {tc['test_case_id']}: {tc['objective']}\n"
            for j, step in enumerate(tc['steps'], 1):
                existing_test_cases_summary += f"   {j}. {step['action']} -> {step['expected_result']}\n"
            existing_test_cases_summary += "\n"

    # Check if there are attachments in the issue
    has_attachments = hasattr(issue.fields, 'attachment') and issue.fields.attachment
    attachment_info = ""
    if has_attachments:
        attachment_info = "\nATTACHMENTS INFORMATION:\n"
        for i, att in enumerate(issue.fields.attachment, 1):
            # Use only the filename attribute which is guaranteed to exist
            attachment_info += f"{i}. {att.filename}\n"

    # --- Test Type Specific Instructions ---
    test_instructions = {
       "positive": (
        "Create only POSITIVE test cases that demonstrate expected system behavior under normal, ideal conditions. "
        "Assume all inputs are valid and all systems are functioning as intended. "
        "Focus on happy paths and typical user actions. "
        "Do not include error handling, edge cases, or invalid data. "
        "Each test case should include 3 to 5 detailed, logically ordered steps to fully validate the functional flow."
    ),
    "negative": (
        "Create only NEGATIVE test cases that explore how the system handles invalid inputs, unexpected user behavior, and edge cases. "
        "Include meaningful validations, error messages, and system safeguards. "
        "Do not include any successful or expected behavior flows. "
        "Each test case should include 3 to 5 detailed, clearly defined steps to verify the robustness of error handling."
    ),
    "security": (
        "Create only SECURITY-focused test cases targeting aspects such as authentication, authorization, input sanitization, and data confidentiality. "
        "Test for vulnerabilities, access control issues, and improper handling of sensitive information. "
        "Avoid including general functional or UI test scenarios. "
        "Each test case should include 3 to 5 steps that focus specifically on verifying security mechanisms and protective controls."
    ),
    "performance": (
        "Create only PERFORMANCE-oriented test cases designed to measure the system's behavior under load, stress, or high concurrency. "
        "Specify clear metrics such as load size, expected response time, and system resource usage. "
        "Avoid generic functionality or UI validation steps. "
        "Each test case should include 3 to 5 technical steps that assess system performance under defined parameters."
    ),
    "mixed": (
        "Create a MIXED set of test cases that includes both POSITIVE and NEGATIVE scenarios. "
        "Clearly label each test case with a 'type' field indicating whether it is 'positive' or 'negative'. "
        "Ensure a good balance between successful workflows and error-handling tests. "
        "Each test case must include 3 to 5 realistic, clearly sequenced steps to thoroughly test the functionality and its robustness."
    ),
}

    # --- JSON Output Structure Definition ---
    # For 'mixed', allow either 'positive' or 'negative' types in the output.
    scenario_structure = (
        '{\n'
        '  "scenario_name": "<Short, unique, descriptive scenario name>",\n'
        f'  "type": "{test_type if test_type != "mixed" else "<positive|negative>"}",\n'
        '  "prerequisites": "<User should have necessary access and permissions for the feature being tested. Write from user perspective.>",\n'
        '  "Test Case Objective": "<The objective should concisely summarize the test steps and expected results. Must start with \'Verify\'>",\n'
        '  "steps": [\n'
        '    {"action": "<Begin with \'Verify if user is able to\' followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"},\n'
        '    {"action": "<Begin with \'Verify if user is able to\' followed by the specific action to test. Be clear and precise>", "expected_result": "<Specific singular expected result in future tense >"}\n'
        '    /* Additional detailed steps encouraged */\n'
        '  ]\n'
        '}'
    )

    # Convert dict to a formatted JSON string for the prompt instructions
    scenario_structure_string = json.dumps(scenario_structure, indent=2)
    # Remove quotes around the placeholder markers for clarity in the prompt
    scenario_structure_string = scenario_structure_string.replace('"<', '<').replace('>"', '>')
    if test_type != "mixed":
        # Ensure the fixed type value is correctly displayed without extra quotes
        scenario_structure_string = scenario_structure_string.replace(f"\"<Must be exactly '{test_type}'>\"", f"\"'{test_type}'\"")

    # --- Core Prompt Construction ---
    # Get additional context from session state if it exists
    additional_context = ""
    try:
        if hasattr(st, 'session_state'):
            # Use the widget's state directly since that's the source of truth
            additional_context = st.session_state.get('context_input_area', '') or st.session_state.get('context_prompt', '')
    except:
        additional_context = ""
    
    context_section = f"\nADDITIONAL CONTEXT FROM QA ENGINEER:\n{additional_context}\n\n" if additional_context.strip() else ""

    prompt = (
        "You are an experienced manual QA engineer tasked with creating detailed test cases that will be executed by another QA engineer.\n\n"
        f"Given the following Test Scenario Summary and Acceptance Criteria, generate exactly {num_scenarios} detailed test cases as a JSON array.\n\n"
        f"TEST TYPE INSTRUCTIONS:\n{test_instructions[test_type]}\n\n"
        "EACH TEST CASE MUST FOLLOW THIS EXACT JSON STRUCTURE:\n"
        f"{scenario_structure_string}\n\n"
        f"{context_section}"
        "STRICT OUTPUT REQUIREMENTS:\n"
        "- Output must ONLY be a JSON array containing exactly the requested test cases.\n"
        "- Do not use markdown, code fences, or any additional explanatory text.\n"
        "- Every test case object must contain all specified keys exactly as provided, with no additional keys or comments.\n"
        "- The Test Case Objective MUST start with 'Verify', 'Validate', or 'Check' and concisely summarize the test steps and expected results.\n"
        "- Each test step MUST begin with 'Verify if user is able to' followed by the specific action to test for consistency and clarity.\n"
        "- Expected Results should be written in future tense using 'should' (e.g., 'Login page should be displayed').\n"
        "- IMPORTANT: Each test case MUST include 3-5 detailed steps to thoroughly test the functionality.\n"
        "- Prerequisites should be written from the user's perspective (e.g., 'User should have valid credentials for the test environment').\n"
        "- Each step should be atomic, precise, and focus on a single testable action.\n"
        "- DO NOT duplicate any of the existing test cases listed below. Create entirely new test scenarios with different objectives and steps.\n\n"
        f"TEST SCENARIO SUMMARY:\n{test_case_summary}\n\n"
        f"ACCEPTANCE CRITERIA:\n{acceptance_criteria}\n"
        f"{attachment_info}\n"
        f"{existing_test_cases_summary}"
    )

    # If an attachment path is provided, return both the image and the prompt
    if attachment_path and os.path.exists(attachment_path):
        try:
            image = Image.open(attachment_path)

            # Add information about the attachment to the prompt
            prompt += f"\n\nNOTE: A screenshot is attached to this prompt. Please analyze the screenshot and incorporate relevant UI elements, workflows, and functionality shown in the image into your test cases. The test cases should reflect the actual UI and functionality shown in the screenshot."

            return image, prompt
        except Exception as e:
            print(f"Error loading attachment image: {e}")
            # If there's an error loading the image, just return the text prompt
            return prompt
    else:
        return prompt

def generate_json_template_from_df(df: pd.DataFrame) -> str:
    """
    Generate a JSON template string from a DataFrame's columns for prompt engineering.
    Each key will have an example value or empty string.
    """
    template = [{col: "<value>" for col in df.columns}]
    return json.dumps(template, indent=2)

def generate_test_case_modification_prompt(test_cases_df: pd.DataFrame, jira_description: str, user_query: str) -> str:
    """
    Generate a prompt for AI to modify test cases using a DataFrame, JIRA description, and user query.
    Sends the full JSON data, a JSON template for output, and strict output instructions.
    """
    # Generate JSON template from DataFrame columns
    json_template = generate_json_template_from_df(test_cases_df)
    # Convert the full DataFrame to JSON
    test_cases_json = test_cases_df.to_json(orient='records', indent=2)
    prompt = (
        "You are an expert QA engineer tasked with modifying existing test cases based on user requirements.\n\n"
        f"ORIGINAL JIRA ISSUE DESCRIPTION:\n{jira_description}\n\n"
        f"EXISTING TEST CASES (JSON array):\n{test_cases_json}\n\n"
        f"USER MODIFICATION REQUEST:\n{user_query}\n\n"
        "STRICT OUTPUT REQUIREMENTS:\n"
        "- Output must ONLY be a JSON array containing the modified test cases.\n"
        "- The output JSON array must have the same structure as the input.\n"
        "- Use the following JSON format for each object in the array (fill in the values, do not change keys):\n"
        f"{json_template}\n\n"
        "- Do not use markdown, code fences, or any additional explanatory text.\n"
        "- Every test case object must contain all specified keys exactly as provided, with no additional keys or comments.\n"
        "- Preserve all columns, even if empty.\n"
        "- Maintain the same number of records and the same step-table format (one row per step) unless the user explicitly requests additions/deletions.\n"
        "- DO NOT merge multiple steps into a single row. Each row must represent exactly one step, and all columns must be present, even if empty.\n"
        "- DO NOT change the number of rows or the structure of the table unless the user explicitly requests it.\n"
        "- Keep existing Test Case IDs unless creating new test cases.\n"
        "- The response MUST start with '[' and end with ']'.\n"
        "- If you do not follow these instructions, your output will be rejected and the request will be considered failed.\n"
    )
    return prompt
