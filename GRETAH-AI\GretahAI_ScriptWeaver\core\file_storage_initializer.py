"""
File Storage Initializer for GretahAI ScriptWeaver

This module handles the initialization and management of the file storage
system for uploaded test data files.

Key Features:
- Directory structure creation and validation
- Storage cleanup and maintenance
- File system health checks
- Integration with application startup

© 2025 Cogniron All Rights Reserved.
"""

import os
import shutil
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any
import json

# Import GRETAH standardized logging
from debug_utils import debug


class FileStorageInitializer:
    """
    Initializes and manages the file storage system for ScriptWeaver.
    
    Handles directory creation, cleanup operations, and storage maintenance
    to ensure the file upload system operates reliably.
    """
    
    def __init__(self, base_upload_dir: str = "test_data_uploads"):
        """
        Initialize the FileStorageInitializer.

        Args:
            base_upload_dir: Base directory for storing uploaded files
        """
        # Ensure the path is relative to current working directory
        if os.path.isabs(base_upload_dir):
            self.base_upload_dir = Path(base_upload_dir)
        else:
            self.base_upload_dir = Path.cwd() / base_upload_dir

        self.temp_uploads_dir = Path.cwd() / "temp_uploads"
        
    def initialize_storage_system(self) -> bool:
        """
        Initialize the complete file storage system.
        
        Returns:
            True if initialization was successful
        """
        try:
            debug("Initializing file storage system",
                  stage="file_storage", operation="initialization_start")
            
            # Create directory structure
            self._create_directory_structure()
            
            # Validate directory permissions
            self._validate_directory_permissions()
            
            # Create .gitignore files for uploaded content
            self._create_gitignore_files()
            
            # Perform cleanup of old files
            self._cleanup_old_files()
            
            debug("File storage system initialized successfully",
                  stage="file_storage", operation="initialization_success")
            
            return True
            
        except Exception as e:
            debug(f"Failed to initialize file storage system: {e}",
                  stage="file_storage", operation="initialization_error",
                  context={'error': str(e)})
            return False
    
    def _create_directory_structure(self):
        """Create the required directory structure for file uploads."""
        from core.file_upload_manager import FileUploadManager
        
        # Get supported file types from FileUploadManager
        file_manager = FileUploadManager()
        supported_types = file_manager.get_supported_file_types()
        
        directories_to_create = [
            self.base_upload_dir,
            self.base_upload_dir / "metadata",
            self.temp_uploads_dir  # Ensure temp_uploads exists
        ]
        
        # Add category directories
        for category in supported_types.keys():
            directories_to_create.append(self.base_upload_dir / category)
        
        # Create all directories
        for directory in directories_to_create:
            directory.mkdir(parents=True, exist_ok=True)
            debug(f"Created directory: {directory}",
                  stage="file_storage", operation="directory_creation",
                  context={'directory': str(directory)})
    
    def _validate_directory_permissions(self):
        """Validate that we have proper permissions for file operations."""
        test_dirs = [
            self.base_upload_dir,
            self.base_upload_dir / "metadata",
            self.temp_uploads_dir
        ]
        
        for test_dir in test_dirs:
            if not test_dir.exists():
                raise PermissionError(f"Directory does not exist: {test_dir}")
            
            # Test write permissions
            test_file = test_dir / ".permission_test"
            try:
                test_file.write_text("test")
                test_file.unlink()
            except Exception as e:
                raise PermissionError(f"No write permission for directory {test_dir}: {e}")
        
        debug("Directory permissions validated successfully",
              stage="file_storage", operation="permission_validation")
    
    def _create_gitignore_files(self):
        """Create .gitignore files to exclude uploaded content from version control."""
        gitignore_content = """# Uploaded test data files - exclude from version control
*
!.gitignore
!README.md
"""
        
        gitignore_locations = [
            self.base_upload_dir / ".gitignore",
            self.temp_uploads_dir / ".gitignore"
        ]
        
        for gitignore_path in gitignore_locations:
            if not gitignore_path.exists():
                gitignore_path.write_text(gitignore_content)
                debug(f"Created .gitignore file: {gitignore_path}",
                      stage="file_storage", operation="gitignore_creation")
    
    def _cleanup_old_files(self, max_age_days: int = 7):
        """
        Clean up old uploaded files to prevent storage bloat.
        
        Args:
            max_age_days: Maximum age of files to keep (in days)
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=max_age_days)
            deleted_count = 0
            
            # Clean up test data uploads
            if self.base_upload_dir.exists():
                deleted_count += self._cleanup_directory(self.base_upload_dir, cutoff_date)
            
            # Clean up temp uploads (Excel files)
            if self.temp_uploads_dir.exists():
                deleted_count += self._cleanup_directory(self.temp_uploads_dir, cutoff_date)
            
            debug(f"Cleanup completed: {deleted_count} old files removed",
                  stage="file_storage", operation="cleanup_success",
                  context={'deleted_count': deleted_count, 'max_age_days': max_age_days})
                  
        except Exception as e:
            debug(f"Error during file cleanup: {e}",
                  stage="file_storage", operation="cleanup_error",
                  context={'error': str(e)})
    
    def _cleanup_directory(self, directory: Path, cutoff_date: datetime) -> int:
        """
        Clean up files in a specific directory older than cutoff date.
        
        Args:
            directory: Directory to clean up
            cutoff_date: Files older than this date will be deleted
            
        Returns:
            Number of files deleted
        """
        deleted_count = 0
        
        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file() and not file_path.name.startswith('.'):
                    try:
                        # Get file modification time
                        file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                        
                        if file_mtime < cutoff_date:
                            file_path.unlink()
                            deleted_count += 1
                            debug(f"Deleted old file: {file_path}",
                                  stage="file_storage", operation="file_deletion",
                                  context={'file_path': str(file_path), 'file_age_days': (datetime.now() - file_mtime).days})
                    
                    except Exception as e:
                        debug(f"Error deleting file {file_path}: {e}",
                              stage="file_storage", operation="file_deletion_error",
                              context={'file_path': str(file_path), 'error': str(e)})
        
        except Exception as e:
            debug(f"Error cleaning directory {directory}: {e}",
                  stage="file_storage", operation="directory_cleanup_error",
                  context={'directory': str(directory), 'error': str(e)})
        
        return deleted_count
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the file storage system.
        
        Returns:
            Dictionary containing storage statistics
        """
        try:
            stats = {
                'base_upload_dir': str(self.base_upload_dir),
                'temp_uploads_dir': str(self.temp_uploads_dir),
                'directories': {},
                'total_files': 0,
                'total_size_bytes': 0,
                'categories': {}
            }
            
            # Analyze base upload directory
            if self.base_upload_dir.exists():
                stats['directories']['test_data_uploads'] = self._analyze_directory(self.base_upload_dir)
                
                # Analyze category subdirectories
                from core.file_upload_manager import FileUploadManager
                file_manager = FileUploadManager()
                supported_types = file_manager.get_supported_file_types()
                
                for category in supported_types.keys():
                    category_dir = self.base_upload_dir / category
                    if category_dir.exists():
                        stats['categories'][category] = self._analyze_directory(category_dir)
            
            # Analyze temp uploads directory
            if self.temp_uploads_dir.exists():
                stats['directories']['temp_uploads'] = self._analyze_directory(self.temp_uploads_dir)
            
            # Calculate totals
            for dir_stats in stats['directories'].values():
                stats['total_files'] += dir_stats['file_count']
                stats['total_size_bytes'] += dir_stats['total_size_bytes']
            
            return stats
            
        except Exception as e:
            debug(f"Error getting storage statistics: {e}",
                  stage="file_storage", operation="statistics_error",
                  context={'error': str(e)})
            return {'error': str(e)}
    
    def _analyze_directory(self, directory: Path) -> Dict[str, Any]:
        """
        Analyze a directory and return statistics.
        
        Args:
            directory: Directory to analyze
            
        Returns:
            Dictionary containing directory statistics
        """
        stats = {
            'path': str(directory),
            'exists': directory.exists(),
            'file_count': 0,
            'total_size_bytes': 0,
            'file_types': {},
            'oldest_file': None,
            'newest_file': None
        }
        
        if not directory.exists():
            return stats
        
        try:
            oldest_time = None
            newest_time = None
            
            for file_path in directory.rglob("*"):
                if file_path.is_file() and not file_path.name.startswith('.'):
                    stats['file_count'] += 1
                    
                    # File size
                    file_size = file_path.stat().st_size
                    stats['total_size_bytes'] += file_size
                    
                    # File type
                    file_ext = file_path.suffix.lower()
                    if file_ext not in stats['file_types']:
                        stats['file_types'][file_ext] = 0
                    stats['file_types'][file_ext] += 1
                    
                    # File age tracking
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if oldest_time is None or file_mtime < oldest_time:
                        oldest_time = file_mtime
                        stats['oldest_file'] = {
                            'path': str(file_path),
                            'modified': file_mtime.isoformat()
                        }
                    
                    if newest_time is None or file_mtime > newest_time:
                        newest_time = file_mtime
                        stats['newest_file'] = {
                            'path': str(file_path),
                            'modified': file_mtime.isoformat()
                        }
        
        except Exception as e:
            stats['error'] = str(e)
        
        return stats
    
    def create_readme_files(self):
        """Create README files in upload directories to document their purpose."""
        readme_content_test_data = """# Test Data Uploads

This directory contains files uploaded as test data for ScriptWeaver test automation.

## Directory Structure

- `images/` - Image files (JPG, PNG, GIF, etc.) for UI testing
- `documents/` - Document files (PDF, DOCX, TXT, etc.) for upload testing  
- `data_files/` - Data files (CSV, JSON, XML, etc.) for data-driven testing
- `archives/` - Archive files (ZIP) for bulk upload testing
- `metadata/` - File metadata and reference information

## File Management

Files are automatically organized by type and include metadata for tracking.
Old files are automatically cleaned up to prevent storage bloat.

## Security

All uploaded files are validated for type and size before storage.
Suspicious content is rejected to maintain system security.
"""
        
        readme_content_temp = """# Temporary Uploads

This directory contains temporarily uploaded Excel files for test case processing.

Files in this directory are automatically cleaned up after processing.
"""
        
        readme_files = [
            (self.base_upload_dir / "README.md", readme_content_test_data),
            (self.temp_uploads_dir / "README.md", readme_content_temp)
        ]
        
        for readme_path, content in readme_files:
            if not readme_path.exists():
                readme_path.write_text(content)
                debug(f"Created README file: {readme_path}",
                      stage="file_storage", operation="readme_creation")


def initialize_file_storage() -> bool:
    """
    Initialize the file storage system for ScriptWeaver.
    
    This function should be called during application startup to ensure
    the file upload system is properly configured.
    
    Returns:
        True if initialization was successful
    """
    try:
        initializer = FileStorageInitializer()
        success = initializer.initialize_storage_system()
        
        if success:
            # Create README files for documentation
            initializer.create_readme_files()
        
        return success
        
    except Exception as e:
        debug(f"Failed to initialize file storage: {e}",
              stage="file_storage", operation="initialization_error",
              context={'error': str(e)})
        return False
