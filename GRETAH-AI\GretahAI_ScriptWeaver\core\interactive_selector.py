"""
Interactive Element Selector Module

This module provides functionality for interactive UI element selection.
It allows users to open a controlled browser window and manually select
elements on a webpage, capturing their locator information for test script generation.
"""

import time
import os
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

from webdriver_manager.chrome import ChromeDriverManager
from debug_utils import debug

try:
    from .performance_monitor import performance_monitor, BrowserPerformanceMonitor, monitor_interactive_selection
except ImportError:
    # Fallback if performance monitor is not available
    def monitor_interactive_selection(func):
        return func
    performance_monitor = None
    BrowserPerformanceMonitor = None

def inject_element_selection_script(driver):
    """
    Inject JavaScript to enable interactive element selection.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if script injection was successful, False otherwise
    """
    try:
        # Inject the element selection script
        script = """
        // Create a global variable to store the selected element
        window.selectedElement = null;
        window.highlightedElement = null;

        // Create a div for displaying element information
        const infoPanel = document.createElement('div');
        infoPanel.id = 'gretah-element-info-panel';
        infoPanel.style.position = 'fixed';
        infoPanel.style.bottom = '0';
        infoPanel.style.left = '0';
        infoPanel.style.right = '0';
        infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        infoPanel.style.color = 'white';
        infoPanel.style.padding = '10px';
        infoPanel.style.fontFamily = 'monospace';
        infoPanel.style.fontSize = '14px';
        infoPanel.style.zIndex = '10000';
        infoPanel.style.maxHeight = '150px';
        infoPanel.style.overflow = 'auto';
        infoPanel.innerHTML = '<strong>GRETAH Element Selector</strong>: Hover over elements and click to select. Press ESC to cancel.';
        document.body.appendChild(infoPanel);

        // Create a style for highlighting elements
        const style = document.createElement('style');
        style.innerHTML = `
            .gretah-element-highlight {
                outline: 2px solid #4A6FE3 !important;
                background-color: rgba(74, 111, 227, 0.2) !important;
            }
            .gretah-element-selected {
                outline: 3px solid #00FF00 !important;
                background-color: rgba(0, 255, 0, 0.2) !important;
            }
        `;
        document.head.appendChild(style);

        // Helper function to filter out GRETAH artificial classes and get natural classes
        function getNaturalClasses(element) {
            if (!element.className || typeof element.className !== 'string') {
                return [];
            }

            const classes = element.className.trim().split(/\\s+/);
            // Filter out GRETAH artificial classes (starting with 'gretah-')
            const naturalClasses = classes.filter(cls =>
                cls &&
                !cls.startsWith('gretah-') &&
                !cls.startsWith('highlight-') &&
                !cls.startsWith('selected-') &&
                cls.length > 0
            );

            return naturalClasses;
        }

        // Optimized CSS selector generation with caching and GRETAH class filtering
        const selectorCache = new WeakMap();
        function getCssSelector(element) {
            // Check cache first
            if (selectorCache.has(element)) {
                return selectorCache.get(element);
            }

            let selector = null;

            if (element.id) {
                selector = '#' + element.id;
            } else if (element.className && typeof element.className === 'string') {
                // Use natural classes that exclude GRETAH internal classes
                const naturalClasses = getNaturalClasses(element);
                if (naturalClasses.length > 0) {
                    selector = element.tagName.toLowerCase() + '.' + naturalClasses[0];
                }
            }

            // If no selector found yet, try with attributes (optimized order)
            if (!selector) {
                const attrs = ['name', 'type', 'role', 'placeholder', 'aria-label', 'data-testid', 'data-test'];
                for (let i = 0; i < attrs.length; i++) {
                    const attr = attrs[i];
                    const attrValue = element.getAttribute(attr);
                    if (attrValue && attrValue.trim()) {
                        selector = `${element.tagName.toLowerCase()}[${attr}="${attrValue}"]`;
                        break;
                    }
                }
            }

            // Fallback to simple tag selector (avoid expensive nth-child calculation)
            if (!selector) {
                selector = element.tagName.toLowerCase();
            }

            // Cache the result
            selectorCache.set(element, selector);
            return selector || 'unknown';
        }

        // Optimized XPath generation with caching
        const xpathCache = new WeakMap();
        function getXPath(element) {
            // Check cache first
            if (xpathCache.has(element)) {
                return xpathCache.get(element);
            }

            let xpath;

            if (element.id) {
                xpath = `//*[@id="${element.id}"]`;
            } else {
                // Simplified XPath generation - avoid expensive sibling counting
                const parts = [];
                let current = element;

                while (current && current.nodeType === 1 && current.tagName !== 'HTML') {
                    const tagName = current.tagName.toLowerCase();
                    parts.unshift(tagName);
                    current = current.parentNode;
                }

                xpath = '/' + parts.join('/');
            }

            // Cache the result
            xpathCache.set(element, xpath);
            return xpath;
        }

        // Function to find ancestor elements with ID attributes
        function findAncestorsWithId(element) {
            const ancestors = [];
            let current = element.parentElement;
            let level = 1;
            const maxLevels = 15; // Prevent infinite loops and limit traversal depth

            while (current && current.tagName !== 'HTML' && level <= maxLevels) {
                // Check for ID attribute that's meaningful (not empty or just whitespace)
                const elementId = current.id ? current.id.trim() : '';
                if (elementId && elementId.length > 0) {
                    // Filter out natural classes to exclude GRETAH internal classes
                    const naturalClasses = getNaturalClasses(current);
                    const naturalClassName = naturalClasses.join(' ');

                    // Get meaningful text content (limited length)
                    let textContent = '';
                    if (current.textContent) {
                        textContent = current.textContent.trim();
                        // Only include text if it's not too long and meaningful
                        if (textContent.length > 100) {
                            textContent = textContent.substring(0, 97) + '...';
                        }
                        // Remove line breaks and extra whitespace
                        textContent = textContent.replace(/\s+/g, ' ');
                    }

                    ancestors.push({
                        tagName: current.tagName.toLowerCase(),
                        id: elementId,
                        className: naturalClassName, // Use filtered natural classes
                        originalClassName: current.className || '', // Keep original for debugging
                        name: current.getAttribute('name') || '',
                        type: current.getAttribute('type') || '',
                        role: current.getAttribute('role') || '',
                        ariaLabel: current.getAttribute('aria-label') || '',
                        dataTestId: current.getAttribute('data-testid') || '',
                        cssSelector: getCssSelector(current),
                        xpath: getXPath(current),
                        text: textContent,
                        level: level,  // Distance from selected element
                        hasClickableChildren: _hasClickableChildren(current),
                        isClickable: _isElementClickable(current)
                    });
                }
                current = current.parentElement;
                level++;
            }

            return ancestors;
        }

        // Helper function to check if element has clickable children
        function _hasClickableChildren(element) {
            const clickableTags = ['a', 'button', 'input', 'select', 'textarea'];
            const clickableElements = element.querySelectorAll(clickableTags.join(','));
            return clickableElements.length > 0;
        }

        // Helper function to check if element is clickable
        function _isElementClickable(element) {
            const clickableTags = ['a', 'button', 'input', 'select', 'textarea', 'option'];
            const tagName = element.tagName.toLowerCase();

            if (clickableTags.includes(tagName)) {
                return true;
            }

            // Check for click handlers or role attributes that indicate clickability
            const role = element.getAttribute('role');
            const clickableRoles = ['button', 'link', 'menuitem', 'tab', 'option'];
            if (role && clickableRoles.includes(role.toLowerCase())) {
                return true;
            }

            // Check for onclick or other click-related attributes
            if (element.hasAttribute('onclick') ||
                element.hasAttribute('ng-click') ||
                element.hasAttribute('v-on:click') ||
                element.style.cursor === 'pointer') {
                return true;
            }

            return false;
        }

        // Helper function to get natural attributes for enhanced locator specificity
        function getNaturalAttributes(element) {
            const attributes = {};

            // Priority order for natural attributes
            const priorityAttributes = ['data-testid', 'data-test', 'name', 'id', 'type', 'role', 'aria-label', 'href', 'title', 'alt', 'placeholder', 'value'];

            for (const attr of priorityAttributes) {
                const value = element.getAttribute(attr);
                if (value && value.trim()) {
                    attributes[attr] = value.trim();
                }
            }

            // Add text content for elements with short, meaningful text
            const textContent = element.textContent ? element.textContent.trim() : '';
            if (textContent && textContent.length > 0 && textContent.length <= 50) {
                // Only use text content if it's not too long and doesn't contain line breaks
                if (!textContent.includes('\\n') && !textContent.includes('\\r')) {
                    attributes['text'] = textContent;
                }
            }

            // Add position-based attributes for elements without better identifiers
            if (Object.keys(attributes).length === 0 || (Object.keys(attributes).length === 1 && attributes['text'])) {
                // Get sibling position for better specificity
                const parent = element.parentElement;
                if (parent) {
                    const siblings = Array.from(parent.children).filter(child =>
                        child.tagName.toLowerCase() === element.tagName.toLowerCase()
                    );
                    const position = siblings.indexOf(element) + 1;
                    if (siblings.length > 1) {
                        attributes['position'] = position;
                    }
                }
            }

            return attributes;
        }

        // Enhanced CSS selector generation that leverages ancestor IDs
        function getEnhancedCssSelector(element, ancestors) {
            // If element has ID, use it directly
            if (element.id && element.id.trim()) {
                return '#' + element.id;
            }

            // Try to use nearest ancestor with ID for more reliable selector
            if (ancestors && ancestors.length > 0) {
                const nearestAncestor = ancestors[0]; // First ancestor is closest
                if (nearestAncestor.id && nearestAncestor.id.trim()) {
                    const ancestorSelector = '#' + nearestAncestor.id;

                    // Generate descendant selector from ancestor to target element
                    const path = [];
                    let current = element;
                    let maxDepth = 10; // Prevent infinite loops
                    let depth = 0;

                    // Build path from element up to the ancestor with ID
                    while (current && current.id !== nearestAncestor.id && depth < maxDepth) {
                        let selector = current.tagName.toLowerCase();

                        // Get natural attributes for this element
                        const naturalAttributes = getNaturalAttributes(current);
                        const naturalClasses = getNaturalClasses(current);

                        // Add natural attributes in priority order
                        if (naturalAttributes['data-testid']) {
                            selector += `[data-testid="${naturalAttributes['data-testid']}"]`;
                        } else if (naturalAttributes['data-test']) {
                            selector += `[data-test="${naturalAttributes['data-test']}"]`;
                        } else if (naturalAttributes['name']) {
                            selector += `[name="${naturalAttributes['name']}"]`;
                        } else if (naturalAttributes['type']) {
                            selector += `[type="${naturalAttributes['type']}"]`;
                        } else if (naturalAttributes['role']) {
                            selector += `[role="${naturalAttributes['role']}"]`;
                        } else if (naturalAttributes['aria-label']) {
                            selector += `[aria-label="${naturalAttributes['aria-label']}"]`;
                        } else if (naturalAttributes['href']) {
                            // For links, use href attribute for specificity
                            selector += `[href="${naturalAttributes['href']}"]`;
                        } else if (naturalClasses.length > 0) {
                            // Only use natural classes if no better attributes available
                            selector += '.' + naturalClasses[0];
                        }

                        path.unshift(selector);
                        current = current.parentElement;
                        depth++;
                    }

                    // Combine ancestor ID with descendant path
                    if (path.length > 0) {
                        return ancestorSelector + ' ' + path.join(' ');
                    } else {
                        // If no path found, return ancestor selector with direct child
                        return ancestorSelector + ' ' + element.tagName.toLowerCase();
                    }
                }
            }

            // Fallback to original CSS selector logic
            const fallbackSelector = getCssSelector(element);
            return fallbackSelector || element.tagName.toLowerCase();
        }

        // Enhanced XPath generation that leverages ancestor IDs
        function getEnhancedXPath(element, ancestors) {
            // If element has ID, use it directly
            if (element.id) {
                return `//*[@id="${element.id}"]`;
            }

            // Try to use nearest ancestor with ID for more reliable XPath
            if (ancestors && ancestors.length > 0) {
                const nearestAncestor = ancestors[0]; // First ancestor is closest
                const ancestorXPath = `//*[@id="${nearestAncestor.id}"]`;

                // Generate descendant XPath from ancestor to target element
                const path = [];
                let current = element;

                // Build path from element up to the ancestor with ID
                while (current && current.id !== nearestAncestor.id) {
                    let selector = current.tagName.toLowerCase();

                    // Get natural attributes for this element
                    const naturalAttributes = getNaturalAttributes(current);
                    const naturalClasses = getNaturalClasses(current);

                    // Add natural attribute predicates in priority order
                    if (naturalAttributes['data-testid']) {
                        selector += `[@data-testid="${naturalAttributes['data-testid']}"]`;
                    } else if (naturalAttributes['name']) {
                        selector += `[@name="${naturalAttributes['name']}"]`;
                    } else if (naturalAttributes['type']) {
                        selector += `[@type="${naturalAttributes['type']}"]`;
                    } else if (naturalAttributes['role']) {
                        selector += `[@role="${naturalAttributes['role']}"]`;
                    } else if (naturalAttributes['href']) {
                        // For links, use href attribute for specificity
                        selector += `[@href="${naturalAttributes['href']}"]`;
                    } else if (naturalClasses.length > 0) {
                        // Only use natural classes if no better attributes available
                        selector += `[@class="${naturalClasses[0]}"]`;
                    }

                    path.unshift(selector);
                    current = current.parentElement;
                }

                // Combine ancestor XPath with descendant path
                if (path.length > 0) {
                    return ancestorXPath + '//' + path.join('//');
                }
            }

            // Fallback to original XPath logic
            return getXPath(element);
        }

        // Function to detect viewport visibility and scroll requirements
        function detectViewportStatus(element) {
            const rect = element.getBoundingClientRect();
            const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

            // Calculate intersection with viewport
            const intersectionLeft = Math.max(0, rect.left);
            const intersectionTop = Math.max(0, rect.top);
            const intersectionRight = Math.min(viewportWidth, rect.right);
            const intersectionBottom = Math.min(viewportHeight, rect.bottom);

            // Calculate visible area
            const intersectionWidth = Math.max(0, intersectionRight - intersectionLeft);
            const intersectionHeight = Math.max(0, intersectionBottom - intersectionTop);
            const visibleArea = intersectionWidth * intersectionHeight;
            const totalArea = rect.width * rect.height;

            // Calculate visibility percentage
            const visibilityPercentage = totalArea > 0 ? Math.round((visibleArea / totalArea) * 100) : 0;
            const isVisible = visibilityPercentage > 0;

            // Determine scroll requirements
            let requiresScroll = false;
            let scrollDirection = null;
            let scrollDistance = 0;

            if (!isVisible || visibilityPercentage < 100) {
                requiresScroll = true;

                // Determine scroll direction and distance
                if (rect.top < 0) {
                    scrollDirection = 'up';
                    scrollDistance = Math.abs(rect.top) + 50; // Add 50px buffer
                } else if (rect.bottom > viewportHeight) {
                    scrollDirection = 'down';
                    scrollDistance = rect.bottom - viewportHeight + 50; // Add 50px buffer
                } else if (rect.left < 0) {
                    scrollDirection = 'left';
                    scrollDistance = Math.abs(rect.left) + 50;
                } else if (rect.right > viewportWidth) {
                    scrollDirection = 'right';
                    scrollDistance = rect.right - viewportWidth + 50;
                } else if (visibilityPercentage < 100) {
                    // Element is partially visible, scroll to center it
                    scrollDirection = 'center';
                    scrollDistance = 0; // scrollIntoView will handle centering
                }
            }

            return {
                is_visible: isVisible,
                visibility_percentage: visibilityPercentage,
                requires_scroll: requiresScroll,
                scroll_direction: scrollDirection,
                scroll_distance: scrollDistance,
                element_rect: {
                    top: rect.top,
                    left: rect.left,
                    bottom: rect.bottom,
                    right: rect.right,
                    width: rect.width,
                    height: rect.height
                },
                viewport_dimensions: {
                    width: viewportWidth,
                    height: viewportHeight
                }
            };
        }

        // Function to detect hover interaction requirements
        function detectHoverRequirements(element) {
            let requiresHover = false;
            let hoverTargetSelector = null;
            let hoverParentChain = [];

            try {
                // Check if element has CSS hover styles
                const computedStyle = window.getComputedStyle(element);

                // Look for common hover indicators in parent elements
                let currentElement = element.parentElement;
                let level = 1;

                while (currentElement && level <= 5) { // Check up to 5 levels up
                    const parentStyle = window.getComputedStyle(currentElement);

                    // Check for dropdown/menu patterns
                    const hasDropdownClass = currentElement.className &&
                        (currentElement.className.includes('dropdown') ||
                         currentElement.className.includes('menu') ||
                         currentElement.className.includes('nav'));

                    // Check for elements that might trigger hover states
                    const isHoverTrigger = currentElement.tagName.toLowerCase() === 'li' ||
                                         hasDropdownClass ||
                                         currentElement.getAttribute('role') === 'menuitem' ||
                                         currentElement.getAttribute('role') === 'button';

                    if (isHoverTrigger) {
                        requiresHover = true;
                        hoverTargetSelector = getCssSelector(currentElement);
                        hoverParentChain.push({
                            level: level,
                            tagName: currentElement.tagName.toLowerCase(),
                            id: currentElement.id || '',
                            className: currentElement.className || '',
                            selector: getCssSelector(currentElement),
                            role: currentElement.getAttribute('role') || ''
                        });
                        break; // Found the hover trigger, stop looking
                    }

                    currentElement = currentElement.parentElement;
                    level++;
                }

                // Additional check: if element is initially hidden but becomes visible on hover
                if (!requiresHover) {
                    const isInitiallyHidden = computedStyle.display === 'none' ||
                                            computedStyle.visibility === 'hidden' ||
                                            computedStyle.opacity === '0';

                    if (isInitiallyHidden) {
                        // Look for a parent that might trigger visibility
                        currentElement = element.parentElement;
                        while (currentElement && currentElement !== document.body) {
                            const parentSelector = getCssSelector(currentElement);
                            // This is a heuristic - in a real implementation, you might want to
                            // check CSS rules more thoroughly
                            requiresHover = true;
                            hoverTargetSelector = parentSelector;
                            break;
                        }
                    }
                }

            } catch (error) {
                console.warn('Error detecting hover requirements:', error);
            }

            return {
                requires_hover: requiresHover,
                hover_target_selector: hoverTargetSelector,
                hover_parent_chain: hoverParentChain
            };
        }

        // Optimized element information with caching and ancestor detection
        const elementInfoCache = new WeakMap();
        function getElementInfo(element) {
            if (!element) return {};

            // Check cache first
            if (elementInfoCache.has(element)) {
                return elementInfoCache.get(element);
            }

            // Find ancestors with ID attributes
            const ancestors = findAncestorsWithId(element);

            // Get natural classes (filtered to exclude GRETAH artificial classes)
            const naturalClasses = getNaturalClasses(element);
            const naturalClassName = naturalClasses.join(' ');

            // Detect viewport visibility and scroll requirements
            const viewportStatus = detectViewportStatus(element);

            // Detect hover interaction requirements
            const interactionRequirements = detectHoverRequirements(element);

            const info = {
                tagName: element.tagName.toLowerCase(),
                id: element.id || '',
                name: element.getAttribute('name') || '',
                className: naturalClassName, // Use filtered natural classes only
                originalClassName: element.className || '', // Keep original for debugging
                type: element.getAttribute('type') || '',
                value: element.value || element.textContent.trim() || '',
                placeholder: element.getAttribute('placeholder') || '',
                href: element.getAttribute('href') || '',
                role: element.getAttribute('role') || '',
                ariaLabel: element.getAttribute('aria-label') || '',
                cssSelector: getCssSelector(element),
                xpath: getXPath(element),
                enhancedCssSelector: getEnhancedCssSelector(element, ancestors),
                enhancedXPath: getEnhancedXPath(element, ancestors),
                text: element.textContent.trim(),
                ancestors: ancestors,  // Include ancestor information
                viewport_status: viewportStatus,  // NEW: Viewport visibility data
                interaction_requirements: interactionRequirements  // NEW: Hover interaction data
            };

            // Cache the result
            elementInfoCache.set(element, info);
            return info;
        }

        // Function to generate natural language description for an element
        function generateElementDescription(element, info) {
            const parts = [];

            // Start with element type
            let elementType = info.tagName;
            if (info.type) {
                elementType += ` (${info.type})`;
            }
            if (info.role) {
                elementType += ` with role "${info.role}"`;
            }
            parts.push(elementType);

            // Add identifying information in order of preference
            if (info.id) {
                parts.push(`with ID "${info.id}"`);
            } else if (info.name) {
                parts.push(`named "${info.name}"`);
            } else if (info.ariaLabel) {
                parts.push(`labeled "${info.ariaLabel}"`);
            } else if (info.text && info.text.length > 0 && info.text.length <= 30) {
                parts.push(`containing text "${info.text}"`);
            } else if (info.placeholder) {
                parts.push(`with placeholder "${info.placeholder}"`);
            } else if (info.href) {
                parts.push(`linking to "${info.href}"`);
            }

            // Add ancestor context if available
            if (info.ancestors && info.ancestors.length > 0) {
                const nearestAncestor = info.ancestors[0];
                if (nearestAncestor.id) {
                    parts.push(`inside ${nearestAncestor.tagName}#${nearestAncestor.id}`);
                }
            }

            return parts.join(' ');
        }

        // Function to update the info panel with ancestor information
        function updateInfoPanel(element) {
            if (!element) {
                infoPanel.innerHTML = '<strong>GRETAH Element Selector</strong>: Hover over elements and click to select. Press ESC to cancel.';
                return;
            }

            const info = getElementInfo(element);
            let html = '<strong>GRETAH Element Selector</strong>: ';

            if (window.selectedElement) {
                html += '<span style="color: #00FF00">Element selected!</span> Press ESC to cancel or click another element.';
            } else {
                html += 'Hover over elements and click to select. Press ESC to cancel.';
            }

            html += '<br><br>';

            // Generate and display natural language description
            const description = generateElementDescription(element, info);
            html += '<div style="border: 1px solid #4A6FE3; padding: 8px; margin: 4px 0; background-color: rgba(74, 111, 227, 0.1);">';
            html += '<strong>📝 Element Description:</strong><br>';
            html += `<em>${description}</em>`;
            html += '</div>';

            // Display technical element information
            html += '<div style="border: 1px solid #ccc; padding: 8px; margin: 4px 0; background-color: rgba(255,255,255,0.1);">';
            html += '<strong>🎯 Technical Details:</strong><br>';
            html += `<strong>Tag:</strong> ${info.tagName}`;

            if (info.id) html += ` | <strong>ID:</strong> ${info.id}`;
            if (info.name) html += ` | <strong>Name:</strong> ${info.name}`;
            if (info.type) html += ` | <strong>Type:</strong> ${info.type}`;
            if (info.text && info.text.length < 30) html += ` | <strong>Text:</strong> ${info.text}`;

            html += '<br>';
            html += `<strong>CSS:</strong> ${info.cssSelector || 'N/A'}`;
            html += '<br>';
            html += `<strong>Enhanced CSS:</strong> ${info.enhancedCssSelector || 'N/A'}`;
            html += '</div>';

            // Display ancestor information if available
            if (info.ancestors && info.ancestors.length > 0) {
                html += '<div style="border: 1px solid #888; padding: 8px; margin: 4px 0; background-color: rgba(255,255,255,0.05);">';
                html += '<strong>🔗 Hierarchical Context:</strong><br>';

                info.ancestors.forEach((ancestor, index) => {
                    html += `<div style="margin: 2px 0; padding-left: ${ancestor.level * 10}px;">`;
                    html += `<strong>Level ${ancestor.level}:</strong> ${ancestor.tagName}`;
                    html += ` | <strong>ID:</strong> ${ancestor.id}`;
                    if (ancestor.className) html += ` | <strong>Class:</strong> ${ancestor.className.split(' ')[0]}`;
                    if (ancestor.isClickable) html += ' | <span style="color: #00FF00">Clickable</span>';
                    html += '</div>';
                });

                html += '</div>';
            }

            // Display enhanced locators section
            html += '<div style="border: 1px solid #555; padding: 8px; margin: 4px 0; background-color: rgba(255,255,255,0.02);">';
            html += '<strong>🚀 Enhanced Locators:</strong><br>';
            html += `<strong>XPath:</strong> ${info.enhancedXPath || 'N/A'}`;
            html += '</div>';

            // Display viewport visibility information
            if (info.viewport_status) {
                const vs = info.viewport_status;
                html += '<div style="border: 1px solid #4CAF50; padding: 8px; margin: 4px 0; background-color: rgba(76, 175, 80, 0.1);">';
                html += '<strong>👁️ Viewport Status:</strong><br>';
                html += `<strong>Visible:</strong> ${vs.is_visible ? '✅ Yes' : '❌ No'} (${vs.visibility_percentage}%)`;

                if (vs.requires_scroll) {
                    const scrollIcon = vs.scroll_direction === 'up' ? '⬆️' :
                                     vs.scroll_direction === 'down' ? '⬇️' :
                                     vs.scroll_direction === 'left' ? '⬅️' :
                                     vs.scroll_direction === 'right' ? '➡️' : '🎯';
                    html += `<br><strong>Scroll Required:</strong> ${scrollIcon} ${vs.scroll_direction}`;
                    if (vs.scroll_distance > 0) {
                        html += ` (${vs.scroll_distance}px)`;
                    }
                } else {
                    html += '<br><strong>Scroll Required:</strong> ✅ No';
                }
                html += '</div>';
            }

            // Display hover interaction requirements
            if (info.interaction_requirements) {
                const ir = info.interaction_requirements;
                html += '<div style="border: 1px solid #FF9800; padding: 8px; margin: 4px 0; background-color: rgba(255, 152, 0, 0.1);">';
                html += '<strong>🖱️ Interaction Requirements:</strong><br>';

                if (ir.requires_hover) {
                    html += '<strong>Hover Required:</strong> ⚠️ Yes<br>';
                    if (ir.hover_target_selector) {
                        html += `<strong>Hover Target:</strong> ${ir.hover_target_selector}<br>`;
                    }
                    if (ir.hover_parent_chain && ir.hover_parent_chain.length > 0) {
                        html += '<strong>Hover Chain:</strong> ';
                        ir.hover_parent_chain.forEach((parent, index) => {
                            html += `${parent.tagName}`;
                            if (parent.id) html += `#${parent.id}`;
                            if (index < ir.hover_parent_chain.length - 1) html += ' → ';
                        });
                    }
                } else {
                    html += '<strong>Hover Required:</strong> ✅ No';
                }
                html += '</div>';
            }

            infoPanel.innerHTML = html;
        }

        // Optimized event handling using event delegation and throttling
        let lastHoverTime = 0;
        const HOVER_THROTTLE = 50; // 50ms throttle for hover events

        // Function to handle mouseover with throttling
        function handleMouseOver(event) {
            if (window.selectedElement) return;

            const now = Date.now();
            if (now - lastHoverTime < HOVER_THROTTLE) return;
            lastHoverTime = now;

            if (window.highlightedElement) {
                window.highlightedElement.classList.remove('gretah-element-highlight');
            }

            window.highlightedElement = event.target;
            event.target.classList.add('gretah-element-highlight');
            updateInfoPanel(event.target);

            event.stopPropagation();
        }

        // Function to handle mouseout
        function handleMouseOut(event) {
            if (window.selectedElement) return;

            event.target.classList.remove('gretah-element-highlight');
            updateInfoPanel(null);

            event.stopPropagation();
        }

        // Function to handle click
        function handleClick(event) {
            // Remove previous selection if any
            if (window.selectedElement) {
                window.selectedElement.classList.remove('gretah-element-selected');
            }

            // Remove highlight from previously highlighted element
            if (window.highlightedElement) {
                window.highlightedElement.classList.remove('gretah-element-highlight');
            }

            // Set the new selected element
            window.selectedElement = event.target;
            window.selectedElement.classList.add('gretah-element-selected');

            // Update the info panel
            updateInfoPanel(window.selectedElement);

            // Store the element info in a global variable for Selenium to access
            window.selectedElementInfo = getElementInfo(window.selectedElement);

            event.stopPropagation();
            event.preventDefault();
        }

        // Function to handle keydown (ESC key)
        function handleKeyDown(event) {
            if (event.key === 'Escape') {
                // Remove selection and highlighting
                if (window.selectedElement) {
                    window.selectedElement.classList.remove('gretah-element-selected');
                    window.selectedElement = null;
                }

                if (window.highlightedElement) {
                    window.highlightedElement.classList.remove('gretah-element-highlight');
                    window.highlightedElement = null;
                }

                updateInfoPanel(null);
                window.selectedElementInfo = null;
            }
        }

        // Use event delegation for better performance - attach listeners to document
        document.addEventListener('mouseover', handleMouseOver, true);
        document.addEventListener('mouseout', handleMouseOut, true);
        document.addEventListener('click', handleClick, true);
        document.addEventListener('keydown', handleKeyDown);

        // Expose functions globally for testing and debugging
        window.getCssSelector = getCssSelector;
        window.getXPath = getXPath;
        window.getElementInfo = getElementInfo;
        window.updateInfoPanel = updateInfoPanel;

        // Return true to indicate successful script injection
        return true;
        """

        result = driver.execute_script(script)
        return result is True
    except Exception as e:
        print(f"Error injecting element selection script: {e}")
        return False

def get_selected_element_info(driver):
    """
    Get information about the selected element.

    Args:
        driver: WebDriver instance

    Returns:
        dict: Information about the selected element, or None if no element is selected
    """
    try:
        # Check if an element has been selected
        selected_element_info = driver.execute_script("return window.selectedElementInfo;")
        return selected_element_info
    except Exception as e:
        print(f"Error getting selected element info: {e}")
        return None






@monitor_interactive_selection
def select_element_with_script_execution(state, merged_script_path=None):
    """
    Enhanced interactive element selection that executes previous test steps first.

    This function executes all previous test steps using the merged script from Stage 6,
    then allows interactive element selection on the resulting application state.

    Args:
        state: StateManager instance containing test case and script information
        merged_script_path (str, optional): Path to merged script. If None, uses state.combined_script_path

    Returns:
        dict: Information about the selected element, or None if selection failed
    """
    import subprocess
    import os
    import tempfile
    from pathlib import Path

    debug("Starting enhanced interactive element selection with script execution",
          stage="interactive_selector", operation="enhanced_selection_start",
          context={"merged_script_provided": merged_script_path is not None})

    # Validate prerequisites
    validation_result = _validate_enhanced_selection_prerequisites(state, merged_script_path)
    if not validation_result["valid"]:
        debug(f"Prerequisites validation failed: {validation_result['reason']}",
              stage="interactive_selector", operation="prerequisites_validation_failed",
              context=validation_result)
        return None

    # Get the merged script path
    script_path = merged_script_path or state.combined_script_path

    # Create lightweight conftest for interactive selection
    conftest_path = _create_lightweight_conftest()
    if not conftest_path:
        debug("Failed to create lightweight conftest configuration",
              stage="interactive_selector", operation="conftest_creation_failed")
        return None

    try:
        debug("=== ENHANCED SELECTION EXECUTION PHASE START ===",
              stage="interactive_selector", operation="execution_phase_start",
              context={"script_path": script_path})

        # Execute the merged script to reach the final application state
        debug("Step 1: Executing merged script to reach application state...",
              stage="interactive_selector", operation="step1_script_execution")

        browser_instance = _execute_script_and_get_browser(script_path, conftest_path, state)
        if not browser_instance:
            debug("Primary script execution failed, trying direct execution fallback...",
                  stage="interactive_selector", operation="primary_execution_failed",
                  context={
                      "script_path": script_path,
                      "conftest_path": conftest_path,
                      "failure_reason": "No browser instance returned from pytest execution"
                  })

            # Fallback: Try direct script execution without pytest
            browser_instance = _execute_script_directly_and_get_browser(script_path, state)

            if not browser_instance:
                debug("FAILED: Both pytest and direct execution failed",
                      stage="interactive_selector", operation="all_execution_methods_failed",
                      context={
                          "script_path": script_path,
                          "conftest_path": conftest_path,
                          "failure_reason": "No browser instance returned from any execution method"
                      })
                return {
                    "error": "script_execution_failed",
                    "message": "Failed to execute previous test steps",
                    "details": "Both pytest-based and direct script execution failed to provide a browser instance for element selection",
                    "troubleshooting": [
                        "Check that the merged script contains valid test functions",
                        "Verify that the script uses the browser fixture correctly",
                        "Ensure the script doesn't close the browser at the end",
                        "Check the script execution logs for specific errors",
                        "Try using regular element selection instead of enhanced mode",
                        "Verify Chrome browser is properly installed and accessible"
                    ]
                }
            else:
                debug("✅ Direct execution fallback succeeded",
                      stage="interactive_selector", operation="direct_execution_success",
                      context={"browser_type": type(browser_instance).__name__})

        debug("✅ Step 1 completed: Browser instance obtained from script execution",
              stage="interactive_selector", operation="step1_completed",
              context={"browser_type": type(browser_instance).__name__})

        # Inject element selection script into the browser
        debug("Step 2: Injecting element selection script into browser...",
              stage="interactive_selector", operation="step2_script_injection")

        if not inject_element_selection_script(browser_instance):
            debug("FAILED: Element selection script injection failed",
                  stage="interactive_selector", operation="script_injection_failed")
            try:
                browser_instance.quit()
            except:
                pass
            return {
                "error": "script_injection_failed",
                "message": "Failed to inject element selection script",
                "details": "The browser was opened successfully but the interactive element selection script could not be injected",
                "troubleshooting": [
                    "Check browser compatibility (Chrome is recommended)",
                    "Verify JavaScript execution is enabled",
                    "Try refreshing the page and attempting again",
                    "Check for browser security restrictions"
                ]
            }

        debug("✅ Step 2 completed: Element selection script injected successfully",
              stage="interactive_selector", operation="step2_completed")

        debug("Step 3: Browser ready for interactive element selection",
              stage="interactive_selector", operation="browser_ready_for_selection")

        # Wait for user to select an element
        debug("Step 4: Waiting for user to select an element...",
              stage="interactive_selector", operation="step3_waiting_for_selection")

        selected_element_info = _wait_for_element_selection(browser_instance)

        if selected_element_info:
            debug("✅ Enhanced selection completed successfully",
                  stage="interactive_selector", operation="enhanced_selection_success",
                  context={"selected_element": selected_element_info})
        else:
            debug("Enhanced selection completed but no element was selected",
                  stage="interactive_selector", operation="enhanced_selection_no_element")

        return selected_element_info

    except Exception as e:
        debug(f"Error during enhanced interactive element selection: {e}",
              stage="interactive_selector", operation="enhanced_selection_error",
              context={"error": str(e), "error_type": type(e).__name__})
        import traceback
        debug(f"Full traceback: {traceback.format_exc()}",
              stage="interactive_selector", operation="enhanced_selection_traceback")
        return {
            "error": "unexpected_error",
            "message": f"Unexpected error during enhanced selection: {e}",
            "details": str(e),
            "troubleshooting": [
                "Try using regular element selection instead",
                "Check the application logs for more details",
                "Verify system resources are available",
                "Contact support if the issue persists"
            ]
        }
    finally:
        # Cleanup temporary conftest file
        try:
            if conftest_path and os.path.exists(conftest_path):
                os.remove(conftest_path)
                debug("Cleaned up temporary conftest file",
                      stage="interactive_selector", operation="conftest_cleanup")
        except Exception as e:
            debug(f"Failed to cleanup conftest file: {e}",
                  stage="interactive_selector", operation="conftest_cleanup_failed")


def generate_enhanced_selection_diagnostic_report(state):
    """
    Generate a comprehensive diagnostic report for enhanced selection troubleshooting.

    Args:
        state: StateManager instance

    Returns:
        dict: Comprehensive diagnostic information
    """
    import glob
    from pathlib import Path

    debug("=== GENERATING ENHANCED SELECTION DIAGNOSTIC REPORT ===",
          stage="interactive_selector", operation="diagnostic_report_generation")

    # Basic state information
    basic_info = {
        "current_stage": str(getattr(state, 'current_stage', 'Unknown')),
        "current_step_index": getattr(state, 'current_step_index', None),
        "total_steps": getattr(state, 'total_steps', None),
        "all_steps_done": getattr(state, 'all_steps_done', None)
    }

    # Test case information
    test_case_info = {
        "has_selected_test_case": hasattr(state, 'selected_test_case') and bool(state.selected_test_case),
        "test_case_data": state.selected_test_case if hasattr(state, 'selected_test_case') else None
    }

    # Script path information
    script_paths = {
        "combined_script_path": getattr(state, 'combined_script_path', None),
        "last_script_file": getattr(state, 'last_script_file', None),
        "generated_script_path": getattr(state, 'generated_script_path', None),
        "optimized_script_path": getattr(state, 'optimized_script_path', None)
    }

    # File existence checks
    file_existence = {}
    for path_name, path_value in script_paths.items():
        if path_value:
            file_existence[path_name] = {
                "path": path_value,
                "exists": os.path.exists(path_value),
                "absolute_path": os.path.abspath(path_value),
                "is_file": os.path.isfile(path_value) if os.path.exists(path_value) else False,
                "size_bytes": os.path.getsize(path_value) if os.path.exists(path_value) else 0
            }
        else:
            file_existence[path_name] = {"path": None, "exists": False}

    # Previous scripts information
    previous_scripts = getattr(state, 'previous_scripts', {})
    previous_scripts_info = {
        "count": len(previous_scripts),
        "keys": list(previous_scripts.keys()),
        "script_lengths": {k: len(v) for k, v in previous_scripts.items()} if previous_scripts else {}
    }

    # Search for potential script files in common directories
    search_patterns = [
        "generated_tests/*.py",
        "generated_tests/*merged*.py",
        "generated_tests/*combined*.py",
        "generated_tests/*final*.py"
    ]

    found_scripts = {}
    for pattern in search_patterns:
        try:
            matches = glob.glob(pattern)
            found_scripts[pattern] = [
                {
                    "path": match,
                    "absolute_path": os.path.abspath(match),
                    "size_bytes": os.path.getsize(match),
                    "modified_time": os.path.getmtime(match)
                }
                for match in matches
            ]
        except Exception as e:
            found_scripts[pattern] = f"Error searching: {e}"

    # Combined script content information
    combined_script_content = getattr(state, 'combined_script_content', '')
    combined_script_info = {
        "has_combined_script_content": hasattr(state, 'combined_script_content'),
        "combined_script_content_length": len(combined_script_content) if combined_script_content else 0,
        "combined_script_content_preview": combined_script_content[:200] + "..." if combined_script_content and len(combined_script_content) > 200 else combined_script_content
    }

    # Validation result
    validation_result = _validate_enhanced_selection_prerequisites(state)

    diagnostic_report = {
        "timestamp": time.time(),
        "basic_info": basic_info,
        "test_case_info": test_case_info,
        "script_paths": script_paths,
        "file_existence": file_existence,
        "previous_scripts_info": previous_scripts_info,
        "found_scripts": found_scripts,
        "combined_script_info": combined_script_info,
        "validation_result": validation_result
    }

    debug("Diagnostic report generated",
          stage="interactive_selector", operation="diagnostic_report_generated",
          context={"report_sections": list(diagnostic_report.keys())})

    return diagnostic_report


def _validate_enhanced_selection_prerequisites(state, merged_script_path=None):
    """
    Validate prerequisites for enhanced interactive element selection with comprehensive diagnostic logging.

    Args:
        state: StateManager instance
        merged_script_path (str, optional): Path to merged script

    Returns:
        dict: Validation result with 'valid' boolean and 'reason' string
    """
    debug("=== ENHANCED SELECTION PREREQUISITES VALIDATION START ===",
          stage="interactive_selector", operation="prerequisites_validation",
          context={"merged_script_path_provided": merged_script_path is not None})

    # Comprehensive state inspection
    debug("StateManager inspection:",
          stage="interactive_selector", operation="state_inspection",
          context={
              "has_selected_test_case": hasattr(state, 'selected_test_case'),
              "selected_test_case_value": getattr(state, 'selected_test_case', None),
              "has_combined_script_path": hasattr(state, 'combined_script_path'),
              "combined_script_path_value": getattr(state, 'combined_script_path', None),
              "has_previous_scripts": hasattr(state, 'previous_scripts'),
              "previous_scripts_count": len(getattr(state, 'previous_scripts', {})),
              "current_step_index": getattr(state, 'current_step_index', 0),
              "total_steps": getattr(state, 'total_steps', 0)
          })

    # Check if we have a selected test case
    if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
        debug("VALIDATION FAILED: No test case selected",
              stage="interactive_selector", operation="prerequisites_validation",
              context={"validation_step": "test_case_check"})
        return {
            "valid": False,
            "reason": "No test case selected",
            "details": "A test case must be selected before using enhanced element selection"
        }

    debug("✅ Test case validation passed",
          stage="interactive_selector", operation="prerequisites_validation",
          context={
              "test_case_id": state.selected_test_case.get('Test Case ID', 'Unknown'),
              "validation_step": "test_case_check"
          })

    # Check if we have a merged script path - try multiple sources
    script_path = merged_script_path or getattr(state, 'combined_script_path', None)

    # Additional script path sources to check
    alternative_paths = []
    if hasattr(state, 'last_script_file'):
        alternative_paths.append(("last_script_file", getattr(state, 'last_script_file', None)))
    if hasattr(state, 'generated_script_path'):
        alternative_paths.append(("generated_script_path", getattr(state, 'generated_script_path', None)))
    if hasattr(state, 'optimized_script_path'):
        alternative_paths.append(("optimized_script_path", getattr(state, 'optimized_script_path', None)))

    debug("Script path resolution attempt:",
          stage="interactive_selector", operation="script_path_resolution",
          context={
              "primary_script_path": script_path,
              "merged_script_path_provided": merged_script_path,
              "state_combined_script_path": getattr(state, 'combined_script_path', None),
              "alternative_paths": alternative_paths
          })

    if not script_path:
        # Try alternative paths
        for path_name, path_value in alternative_paths:
            if path_value and os.path.exists(path_value):
                debug(f"Found alternative script path: {path_name}",
                      stage="interactive_selector", operation="script_path_resolution",
                      context={"alternative_path": path_value, "source": path_name})
                script_path = path_value
                break

    if not script_path:
        debug("VALIDATION FAILED: No merged script path found",
              stage="interactive_selector", operation="prerequisites_validation",
              context={
                  "validation_step": "script_path_check",
                  "checked_paths": {
                      "combined_script_path": getattr(state, 'combined_script_path', None),
                      "alternative_paths": alternative_paths
                  }
              })
        return {
            "valid": False,
            "reason": "No merged script available",
            "details": "Previous test steps must be completed and merged script generated in Stage 6"
        }

    debug("✅ Script path validation passed",
          stage="interactive_selector", operation="prerequisites_validation",
          context={
              "script_path": script_path,
              "validation_step": "script_path_check"
          })

    # Check if the script file exists
    file_exists = os.path.exists(script_path)
    debug(f"File existence check for: {script_path}",
          stage="interactive_selector", operation="file_existence_check",
          context={
              "script_path": script_path,
              "file_exists": file_exists,
              "absolute_path": os.path.abspath(script_path) if script_path else None
          })

    if not file_exists:
        debug("VALIDATION FAILED: Script file does not exist",
              stage="interactive_selector", operation="prerequisites_validation",
              context={
                  "validation_step": "file_existence_check",
                  "script_path": script_path,
                  "absolute_path": os.path.abspath(script_path) if script_path else None
              })
        return {
            "valid": False,
            "reason": "Merged script file not found",
            "details": f"Script file does not exist: {script_path}"
        }

    debug("✅ File existence validation passed",
          stage="interactive_selector", operation="prerequisites_validation",
          context={
              "script_path": script_path,
              "validation_step": "file_existence_check"
          })

    # Check if we have previous steps completed
    previous_scripts = getattr(state, 'previous_scripts', {})
    if not previous_scripts:
        debug("VALIDATION FAILED: No previous scripts found",
              stage="interactive_selector", operation="prerequisites_validation",
              context={
                  "validation_step": "previous_scripts_check",
                  "previous_scripts_type": type(previous_scripts).__name__,
                  "previous_scripts_value": previous_scripts
              })
        return {
            "valid": False,
            "reason": "No previous steps completed",
            "details": "At least one previous test step must be completed before using enhanced element selection"
        }

    debug("✅ Previous scripts validation passed",
          stage="interactive_selector", operation="prerequisites_validation",
          context={
              "previous_scripts_count": len(previous_scripts),
              "previous_script_keys": list(previous_scripts.keys()),
              "validation_step": "previous_scripts_check"
          })

    # Check current step index to ensure we're not on the first step
    current_step_index = getattr(state, 'current_step_index', 0)
    if current_step_index == 0:
        debug("VALIDATION FAILED: Cannot use enhanced selection on first step",
              stage="interactive_selector", operation="prerequisites_validation",
              context={
                  "validation_step": "step_index_check",
                  "current_step_index": current_step_index
              })
        return {
            "valid": False,
            "reason": "Cannot use enhanced selection on first step",
            "details": "Enhanced element selection requires previous steps to execute. Use regular element selection for the first step."
        }

    debug("✅ Step index validation passed",
          stage="interactive_selector", operation="prerequisites_validation",
          context={
              "current_step_index": current_step_index,
              "validation_step": "step_index_check"
          })

    debug("=== ENHANCED SELECTION PREREQUISITES VALIDATION SUCCESS ===",
          stage="interactive_selector", operation="prerequisites_validation",
          context={
              "final_script_path": script_path,
              "all_validations_passed": True
          })

    return {
        "valid": True,
        "reason": "All prerequisites met",
        "details": f"Ready for enhanced element selection with script: {script_path}"
    }


def _create_lightweight_conftest():
    """
    Create a lightweight conftest.py configuration for interactive element selection.

    This conftest excludes heavy performance monitoring, detailed logging, and
    artifact collection that are unnecessary for element selection purposes.

    Uses file-based browser persistence to survive process boundaries.

    Returns:
        str: Path to the created conftest file, or None if creation failed
    """
    import tempfile
    import os

    conftest_content = '''"""
Lightweight conftest.py for interactive element selection.
Excludes heavy monitoring and artifact collection.
Uses file-based browser persistence to survive process boundaries.
"""

import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import logging
import atexit
import json
import tempfile
import os
import time

# Configure minimal logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

# Global variable to store browser instance
_interactive_browser_instance = None
_browser_info_file = None

def create_browser_for_interactive_selection():
    """Create a browser instance specifically for interactive element selection."""
    global _interactive_browser_instance, _browser_info_file

    # Configure Chrome options for interactive use
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")

    # Add remote debugging port for persistence
    chrome_options.add_argument("--remote-debugging-port=9222")
    chrome_options.add_argument("--user-data-dir=" + tempfile.mkdtemp())

    # Disable logging preferences to reduce overhead
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    try:
        # Create the WebDriver instance
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Set implicit wait time
        driver.implicitly_wait(10)

        # Anti-detection measures
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        logger.info("Lightweight browser initialized for interactive element selection")

        # Store globally for access by interactive selector
        _interactive_browser_instance = driver

        # Create browser info file for persistence across processes
        browser_info = {
            "session_id": driver.session_id,
            "command_executor_url": driver.command_executor._url,
            "created_at": time.time(),
            "status": "active"
        }

        # Create temporary file to store browser info
        with tempfile.NamedTemporaryFile(mode='w', suffix='_browser_info.json', delete=False) as f:
            json.dump(browser_info, f)
            _browser_info_file = f.name

        logger.info(f"Browser info saved to: {_browser_info_file}")

        return driver

    except Exception as e:
        logger.error(f"Failed to initialize lightweight browser: {e}")
        raise

@pytest.fixture(scope="session")  # Changed to session scope to prevent automatic cleanup
def browser():
    """Lightweight browser fixture for interactive element selection."""
    global _interactive_browser_instance

    if _interactive_browser_instance is None:
        _interactive_browser_instance = create_browser_for_interactive_selection()

    # Store browser instance info for cross-process access
    if _interactive_browser_instance and _browser_info_file:
        # Update browser info file with current status
        try:
            browser_info = {
                "session_id": _interactive_browser_instance.session_id,
                "command_executor_url": _interactive_browser_instance.command_executor._url,
                "created_at": time.time(),
                "status": "active_in_fixture",
                "process_id": os.getpid()
            }

            with open(_browser_info_file, 'w') as f:
                json.dump(browser_info, f)

        except Exception as e:
            logger.warning(f"Failed to update browser info file: {e}")

    # Return the browser instance - session scope prevents automatic cleanup
    return _interactive_browser_instance

def get_interactive_browser():
    """Get the current interactive browser instance."""
    global _interactive_browser_instance

    # If browser instance exists and is still alive, return it
    if _interactive_browser_instance:
        try:
            # Test if browser is still responsive
            _interactive_browser_instance.current_url
            return _interactive_browser_instance
        except Exception as e:
            logger.warning(f"Browser instance not responsive: {e}")
            _interactive_browser_instance = None

    # CRITICAL FIX: Always try to restore browser from file since global variables
    # don't persist across process boundaries (pytest subprocess)
    import tempfile
    temp_dir = tempfile.gettempdir()

    try:
        # Look for browser info files created during pytest execution
        browser_info_files = [f for f in os.listdir(temp_dir) if f.endswith('_browser_info.json')]

        if browser_info_files:
            # Use the most recent browser info file
            browser_info_files.sort(key=lambda x: os.path.getmtime(os.path.join(temp_dir, x)), reverse=True)
            browser_info_file = os.path.join(temp_dir, browser_info_files[0])

            logger.info(f"Attempting to restore browser from: {browser_info_file}")

            with open(browser_info_file, 'r') as f:
                browser_info = json.load(f)

            logger.info(f"Browser info loaded: {browser_info}")

            # SIMPLIFIED APPROACH: Since browser session reconnection is complex and unreliable,
            # just create a new browser instance and navigate to the same state
            logger.info("Browser info file found, but creating new browser instance for reliability")

            # Create new browser instance
            new_browser = create_browser_for_interactive_selection()

            if new_browser and "current_url" in browser_info:
                try:
                    # Navigate to the same URL as the previous browser
                    new_browser.get(browser_info["current_url"])
                    logger.info(f"New browser navigated to: {browser_info['current_url']}")

                    _interactive_browser_instance = new_browser
                    return new_browser

                except Exception as nav_error:
                    logger.warning(f"Failed to navigate new browser: {nav_error}")
                    # Return the browser anyway, it can be used for element selection
                    _interactive_browser_instance = new_browser
                    return new_browser

            elif new_browser:
                # Return new browser even without navigation
                _interactive_browser_instance = new_browser
                return new_browser

    except Exception as e:
        logger.warning(f"Failed to restore browser from file: {e}")

    # Final fallback: create new browser if all restoration attempts failed
    if not _interactive_browser_instance:
        logger.info("No browser instance available, creating new one")
        _interactive_browser_instance = create_browser_for_interactive_selection()

    return _interactive_browser_instance

def get_browser_info_file():
    """Get the path to the browser info file."""
    return _browser_info_file

def cleanup_interactive_browser():
    """Cleanup the interactive browser instance."""
    global _interactive_browser_instance, _browser_info_file

    if _interactive_browser_instance:
        try:
            _interactive_browser_instance.quit()
        except:
            pass
        _interactive_browser_instance = None

    # Clean up browser info file
    if _browser_info_file and os.path.exists(_browser_info_file):
        try:
            os.remove(_browser_info_file)
        except:
            pass
        _browser_info_file = None

# Don't register automatic cleanup to preserve browser for interactive selection
# atexit.register(cleanup_interactive_browser)

def force_browser_persistence():
    """Force browser instance to persist by creating a persistent reference."""
    global _interactive_browser_instance

    if _interactive_browser_instance:
        # Create a persistent browser info file with detailed session information
        browser_info = {
            "session_id": _interactive_browser_instance.session_id,
            "command_executor_url": _interactive_browser_instance.command_executor._url,
            "created_at": time.time(),
            "status": "forced_persistence",
            "process_id": os.getpid(),
            "current_url": _interactive_browser_instance.current_url,
            "window_handles": _interactive_browser_instance.window_handles
        }

        # Create multiple persistence files for redundancy
        persistence_files = []
        for i in range(3):
            with tempfile.NamedTemporaryFile(mode='w', suffix=f'_persistent_browser_{i}.json', delete=False) as f:
                json.dump(browser_info, f)
                persistence_files.append(f.name)

        logger.info(f"Browser persistence forced with {len(persistence_files)} backup files")
        return persistence_files

    return []

# Register persistence function to run at module exit
atexit.register(force_browser_persistence)
'''

    try:
        # Create temporary conftest file
        with tempfile.NamedTemporaryFile(mode='w', suffix='_conftest.py', delete=False) as f:
            f.write(conftest_content)
            conftest_path = f.name

        debug(f"Created lightweight conftest at: {conftest_path}",
              stage="interactive_selector", operation="conftest_creation",
              context={"conftest_path": conftest_path})

        return conftest_path

    except Exception as e:
        debug(f"Failed to create lightweight conftest: {e}",
              stage="interactive_selector", operation="conftest_creation_failed",
              context={"error": str(e)})
        return None


def _create_standalone_browser():
    """
    Create a standalone browser instance without pytest fixtures.

    This is used as a fallback when the pytest-based browser creation fails.

    Returns:
        WebDriver: Browser instance, or None if creation failed
    """
    debug("Creating standalone browser instance as fallback",
          stage="interactive_selector", operation="standalone_browser_creation")

    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager

        # Configure Chrome options for interactive use
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--remote-debugging-port=9223")  # Different port to avoid conflicts

        # Disable logging preferences to reduce overhead
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Create the WebDriver instance
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Set implicit wait time
        driver.implicitly_wait(10)

        # Anti-detection measures
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set CDP to disable automation flags
        try:
            driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                '''
            })
        except Exception as cdp_error:
            debug(f"CDP commands failed (non-critical): {cdp_error}",
                  stage="interactive_selector", operation="cdp_commands_failed")

        debug("Standalone browser created successfully",
              stage="interactive_selector", operation="standalone_browser_success",
              context={"browser_type": type(driver).__name__})

        return driver

    except Exception as e:
        debug(f"Failed to create standalone browser: {e}",
              stage="interactive_selector", operation="standalone_browser_failed",
              context={"error": str(e)})
        return None


def _execute_script_directly_and_get_browser(script_path, state):
    """
    Alternative approach: Execute script directly without pytest and create browser manually.

    This bypasses pytest fixture issues entirely by running the script logic directly
    and creating a browser instance manually.

    Args:
        script_path (str): Path to the merged script to execute
        state: StateManager instance

    Returns:
        WebDriver: Browser instance after script execution, or None if failed
    """
    debug("Attempting direct script execution without pytest",
          stage="interactive_selector", operation="direct_execution_start",
          context={"script_path": script_path})

    try:
        # Create a standalone browser instance
        browser = _create_standalone_browser()
        if not browser:
            debug("Failed to create standalone browser for direct execution",
                  stage="interactive_selector", operation="direct_execution_browser_failed")
            return None

        # Read the script content
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()

        # Extract test functions from the script
        import ast
        import types

        # Parse the script to extract test functions
        tree = ast.parse(script_content)

        # Create a module to execute the script in
        script_module = types.ModuleType("test_script")

        # Add necessary imports to the module
        script_module.pytest = __import__('pytest')
        script_module.webdriver = __import__('selenium.webdriver', fromlist=['webdriver'])
        script_module.By = __import__('selenium.webdriver.common.by', fromlist=['By']).By
        script_module.WebDriverWait = __import__('selenium.webdriver.support.ui', fromlist=['WebDriverWait']).WebDriverWait
        script_module.expected_conditions = __import__('selenium.webdriver.support.expected_conditions', fromlist=['expected_conditions'])

        # Execute the script in the module context
        exec(script_content, script_module.__dict__)

        # Find and execute test functions
        test_functions = [name for name in dir(script_module) if name.startswith('test_') and callable(getattr(script_module, name))]

        debug(f"Found test functions: {test_functions}",
              stage="interactive_selector", operation="test_functions_found",
              context={"functions": test_functions})

        # Execute each test function with the browser
        for func_name in test_functions:
            try:
                test_func = getattr(script_module, func_name)
                debug(f"Executing test function: {func_name}",
                      stage="interactive_selector", operation="executing_test_function",
                      context={"function": func_name})

                # Call the test function with the browser
                test_func(browser)

                debug(f"Successfully executed test function: {func_name}",
                      stage="interactive_selector", operation="test_function_success",
                      context={"function": func_name})

            except Exception as e:
                debug(f"Error executing test function {func_name}: {e}",
                      stage="interactive_selector", operation="test_function_error",
                      context={"function": func_name, "error": str(e)})
                # Continue with other functions

        # Verify browser is still responsive
        try:
            current_url = browser.current_url
            debug(f"Direct execution completed, browser responsive at: {current_url}",
                  stage="interactive_selector", operation="direct_execution_success",
                  context={"url": current_url})
            return browser
        except Exception as e:
            debug(f"Browser not responsive after direct execution: {e}",
                  stage="interactive_selector", operation="direct_execution_browser_not_responsive")
            try:
                browser.quit()
            except:
                pass
            return None

    except Exception as e:
        debug(f"Error in direct script execution: {e}",
              stage="interactive_selector", operation="direct_execution_error",
              context={"error": str(e)})
        import traceback
        debug(f"Direct execution traceback: {traceback.format_exc()}",
              stage="interactive_selector", operation="direct_execution_traceback")
        return None


def _execute_script_and_get_browser(script_path, conftest_path, state):
    """
    Execute the merged script and return the browser instance for interactive selection.

    Enhanced with multiple browser retrieval strategies and comprehensive error handling.

    Args:
        script_path (str): Path to the merged script to execute
        conftest_path (str): Path to the lightweight conftest configuration
        state: StateManager instance

    Returns:
        WebDriver: Browser instance after script execution, or None if failed
    """
    import subprocess
    import sys
    import time
    import importlib.util
    import json
    from pathlib import Path

    debug(f"=== SCRIPT EXECUTION FOR INTERACTIVE SELECTION START ===",
          stage="interactive_selector", operation="script_execution_start",
          context={"script_path": script_path, "conftest_path": conftest_path})

    # Read and log script content for debugging
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
            debug(f"Script content preview (first 500 chars): {script_content[:500]}...",
                  stage="interactive_selector", operation="script_content_preview",
                  context={"script_length": len(script_content)})
    except Exception as e:
        debug(f"Failed to read script content: {e}",
              stage="interactive_selector", operation="script_content_read_failed")

    temp_conftest_in_script_dir = None
    browser_info_file = None

    try:
        # Get the directory containing the script
        script_dir = os.path.dirname(os.path.abspath(script_path))
        conftest_dir = os.path.dirname(conftest_path)

        debug(f"Script execution directories:",
              stage="interactive_selector", operation="directory_setup",
              context={
                  "script_dir": script_dir,
                  "conftest_dir": conftest_dir,
                  "script_absolute_path": os.path.abspath(script_path)
              })

        # Copy conftest to script directory temporarily
        import shutil
        temp_conftest_in_script_dir = os.path.join(script_dir, "conftest.py")
        shutil.copy2(conftest_path, temp_conftest_in_script_dir)

        debug(f"Copied conftest to script directory: {temp_conftest_in_script_dir}",
              stage="interactive_selector", operation="conftest_copy")

        # Prepare pytest command for script execution with session scope
        pytest_cmd = [
            sys.executable, "-m", "pytest",
            script_path,
            "-v",
            "--tb=long",
            "--no-header",
            "--disable-warnings",
            f"--confcutdir={script_dir}",
            "-s",  # Don't capture output
            "--setup-show"  # Show fixture setup for debugging
        ]

        debug(f"Executing pytest command: {' '.join(pytest_cmd)}",
              stage="interactive_selector", operation="pytest_execution",
              context={"command": pytest_cmd, "working_directory": script_dir})

        # Execute the script using pytest
        process = subprocess.Popen(
            pytest_cmd,
            cwd=script_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        debug("Waiting for script execution to complete...",
              stage="interactive_selector", operation="script_execution_waiting")

        # Wait for process to complete
        stdout, stderr = process.communicate(timeout=180)

        debug(f"Script execution completed with return code: {process.returncode}",
              stage="interactive_selector", operation="script_execution_completed",
              context={
                  "return_code": process.returncode,
                  "stdout_length": len(stdout),
                  "stderr_length": len(stderr),
                  "stdout_preview": stdout[:1000] if stdout else "No stdout",
                  "stderr_preview": stderr[:1000] if stderr else "No stderr"
              })

        # Log full output for debugging
        if stdout:
            debug(f"STDOUT: {stdout}",
                  stage="interactive_selector", operation="script_stdout")
        if stderr:
            debug(f"STDERR: {stderr}",
                  stage="interactive_selector", operation="script_stderr")

        # Check if execution was successful (allow warnings but not errors)
        if process.returncode not in [0, 1]:  # 0 = success, 1 = tests failed but pytest ran
            debug(f"Script execution failed with return code {process.returncode}",
                  stage="interactive_selector", operation="script_execution_failed",
                  context={
                      "return_code": process.returncode,
                      "stderr": stderr,
                      "stdout": stdout,
                      "script_path": script_path
                  })
            return None

        debug("Script execution completed, attempting multiple browser retrieval strategies...",
              stage="interactive_selector", operation="browser_retrieval_start")

        # Strategy 1: Try to import conftest module and get browser instance
        browser_instance = None
        try:
            spec = importlib.util.spec_from_file_location("conftest", temp_conftest_in_script_dir)
            conftest_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(conftest_module)

            debug("Conftest module loaded successfully",
                  stage="interactive_selector", operation="conftest_module_loaded")

            # Try multiple methods to get browser instance from conftest

            # Method 1a: Call get_interactive_browser()
            browser_instance = conftest_module.get_interactive_browser()

            if not browser_instance:
                debug("Strategy 1a FAILED: get_interactive_browser() returned None",
                      stage="interactive_selector", operation="strategy1a_failed")

                # Method 1b: Try to access global variable directly
                if hasattr(conftest_module, '_interactive_browser_instance'):
                    browser_instance = getattr(conftest_module, '_interactive_browser_instance')
                    debug("Strategy 1b: Accessed _interactive_browser_instance directly",
                          stage="interactive_selector", operation="strategy1b_attempt")

                # Method 1c: Try to call browser fixture directly
                if not browser_instance and hasattr(conftest_module, 'browser'):
                    try:
                        # This won't work outside pytest context, but worth trying
                        browser_instance = conftest_module.browser()
                        debug("Strategy 1c: Called browser fixture directly",
                              stage="interactive_selector", operation="strategy1c_attempt")
                    except Exception as fixture_error:
                        debug(f"Strategy 1c FAILED: {fixture_error}",
                              stage="interactive_selector", operation="strategy1c_failed")

                # Method 1d: Try to recreate browser using conftest functions
                if not browser_instance and hasattr(conftest_module, 'create_browser_for_interactive_selection'):
                    try:
                        browser_instance = conftest_module.create_browser_for_interactive_selection()
                        debug("Strategy 1d: Created new browser using conftest function",
                              stage="interactive_selector", operation="strategy1d_attempt")
                    except Exception as create_error:
                        debug(f"Strategy 1d FAILED: {create_error}",
                              stage="interactive_selector", operation="strategy1d_failed")

            if browser_instance:
                debug("Strategy 1 SUCCESS: Got browser instance from conftest module",
                      stage="interactive_selector", operation="strategy1_success",
                      context={"browser_type": type(browser_instance).__name__})

                # Test browser responsiveness
                try:
                    current_url = browser_instance.current_url
                    debug(f"Browser is responsive, current URL: {current_url}",
                          stage="interactive_selector", operation="browser_responsiveness_check")
                    return browser_instance
                except Exception as e:
                    debug(f"Browser not responsive: {e}",
                          stage="interactive_selector", operation="browser_not_responsive")
                    browser_instance = None
            else:
                debug("Strategy 1 FAILED: All methods failed to get browser instance from conftest",
                      stage="interactive_selector", operation="strategy1_all_methods_failed")

        except Exception as e:
            debug(f"Strategy 1 ERROR: Failed to load conftest module: {e}",
                  stage="interactive_selector", operation="strategy1_error",
                  context={"error": str(e)})

        # Strategy 2: Try to get browser info file and reconnect
        try:
            # Look for browser info file in temp directory
            import tempfile
            temp_dir = tempfile.gettempdir()
            browser_info_files = [f for f in os.listdir(temp_dir) if f.endswith('_browser_info.json')]

            if browser_info_files:
                # Use the most recent browser info file
                browser_info_files.sort(key=lambda x: os.path.getmtime(os.path.join(temp_dir, x)), reverse=True)
                browser_info_file = os.path.join(temp_dir, browser_info_files[0])

                debug(f"Strategy 2: Found browser info file: {browser_info_file}",
                      stage="interactive_selector", operation="strategy2_browser_info_found")

                with open(browser_info_file, 'r') as f:
                    browser_info = json.load(f)

                # SIMPLIFIED APPROACH: Create new browser and navigate to same state
                debug("Strategy 2: Creating new browser instance instead of reconnecting",
                      stage="interactive_selector", operation="strategy2_new_browser")

                # Create new browser instance
                from selenium import webdriver
                from selenium.webdriver.chrome.service import Service
                from selenium.webdriver.chrome.options import Options
                from webdriver_manager.chrome import ChromeDriverManager

                chrome_options = Options()
                chrome_options.add_argument("--start-maximized")
                chrome_options.add_argument("--disable-extensions")
                chrome_options.add_argument("--disable-popup-blocking")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                driver = webdriver.Chrome(
                    service=Service(ChromeDriverManager().install()),
                    options=chrome_options
                )

                driver.implicitly_wait(10)
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                # Navigate to the same URL if available
                if "current_url" in browser_info and browser_info["current_url"] != "data:,":
                    try:
                        driver.get(browser_info["current_url"])
                        debug(f"Strategy 2: Navigated to {browser_info['current_url']}",
                              stage="interactive_selector", operation="strategy2_navigation")
                    except Exception as nav_error:
                        debug(f"Strategy 2: Navigation failed: {nav_error}",
                              stage="interactive_selector", operation="strategy2_navigation_failed")

                debug("Strategy 2 SUCCESS: Created new browser instance",
                      stage="interactive_selector", operation="strategy2_success")
                return driver

        except Exception as e:
            debug(f"Strategy 2 ERROR: Failed to reconnect to browser: {e}",
                  stage="interactive_selector", operation="strategy2_error",
                  context={"error": str(e)})

        # Strategy 3: Create a new browser instance as fallback
        debug("Strategy 3: Creating new browser instance as fallback",
              stage="interactive_selector", operation="strategy3_fallback")

        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager

            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=chrome_options
            )

            driver.implicitly_wait(10)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            debug("Strategy 3 SUCCESS: Created new browser instance",
                  stage="interactive_selector", operation="strategy3_success",
                  context={"browser_type": type(driver).__name__})

            return driver

        except Exception as e:
            debug(f"Strategy 3 ERROR: Failed to create new browser: {e}",
                  stage="interactive_selector", operation="strategy3_error",
                  context={"error": str(e)})

        debug("All browser retrieval strategies failed",
              stage="interactive_selector", operation="all_strategies_failed")
        return None

    except subprocess.TimeoutExpired:
        debug("Script execution timed out after 3 minutes",
              stage="interactive_selector", operation="script_execution_timeout")
        if 'process' in locals():
            process.kill()
        return None
    except Exception as e:
        debug(f"Error executing script: {e}",
              stage="interactive_selector", operation="script_execution_error",
              context={"error": str(e), "error_type": type(e).__name__})
        import traceback
        debug(f"Full traceback: {traceback.format_exc()}",
              stage="interactive_selector", operation="script_execution_traceback")
        return None
    finally:
        # Cleanup temporary conftest in script directory
        try:
            if temp_conftest_in_script_dir and os.path.exists(temp_conftest_in_script_dir):
                os.remove(temp_conftest_in_script_dir)
                debug("Cleaned up temporary conftest file",
                      stage="interactive_selector", operation="temp_conftest_cleanup")
        except Exception as e:
            debug(f"Failed to cleanup temporary conftest: {e}",
                  stage="interactive_selector", operation="temp_conftest_cleanup_failed")


def _wait_for_element_selection(browser_instance):
    """
    Wait for user to select an element in the browser.

    Enhanced with better error handling and controlled browser lifecycle.

    Args:
        browser_instance: WebDriver instance with element selection script injected

    Returns:
        dict: Selected element information, or None if selection failed/timed out
    """
    import time

    debug("Waiting for user to select an element",
          stage="interactive_selector", operation="waiting_for_selection")

    try:
        # Verify browser is responsive before starting selection
        try:
            current_url = browser_instance.current_url
            debug(f"Browser is responsive, starting element selection on: {current_url}",
                  stage="interactive_selector", operation="browser_responsiveness_verified")
        except Exception as e:
            debug(f"Browser not responsive before selection: {e}",
                  stage="interactive_selector", operation="browser_not_responsive_before_selection")
            return None

        # Optimized polling with exponential backoff
        selected_element_info = None
        max_wait_time = 300  # 5 minutes timeout
        start_time = time.time()
        poll_interval = 0.1  # Start with faster polling
        max_poll_interval = 2.0  # Maximum polling interval

        print("🎯 Enhanced Interactive Element Selection Mode Activated")
        print("📍 Please select an element in the browser window")
        print("🔄 The browser contains the application state after executing all previous test steps")
        print("⌨️  Press ESC to cancel selection")
        print("⏱️  Selection will timeout in 5 minutes")

        while time.time() - start_time < max_wait_time:
            try:
                # Check if an element has been selected
                selected_element_info = get_selected_element_info(browser_instance)
                if selected_element_info:
                    debug("Element selected in enhanced mode!",
                          stage="interactive_selector", operation="enhanced_element_selected",
                          context={"element_info": selected_element_info})
                    print("✅ Element selected successfully!")
                    break

                # Exponential backoff for polling interval
                time.sleep(poll_interval)
                poll_interval = min(poll_interval * 1.2, max_poll_interval)

            except Exception as e:
                debug(f"Error during element selection polling: {e}",
                      stage="interactive_selector", operation="selection_polling_error",
                      context={"error": str(e)})
                # Continue polling despite errors
                time.sleep(1)

        if not selected_element_info:
            debug("No element selected within timeout period",
                  stage="interactive_selector", operation="enhanced_selection_timeout",
                  context={"timeout_seconds": max_wait_time})
            print("⏰ Selection timed out. No element was selected.")

        return selected_element_info

    except Exception as e:
        debug(f"Error during element selection wait: {e}",
              stage="interactive_selector", operation="selection_wait_error",
              context={"error": str(e)})
        return None
    finally:
        # Keep the browser open for a moment so the user can see the selection
        print("🔄 Keeping browser open for 3 seconds...")
        time.sleep(3)

        # Controlled browser cleanup with user notification
        try:
            print("🧹 Closing browser...")
            browser_instance.quit()
            debug("Browser closed after element selection",
                  stage="interactive_selector", operation="browser_cleanup")
            print("✅ Browser closed successfully")
        except Exception as e:
            debug(f"Failed to close browser: {e}",
                  stage="interactive_selector", operation="browser_cleanup_failed")
            print("⚠️  Warning: Failed to close browser cleanly")
