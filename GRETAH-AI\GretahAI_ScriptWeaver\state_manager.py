"""
State Manager for GretahAI ScriptWeaver

This module provides a centralized state management class for the ScriptWeaver application.
It encapsulates all session state variables and provides helper methods for state mutations.

The StateManager follows a singleton pattern within the Streamlit session state:
1. A single instance is created and stored in st.session_state["state"]
2. All components access this shared instance via StateManager.get(st)
3. State mutations are performed directly on this instance

Key features:
- Organizes state by logical categories (metadata, counters, artifacts)
- Provides type hints for better IDE support and code safety
- Centralizes all state in one place to avoid scattered session_state access
- Includes helper methods for common state operations

Usage:
    # Initialize in main app
    StateManager().init_in_session(st)

    # Access in any function
    state = StateManager.get(st)

    # Mutate state directly
    state.current_step_index += 1
    state.test_data = {"username": "test_user"}

CLEANUP PHASES COMPLETED:
========================

✅ PHASE 1 - DEPRECATED METHODS REMOVAL (259 lines removed):
- update_stage_based_on_completion() - Deprecated (84 lines)
- current_app_stage property - Backward compatibility (12 lines)
- add_validation_feedback() - Unused analytics (38 lines)
- get_common_validation_issues() - Unused analytics (63 lines)
- track_script_regeneration() - Unused metrics (25 lines)
- get_feedback_effectiveness_metrics() - Unused analytics (55 lines)

✅ PHASE 2 - DEAD CODE CLEANUP (134 lines removed):
- Duplicate _upgrade_existing_state method (44 lines)
- Obsolete upgrade logic for extracted mixin methods (20 lines)
- Orphaned placeholder comments referencing moved methods (26 lines)
- Excessive whitespace cleanup (40 lines)
- Updated header documentation references (4 lines)

✅ PHASE 3 - DEBUG STATEMENT OPTIMIZATION (129 lines removed):
- Removed verbose debug statements that provided minimal value
- Kept critical error handling and validation debug statements
- Simplified flag cleanup logic by removing unused special transition tracking
- Maintained essential debugging for stage transitions and critical operations

✅ PHASE 4 - CONSTANTS EXTRACTION (27 lines added for configuration):
- Extracted hardcoded initialization values to configuration constants
- Added DEFAULT_ELEMENT_TIMEOUT, DEFAULT_WEBSITE_URL, DEFAULT_INITIAL_STAGE
- Added DEFAULT_STEP_INDEX, DEFAULT_TOTAL_STEPS, DEFAULT_REGEN_ATTEMPTS
- Added EMPTY_STRING, EMPTY_MARKDOWN, DEFAULT_FALSE, DEFAULT_TRUE
- Replaced 35+ hardcoded values throughout StateManager with constants
- Improved maintainability and centralized configuration management

✅ PHASE 5 - COMPREHENSIVE WHITESPACE CLEANUP (26 lines removed):
- Reduced excessive consecutive blank lines (3+ lines → max 2 lines)
- Standardized method separation spacing (consistent 1-2 blank lines)
- Maintained proper section separation (2 blank lines for major sections)
- Preserved meaningful whitespace for code readability
- Improved professional code formatting following Python style guidelines

✅ MODULAR REFACTORING COMPLETED:
- Phase 2: Extract hybrid editing methods → core/state_mixins/hybrid_editing.py (216 lines)
- Phase 3: Extract data persistence → core/state_mixins/data_persistence.py (238 lines)
- Phase 4: Extract script management → core/state_mixins/script_management.py (614 lines)

FINAL RESULT: 1,584 lines (clean, professional formatting with optimized whitespace)
TOTAL REDUCTION: ~1,340 lines removed from original 2,924 lines (46% total reduction)
"""

import os
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from enum import Enum

# Import structured debug function for GRETAH logging compliance
from debug_utils import debug

# Import state management mixins for modular StateManager composition
from core.state_mixins.hybrid_editing import HybridEditingMixin
from core.state_mixins.data_persistence import DataPersistenceMixin
from core.state_mixins.script_management import ScriptManagementMixin


class StateStage(Enum):
    """
    Authoritative stage enumeration for GretahAI ScriptWeaver workflow.

    This enum serves as the single source of truth for stage determination,
    replacing the fragile distributed boolean flag interrogation system.
    """
    STAGE1_UPLOAD = "stage1_upload"  # CSV Upload
    STAGE2_WEBSITE = "stage2_website"  # Website Configuration
    STAGE3_CONVERT = "stage3_convert"  # Test Case Analysis and Conversion
    STAGE4_DETECT = "stage4_detect"  # UI Element Detection and Step Selection
    STAGE5_DATA = "stage5_data"  # Manual Data Entry
    STAGE6_GENERATE = "stage6_generate"  # Test Script Generation
    STAGE6B_GENERATE_TESTCASE = "stage6b_generate_testcase"  # Test Case-Level Script Generation
    STAGE7_EXECUTE = "stage7_execute"  # Test Script Execution
    STAGE7B_EXECUTE_TESTCASE = "stage7b_execute_testcase"  # Test Case-Level Script Execution
    STAGE8_OPTIMIZE = "stage8_optimize"  # Script Consolidation and Optimization
    STAGE9_BROWSE = "stage9_browse"  # Script Browser and Comparison
    STAGE10_PLAYGROUND = "stage10_playground"  # Script Playground

    def __str__(self):
        return self.value

    def get_stage_number(self) -> int:
        """Get the numeric stage number (1-10, with 6B as 6.5)."""
        stage_map = {
            StateStage.STAGE1_UPLOAD: 1,
            StateStage.STAGE2_WEBSITE: 2,
            StateStage.STAGE3_CONVERT: 3,
            StateStage.STAGE4_DETECT: 4,
            StateStage.STAGE5_DATA: 5,
            StateStage.STAGE6_GENERATE: 6,
            StateStage.STAGE6B_GENERATE_TESTCASE: 6.5,  # Alternative to Stage 6
            StateStage.STAGE7_EXECUTE: 7,
            StateStage.STAGE7B_EXECUTE_TESTCASE: 7.5,  # Alternative to Stage 7
            StateStage.STAGE8_OPTIMIZE: 8,
            StateStage.STAGE9_BROWSE: 9,
            StateStage.STAGE10_PLAYGROUND: 10
        }
        return stage_map.get(self, 1)

    def get_display_name(self) -> str:
        """Get the human-readable stage name."""
        stage_names = {
            StateStage.STAGE1_UPLOAD: "Stage 1: CSV Upload",
            StateStage.STAGE2_WEBSITE: "Stage 2: Website Configuration",
            StateStage.STAGE3_CONVERT: "Stage 3: Test Case Analysis and Conversion",
            StateStage.STAGE4_DETECT: "Stage 4: UI Element Detection and Step Selection",
            StateStage.STAGE5_DATA: "Stage 5: Manual Data Entry",
            StateStage.STAGE6_GENERATE: "Stage 6: Test Script Generation",
            StateStage.STAGE6B_GENERATE_TESTCASE: "Stage 6B: Test Case-Level Script Generation",
            StateStage.STAGE7_EXECUTE: "Stage 7: Test Script Execution",
            StateStage.STAGE7B_EXECUTE_TESTCASE: "Stage 7B: Test Case-Level Script Execution",
            StateStage.STAGE8_OPTIMIZE: "Stage 8: Script Consolidation and Optimization",
            StateStage.STAGE9_BROWSE: "Stage 9: Script Browser and Comparison",
            StateStage.STAGE10_PLAYGROUND: "Stage 10: Script Playground"
        }
        return stage_names.get(self, "Unknown Stage")

    @classmethod
    def from_number(cls, stage_number: int) -> 'StateStage':
        """Create StateStage from numeric stage number (1-10)."""
        stage_map = {
            1: cls.STAGE1_UPLOAD,
            2: cls.STAGE2_WEBSITE,
            3: cls.STAGE3_CONVERT,
            4: cls.STAGE4_DETECT,
            5: cls.STAGE5_DATA,
            6: cls.STAGE6_GENERATE,
            7: cls.STAGE7_EXECUTE,
            8: cls.STAGE8_OPTIMIZE,
            9: cls.STAGE9_BROWSE,
            10: cls.STAGE10_PLAYGROUND
        }
        if stage_number not in stage_map:
            raise ValueError(f"Invalid stage number: {stage_number}. Must be 1-10.")
        return stage_map[stage_number]

    @classmethod
    def from_string(cls, stage_string: str) -> 'StateStage':
        """
        Create StateStage from string representation.

        This method handles both enum values and display names to ensure
        robust stage recovery from various string formats.

        Args:
            stage_string: String representation of the stage

        Returns:
            StateStage: The corresponding enum instance

        Raises:
            ValueError: If the string doesn't match any known stage
        """
        # Handle direct enum value matches
        for stage in cls:
            if stage.value == stage_string:
                return stage

        # Handle display name matches (case-insensitive)
        stage_string_lower = stage_string.lower()
        display_name_map = {
            "stage 1: csv upload": cls.STAGE1_UPLOAD,
            "stage 2: website configuration": cls.STAGE2_WEBSITE,
            "stage 3: test case analysis and conversion": cls.STAGE3_CONVERT,
            "stage 4: ui element detection and step selection": cls.STAGE4_DETECT,
            "stage 5: manual data entry": cls.STAGE5_DATA,
            "stage 6: test script generation": cls.STAGE6_GENERATE,
            "stage 6b: test case-level script generation": cls.STAGE6B_GENERATE_TESTCASE,
            "stage 7: test script execution": cls.STAGE7_EXECUTE,
            "stage 8: script consolidation and optimization": cls.STAGE8_OPTIMIZE,
            "stage 9: script browser and comparison": cls.STAGE9_BROWSE,
            "stage 10: script playground": cls.STAGE10_PLAYGROUND
        }

        if stage_string_lower in display_name_map:
            return display_name_map[stage_string_lower]

        # Handle partial matches for common patterns
        if "stage1" in stage_string_lower or "upload" in stage_string_lower:
            return cls.STAGE1_UPLOAD
        elif "stage2" in stage_string_lower or "website" in stage_string_lower:
            return cls.STAGE2_WEBSITE
        elif "stage3" in stage_string_lower or "convert" in stage_string_lower:
            return cls.STAGE3_CONVERT
        elif "stage4" in stage_string_lower or "detect" in stage_string_lower:
            return cls.STAGE4_DETECT
        elif "stage5" in stage_string_lower or "data" in stage_string_lower:
            return cls.STAGE5_DATA
        elif "stage6" in stage_string_lower or "generate" in stage_string_lower:
            return cls.STAGE6_GENERATE
        elif "stage7" in stage_string_lower or "execute" in stage_string_lower:
            return cls.STAGE7_EXECUTE
        elif "stage8" in stage_string_lower or "optimize" in stage_string_lower:
            return cls.STAGE8_OPTIMIZE
        elif "stage9" in stage_string_lower or "browse" in stage_string_lower:
            return cls.STAGE9_BROWSE
        elif "stage10" in stage_string_lower or "playground" in stage_string_lower:
            return cls.STAGE10_PLAYGROUND

        raise ValueError(f"Unknown stage string: {stage_string}")

    @classmethod
    def safe_from_any(cls, stage_input) -> 'StateStage':
        """
        Safely create StateStage from any input type.

        This method provides robust stage recovery by handling:
        - StateStage enum instances (return as-is)
        - String representations (via from_string)
        - Numeric values (via from_number)
        - Invalid inputs (fallback to STAGE1_UPLOAD)

        Args:
            stage_input: Any input that might represent a stage

        Returns:
            StateStage: The corresponding enum instance or STAGE1_UPLOAD as fallback
        """
        # Already a StateStage enum
        if isinstance(stage_input, cls):
            return stage_input

        # String input
        if isinstance(stage_input, str):
            try:
                return cls.from_string(stage_input)
            except ValueError as e:
                debug(f"Failed to parse stage string, falling back to Stage 1",
                      stage="state_management", operation="stage_parsing",
                      context={'input': stage_input, 'error': str(e)})
                return cls.STAGE1_UPLOAD

        # Numeric input
        if isinstance(stage_input, (int, float)):
            try:
                return cls.from_number(int(stage_input))
            except ValueError as e:
                debug(f"Failed to parse stage number, falling back to Stage 1",
                      stage="state_management", operation="stage_parsing",
                      context={'input': stage_input, 'error': str(e)})
                return cls.STAGE1_UPLOAD

        # Unknown input type
        debug(f"Unknown stage input type, falling back to Stage 1",
              stage="state_management", operation="stage_parsing",
              context={'input_type': type(stage_input).__name__, 'input': str(stage_input)})
        return cls.STAGE1_UPLOAD

# ───── Configuration Constants ─────
# Default values extracted from StateManager initialization
DEFAULT_ELEMENT_TIMEOUT = 10  # Default timeout for element detection in seconds
DEFAULT_WEBSITE_URL = "https://example.com"  # Default placeholder website URL
DEFAULT_INITIAL_STAGE = StateStage.STAGE1_UPLOAD  # Initial stage for new state instances

# Counter and index defaults
DEFAULT_STEP_INDEX = 0  # Starting step index
DEFAULT_TOTAL_STEPS = 0  # Initial total steps count
DEFAULT_REGEN_ATTEMPTS = 0  # Initial regeneration attempts

# String defaults
EMPTY_STRING = ""  # Default empty string for various fields
EMPTY_MARKDOWN = ""  # Default empty markdown content

# Boolean defaults
DEFAULT_FALSE = False  # Default false value for boolean flags
DEFAULT_TRUE = True  # Default true value for boolean flags

@dataclass
class StateManager(HybridEditingMixin, DataPersistenceMixin, ScriptManagementMixin):
    """
    Centralized state manager for the ScriptWeaver application.

    This class stores all application state in a structured way, organized by logical categories.
    It uses Python's dataclass for clean definition and type hints for better IDE support.

    The state is organized into these categories:
    1. Core test-run metadata - Basic information about the test case and selected steps
    2. Step-progress counters - Track progress through multi-step test cases
    3. Per-step artifacts - Data generated for each step (scripts, analysis, etc.)
    4. Browser and element detection - UI elements and browser instances
    5. Flags - Boolean indicators for application flow control
    6. Usage tracking - Metrics for API usage and performance

    All state mutations should be performed directly on the StateManager instance
    retrieved via StateManager.get(st).
    """

    # ───── Centralized Stage Management ─────
    current_stage: StateStage = DEFAULT_INITIAL_STAGE  # Authoritative current stage (single source of truth)

    def __post_init__(self):
        """
        Post-initialization to ensure stage consistency.

        This method ensures that the current_stage is always a proper StateStage enum,
        even if it was loaded from session state as a string or other format.
        """
        # Ensure current_stage is always a proper StateStage enum
        if not isinstance(self.current_stage, StateStage):
            self.current_stage = StateStage.safe_from_any(self.current_stage)

    # ───── Core test-run metadata ─────
    uploaded_excel: Optional[str] = None  # Path to the uploaded Excel file
    uploaded_file: Optional[str] = None  # Alias for uploaded_excel (for backward compatibility)
    last_file_content_hash: Optional[int] = None  # Hash of the last processed file content
    test_cases: Optional[List[Dict[str, Any]]] = None  # All test cases from Excel
    selected_test_case: Optional[Dict[str, Any]] = None  # Currently selected test case
    original_test_case: Optional[Dict[str, Any]] = None  # Original unmodified test case
    google_api_key: Optional[str] = None  # Google AI API key
    selected_step: Optional[Dict[str, Any]] = None  # Currently selected test step
    selected_step_table_entry: Optional[Dict[str, Any]] = None  # Step in automation-ready format
    step_elements: List[Dict[str, Any]] = field(default_factory=list)  # UI elements for current step
    website_url: Optional[str] = None  # Target website URL

    # ───── Step-progress counters ─────
    current_step_index: int = DEFAULT_STEP_INDEX  # Index of current step in step_table_json
    total_steps: int = DEFAULT_TOTAL_STEPS  # Total number of steps in the test case
    all_steps_done: bool = DEFAULT_FALSE  # Flag indicating all steps are processed
    completed_steps: List[str] = field(default_factory=list)  # List of completed step numbers
    step_context: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # Context from previous steps

    # ───── Per-step artifacts ─────
    step_table_markdown: str = EMPTY_MARKDOWN  # Markdown representation of step table
    step_table_json: List[Dict[str, Any]] = field(default_factory=list)  # JSON representation of step table
    step_table_analysis: Optional[Dict[str, Any]] = None  # Analysis of step table

    # ───── Hybrid editing system for AI + manual steps ─────
    ai_generated_steps: Optional[List[Dict[str, Any]]] = None  # Original AI-generated steps (locked)
    manual_steps: List[Dict[str, Any]] = field(default_factory=list)  # User-added manual steps
    step_insertion_points: Dict[str, List[Dict[str, Any]]] = field(default_factory=dict)  # Manual steps by insertion point
    hybrid_editing_enabled: bool = DEFAULT_FALSE  # Flag to enable hybrid editing mode
    combined_step_table: Optional[List[Dict[str, Any]]] = None  # Final combined AI + manual steps
    completeness_validation: Optional[Dict[str, Any]] = None  # AI completeness validation results
    ui_elements: List[Dict[str, Any]] = field(default_factory=list)  # All UI elements
    element_matches: Dict[str, Any] = field(default_factory=dict)  # Matched elements for steps
    step_matches: Dict[str, Any] = field(default_factory=dict)  # Alias for element_matches
    test_data: Dict[str, Any] = field(default_factory=dict)  # Test data for current step
    manual_test_data: Dict[str, Any] = field(default_factory=dict)  # Manually entered test data
    test_data_skipped: bool = DEFAULT_FALSE  # Flag indicating test data was skipped
    llm_step_analysis: Dict[str, Any] = field(default_factory=dict)  # LLM analysis of current step
    test_data_analysis: Dict[str, Any] = field(default_factory=dict)  # Analysis of test data requirements
    locator_resolution_results: Dict[str, Any] = field(default_factory=dict)  # Locator resolution results per step
    generated_script_path: Optional[str] = None  # Path to generated test script
    last_script_content: str = EMPTY_STRING  # Content of last generated script
    last_script_file: str = EMPTY_STRING  # Path to last generated script file
    test_results: Optional[Dict[str, Any]] = None  # Results of test execution
    script_validation_results: Dict[str, Any] = field(default_factory=dict)  # Results from script validation
    validation_feedback_history: List[Dict[str, Any]] = field(default_factory=list)  # Historical validation feedback for learning
    script_regeneration_count: int = field(default=DEFAULT_REGEN_ATTEMPTS)  # Track regeneration attempts for feedback loop analysis (DEPRECATED - use per_step_regeneration_counts)
    regen_attempts: int = DEFAULT_REGEN_ATTEMPTS  # Track current regeneration attempts for limiting infinite loops
    per_step_regeneration_counts: Dict[str, int] = field(default_factory=dict)  # Track regeneration attempts per step (step_no -> count)

    # ───── Stage 8 optimization artifacts ─────
    optimized_script_path: Optional[str] = None  # Path to optimized script from Stage 8
    optimized_script_content: str = EMPTY_STRING  # Content of optimized script
    optimization_in_progress: bool = DEFAULT_FALSE  # Flag indicating optimization is in progress
    optimization_complete: bool = DEFAULT_FALSE  # Flag indicating optimization is complete
    optimization_start_time: Optional[datetime] = None  # Timestamp when optimization started
    optimization_chunks: List[Dict[str, Any]] = field(default_factory=list)  # Chunks for large script optimization
    optimized_script_test_results: Optional[Dict[str, Any]] = None  # Test results for optimized script
    combined_script_path: Optional[str] = None  # Path to combined script file

    # ───── Stage 6 regeneration and comment enhancement ─────
    user_generation_comment: str = EMPTY_STRING  # User's raw comment/feedback for script generation improvement
    ai_enhanced_generation_comment: str = EMPTY_STRING  # AI-enhanced version of user's generation comment
    generation_comment_enhancement_done: bool = DEFAULT_FALSE  # Flag indicating AI generation comment enhancement is complete
    use_enhanced_generation_comment: bool = DEFAULT_FALSE  # Flag indicating user chose to use AI-enhanced generation comment
    generation_custom_instructions: Optional[str] = None  # Custom instructions for script generation regeneration

    # ───── Stage 8 validation and comment enhancement ─────
    optimized_script_validation_results: Dict[str, Any] = field(default_factory=dict)  # Validation results for optimized script
    optimized_script_validation_done: bool = DEFAULT_FALSE  # Flag indicating optimized script validation is complete
    user_optimization_comment: str = EMPTY_STRING  # User's raw comment/feedback for script improvement
    ai_enhanced_comment: str = EMPTY_STRING  # AI-enhanced version of user's comment
    comment_enhancement_done: bool = DEFAULT_FALSE  # Flag indicating AI comment enhancement is complete
    use_enhanced_comment: bool = DEFAULT_FALSE  # Flag indicating user chose to use AI-enhanced comment
    regeneration_custom_instructions: Optional[str] = None  # Custom instructions for script regeneration

    # ───── Script continuity tracking ─────
    combined_script_content: Optional[str] = None  # Combined script content for all steps
    script_imports: List[str] = field(default_factory=list)  # List of import statements
    script_fixtures: List[str] = field(default_factory=list)  # List of fixture definitions
    script_variables: Dict[str, str] = field(default_factory=dict)  # Shared variables between steps
    script_functions: Dict[str, str] = field(default_factory=dict)  # Helper functions defined in scripts
    browser_initialized: bool = DEFAULT_FALSE  # Flag indicating browser has been initialized
    previous_scripts: Dict[str, str] = field(default_factory=dict)  # Dictionary of previous scripts by step number

    # ───── Manual script editing ─────
    script_manually_edited: bool = DEFAULT_FALSE  # Flag indicating if current script was manually edited
    original_ai_script_content: str = EMPTY_STRING  # Original AI-generated script content for revert functionality
    manual_edit_timestamp: Optional[datetime] = None  # Timestamp when manual edit was made
    script_edit_mode: bool = DEFAULT_FALSE  # Flag indicating if script editor is in edit mode

    # ───── Script history tracking (Stage 9) ─────
    script_history: List[Dict[str, Any]] = field(default_factory=list)  # Complete history of all generated scripts
    script_metadata: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # Metadata for each script version
    _script_storage: Any = field(default=None, init=False)  # Persistent storage instance

    # ───── Browser and element detection ─────
    test_browser: Any = None  # Browser instance for testing
    detected_elements: List[Dict[str, Any]] = field(default_factory=list)  # All detected UI elements
    qa_relevant_elements: List[Dict[str, Any]] = field(default_factory=list)  # Filtered QA-relevant elements
    element_timeout: int = DEFAULT_ELEMENT_TIMEOUT  # Default timeout for element detection in seconds

    # ───── Flags (use one per concept only) ─────
    conversion_done: bool = DEFAULT_FALSE  # Flag indicating test case conversion is complete
    converted_test_case_id: Optional[str] = None  # ID of the test case that was converted (for synchronization)
    step_ready_for_script: bool = DEFAULT_FALSE  # Flag indicating step is ready for script generation
    script_just_generated: bool = DEFAULT_FALSE  # Flag indicating script was just generated
    script_validation_done: bool = DEFAULT_FALSE  # Flag indicating script validation is complete
    script_validation_passed: bool = DEFAULT_FALSE  # Flag indicating script validation passed

    # ───── Error handling and workflow control ─────
    execution_error_occurred: bool = DEFAULT_FALSE  # Flag indicating script execution failed
    execution_error_acknowledged: bool = DEFAULT_FALSE  # Flag indicating user acknowledged the error
    execution_error_details: Dict[str, Any] = field(default_factory=dict)  # Error details for display

    # ───── Google AI Studio usage tracking ─────
    google_request_timestamps: List[datetime] = field(default_factory=list)  # Timestamps of API requests
    google_token_usage: List[Any] = field(default_factory=list)  # Token usage per request

    # ───── Helper methods ─────
    def init_in_session(self, st):
        """
        Initialize the state manager in the Streamlit session state.

        This method stores the StateManager instance in st.session_state["state"]
        if it doesn't already exist. This ensures we have a single source of truth
        for application state.

        Args:
            st: The Streamlit module instance
        """
        if "state" not in st.session_state:
            st.session_state["state"] = self  # persist
            # Initialize persistent storage
            self._init_script_storage()
        else:
            # Check if existing state needs upgrades
            existing_state = st.session_state["state"]
            # CRITICAL FIX: Add protection flag if missing
            if not hasattr(existing_state, '_session_state_protected'):
                existing_state._session_state_protected = True

            needs_upgrade = (
                not hasattr(existing_state, 'set_execution_error') or
                not hasattr(existing_state, 'current_stage') or
                not hasattr(existing_state, '_script_storage') or
                not hasattr(existing_state, 'clear_all_script_history') or
                not hasattr(existing_state, 'get_all_scripts_with_history') or
                not hasattr(existing_state, 'add_script_to_history') or
                not hasattr(existing_state, 'script_manually_edited') or

                not hasattr(existing_state, '_determine_stage_from_state')
            )
            if needs_upgrade:
                # Update existing state with new fields and methods
                self._upgrade_existing_state(existing_state)
                # Initialize persistent storage if not present
                if not hasattr(existing_state, '_script_storage') or existing_state._script_storage is None:
                    existing_state._init_script_storage()

    def _upgrade_existing_state(self, existing_state):
        """
        Upgrade an existing StateManager instance with new capabilities.

        Args:
            existing_state: The existing StateManager instance to upgrade
        """
        upgrade_fields = []

        # Add centralized stage management if it doesn't exist
        if not hasattr(existing_state, 'current_stage'):
            # Determine correct stage BEFORE setting current_stage
            # This prevents the race condition that causes reversion to Stage 1
            determined_stage = self._analyze_state_for_stage(existing_state)
            existing_state.current_stage = determined_stage
            debug("Added current_stage field to existing state",
                  stage="state_management", operation="state_upgrade",
                  context={'determined_stage': determined_stage.get_display_name()})
            upgrade_fields.append('current_stage')

        # Add new error handling fields if they don't exist
        if not hasattr(existing_state, 'execution_error_occurred'):
            existing_state.execution_error_occurred = DEFAULT_FALSE
            upgrade_fields.append('execution_error_occurred')

        if not hasattr(existing_state, 'execution_error_acknowledged'):
            existing_state.execution_error_acknowledged = DEFAULT_FALSE
            upgrade_fields.append('execution_error_acknowledged')

        if not hasattr(existing_state, 'execution_error_details'):
            existing_state.execution_error_details = {}
            upgrade_fields.append('execution_error_details')

        # Add new methods to existing state
        existing_state.set_execution_error = self.set_execution_error.__get__(existing_state, StateManager)
        existing_state.acknowledge_execution_error = self.acknowledge_execution_error.__get__(existing_state, StateManager)
        existing_state.clear_execution_error = self.clear_execution_error.__get__(existing_state, StateManager)

        # Add script history fields if they don't exist
        if not hasattr(existing_state, 'script_history'):
            existing_state.script_history = []
            upgrade_fields.append('script_history')

        if not hasattr(existing_state, 'script_metadata'):
            existing_state.script_metadata = {}
            upgrade_fields.append('script_metadata')

        # Add conversion tracking field if it doesn't exist
        if not hasattr(existing_state, 'converted_test_case_id'):
            existing_state.converted_test_case_id = None
            upgrade_fields.append('converted_test_case_id')

        # Add element timeout field if it doesn't exist
        if not hasattr(existing_state, 'element_timeout'):
            existing_state.element_timeout = DEFAULT_ELEMENT_TIMEOUT
            upgrade_fields.append('element_timeout')

        # Add hybrid editing fields if they don't exist
        if not hasattr(existing_state, 'ai_generated_steps'):
            existing_state.ai_generated_steps = None
            upgrade_fields.append('ai_generated_steps')

        if not hasattr(existing_state, 'manual_steps'):
            existing_state.manual_steps = []
            upgrade_fields.append('manual_steps')

        if not hasattr(existing_state, 'step_insertion_points'):
            existing_state.step_insertion_points = {}
            upgrade_fields.append('step_insertion_points')

        if not hasattr(existing_state, 'hybrid_editing_enabled'):
            existing_state.hybrid_editing_enabled = DEFAULT_FALSE
            upgrade_fields.append('hybrid_editing_enabled')

        if not hasattr(existing_state, 'combined_step_table'):
            existing_state.combined_step_table = None
            upgrade_fields.append('combined_step_table')

        # Add hybrid editing methods
        existing_state.enable_hybrid_editing = self.enable_hybrid_editing.__get__(existing_state, StateManager)
        existing_state.disable_hybrid_editing = self.disable_hybrid_editing.__get__(existing_state, StateManager)
        existing_state.add_manual_step = self.add_manual_step.__get__(existing_state, StateManager)
        existing_state.remove_manual_step = self.remove_manual_step.__get__(existing_state, StateManager)
        existing_state.get_combined_steps = self.get_combined_steps.__get__(existing_state, StateManager)
        existing_state.get_effective_step_table = self.get_effective_step_table.__get__(existing_state, StateManager)
        existing_state.get_effective_total_steps = self.get_effective_total_steps.__get__(existing_state, StateManager)
        existing_state.sync_step_table_with_combined = self.sync_step_table_with_combined.__get__(existing_state, StateManager)

        # Add the new stage determination method
        if not hasattr(existing_state, '_determine_stage_from_state'):
            existing_state._determine_stage_from_state = self._determine_stage_from_state.__get__(existing_state, StateManager)
            upgrade_fields.append('_determine_stage_from_state')

        # Log consolidated upgrade summary
        if upgrade_fields:
            debug("Successfully upgraded existing StateManager",
                  stage="state_management", operation="state_upgrade",
                  context={'upgraded_fields': upgrade_fields, 'field_count': len(upgrade_fields)})

    def _determine_stage_from_state(self, state_obj) -> StateStage:
        """
        Determine the correct stage based on existing state without causing reversion.

        This method analyzes the state object to determine the appropriate stage
        without triggering the problematic update_stage_based_on_completion logic.

        Args:
            state_obj: The state object to analyze

        Returns:
            StateStage: The determined stage
        """
        # Start with Stage 1 as baseline
        target_stage = DEFAULT_INITIAL_STAGE

        # Check Stage 1 completion (file uploaded)
        if (hasattr(state_obj, 'uploaded_excel') and state_obj.uploaded_excel) or \
           (hasattr(state_obj, 'uploaded_file') and state_obj.uploaded_file) or \
           (hasattr(state_obj, 'test_cases') and state_obj.test_cases):
            target_stage = StateStage.STAGE2_WEBSITE

            # Check Stage 2 completion (website URL configured)
            if hasattr(state_obj, 'website_url') and state_obj.website_url and \
               state_obj.website_url != DEFAULT_WEBSITE_URL:
                target_stage = StateStage.STAGE3_CONVERT

                # Check Stage 3 completion (test case selected and converted)
                if hasattr(state_obj, 'selected_test_case') and state_obj.selected_test_case and \
                   hasattr(state_obj, 'conversion_done') and state_obj.conversion_done and \
                   hasattr(state_obj, 'step_table_json') and state_obj.step_table_json:
                    target_stage = StateStage.STAGE4_DETECT

                    # Check Stage 4 completion (step selected and elements matched)
                    if hasattr(state_obj, 'selected_step') and state_obj.selected_step and \
                       (hasattr(state_obj, 'step_matches') and state_obj.step_matches or \
                        hasattr(state_obj, 'element_matches') and state_obj.element_matches):
                        target_stage = StateStage.STAGE5_DATA

                        # Check Stage 5 completion (test data configured or skipped)
                        if (hasattr(state_obj, 'test_data') and state_obj.test_data) or \
                           (hasattr(state_obj, 'test_data_skipped') and state_obj.test_data_skipped):
                            target_stage = StateStage.STAGE6_GENERATE

                            # Check Stage 6 completion (script generated)
                            if hasattr(state_obj, 'generated_script_path') and state_obj.generated_script_path:
                                target_stage = StateStage.STAGE7_EXECUTE

                                # Check Stage 7 completion (all steps done)
                                if hasattr(state_obj, 'all_steps_done') and state_obj.all_steps_done:
                                    target_stage = StateStage.STAGE8_OPTIMIZE

        return target_stage

    def _analyze_state_for_stage(self, state_obj) -> StateStage:
        """
        Analyze state object to determine appropriate stage (helper for upgrade logic).

        This is a direct implementation that doesn't rely on methods that might not exist yet.

        Args:
            state_obj: The state object to analyze

        Returns:
            StateStage: The determined stage
        """
        # Start with Stage 1 as baseline
        target_stage = DEFAULT_INITIAL_STAGE

        # Check Stage 1 completion (file uploaded)
        has_file = (hasattr(state_obj, 'uploaded_excel') and state_obj.uploaded_excel) or \
                   (hasattr(state_obj, 'uploaded_file') and state_obj.uploaded_file) or \
                   (hasattr(state_obj, 'test_cases') and state_obj.test_cases)

        if has_file:
            target_stage = StateStage.STAGE2_WEBSITE

            # Check Stage 2 completion (website URL configured)
            has_website = hasattr(state_obj, 'website_url') and state_obj.website_url and \
                         state_obj.website_url != DEFAULT_WEBSITE_URL

            if has_website:
                target_stage = StateStage.STAGE3_CONVERT

                # Check Stage 3 completion (test case selected and converted)
                has_conversion = hasattr(state_obj, 'selected_test_case') and state_obj.selected_test_case and \
                               hasattr(state_obj, 'conversion_done') and state_obj.conversion_done and \
                               hasattr(state_obj, 'step_table_json') and state_obj.step_table_json

                if has_conversion:
                    target_stage = StateStage.STAGE4_DETECT

                    # Check Stage 4 completion (step selected and elements matched)
                    has_step_selection = hasattr(state_obj, 'selected_step') and state_obj.selected_step and \
                                       (hasattr(state_obj, 'step_matches') and state_obj.step_matches or \
                                        hasattr(state_obj, 'element_matches') and state_obj.element_matches)

                    if has_step_selection:
                        target_stage = StateStage.STAGE5_DATA

                        # Check Stage 5 completion (test data configured or skipped)
                        has_test_data = (hasattr(state_obj, 'test_data') and state_obj.test_data) or \
                                       (hasattr(state_obj, 'test_data_skipped') and state_obj.test_data_skipped)

                        if has_test_data:
                            target_stage = StateStage.STAGE6_GENERATE

                            # Check Stage 6 completion (script generated)
                            has_script = hasattr(state_obj, 'generated_script_path') and state_obj.generated_script_path

                            if has_script:
                                # Check if this is a test case-level script (from Stage 6B)
                                # If combined_script_path exists and all_steps_done is True, use Stage 7B
                                is_testcase_level = (hasattr(state_obj, 'combined_script_path') and
                                                   state_obj.combined_script_path and
                                                   hasattr(state_obj, 'all_steps_done') and
                                                   state_obj.all_steps_done)

                                if is_testcase_level:
                                    target_stage = StateStage.STAGE7B_EXECUTE_TESTCASE
                                else:
                                    target_stage = StateStage.STAGE7_EXECUTE

                                # Check Stage 7/7B completion (all steps done)
                                all_done = hasattr(state_obj, 'all_steps_done') and state_obj.all_steps_done

                                if all_done:
                                    # CRITICAL FIX: If we're already in Stage 8 with optimization in progress,
                                    # don't override the current stage
                                    if (hasattr(state_obj, 'current_stage') and
                                        state_obj.current_stage == StateStage.STAGE8_OPTIMIZE and
                                        hasattr(state_obj, 'optimization_in_progress') and
                                        state_obj.optimization_in_progress):
                                        target_stage = StateStage.STAGE8_OPTIMIZE
                                    else:
                                        target_stage = StateStage.STAGE8_OPTIMIZE

        return target_stage

    @staticmethod
    def get(st) -> "StateManager":
        """
        Get the state manager from the Streamlit session state.

        This static method retrieves the StateManager instance from the
        Streamlit session state. It should be used by all components that
        need to access or modify application state.

        Args:
            st: The Streamlit module instance

        Returns:
            StateManager: The singleton StateManager instance
        """
        return st.session_state["state"]

    def update_step_progress(self, current_step_index=None, total_steps=None, all_steps_done=None,
                           step_ready_for_script=None, script_just_generated=None):
        """
        Update step progress counters with validation and logging.

        This method provides a centralized way to update step progress counters,
        ensuring that all updates are properly validated and logged.

        Args:
            current_step_index: New value for current_step_index
            total_steps: New value for total_steps
            all_steps_done: New value for all_steps_done
            step_ready_for_script: New value for step_ready_for_script
            script_just_generated: New value for script_just_generated

        Returns:
            bool: True if any state was updated, False otherwise
        """
        updated = False
        changes = {}

        if current_step_index is not None and current_step_index != self.current_step_index:
            # Validate the new step index
            if total_steps is not None:
                max_index = total_steps - 1
            else:
                max_index = self.total_steps - 1 if self.total_steps > 0 else 0

            if current_step_index < 0 or (max_index > 0 and current_step_index > max_index):
                debug(f"Invalid step index clamped to valid range",
                      stage="state_management", operation="validation_warning",
                      context={'requested': current_step_index, 'valid_range': f"0-{max_index}"})
                # Clamp to valid range
                current_step_index = max(0, min(current_step_index, max_index))

            if current_step_index != self.current_step_index:
                changes['current_step_index'] = {'old': self.current_step_index, 'new': current_step_index}
                self.current_step_index = current_step_index
                updated = True

        if total_steps is not None and total_steps != self.total_steps:
            changes['total_steps'] = {'old': self.total_steps, 'new': total_steps}
            self.total_steps = total_steps
            updated = True

        if all_steps_done is not None and all_steps_done != self.all_steps_done:
            changes['all_steps_done'] = {'old': self.all_steps_done, 'new': all_steps_done}
            self.all_steps_done = all_steps_done
            updated = True

        if step_ready_for_script is not None and step_ready_for_script != self.step_ready_for_script:
            changes['step_ready_for_script'] = {'old': self.step_ready_for_script, 'new': step_ready_for_script}
            self.step_ready_for_script = step_ready_for_script
            updated = True

        if script_just_generated is not None and script_just_generated != self.script_just_generated:
            changes['script_just_generated'] = {'old': self.script_just_generated, 'new': script_just_generated}
            self.script_just_generated = script_just_generated
            updated = True

        return updated

    def reset_step_state(self, confirm=False, reason=""):
        """
        Reset step-specific state variables.

        This method provides a centralized way to reset step-specific state variables,
        ensuring that all resets are properly validated, logged, and confirmed by the user.

        Args:
            confirm: Whether the reset has been confirmed by the user
            reason: The reason for the reset (for logging)

        Returns:
            bool: True if state was reset, False otherwise
        """
        if not confirm:
            debug(f"Attempted to reset step state without confirmation",
                  stage="state_management", operation="validation_warning",
                  context={'reason': reason})
            return False

        # Reset step-specific state variables
        self.step_elements = []
        self.step_matches = {}
        self.element_matches = {}
        self.test_data = {}
        self.manual_test_data = {}  # Reset manually entered test data
        self.test_data_skipped = False
        self.llm_step_analysis = {}
        self.step_ready_for_script = False
        self.script_just_generated = False
        self.generated_script_path = None

        # Reset validation state
        self.script_validation_done = False
        self.script_validation_passed = False
        self.script_validation_results = {}

        # Reset regeneration attempts counter
        self.regen_attempts = 0

        # Reset error state
        self.execution_error_occurred = False
        self.execution_error_acknowledged = False
        self.execution_error_details = {}

        return True

    def reset_test_case_state(self, confirm=False, reason="", preserve_step_progress=False):
        """
        Reset test case state variables.

        This method provides a centralized way to reset test case state variables,
        ensuring that all resets are properly validated, logged, and confirmed by the user.

        Args:
            confirm: Whether the reset has been confirmed by the user
            reason: The reason for the reset (for logging)
            preserve_step_progress: Whether to preserve completed_steps and step_context

        Returns:
            bool: True if state was reset, False otherwise
        """
        if not confirm:
            debug(f"Attempted to reset test case state without confirmation",
                  stage="state_management", operation="validation_warning",
                  context={'reason': reason})
            return False

        # Reset test case state variables
        self.selected_test_case = None
        self.original_test_case = None
        self.selected_step = None  # Clear selected step as part of test case reset
        self.selected_step_table_entry = None  # Clear step table entry as well
        self.step_table_markdown = ""
        self.step_table_json = []
        self.step_table_analysis = None
        self.conversion_done = False
        self.converted_test_case_id = None  # Clear conversion tracking

        # Also reset step state
        self.reset_step_state(confirm=True, reason=f"Part of test case reset: {reason}")

        # Reset step progression counters (conditionally preserve step progress)
        self.current_step_index = 0
        self.total_steps = 0
        self.all_steps_done = False

        # CRITICAL FIX: Only clear step progress if not preserving it
        if not preserve_step_progress:
            self.completed_steps = []
            self.step_context = {}

        # Reset script continuity tracking
        self.combined_script_content = None
        self.combined_script_path = None
        self.script_imports = []
        self.script_fixtures = []
        self.script_variables = {}
        self.script_functions = {}
        self.browser_initialized = False
        self.previous_scripts = {}

        # Reset manual script editing state
        self.script_manually_edited = False
        self.original_ai_script_content = ""
        self.manual_edit_timestamp = None
        self.script_edit_mode = False

        # Reset Stage 8 optimization state
        self.optimized_script_path = None
        self.optimized_script_content = ""
        self.optimization_in_progress = False
        self.optimization_complete = False
        self.optimization_start_time = None
        self.optimization_chunks = []
        self.optimized_script_test_results = None  # Test results for optimized script

        # Reset Stage 6 regeneration and comment enhancement state
        self.user_generation_comment = ""
        self.ai_enhanced_generation_comment = ""
        self.generation_comment_enhancement_done = False
        self.use_enhanced_generation_comment = False
        self.generation_custom_instructions = None
        self.script_regeneration_count = DEFAULT_REGEN_ATTEMPTS  # Reset script regeneration count for new test case
        self.per_step_regeneration_counts = {}  # Reset per-step regeneration counts for new test case

        # Reset Stage 8 validation and comment enhancement state
        self.optimized_script_validation_results = {}
        self.optimized_script_validation_done = False
        self.user_optimization_comment = ""
        self.ai_enhanced_comment = ""
        self.comment_enhancement_done = False
        self.use_enhanced_comment = False
        self.regeneration_custom_instructions = None

        # Reset current stage to Stage 3 (test case selection) using centralized method
        self.advance_to(StateStage.STAGE3_CONVERT, f"Test case reset: {reason}")

        return True

    def get_step_regeneration_count(self, step_no: str) -> int:
        """
        Get the regeneration count for a specific step.

        Args:
            step_no: The step number as a string

        Returns:
            int: The regeneration count for this step (0 if never regenerated)
        """
        return self.per_step_regeneration_counts.get(step_no, 0)

    def increment_step_regeneration_count(self, step_no: str) -> int:
        """
        Increment the regeneration count for a specific step.

        Args:
            step_no: The step number as a string

        Returns:
            int: The new regeneration count for this step
        """
        current_count = self.per_step_regeneration_counts.get(step_no, 0)
        new_count = current_count + 1
        self.per_step_regeneration_counts[step_no] = new_count

        debug(f"Incremented regeneration count for step {step_no}: {current_count} → {new_count}",
              stage="state_management", operation="step_regeneration_tracking",
              context={'step_no': step_no, 'old_count': current_count, 'new_count': new_count})

        return new_count

    def reset_step_regeneration_count(self, step_no: str):
        """
        Reset the regeneration count for a specific step to 0.

        Args:
            step_no: The step number as a string
        """
        if step_no in self.per_step_regeneration_counts:
            old_count = self.per_step_regeneration_counts[step_no]
            self.per_step_regeneration_counts[step_no] = 0
            debug(f"Reset regeneration count for step {step_no}: {old_count} → 0",
                  stage="state_management", operation="step_regeneration_reset",
                  context={'step_no': step_no, 'old_count': old_count})

    def get_effective_step_table(self) -> List[Dict[str, Any]]:
        """
        Get the effective step table from persistent JSON storage ONLY.

        This method enforces JSON-only data loading as the single source of truth.
        No fallbacks to in-memory state are allowed.

        Returns:
            List of steps from JSON storage

        Raises:
            ValueError: If no JSON data exists for the current test case
        """
        # Ensure we have a selected test case
        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            error_msg = "No test case selected - cannot load step data"
            debug(error_msg, stage="state_management", operation="critical_operation")
            raise ValueError(error_msg)

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            error_msg = "Selected test case has no ID - cannot load step data"
            debug(error_msg, stage="state_management", operation="critical_operation")
            raise ValueError(error_msg)

        # Load from persistent JSON storage (ONLY source of truth)
        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()
            json_data = storage.load_step_data(test_case_id)

            if json_data:
                step_data, metadata = json_data

                # Update in-memory state as working copy (but JSON remains authoritative)
                self.step_table_json = step_data.copy()
                self.total_steps = len(step_data)

                return step_data
            else:
                error_msg = f"No step data found in JSON storage for test case: {test_case_id}"
                debug(error_msg, stage="state_management", operation="critical_operation",
                      context={'test_case_id': test_case_id})
                raise ValueError(error_msg)

        except Exception as e:
            error_msg = f"Failed to load step data from JSON storage: {e}"
            debug(error_msg, stage="state_management", operation="error_handling",
                  context={'test_case_id': test_case_id, 'error': str(e)})
            raise ValueError(error_msg)

    def get_effective_total_steps(self) -> int:
        """
        Get the effective total step count, considering hybrid editing.

        Returns:
            Total number of steps to process
        """
        effective_steps = self.get_effective_step_table()
        return len(effective_steps) if effective_steps else 0

    def advance_to(self, new_stage: StateStage, reason: str = "") -> bool:
        """
        Centralized stage transition method with validation and comprehensive flag cleanup.

        This method serves as the single source of truth for stage transitions,
        replacing the fragile distributed boolean flag manipulation that causes phantom stage jumps.

        Args:
            new_stage: The target stage to transition to
            reason: The reason for the transition (for logging)

        Returns:
            bool: True if transition was successful, False if invalid
        """
        # CRITICAL FIX: File dependency validation before any stage transition
        # Special handling for quick navigation to Stage 3 - allow if user has some progress
        if new_stage == StateStage.STAGE3_CONVERT and reason.startswith("Quick nav"):
            # For quick navigation to Stage 3, be more permissive
            # Allow if user has test cases OR if they're coming from a valid stage
            has_test_cases = bool(getattr(self, 'test_cases', None))
            current_stage_num = self.current_stage.get_stage_number()

            if has_test_cases or current_stage_num >= 2:
                debug(f"Quick navigation to Stage 3 allowed with relaxed validation",
                      stage="navigation",
                      operation="quick_nav_validation",
                      context={
                          "current_stage": self.current_stage.get_display_name(),
                          "target_stage": new_stage.get_display_name(),
                          "has_test_cases": has_test_cases,
                          "current_stage_num": current_stage_num
                      })
            else:
                debug(f"Quick navigation to Stage 3 blocked: No test cases and early stage",
                      stage="navigation",
                      operation="validation_error",
                      context={
                          "current_stage": self.current_stage.get_display_name(),
                          "target_stage": new_stage.get_display_name(),
                          "reason": "No test cases available and user is in early stage"
                      })
                return False
        elif not self._validate_file_dependencies(new_stage):
            debug(f"Stage transition blocked: File dependency validation failed",
                  stage="navigation",
                  operation="validation_error",
                  context={
                      "current_stage": self.current_stage.get_display_name(),
                      "target_stage": new_stage.get_display_name(),
                      "reason": "Missing required Excel file"
                  })
            return False

        # Validate transition is legal
        current_stage_num = self.current_stage.get_stage_number()
        target_stage_num = new_stage.get_stage_number()

        # Stage 9 (Script Browser) is always accessible from any stage
        if target_stage_num == 9:
            # Update the authoritative current stage
            self.current_stage = new_stage
            return True

        # Define legal transitions (can go forward, or specific backward transitions)
        legal_backward_transitions = {
            # Stage 7 -> Stage 4 (after script execution, return to step selection)
            (7, 4): "Script execution completed, returning to step selection",
            # Stage 8 -> Stage 3 (after optimization, return to test case selection)
            (8, 3): "Script optimization completed, returning to test case selection",
            # Stage 9 -> Any stage (from script browser to any workflow stage)
            (9, 1): "Script browser navigation to Stage 1",
            (9, 2): "Script browser navigation to Stage 2",
            (9, 3): "Script browser navigation to Stage 3",
            (9, 4): "Script browser navigation to Stage 4",
            (9, 5): "Script browser navigation to Stage 5",
            (9, 6): "Script browser navigation to Stage 6",
            (9, 6.5): "Script browser navigation to Stage 6B",  # Stage 6B support
            (9, 7): "Script browser navigation to Stage 7",
            (9, 8): "Script browser navigation to Stage 8",
            # Stage 10 -> Any stage (from script playground to any workflow stage)
            (10, 1): "Script playground navigation to Stage 1",
            (10, 2): "Script playground navigation to Stage 2",
            (10, 3): "Script playground navigation to Stage 3",
            (10, 4): "Script playground navigation to Stage 4",
            (10, 5): "Script playground navigation to Stage 5",
            (10, 6): "Script playground navigation to Stage 6",
            (10, 7): "Script playground navigation to Stage 7",
            (10, 8): "Script playground navigation to Stage 8",
            (10, 9): "Script playground navigation to Stage 9",
            # Stage 4 -> Stage 4 (step navigation within Stage 4)
            (4, 4): "Step navigation within Stage 4",
            # Any stage -> Stage 3 (test case reset)
            (4, 3): "Test case reset from Stage 4",
            (5, 3): "Test case reset from Stage 5",
            (6, 3): "Test case reset from Stage 6",
            (7, 3): "Test case reset from Stage 7",
            (8, 3): "Test case reset from Stage 8",
            # Any stage -> Stage 9 (script browser access)
            (1, 9): "Access script browser from Stage 1",
            (2, 9): "Access script browser from Stage 2",
            (3, 9): "Access script browser from Stage 3",
            (4, 9): "Access script browser from Stage 4",
            (5, 9): "Access script browser from Stage 5",
            (6, 9): "Access script browser from Stage 6",
            (7, 9): "Access script browser from Stage 7",
            (8, 9): "Access script browser from Stage 8",
            # Any stage -> Stage 10 (script playground access)
            (1, 10): "Access script playground from Stage 1",
            (2, 10): "Access script playground from Stage 2",
            (3, 10): "Access script playground from Stage 3",
            (4, 10): "Access script playground from Stage 4",
            (5, 10): "Access script playground from Stage 5",
            (6, 10): "Access script playground from Stage 6",
            (7, 10): "Access script playground from Stage 7",
            (8, 10): "Access script playground from Stage 8",
            (9, 10): "Access script playground from Stage 9"
        }

        # Check if transition is legal
        is_forward = target_stage_num > current_stage_num
        is_same_stage = target_stage_num == current_stage_num
        is_legal_backward = (current_stage_num, target_stage_num) in legal_backward_transitions

        # Special case: Stage 3 can transition to Stage 6B (test case-level generation)
        is_stage3_to_6b = (current_stage_num == 3 and target_stage_num == 6.5)

        # Special case: Stage 6B can transition to Stage 7 (execution)
        is_stage6b_to_7 = (current_stage_num == 6.5 and target_stage_num == 7)

        if is_stage6b_to_7:
            debug("State Manager: Stage 6B → Stage 7 transition detected", stage="state_management",
                  operation="stage6b_to_7_transition",
                  context={
                      'current_stage': self.current_stage.get_display_name(),
                      'target_stage': new_stage.get_display_name(),
                      'reason': reason,
                      'generated_script_path': getattr(self, 'generated_script_path', None),
                      'all_steps_done': getattr(self, 'all_steps_done', None)
                  })

        # CRITICAL FIX: Enhanced complete reset logic with safety checks
        is_complete_reset = False
        if target_stage_num == 1:
            # CRITICAL FIX: Allow Stage 1 transitions based on progress analysis
            has_file = bool(getattr(self, 'uploaded_excel', None) or getattr(self, 'uploaded_file', None))
            has_test_cases = bool(getattr(self, 'test_cases', None))
            has_website = bool(getattr(self, 'website_url', None) and self.website_url != DEFAULT_WEBSITE_URL)
            has_selected_test_case = bool(getattr(self, 'selected_test_case', None))
            has_conversion = bool(getattr(self, 'conversion_done', None))

            # Allow Stage 1 if there's no significant progress
            if not (has_file or has_test_cases or has_website or has_selected_test_case or has_conversion):
                is_complete_reset = True

            # For stages 4+, require explicit confirmation due to high risk of data loss
            elif current_stage_num >= 4:
                debug("CRITICAL FIX: Blocking Stage 1 transition from advanced stage - user has significant progress",
                      stage="navigation",
                      operation="validation_warning",
                      context={"current_stage": current_stage_num, "has_file": has_file, "has_test_cases": has_test_cases,
                              "has_website": has_website, "has_selected_test_case": has_selected_test_case, "has_conversion": has_conversion})
                debug("Use force_reset_to_stage1() method if you want to clear progress",
                      stage="navigation",
                      operation="validation_warning",
                      context={"current_stage": current_stage_num})
                return False
            # For stages 2-3, allow with warning (less data loss risk)
            else:
                is_complete_reset = True


        if not (is_forward or is_same_stage or is_legal_backward or is_complete_reset or is_stage3_to_6b or is_stage6b_to_7):
            debug(f"Illegal stage transition: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}",
                  stage="navigation",
                  operation="validation_error",
                  context={"current_stage": current_stage_num, "target_stage": target_stage_num, "legal_transitions": f"forward to {current_stage_num + 1}-10, or specific backward transitions"})
            return False

        # Log the transition (removed verbose debug statements)

        # Store previous stage for cleanup logic
        previous_stage = self.current_stage

        # CRITICAL FIX: Record stage transition for monitoring
        try:
            from utils.stage_monitor import record_stage_transition
            record_stage_transition(previous_stage, new_stage, reason, self)
        except Exception as e:
            debug(f"Failed to record stage transition: {e}",
                  stage="navigation",
                  operation="monitoring_error",
                  context={"previous_stage": previous_stage.get_display_name(), "new_stage": new_stage.get_display_name(), "error": str(e)})

        # Update the authoritative current stage
        self.current_stage = new_stage

        debug("State Manager: Stage transition completed", stage="state_management",
              operation="stage_transition_completed",
              context={
                  'previous_stage': previous_stage.get_display_name(),
                  'new_stage': new_stage.get_display_name(),
                  'reason': reason,
                  'transition_type': 'stage6b_to_7' if is_stage6b_to_7 else 'normal'
              })

        # Perform stage-specific cleanup based on transition type
        self._cleanup_flags_for_stage_transition(previous_stage, new_stage, reason)

        return True

    def force_reset_to_stage1(self, reason: str = "Explicit user reset") -> bool:
        """
        Force a reset to Stage 1, bypassing all safety checks.

        This method should only be used when the user explicitly wants to reset
        the application and lose all progress.

        Args:
            reason: The reason for the forced reset

        Returns:
            bool: True if reset was successful
        """
        debug(f"FORCE RESET: Resetting to Stage 1 - {reason}",
              stage="navigation",
              operation="critical_operation",
              context={"operation_type": "force_reset", "reason": reason, "warning": "This will clear ALL user progress!"})

        # Store previous stage for cleanup
        previous_stage = self.current_stage

        # Record the transition
        try:
            from utils.stage_monitor import record_stage_transition
            record_stage_transition(previous_stage, StateStage.STAGE1_UPLOAD, f"FORCE RESET: {reason}", self)
        except Exception as e:
            debug(f"Failed to record stage transition: {e}",
                  stage="navigation",
                  operation="monitoring_error",
                  context={"previous_stage": previous_stage.get_display_name(), "new_stage": "STAGE1_UPLOAD", "error": str(e)})

        # Force update to Stage 1
        self.current_stage = StateStage.STAGE1_UPLOAD

        # Clear ALL progress
        self.uploaded_excel = None
        self.uploaded_file = None
        self.test_cases = None
        self.website_url = DEFAULT_WEBSITE_URL
        self.selected_test_case = None
        self.conversion_done = DEFAULT_FALSE
        self.step_table_json = []
        self.selected_step = None
        self.step_matches = {}
        self.element_matches = {}
        self.test_data = {}
        self.test_data_skipped = False
        self.generated_script_path = None
        self.all_steps_done = False

        # Perform cleanup
        self._cleanup_flags_for_stage_transition(previous_stage, StateStage.STAGE1_UPLOAD, reason)

        debug("FORCE RESET COMPLETE: All progress cleared",
              stage="navigation",
              operation="critical_operation",
              context={"operation_type": "force_reset_complete", "previous_stage": previous_stage.get_display_name()})
        return True

    def _validate_file_dependencies(self, target_stage: StateStage) -> bool:
        """
        Validate that required file dependencies exist for the target stage.

        This method ensures that Excel file dependencies are properly validated
        before allowing stage transitions, preventing workflow corruption.

        Args:
            target_stage: The stage we're trying to transition to

        Returns:
            bool: True if dependencies are satisfied, False otherwise
        """
        target_stage_num = target_stage.get_stage_number()

        # Stage 1 doesn't require file dependencies
        if target_stage_num == 1:
            return True

        # Stage 9 (Script Browser) and Stage 10 (Script Playground) are always accessible
        # regardless of file dependencies - they are independent utility tools
        if target_stage_num == 9 or target_stage_num == 10:
            return True

        # Stages 2-8 require a valid Excel file
        if target_stage_num >= 2 and target_stage_num <= 8:
            # CRITICAL FIX: Use the same logic as get_file_dependency_status
            # Prioritize test cases over physical file existence
            file_path = getattr(self, 'uploaded_excel', None) or getattr(self, 'uploaded_file', None)
            has_file_path = bool(file_path)
            file_exists = bool(file_path and os.path.exists(file_path))
            has_test_cases = bool(getattr(self, 'test_cases', None))

            # Dependencies are satisfied if we have test cases (data in memory)
            # OR if we have both file path and physical file existence
            dependencies_satisfied = has_test_cases or (has_file_path and file_exists)

            if not dependencies_satisfied:
                # Determine the specific reason for failure
                if not has_test_cases and not has_file_path:
                    debug("File dependency validation failed: No file path and no test cases",
                          stage="file_validation",
                          operation="dependency_check",
                          context={
                              "target_stage": target_stage.get_display_name(),
                              "uploaded_excel": getattr(self, 'uploaded_excel', None),
                              "uploaded_file": getattr(self, 'uploaded_file', None),
                              "has_test_cases": has_test_cases
                          })
                elif not has_test_cases and has_file_path and not file_exists:
                    debug("File dependency validation failed: File does not exist and no test cases",
                          stage="file_validation",
                          operation="dependency_check",
                          context={
                              "target_stage": target_stage.get_display_name(),
                              "file_path": file_path,
                              "file_exists": file_exists,
                              "has_test_cases": has_test_cases
                          })
                else:
                    debug("File dependency validation failed: Unknown reason",
                          stage="file_validation",
                          operation="dependency_check",
                          context={
                              "target_stage": target_stage.get_display_name(),
                              "file_path": file_path,
                              "has_file_path": has_file_path,
                              "file_exists": file_exists,
                              "has_test_cases": has_test_cases
                          })
                return False


        return True

    def remove_uploaded_file(self, reason: str = "User requested file removal") -> bool:
        """
        Remove uploaded Excel file and clean up related state.

        This method handles file removal with proper state cleanup and validation,
        ensuring workflow integrity is maintained.

        Args:
            reason: The reason for file removal (for logging)

        Returns:
            bool: True if removal was successful, False otherwise
        """
        file_path = getattr(self, 'uploaded_excel', None) or getattr(self, 'uploaded_file', None)

        # Remove physical file if it exists
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)

            except Exception as e:
                debug("Failed to remove physical file",
                      stage="file_management",
                      operation="file_removal_error",
                      context={"file_path": file_path, "error": str(e)})

        # Clear file-related state
        self.uploaded_excel = None
        self.uploaded_file = None
        self.test_cases = None
        self.last_file_content_hash = None

        # Clear dependent state that relies on file data
        self.selected_test_case = None
        self.original_test_case = None
        self.conversion_done = False
        self.step_table_json = []

        # Reset to Stage 1 since file dependency is gone
        previous_stage = self.current_stage
        self.current_stage = StateStage.STAGE1_UPLOAD

        # Perform comprehensive cleanup
        self._cleanup_flags_for_stage_transition(previous_stage, StateStage.STAGE1_UPLOAD, f"File removed: {reason}")

        return True

    def get_file_dependency_status(self) -> Dict[str, Any]:
        """
        Get comprehensive file dependency status for UI components.

        Returns:
            Dict containing file dependency status information
        """
        file_path = getattr(self, 'uploaded_excel', None) or getattr(self, 'uploaded_file', None)
        has_file_path = bool(file_path)
        file_exists = bool(file_path and os.path.exists(file_path))
        has_test_cases = bool(getattr(self, 'test_cases', None))

        # Use the improved counting logic from excel_parser
        if has_test_cases:
            from core.excel_parser import get_test_case_count
            test_cases_count = get_test_case_count(getattr(self, 'test_cases', []))
        else:
            test_cases_count = 0

        # CRITICAL FIX: Use the same logic as _validate_file_dependencies for consistency
        # Dependencies are satisfied if we have test cases (data in memory)
        # OR if we have both file path and physical file existence
        dependencies_satisfied = has_test_cases or (has_file_path and file_exists)

        # Debug logging for quick navigation troubleshooting
        debug("File dependency status calculated",
              stage="file_validation",
              operation="dependency_status_check",
              context={
                  "has_file_path": has_file_path,
                  "file_path": file_path,
                  "file_exists": file_exists,
                  "has_test_cases": has_test_cases,
                  "test_cases_count": test_cases_count,
                  "dependencies_satisfied": dependencies_satisfied
              })

        status = {
            "has_file_path": has_file_path,
            "file_path": file_path,
            "file_exists": file_exists,
            "has_test_cases": has_test_cases,
            "test_cases_count": test_cases_count,
            "dependencies_satisfied": dependencies_satisfied,
            "can_proceed_to_stage2": dependencies_satisfied,
            "error_message": None
        }

        # Determine specific error message if dependencies not satisfied
        if not status["dependencies_satisfied"]:
            if not has_test_cases:
                if not has_file_path:
                    status["error_message"] = "No Excel file uploaded. Please upload a test case file to continue."
                elif not file_exists:
                    status["error_message"] = f"Excel file not found at {file_path}. Please re-upload the file."
                else:
                    status["error_message"] = "No test cases found in uploaded file. Please check file format and content."
            # If we have test cases but no file path, that's actually OK (data in memory)
            # No error message needed in this case

        return status

    def _cleanup_flags_for_stage_transition(self, previous_stage: StateStage, target_stage: StateStage, reason: str):
        """
        Clean up boolean flags that could interfere with stage detection.

        This method ensures that stale flags from previous stages don't cause
        phantom stage jumps by systematically clearing flags that are no longer relevant.

        Args:
            previous_stage: The stage we're transitioning from
            target_stage: The stage we're transitioning to
            reason: The reason for the transition (for logging)
        """
        target_stage_num = target_stage.get_stage_number()
        cleared_flags = []

        # Clear flags that are ahead of the target stage to prevent phantom jumps
        if target_stage_num < 3:
            # Going back to Stage 1 or 2 - clear all test case related flags
            if self.conversion_done:
                self.conversion_done = False
                cleared_flags.append('conversion_done')

        if target_stage_num < 4:
            # Going back before Stage 4 - clear step selection flags
            if self.step_ready_for_script:
                self.step_ready_for_script = False
                cleared_flags.append('step_ready_for_script')

        if target_stage_num < 5:
            # Going back before Stage 5 - clear element matching flags
            if self.step_matches:
                self.step_matches = {}
                cleared_flags.append('step_matches')
            if self.element_matches:
                self.element_matches = {}
                cleared_flags.append('element_matches')

        if target_stage_num < 6:
            # Going back before Stage 6 - clear test data flags
            if self.test_data:
                self.test_data = {}
                cleared_flags.append('test_data')
            if self.test_data_skipped:
                self.test_data_skipped = False
                cleared_flags.append('test_data_skipped')

        if target_stage_num < 7:
            # Going back before Stage 7 - clear script generation flags
            if self.generated_script_path:
                self.generated_script_path = None
                cleared_flags.append('generated_script_path')
            if self.script_just_generated:
                self.script_just_generated = False
                cleared_flags.append('script_just_generated')
            if self.script_validation_done:
                self.script_validation_done = False
                cleared_flags.append('script_validation_done')
            if self.script_validation_passed:
                self.script_validation_passed = False
                cleared_flags.append('script_validation_passed')

        if target_stage_num < 8:
            # Going back before Stage 8 - clear optimization flags
            if self.all_steps_done:
                self.all_steps_done = False
                cleared_flags.append('all_steps_done')
            if self.optimization_in_progress:
                self.optimization_in_progress = False
                cleared_flags.append('optimization_in_progress')
            if self.optimization_complete:
                self.optimization_complete = False
                cleared_flags.append('optimization_complete')

    def update_step_url_tracking(self, step_no: str, url_data: Dict[str, Any]) -> bool:
        """
        Update URL tracking information for a specific step.

        Args:
            step_no: Step number to update
            url_data: Dictionary containing URL tracking information

        Returns:
            bool: True if updated successfully, False otherwise
        """
        if not self.selected_test_case:
            return False

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()
            test_case_id = self.selected_test_case.get('Test Case ID', '')

            success = storage.update_step_url_tracking(test_case_id, step_no, url_data)

            return success

        except Exception as e:
            debug("Failed to update URL tracking for step",
                  stage="state_management", operation="error_handling",
                  context={'step_no': step_no, 'error': str(e)})
            return False

    def get_step_url_history(self, step_no: str = None) -> Dict[str, Any]:
        """
        Get URL history for a specific step or all steps.

        Args:
            step_no: Optional step number to get history for (if None, returns all steps)

        Returns:
            Dict containing URL history information
        """
        if not self.selected_test_case:
            return {}

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()
            test_case_id = self.selected_test_case.get('Test Case ID', '')

            url_history = storage.get_step_url_history(test_case_id, step_no)

            return url_history

        except Exception as e:
            debug("Failed to get URL history",
                  stage="state_management", operation="error_handling",
                  context={'step_no': step_no, 'error': str(e)})
            return {}

    def get_current_step_url(self) -> Optional[str]:
        """
        Get the current URL for the currently selected step.

        Returns:
            Optional[str]: Current URL for the step, or None if not available
        """
        if not self.selected_step_table_entry:
            return None

        step_no = self.selected_step_table_entry.get('step_no', '')
        url_history = self.get_step_url_history(step_no)

        if step_no in url_history:
            return url_history[step_no].get('current_url')
        return None

    def can_access_stage(self, target_stage: StateStage) -> bool:
        """
        Check if a stage can be accessed based on current state and prerequisites.

        Args:
            target_stage: The stage to check access for

        Returns:
            bool: True if stage is accessible, False otherwise
        """
        try:
            # Import navigation helpers to avoid circular imports
            from core.navigation_helpers import get_stage_accessibility

            accessibility_info = get_stage_accessibility(self, target_stage)
            return accessibility_info.get('accessible', False)

        except Exception as e:
            debug(f"Error checking stage accessibility: {e}",
                  stage="navigation",
                  operation="validation_error",
                  context={"target_stage": target_stage.get_display_name(), "error": str(e)})
            return False

    def get_stage_prerequisites(self, target_stage: StateStage) -> List[str]:
        """
        Get the list of missing prerequisites for a stage.

        Args:
            target_stage: The stage to check prerequisites for

        Returns:
            List of missing prerequisite descriptions
        """
        try:
            # Import navigation helpers to avoid circular imports
            from core.navigation_helpers import get_stage_accessibility

            accessibility_info = get_stage_accessibility(self, target_stage)
            return accessibility_info.get('missing_prerequisites', [])

        except Exception as e:
            debug(f"Error getting stage prerequisites: {e}",
                  stage="navigation",
                  operation="validation_error",
                  context={"target_stage": target_stage.get_display_name(), "error": str(e)})
            return ['Error checking prerequisites']

    def set_execution_error(self, error_details: Dict[str, Any]):
        """
        Set execution error state with detailed error information.

        Args:
            error_details: Dictionary containing error information including:
                - error_message: Main error message
                - traceback: Full traceback if available
                - returncode: Exit code from script execution
                - stdout: Standard output from execution
                - stderr: Standard error from execution
                - timestamp: When the error occurred
                - step_no: Which step failed
        """
        self.execution_error_occurred = True
        self.execution_error_acknowledged = False
        self.execution_error_details = error_details.copy()

        debug(f"State change: execution_error_occurred = True for step {error_details.get('step_no', 'Unknown')}",
              stage="error_handling",
              operation="error_occurred",
              context={"step_no": error_details.get('step_no', 'Unknown'), "error_message": error_details.get('error_message', 'No message')})

    def acknowledge_execution_error(self):
        """
        Mark the current execution error as acknowledged by the user.
        """
        if self.execution_error_occurred and not self.execution_error_acknowledged:
            self.execution_error_acknowledged = True
            debug("State change: execution_error_acknowledged = True",
                  stage="error_handling",
                  operation="state_change",
                  context={"execution_error_acknowledged": True})
            return True
        return False

    def clear_execution_error(self):
        """
        Clear execution error state after user acknowledgment.
        """
        if self.execution_error_occurred:
            debug("State change: clearing execution error state",
                  stage="error_handling",
                  operation="state_change",
                  context={"execution_error_occurred": False, "execution_error_acknowledged": False})
            self.execution_error_occurred = False
            self.execution_error_acknowledged = False
            self.execution_error_details = {}
            return True
        return False