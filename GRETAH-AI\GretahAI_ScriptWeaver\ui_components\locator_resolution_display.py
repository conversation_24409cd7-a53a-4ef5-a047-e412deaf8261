"""
Locator Resolution Display Components for GretahAI ScriptWeaver

This module provides UI components for displaying locator resolution results
in Stage 4, giving users visibility into the element identification strategy
that will be used in test script generation.

Key Features:
- Display resolved locator strategy and value
- Show element attributes captured during selection
- Display fallback locators and alternatives
- Provide confidence scoring and resolution reasoning
- Professional styling with clear visual hierarchy
"""

import streamlit as st
from typing import Dict, List, Any, Optional
from debug_utils import debug


def render_locator_resolution_display(
    resolution_result: Dict[str, Any],
    element_data: Dict[str, Any],
    step_info: Dict[str, Any] = None,
    use_expanders: bool = True
) -> None:
    """
    Render the locator resolution display section.

    Args:
        resolution_result: Result from resolve_locator_conflicts()
        element_data: Selected element data with attributes
        step_info: Optional step information for context
        use_expanders: Whether to use expanders (False for nested display)
    """
    debug("Rendering locator resolution display",
          stage="stage4", operation="ui_display",
          context={
              'has_resolution': bool(resolution_result),
              'has_element_data': bool(element_data),
              'resolved_strategy': resolution_result.get('resolved_locator_strategy', 'unknown')
          })

    # Add custom CSS for better locator display
    st.markdown("""
    <style>
    .locator-display {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.85em;
        line-height: 1.4;
        word-break: break-all;
        background-color: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
        border-left: 3px solid #007acc;
    }
    .metric-container {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 6px;
        margin: 5px 0;
    }
    </style>
    """, unsafe_allow_html=True)

    st.markdown("---")
    st.markdown("### 🎯 Element Identification Strategy")
    st.markdown("*This shows how the selected element will be identified in the generated test script*")
    
    # Create main display columns - give more space to locator information
    col1, col2 = st.columns([3, 1])

    with col1:
        _render_primary_locator_info(resolution_result, element_data)

    with col2:
        _render_confidence_and_status(resolution_result)
    
    # Additional details in expandable sections or containers
    if use_expanders:
        with st.expander("📋 Element Attributes", expanded=False):
            _render_element_attributes(element_data)

        with st.expander("🔄 Alternative Locators", expanded=False):
            _render_alternative_locators(resolution_result, element_data)

        with st.expander("🔍 Resolution Details", expanded=False):
            _render_resolution_details(resolution_result, step_info)
    else:
        # Nested mode - use simple containers with headers
        st.markdown("---")

        # Element Attributes section
        st.markdown("#### 📋 Element Attributes")
        with st.container():
            _render_element_attributes(element_data)

        st.markdown("---")

        # Alternative Locators section
        st.markdown("#### 🔄 Alternative Locators")
        with st.container():
            _render_alternative_locators(resolution_result, element_data)

        st.markdown("---")

        # Resolution Details section
        st.markdown("#### 🔍 Resolution Details")
        with st.container():
            _render_resolution_details(resolution_result, step_info)



def _render_primary_locator_info(resolution_result: Dict[str, Any], element_data: Dict[str, Any]) -> None:
    """Render the primary locator strategy and value."""
    strategy = resolution_result.get('resolved_locator_strategy', 'unknown')
    locator = resolution_result.get('resolved_locator', 'N/A')

    # Strategy display with icon
    strategy_icons = {
        'id': '🆔',
        'css': '🎨',
        'xpath': '🗂️',
        'name': '📛',
        'class': '🏷️',
        'tag': '🏗️',
        'text': '📝',
        'url': '🌐'
    }

    icon = strategy_icons.get(strategy.lower(), '🔍')

    st.markdown(f"**Primary Locator Strategy:** {icon} `{strategy.upper()}`")

    # Verify and display what will actually be used in generated code
    actual_locator = _get_actual_code_generation_locator(resolution_result, element_data)

    # Show resolved locator with improved display for long locators
    if locator and locator != 'N/A':
        _render_primary_locator_value(locator, strategy, "primary_display")

        # Show verification if different from what will be used in code
        if actual_locator != locator:
            st.warning(f"⚠️ **Code Generation Note**: The actual locator used in generated code will be: `{actual_locator}`")
            st.info("This difference occurs when enhanced locators with ancestor IDs are available for better reliability.")
    else:
        st.markdown("*No specific locator value*")

    # Element name for context
    element_name = element_data.get('name', 'Unknown Element')
    st.markdown(f"**Element:** {element_name}")

    # Show enhanced locator information if available
    enhanced_css = element_data.get('enhanced_css_selector', '')
    enhanced_xpath = element_data.get('enhanced_xpath', '')

    if enhanced_css and enhanced_css != element_data.get('selector', ''):
        st.info(f"🚀 **Enhanced CSS Available**: `{enhanced_css}` (uses ancestor IDs for better reliability)")

    if enhanced_xpath and enhanced_xpath != element_data.get('xpath', ''):
        st.info(f"🚀 **Enhanced XPath Available**: `{enhanced_xpath}` (uses ancestor IDs for better reliability)")


def _render_primary_locator_value(locator: str, strategy: str, context: str = "primary") -> None:
    """
    Render the primary locator value with appropriate formatting and handling for long locators.

    Args:
        locator: The locator string to display
        strategy: The locator strategy (css, xpath, etc.)
        context: Context identifier to ensure unique element keys
    """
    locator_str = str(locator)

    # Determine language for syntax highlighting
    if strategy.lower() == 'xpath':
        language = 'xpath'
    elif strategy.lower() in ['css', 'css_selector', 'id', 'class']:
        language = 'css'
    else:
        language = None

    # Handle different locator lengths
    if len(locator_str) <= 100:
        # Short to medium locators - display normally
        st.code(locator_str, language=language)
    else:
        # Long locators - use button-controlled display to avoid nested expanders
        # Show truncated version first
        truncated = locator_str[:80] + "..."
        st.code(truncated, language=language)

        # Create unique key for this locator to avoid session state conflicts
        import hashlib
        locator_hash = hashlib.md5(locator_str.encode()).hexdigest()[:8]
        show_full_key = f"show_full_locator_{context}_{locator_hash}"

        # Initialize session state for this locator if not exists
        if show_full_key not in st.session_state:
            st.session_state[show_full_key] = False

        # Toggle button for showing/hiding full locator
        col1, col2 = st.columns([1, 3])
        with col1:
            if st.button("🔍 Toggle Full Locator", key=f"btn_{show_full_key}",
                        help="Click to show/hide the complete locator"):
                st.session_state[show_full_key] = not st.session_state[show_full_key]

        # Show full locator if toggled on
        if st.session_state[show_full_key]:
            with st.container():
                st.markdown("**Full Locator:**")
                st.code(locator_str, language=language)

                # Add helpful information in a container
                st.markdown(f"""
                <div style="font-size: 0.85em; color: #666; margin-top: 10px;
                           padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                    📏 <strong>Length:</strong> {len(locator_str)} characters<br>
                    🎯 <strong>Strategy:</strong> {strategy.upper()}<br>
                    💡 <strong>Tip:</strong> Long locators often indicate complex DOM structures.
                    Enhanced locators with ancestor IDs provide better reliability.
                </div>
                """, unsafe_allow_html=True)


def _render_confidence_and_status(resolution_result: Dict[str, Any]) -> None:
    """Render confidence score and resolution status."""
    confidence = resolution_result.get('confidence_score', 0.0)
    conflict_detected = resolution_result.get('conflict_detected', False)
    
    # Confidence score with color coding
    st.markdown("**Confidence Score:**")
    
    # Color based on confidence level
    if confidence >= 0.8:
        color = "green"
        status = "High"
    elif confidence >= 0.6:
        color = "orange"
        status = "Medium"
    else:
        color = "red"
        status = "Low"
    
    st.progress(confidence)
    st.markdown(f"<span style='color: {color}'>{status} ({confidence:.1%})</span>", 
                unsafe_allow_html=True)
    
    # Conflict status
    if conflict_detected:
        st.warning("⚠️ Conflicts resolved")
    else:
        st.success("✅ No conflicts")


def _render_element_attributes(element_data: Dict[str, Any]) -> None:
    """Render captured element attributes."""
    attributes = element_data.get('attributes', {})

    if not attributes:
        st.info("No attributes captured")
        return

    # Group attributes by importance
    primary_attrs = ['id', 'name', 'class', 'type', 'tag']
    content_attrs = ['text', 'value', 'placeholder']
    position_attrs = ['x', 'y', 'width', 'height']
    other_attrs = [k for k in attributes.keys()
                   if k not in primary_attrs + content_attrs + position_attrs]

    # Display primary attributes
    if any(attributes.get(attr) for attr in primary_attrs):
        st.markdown("**Primary Attributes:**")
        for attr in primary_attrs:
            value = attributes.get(attr)
            if value:
                st.markdown(f"- **{attr}:** `{value}`")

    # Display viewport status if available
    viewport_status = element_data.get('viewport_status', {})
    if viewport_status:
        st.markdown("**👁️ Viewport Status:**")
        visibility_icon = "✅" if viewport_status.get('is_visible', False) else "❌"
        visibility_pct = viewport_status.get('visibility_percentage', 0)
        st.markdown(f"- **Visible:** {visibility_icon} {visibility_pct}%")

        if viewport_status.get('requires_scroll', False):
            scroll_direction = viewport_status.get('scroll_direction', 'unknown')
            scroll_distance = viewport_status.get('scroll_distance', 0)
            scroll_icon = {'up': '⬆️', 'down': '⬇️', 'left': '⬅️', 'right': '➡️', 'center': '🎯'}.get(scroll_direction, '📍')
            st.markdown(f"- **Scroll Required:** {scroll_icon} {scroll_direction}")
            if scroll_distance > 0:
                st.markdown(f"- **Scroll Distance:** {scroll_distance}px")
        else:
            st.markdown("- **Scroll Required:** ✅ No")

    # Display interaction requirements if available
    interaction_req = element_data.get('interaction_requirements', {})
    if interaction_req:
        st.markdown("**🖱️ Interaction Requirements:**")
        if interaction_req.get('requires_hover', False):
            st.markdown("- **Hover Required:** ⚠️ Yes")
            hover_target = interaction_req.get('hover_target_selector')
            if hover_target:
                st.markdown(f"- **Hover Target:** `{hover_target}`")
            hover_chain = interaction_req.get('hover_parent_chain', [])
            if hover_chain:
                chain_text = " → ".join([f"{p.get('tagName', 'unknown')}" + (f"#{p.get('id')}" if p.get('id') else "") for p in hover_chain])
                st.markdown(f"- **Hover Chain:** {chain_text}")
        else:
            st.markdown("- **Hover Required:** ✅ No")
    
    # Display content attributes
    content_values = [attributes.get(attr) for attr in content_attrs if attributes.get(attr)]
    if content_values:
        st.markdown("**Content:**")
        for attr in content_attrs:
            value = attributes.get(attr)
            if value:
                # Truncate long text
                display_value = value[:50] + "..." if len(str(value)) > 50 else value
                st.markdown(f"- **{attr}:** `{display_value}`")
    
    # Display position (if available)
    if any(attributes.get(attr) for attr in position_attrs):
        st.markdown("**Position:**")
        x, y = attributes.get('x', 0), attributes.get('y', 0)
        w, h = attributes.get('width', 0), attributes.get('height', 0)
        st.markdown(f"- **Location:** ({x}, {y})")
        st.markdown(f"- **Size:** {w} × {h}")
    
    # Display other attributes
    if other_attrs:
        st.markdown("**Other Attributes:**")
        for attr in other_attrs:
            value = attributes.get(attr)
            if value:
                st.markdown(f"- **{attr}:** `{value}`")


def _render_alternative_locators(resolution_result: Dict[str, Any], element_data: Dict[str, Any]) -> None:
    """Render alternative locator strategies."""
    original_matches = resolution_result.get('original_element_matches', [])
    
    if not original_matches:
        st.info("No alternative locators available")
        return
    
    st.markdown("**Available Alternatives:**")
    
    # Extract unique locator strategies from element matches
    alternatives = []
    for match in original_matches:
        element = match.get('element', {})
        
        # Check for different locator types
        attrs = element.get('attributes', {})
        
        if attrs.get('id'):
            alternatives.append(('ID', attrs['id'], 'High'))
        if element.get('selector'):
            alternatives.append(('CSS Selector', element['selector'], 'Medium'))
        if element.get('xpath'):
            alternatives.append(('XPath', element['xpath'], 'Medium'))
        if attrs.get('name'):
            alternatives.append(('Name', attrs['name'], 'Medium'))
        if attrs.get('class'):
            alternatives.append(('Class', attrs['class'], 'Low'))
    
    # Remove duplicates and display with improved formatting for long locators
    seen = set()
    for strategy, locator, reliability in alternatives:
        key = (strategy, locator)
        if key not in seen:
            seen.add(key)

            # Reliability indicator
            reliability_color = {
                'High': 'green',
                'Medium': 'orange',
                'Low': 'red'
            }.get(reliability, 'gray')

            # Handle long locators in alternatives
            locator_str = str(locator)
            if len(locator_str) <= 60:
                # Short locator - display inline
                st.markdown(f"- **{strategy}:** `{locator_str}` "
                           f"<span style='color: {reliability_color}'>({reliability})</span>",
                           unsafe_allow_html=True)
            else:
                # Long locator - display on separate line with truncation
                truncated = locator_str[:57] + "..."
                st.markdown(f"- **{strategy}:** <span style='color: {reliability_color}'>({reliability})</span>",
                           unsafe_allow_html=True)
                st.markdown(f"  `{truncated}` <small style='color: #888;'>(hover for full)</small>",
                           help=f"Full {strategy} locator: {locator_str}",
                           unsafe_allow_html=True)
    
    if not alternatives:
        st.info("No alternative locators detected")


def _render_resolution_details(resolution_result: Dict[str, Any], step_info: Dict[str, Any] = None) -> None:
    """Render detailed resolution information."""
    reason = resolution_result.get('resolution_reason', 'No reason provided')
    original_step_locator = resolution_result.get('original_step_locator', {})
    
    st.markdown("**Resolution Reasoning:**")
    st.markdown(f"*{reason}*")
    
    # Original step locator (if different)
    if original_step_locator:
        orig_strategy = original_step_locator.get('strategy', '')
        orig_locator = original_step_locator.get('locator', '')
        
        if orig_strategy and orig_strategy.lower() not in ['', 'none', 'n/a']:
            st.markdown("**Original Step Locator:**")
            st.markdown(f"- **Strategy:** `{orig_strategy}`")
            if orig_locator:
                st.markdown(f"- **Value:** `{orig_locator}`")
    
    # Step context (if provided)
    if step_info:
        st.markdown("**Step Context:**")
        step_no = step_info.get('step_no', 'Unknown')
        action = step_info.get('action', 'Unknown')
        st.markdown(f"- **Step:** {step_no}")
        st.markdown(f"- **Action:** {action}")
    
    # Technical details
    st.markdown("**Technical Details:**")
    st.json(resolution_result)


def _get_actual_code_generation_locator(resolution_result: Dict[str, Any], element_data: Dict[str, Any]) -> str:
    """
    Determine what locator will actually be used in code generation.

    This mirrors the logic in prompt_builder.py to ensure UI display matches generated code.

    Args:
        resolution_result: Result from resolve_locator_conflicts()
        element_data: Selected element data

    Returns:
        str: The actual locator that will be used in generated test code
    """
    resolved_locator = resolution_result.get('resolved_locator', '')
    resolved_strategy = resolution_result.get('resolved_locator_strategy', '').lower()

    # Get enhanced locators from element data
    enhanced_css = element_data.get('enhanced_css_selector', '')
    enhanced_xpath = element_data.get('enhanced_xpath', '')
    standard_css = element_data.get('selector', '')

    # Apply the same logic as prompt_builder.py
    if resolved_locator and resolved_locator != standard_css:
        if resolved_strategy in ['css', 'id', 'class']:
            # For CSS-based strategies, use resolved locator as enhanced CSS
            if resolved_strategy == 'id' and not resolved_locator.startswith('#'):
                return f"#{resolved_locator}"
            else:
                return resolved_locator
        elif resolved_strategy == 'xpath':
            return resolved_locator

    # Check if enhanced locators will be used (they take priority in code generation)
    if enhanced_css and enhanced_css != standard_css:
        # Enhanced CSS will be used if it contains ancestor IDs
        if '#' in enhanced_css and ' ' in enhanced_css:
            return enhanced_css

    if enhanced_xpath and enhanced_xpath != element_data.get('xpath', ''):
        # Enhanced XPath will be used if it contains ancestor IDs
        if '@id=' in enhanced_xpath and '//' in enhanced_xpath:
            return enhanced_xpath

    # Fallback to resolved locator
    return resolved_locator


def render_locator_resolution_summary(resolution_result: Dict[str, Any], context: str = "default") -> None:
    """
    Render a compact summary of the locator resolution for quick reference.

    Args:
        resolution_result: Result from resolve_locator_conflicts()
        context: Context identifier to ensure unique element keys (e.g., "stage4", "stage6")
    """
    if not resolution_result:
        return

    strategy = resolution_result.get('resolved_locator_strategy', 'unknown')
    locator = resolution_result.get('resolved_locator', 'N/A')
    confidence = resolution_result.get('confidence_score', 0.0)

    # Improved layout: more space for locator value, smaller confidence column
    col1, col2, col3 = st.columns([1.5, 3.5, 1])

    with col1:
        st.metric("Strategy", strategy.upper())

    with col2:
        # Enhanced locator display with expandable functionality
        _render_expandable_locator_metric(locator, context)

    with col3:
        st.metric("Confidence", f"{confidence:.1%}")


def _render_expandable_locator_metric(locator: str, context: str = "default") -> None:
    """
    Render locator value with expandable functionality for long locators.

    Args:
        locator: The locator string to display
        context: Context identifier to ensure unique element keys
    """
    if not locator or locator == 'N/A':
        st.metric("Locator Value", "N/A")
        return

    locator_str = str(locator)

    # Determine display strategy based on locator length
    if len(locator_str) <= 40:
        # Short locator - display normally
        st.metric("Locator Value", locator_str)
    elif len(locator_str) <= 80:
        # Medium locator - show with tooltip
        truncated = locator_str[:37] + "..."
        st.metric("Locator Value", truncated, help=f"Full locator: {locator_str}")
    else:
        # Long locator - use expandable display
        truncated = locator_str[:37] + "..."

        # Create a unique key for this locator to avoid conflicts
        import hashlib
        locator_hash = hashlib.md5(locator_str.encode()).hexdigest()[:8]
        expand_key = f"expand_locator_{context}_{locator_hash}"

        # Display metric with truncated value
        st.metric("Locator Value", truncated)

        # Add expandable section below the metric
        if st.button("📋 Show Full Locator", key=expand_key, help="Click to see the complete locator"):
            st.code(locator_str, language='css' if not locator_str.startswith('//') else 'xpath')

            # Add copy-to-clipboard functionality
            st.markdown(f"""
            <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                💡 <strong>Tip:</strong> You can select and copy the locator above
            </div>
            """, unsafe_allow_html=True)
