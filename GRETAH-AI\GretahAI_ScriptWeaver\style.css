/*
 * ==========================================================================
 * STYLE SHEET: style.css for GretahAI ScriptWeaver
 * ==========================================================================
 *
 * Description:
 * This CSS file defines the visual styling for the GretahAI ScriptWeaver application.
 * It includes definitions for color themes, typography, layout elements,
 * animations, and specific component styles.
 *
 * Structure:
 * 1.  `:root`        - Defines global CSS variables for colors.
 * 2.  Light Mode     - Styles applied when the 'light-mode' class is active.
 * 3.  Dark Mode      - Styles applied when the 'dark-mode' class is active.
 * 4.  Headers        - Styles for main and sub-headers, including animations.
 * 5.  Panels         - Styles for different types of informational panels.
 * 6.  Cards          - Styles for professional card layout.
 * 7.  Buttons        - Base styles and hover effects for Streamlit buttons.
 * 8.  Sidebar        - Styles and animations for the Streamlit sidebar.
 * 9.  Status         - Styles for visual status indicators.
 * 10. Action Buttons - Basic styling for action buttons.
 * 11. Logo           - Styles for the logo container and text.
 * 12. Footer         - Styles for the application footer.
 * 13. Animations     - Keyframe definitions for animations.
 *
 * ==========================================================================
 */

:root {
    /* ScriptWeaver-specific color palette */
    --primary-color: #673AB7; /* Deep Purple */
    --secondary-color: #8E24AA; /* Purple */
    --accent-color: #FFC107; /* Amber */
    --background-color-light: #F5F0FF; /* Very Light Purple */
    --background-color-dark: #1E1A2E; /* Dark Purple-Gray */
    --text-color-light: #37474F; /* Dark Blue-Gray */
    --text-color-dark: #F5F5F5; /* White Smoke */
    --success-color: #4CAF50; /* Green */
    --warning-color: #FF9800; /* Orange */
    --error-color: #F44336; /* Red */
    --info-color: #2196F3; /* Blue */

    /* Theme-adaptive colors for confidential text */
    --confidential-text-color: #37474F; /* Default to dark text for light mode */
    --confidential-text-secondary: #5A5A5A; /* Secondary confidential text */
    --confidential-link-color: #673AB7; /* Links in confidential text */
}

/* Light Mode Styles */
.light-mode {
    background-color: var(--background-color-light);
    color: var(--text-color-light);
}

.light-mode .stButton > button {
    background-color: var(--primary-color);
    color: white;
}

/* Dark Mode Styles */
.dark-mode {
    background-color: var(--background-color-dark);
    color: var(--text-color-dark);
}

.dark-mode .stButton > button {
    background-color: var(--secondary-color);
    color: var(--text-color-dark);
}

/* Header Animations - ScriptWeaver specific */
.main-header {
    font-size: 2.5rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    background: linear-gradient(120deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sub-header {
    font-size: 1.8rem;
    color: var(--secondary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
    animation: fadeIn 1s ease-in-out;
    font-weight: 600;
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 5px;
    display: inline-block;
}

/* Professional panels - ScriptWeaver specific */
.feature-panel {
    background-color: rgba(103, 58, 183, 0.05);
    border-left: 3px solid var(--primary-color);
    padding: 15px;
    margin: 15px 0;
    font-size: 0.95rem;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.success-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(76, 175, 80, 0.1);
    border-left: 5px solid var(--success-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.info-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(33, 150, 243, 0.1);
    border-left: 5px solid var(--info-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.warning-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(255, 152, 0, 0.1);
    border-left: 5px solid var(--warning-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.error-box {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(244, 67, 54, 0.1);
    border-left: 5px solid var(--error-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* Professional card layout */
.pro-card {
    border: 1px solid rgba(103, 58, 183, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 3px 10px rgba(103, 58, 183, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pro-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(103, 58, 183, 0.15);
}

/* Button Styling */
.stButton > button {
    width: 100%;
    border-radius: 8px;
    font-weight: 600;
    padding: 0.6rem 1.2rem;
    border: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stButton > button:hover {
    background-color: var(--accent-color);
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stDownloadButton > button {
    width: 100%;
    border-radius: 8px;
    background-color: var(--success-color);
    color: white;
    font-weight: 600;
    padding: 0.6rem 1.2rem;
    border: none;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stDownloadButton > button:hover {
    background-color: #43A047;
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Sidebar Animation and Styling */
.stSidebar {
    animation: slideIn 0.5s ease-in-out;
    background-color: #f0e6ff;
    border-right: 1px solid rgba(103, 58, 183, 0.2);
}

.dark-mode .stSidebar {
    background-color: #1a172b;
    border-right: 1px solid rgba(142, 36, 170, 0.2);
}

/* Status indicators */
.status-passed {
    color: var(--success-color);
    font-weight: bold;
}

.status-failed {
    color: var(--error-color);
    font-weight: bold;
}

.status-blocked {
    color: var(--warning-color);
    font-weight: bold;
}

/* Form elements */
.stTextArea textarea {
    border-radius: 8px;
    border: 1px solid rgba(103, 58, 183, 0.3);
    transition: border 0.3s ease, box-shadow 0.3s ease;
}

.stTextArea textarea:focus {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.2);
}

div[data-testid="stDataFrame"] {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
}

/* Responsive table styling for markdown tables */
.responsive-table-container,
.step-table-container {
    width: 100%;
    overflow-x: auto;
    margin: 1rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(103, 58, 183, 0.1);
    background-color: white;
    border: 1px solid rgba(103, 58, 183, 0.2);
}

.responsive-table-container table,
.step-table-container table {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.9rem;
    background-color: white;
    margin: 0;
}

.responsive-table-container th,
.step-table-container th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 12px 8px;
    text-align: left;
    border-bottom: 2px solid #5E35B1;
    position: sticky;
    top: 0;
    z-index: 10;
}

.responsive-table-container td,
.step-table-container td {
    padding: 10px 8px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: top;
    word-wrap: break-word;
    word-break: break-word;
    max-width: 200px;
    line-height: 1.4;
    color: #333333; /* Dark gray text for high contrast on white background */
    background-color: #ffffff; /* Ensure white background */
}

.responsive-table-container tr:nth-child(even),
.step-table-container tr:nth-child(even) {
    background-color: #f8f9fa; /* Light gray background for alternating rows */
}

.responsive-table-container tr:nth-child(even) td,
.step-table-container tr:nth-child(even) td {
    background-color: #f8f9fa; /* Ensure alternating row cells have light gray background */
    color: #333333; /* Maintain dark text for contrast */
}

.responsive-table-container tr:hover,
.step-table-container tr:hover {
    background-color: rgba(103, 58, 183, 0.08); /* Slightly more visible hover effect */
    transition: background-color 0.2s ease;
}

.responsive-table-container tr:hover td,
.step-table-container tr:hover td {
    background-color: rgba(103, 58, 183, 0.08); /* Ensure hover effect applies to cells */
    color: #333333; /* Maintain dark text on hover */
}

/* Column-specific styling for step tables */
.step-table-container td:nth-child(1) {
    /* Step No column */
    min-width: 60px;
    max-width: 80px;
    text-align: center;
    font-weight: 600;
    color: #4527A0; /* Darker purple for better contrast (WCAG AA compliant) */
    background-color: #ffffff; /* Ensure white background */
}

.step-table-container td:nth-child(2) {
    /* Action column */
    min-width: 120px;
    max-width: 180px;
    font-weight: 500;
    color: #333333; /* Dark gray text for high contrast */
    background-color: #ffffff; /* White background */
}

.step-table-container td:nth-child(3) {
    /* Locator column */
    min-width: 150px;
    max-width: 250px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    background-color: #f8f9fa; /* Light gray background for code-like content */
    border-radius: 4px;
    padding: 8px;
    color: #000000; /* Black text for maximum contrast on light background */
}

.step-table-container td:nth-child(4) {
    /* Expected Result column */
    min-width: 120px;
    max-width: 200px;
    color: #333333; /* Dark gray text for high contrast */
    background-color: #ffffff; /* White background */
}

/* Responsive breakpoints for tables */
@media (max-width: 768px) {
    .responsive-table-container,
    .step-table-container {
        font-size: 0.8rem;
    }

    .responsive-table-container th,
    .responsive-table-container td,
    .step-table-container th,
    .step-table-container td {
        padding: 8px 6px;
    }

    .step-table-container td:nth-child(3) {
        max-width: 150px;
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .responsive-table-container,
    .step-table-container {
        font-size: 0.75rem;
    }

    .responsive-table-container th,
    .responsive-table-container td,
    .step-table-container th,
    .step-table-container td {
        padding: 6px 4px;
    }

    .step-table-container td:nth-child(3) {
        max-width: 100px;
    }
}

/* Footer style - ScriptWeaver specific */
.scriptweaver-footer {
    text-align: center;
    padding: 20px;
    margin-top: 40px;
    font-size: 0.9rem;
    background: linear-gradient(to right, rgba(103, 58, 183, 0.05), rgba(142, 36, 170, 0.05));
    border-top: 2px solid rgba(103, 58, 183, 0.2);
    border-bottom: 1px solid rgba(103, 58, 183, 0.1);
    color: var(--primary-color);
    font-weight: 500;
    line-height: 1.4;
}

.scriptweaver-footer a {
    text-decoration: none;
    font-weight: 600;
}

.scriptweaver-footer a:hover {
    text-decoration: underline;
    opacity: 0.8;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* ========================================================================== */
/* AUTOMATIC THEME DETECTION USING BROWSER'S PREFERS-COLOR-SCHEME */
/* ========================================================================== */

/* Light Mode (Browser's Light Theme) */
@media (prefers-color-scheme: light) {
    :root {
        --confidential-text-color: #333333; /* Dark text for light backgrounds */
        --confidential-text-secondary: #555555; /* Slightly lighter dark text */
        --confidential-link-color: #673AB7; /* Purple links for light mode */
    }
}

/* Dark Mode (Browser's Dark Theme) */
@media (prefers-color-scheme: dark) {
    :root {
        --confidential-text-color: #F0F0F0; /* Light text for dark backgrounds */
        --confidential-text-secondary: #CCCCCC; /* Slightly darker light text */
        --confidential-link-color: #B39DDB; /* Light purple links for dark mode */
    }
}

/* ========================================================================== */
/* RESPONSIVE TABLE THEME ADAPTATION */
/* ========================================================================== */

/* Light Mode Table Styling (Browser's Light Theme) */
@media (prefers-color-scheme: light) {
    .responsive-table-container,
    .step-table-container {
        background-color: #ffffff; /* White container background */
        border: 1px solid rgba(103, 58, 183, 0.2);
    }

    .responsive-table-container table,
    .step-table-container table {
        background-color: #ffffff; /* White table background */
    }

    .responsive-table-container td,
    .step-table-container td {
        color: #333333 !important; /* Dark gray text for high contrast */
        background-color: #ffffff; /* White cell background */
    }

    .responsive-table-container tr:nth-child(even) td,
    .step-table-container tr:nth-child(even) td {
        background-color: #f8f9fa !important; /* Light gray for alternating rows */
        color: #333333 !important; /* Maintain dark text */
    }

    .responsive-table-container tr:hover td,
    .step-table-container tr:hover td {
        background-color: rgba(103, 58, 183, 0.08) !important; /* Light purple hover */
        color: #333333 !important; /* Maintain dark text on hover */
    }

    .step-table-container td:nth-child(1) {
        color: #4527A0 !important; /* Darker purple for step numbers */
        background-color: #ffffff !important;
    }

    .step-table-container td:nth-child(3) {
        background-color: #f8f9fa !important; /* Light gray for locator column */
        color: #000000 !important; /* Black text for code content */
    }
}

/* Dark Mode Table Styling (Browser's Dark Theme) */
@media (prefers-color-scheme: dark) {
    .responsive-table-container,
    .step-table-container {
        background-color: #ffffff; /* Keep white background for tables in dark mode */
        border: 1px solid rgba(103, 58, 183, 0.3);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* Stronger shadow for dark mode */
    }

    .responsive-table-container table,
    .step-table-container table {
        background-color: #ffffff; /* Keep white table background */
    }

    .responsive-table-container td,
    .step-table-container td {
        color: #333333 !important; /* Keep dark text on white background */
        background-color: #ffffff !important; /* Keep white cell background */
    }

    .responsive-table-container tr:nth-child(even) td,
    .step-table-container tr:nth-child(even) td {
        background-color: #f8f9fa !important; /* Keep light gray for alternating rows */
        color: #333333 !important; /* Keep dark text */
    }

    .responsive-table-container tr:hover td,
    .step-table-container tr:hover td {
        background-color: rgba(103, 58, 183, 0.12) !important; /* Slightly stronger hover in dark mode */
        color: #333333 !important; /* Keep dark text on hover */
    }

    .step-table-container td:nth-child(1) {
        color: #4527A0 !important; /* Keep darker purple for step numbers */
        background-color: #ffffff !important;
    }

    .step-table-container td:nth-child(3) {
        background-color: #f8f9fa !important; /* Keep light gray for locator column */
        color: #000000 !important; /* Keep black text for code content */
    }
}

/* ========================================================================== */
/* CONFIDENTIAL TEXT STYLING WITH THEME ADAPTATION */
/* ========================================================================== */

.confidential-text {
    color: var(--confidential-text-color) !important;
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.confidential-text-secondary {
    color: var(--confidential-text-secondary) !important;
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.confidential-link {
    color: var(--confidential-link-color) !important;
    text-decoration: none;
    font-weight: 600;
}

.confidential-link:hover {
    text-decoration: underline;
    opacity: 0.8;
}

/* Ensure proper contrast for accessibility compliance */
@media (prefers-color-scheme: light) {
    .confidential-text {
        color: #333333 !important; /* WCAG AA compliant contrast ratio 4.5:1 */
    }
    .confidential-text-secondary {
        color: #555555 !important; /* WCAG AA compliant contrast ratio 4.5:1 */
    }
}

@media (prefers-color-scheme: dark) {
    .confidential-text {
        color: #F0F0F0 !important; /* WCAG AA compliant contrast ratio 4.5:1 */
    }
    .confidential-text-secondary {
        color: #CCCCCC !important; /* WCAG AA compliant contrast ratio 4.5:1 */
    }
}

/* ========================================================================== */
/* SIDEBAR BUTTON VISIBILITY FIX */
/* ========================================================================== */

/* Fix button visibility in sidebar by overriding the global white text rule */
section[data-testid="stSidebar"] .stButton > button {
    background-color: var(--primary-color); /* Purple background */
    color: #FFFFFF; /* White text on purple background */
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 600;
    transition: all 0.3s ease;
}

section[data-testid="stSidebar"] .stButton > button:hover {
    background-color: var(--secondary-color); /* Darker purple on hover */
    color: #FFFFFF; /* Keep white text on hover */
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Fix radio button visibility in sidebar */
section[data-testid="stSidebar"] .stRadio > div {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px;
    margin: 4px 0;
}

section[data-testid="stSidebar"] .stRadio label {
    color: #FFFFFF !important; /* Ensure radio button labels are white */
    font-weight: 500 !important;
}

/* Fix expander button visibility in sidebar */
section[data-testid="stSidebar"] .streamlit-expanderHeader {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #FFFFFF !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
}

section[data-testid="stSidebar"] .streamlit-expanderHeader:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Fix download button visibility in sidebar */
section[data-testid="stSidebar"] .stDownloadButton > button {
    background-color: var(--success-color) !important; /* Green background */
    color: #FFFFFF !important; /* White text */
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-weight: 600 !important;
}

section[data-testid="stSidebar"] .stDownloadButton > button:hover {
    background-color: #43A047 !important; /* Darker green on hover */
    color: #FFFFFF !important;
    transform: scale(1.02) !important;
}

/* Theme-adaptive sidebar button styling */
@media (prefers-color-scheme: light) {
    section[data-testid="stSidebar"] .stButton > button {
        background-color: var(--primary-color) !important;
        color: #FFFFFF !important; /* White text on purple - good contrast */
    }
}

@media (prefers-color-scheme: dark) {
    section[data-testid="stSidebar"] .stButton > button {
        background-color: var(--secondary-color) !important;
        color: #FFFFFF !important; /* White text on purple - good contrast */
    }
}

/* ========================================================================== */
/* ADDITIONAL SIDEBAR INTERACTIVE ELEMENTS FIX */
/* ========================================================================== */

/* Fix selectbox visibility in sidebar */
section[data-testid="stSidebar"] .stSelectbox > div > div {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #FFFFFF !important;
}

/* Fix checkbox visibility in sidebar */
section[data-testid="stSidebar"] .stCheckbox > label {
    color: #FFFFFF !important;
}

section[data-testid="stSidebar"] .stCheckbox input[type="checkbox"] {
    accent-color: var(--accent-color) !important;
}

/* Fix text input visibility in sidebar */
section[data-testid="stSidebar"] .stTextInput > div > div > input {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #FFFFFF !important;
}

section[data-testid="stSidebar"] .stTextInput > div > div > input::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Fix number input visibility in sidebar */
section[data-testid="stSidebar"] .stNumberInput > div > div > input {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #FFFFFF !important;
}

/* Fix slider visibility in sidebar */
section[data-testid="stSidebar"] .stSlider > div > div > div {
    color: #FFFFFF !important;
}

/* Fix tabs visibility in sidebar */
section[data-testid="stSidebar"] .stTabs [data-baseweb="tab-list"] {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

section[data-testid="stSidebar"] .stTabs [data-baseweb="tab"] {
    color: #FFFFFF !important;
    background-color: transparent !important;
}

section[data-testid="stSidebar"] .stTabs [aria-selected="true"] {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: #FFFFFF !important;
}
