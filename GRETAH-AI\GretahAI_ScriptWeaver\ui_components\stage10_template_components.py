"""
Stage 10 Template Components for GretahAI ScriptWeaver

Template loading, selection, preview, and metadata display components.
Extracted from stage10_components.py for better maintainability.
"""

import streamlit as st
from typing import Dict, List, Any, Optional

# Import GRETAH standardized logging
from debug_utils import debug
from ui_components.optimization_prevention import (
    show_optimization_confirmation_dialog,
    should_show_confirmation_for_test_case,
    render_optimization_history_expander
)


def render_empty_playground_message():
    """
    Render the empty playground message.
    """
    st.info("🎮 **No Templates Available**")
    st.markdown("Complete Stage 8 to create optimized script templates.")

    col1, col2, col3 = st.columns(3)
    return col1, col2, col3


def render_no_test_cases_message():
    """
    Render the no test cases available message.
    """
    st.warning("⚠️ **No Test Cases Available**")
    st.markdown("Upload a CSV file with test cases first.")


def render_template_selection_interface(optimized_scripts, template_map):
    """
    Render the template selection interface.

    Args:
        optimized_scripts: List of available optimized scripts
        template_map: Mapping of display options to script objects

    Returns:
        tuple: (selected_template, template_options) or (None, None) if no templates
    """
    with st.expander("🎯 Template Selection", expanded=True):
        if not optimized_scripts:
            st.info("No templates available.")
            return None, None

        # Import template helpers inside function to avoid circular imports
        from core.template_helpers import format_template_script_display

        # Create template options
        template_options = []
        for script in optimized_scripts:
            display_info = format_template_script_display(script)
            option_text = f"{display_info['title']} - {display_info['timestamp']}"
            template_options.append(option_text)

        if not template_options:
            st.info("No template options available.")
            return None, None

        selected_template_option = st.selectbox(
            "Template",
            template_options,
            key="template_selection"
        )

        selected_template = template_map[selected_template_option]

        # Display template details
        _render_template_details_columns(selected_template)

        # Template preview toggle
        if st.checkbox("📄 Preview", key="show_template_preview"):
            template_content = selected_template.get('content', 'No content available')
            st.code(template_content, language='python')

        return selected_template, template_options


def _render_template_details_columns(selected_template):
    """
    Render template details in two-column layout.

    Args:
        selected_template: Selected template script object
    """
    from core.template_helpers import format_template_script_display

    display_info = format_template_script_display(selected_template)

    col1, col2 = st.columns(2)
    with col1:
        st.info(f"""
        **Details:**
        - Test Case: {selected_template.get('test_case_id', 'Unknown')}
        - Created: {display_info['timestamp']}
        - Size: {display_info['size_info']}
        """)

    with col2:
        st.info(f"""
        **Status:**
        - ✅ Optimized
        - Type: {selected_template.get('type', 'Unknown').title()}
        - {display_info['optimization_info']}
        """)


def render_test_case_selection_interface(available_test_cases, test_case_map):
    """
    Render the test case selection interface.

    Args:
        available_test_cases: List of available test cases
        test_case_map: Mapping of display options to test case objects

    Returns:
        selected_test_case or None
    """
    with st.expander("📋 Target Test Case", expanded=True):
        if not available_test_cases:
            st.info("No test cases available.")
            return None

        # Import template helpers inside function to avoid circular imports
        from core.template_helpers import format_test_case_display

        # Create test case options
        test_case_options = []
        for test_case in available_test_cases:
            option_text = format_test_case_display(test_case)
            test_case_options.append(option_text)

        if not test_case_options:
            st.info("No test case options available.")
            return None

        selected_test_case_option = st.selectbox(
            "Test Case",
            test_case_options,
            key="test_case_selection",
            help="Test cases marked with ✅ have already been optimized. You will be asked to confirm re-processing."
        )

        selected_test_case = test_case_map[selected_test_case_option]

        # Check if optimization confirmation is needed for Stage 10
        if should_show_confirmation_for_test_case(selected_test_case):
            should_proceed, dialog_closed = show_optimization_confirmation_dialog(selected_test_case)

            if not dialog_closed:
                # Dialog is still open, wait for user input
                return None

            if not should_proceed:
                # User cancelled, return None to prevent further processing
                st.info("Please select a different test case or confirm re-processing.")
                return None

        # Show optimization history if requested
        test_case_id = selected_test_case.get('Test Case ID', '')
        # Use the state parameter for history functionality if provided
        if state is not None:
            render_optimization_history_expander(state, test_case_id)

        # Display test case details
        _render_test_case_details(selected_test_case)

        return selected_test_case


def render_template_target_validation_feedback(selected_template, selected_test_case):
    """
    Render validation feedback for template and target test case selection.

    This function provides real-time feedback to users about their selections
    without blocking the interface, helping guide them toward valid combinations.

    Args:
        selected_template: Currently selected template script
        selected_test_case: Currently selected target test case
    """
    if not selected_template or not selected_test_case:
        return

    # Import validation function
    from core.template_helpers import validate_template_target_compatibility

    validation_result = validate_template_target_compatibility(selected_template, selected_test_case)

    if not validation_result['is_valid']:
        error_type = validation_result.get('error_type', 'general_validation')

        if error_type == 'duplicate_selection':
            st.warning("""
            ⚠️ **Selection Notice**: You have selected the same test case as both template source and target.
            This configuration is not valid for script transformation. Please select a different target test case
            or choose a template generated from a different test case.
            """)
        else:
            error_message = validation_result.get('error_message', 'Unknown validation issue')
            st.warning(f"⚠️ **Selection Issue**: {error_message}")
    else:
        # Show positive feedback for valid selections
        template_source_id = selected_template.get('test_case_id', 'Unknown')
        target_id = selected_test_case.get('Test Case ID', 'Unknown')

        st.success(f"""
        ✅ **Valid Selection**: Template from `{template_source_id}` → Target `{target_id}`

        This is a valid transformation configuration. You can proceed with script generation.
        """)


def _render_test_case_details(selected_test_case):
    """
    Render test case details.

    Args:
        selected_test_case: Selected test case object
    """
    tc_id = selected_test_case.get('Test Case ID', 'Unknown')
    tc_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
    tc_steps = selected_test_case.get('Steps', [])

    st.info(f"""
    **Target:**
    - ID: {tc_id}
    - Objective: {tc_objective}
    - Steps: {len(tc_steps)}
    """)
