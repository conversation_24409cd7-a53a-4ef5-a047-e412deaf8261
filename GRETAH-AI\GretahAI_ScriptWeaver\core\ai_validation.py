"""
AI-powered script validation functionality.

This module handles the validation of generated test scripts using AI analysis,
providing quality scores, issue detection, and improvement recommendations.
"""

from typing import Optional, Dict, Any

# Import debug utility
from debug_utils import debug


def _calculate_fallback_quality_score(validation_results: Dict[str, Any]) -> int:
    """
    Calculate a fallback quality score based on validation results when AI doesn't provide one.

    This function provides a deterministic quality score calculation based on:
    - Syntax validity
    - Number and severity of issues found
    - Ready for execution status
    - Confidence rating

    Args:
        validation_results: The validation results dictionary

    Returns:
        int: Quality score between 0-100
    """
    try:
        # Start with base score
        score = 100

        # Deduct points for syntax issues
        if not validation_results.get('syntax_valid', True):
            score -= 40  # Major deduction for syntax errors

        # Deduct points for not being ready for execution
        if not validation_results.get('ready_for_execution', True):
            score -= 30  # Significant deduction for execution issues

        # Deduct points based on issues found
        issues = validation_results.get('issues_found', [])
        for issue in issues:
            severity = issue.get('severity', 'medium').lower()
            if severity == 'high':
                score -= 15  # High severity issues
            elif severity == 'medium':
                score -= 8   # Medium severity issues
            elif severity == 'low':
                score -= 3   # Low severity issues

        # Adjust based on confidence rating
        confidence = validation_results.get('confidence_rating', 'medium').lower()
        if confidence == 'low':
            score -= 10  # Reduce score for low confidence
        elif confidence == 'high':
            score += 5   # Small bonus for high confidence

        # Ensure score is within valid range
        score = max(0, min(100, score))

        debug(f"Calculated fallback quality score: {score} (syntax_valid: {validation_results.get('syntax_valid')}, "
              f"ready_for_execution: {validation_results.get('ready_for_execution')}, "
              f"issues_count: {len(issues)}, confidence: {confidence})",
              stage="ai_validation", operation="calculate_fallback_score",
              context={'score': score, 'syntax_valid': validation_results.get('syntax_valid'),
                      'ready_for_execution': validation_results.get('ready_for_execution'),
                      'issues_count': len(issues), 'confidence': confidence})

        return score

    except Exception as e:
        debug(f"Error calculating fallback quality score: {e}",
              stage="ai_validation", operation="calculate_fallback_score",
              context={'error_type': type(e).__name__, 'error_message': str(e)})
        return 50  # Default middle score on error


def validate_generated_script(
    script_content: str,
    test_case: Dict[str, Any],
    step_table_entry: Optional[Dict[str, Any]] = None,
    test_data: Optional[Dict[str, Any]] = None,
    element_matches: Optional[Dict[str, Any]] = None,
    api_key: Optional[str] = None,
    model_name: str = "gemini-2.0-flash"
) -> Dict[str, Any]:
    """
    Validate a generated test script using AI analysis.

    This function performs automated code quality validation on generated test scripts,
    analyzing syntax, best practices, WebDriver usage, and integration with test data.
    All AI calls are routed through generate_llm_response for centralized logging.

    Args:
        script_content: The generated test script to validate
        test_case: The test case information for context
        step_table_entry: The step table entry for context
        test_data: Test data used in the script
        element_matches: Element matches used in the script
        api_key: Google AI API key
        model_name: AI model to use for validation

    Returns:
        Dict containing validation results with keys:
        - syntax_valid: Boolean indicating syntax validity
        - issues_found: List of issues with category, severity, description
        - recommendations: List of improvement recommendations
        - confidence_rating: String indicating AI confidence (low/medium/high)
        - ready_for_execution: Boolean indicating if script is ready to run
        - validation_status: String status (excellent/good/needs_improvement/failed)
        - validation_error: String error message if validation failed
    """
    try:
        # Import here to avoid circular imports
        from core.prompt_builder import generate_script_validation_prompt
        from .ai import generate_llm_response

        debug(f"Starting script validation for test case {test_case.get('Test Case ID', 'Unknown')}",
              stage="ai_validation", operation="validate_script",
              context={'test_case_id': test_case.get('Test Case ID', 'Unknown')})

        # Generate the validation prompt for AI analysis
        validation_prompt = generate_script_validation_prompt(
            script_content=script_content,
            test_case=test_case,
            step_table_entry=step_table_entry,
            test_data=test_data,
            element_matches=element_matches
        )

        # Create context for logging
        context = {
            "test_case_id": test_case.get('Test Case ID', 'Unknown'),
            "step_no": step_table_entry.get('step_no', '1') if step_table_entry else '1',
            "script_length": len(script_content),
            "has_test_data": bool(test_data),
            "has_element_matches": bool(element_matches)
        }

        # Call the AI through centralized function
        debug(f"Sending script validation request to {model_name}",
              stage="ai_validation", operation="validate_script",
              context={'model_name': model_name, 'test_case_id': test_case.get('Test Case ID', 'Unknown')})
        response_text = generate_llm_response(
            prompt=validation_prompt,
            model_name=model_name,
            api_key=api_key,
            context=context,
            category="script_validation",
            function_name="validate_generated_script"
        )

        if not response_text or not response_text.strip():
            debug("Empty response from AI validation",
                  stage="ai_validation", operation="validate_script",
                  context={'error_type': 'empty_response'})
            return {
                "syntax_valid": False,
                "quality_score": 0,
                "issues_found": [{"category": "validation", "severity": "high", "description": "AI validation failed - empty response"}],
                "recommendations": ["Retry validation or review script manually"],
                "confidence_rating": "low",
                "ready_for_execution": False,
                "validation_status": "failed",
                "validation_error": "Empty response from AI validation"
            }

        # Parse the JSON response
        try:
            import json
            # Extract JSON from response (handle potential markdown formatting)
            response_clean = response_text.strip()
            if response_clean.startswith('```json'):
                response_clean = response_clean.split('```json')[1].split('```')[0].strip()
            elif response_clean.startswith('```'):
                response_clean = response_clean.split('```')[1].split('```')[0].strip()

            validation_results = json.loads(response_clean)

            # Validate the response structure
            required_keys = ['syntax_valid', 'quality_score', 'issues_found', 'recommendations', 'confidence_rating', 'ready_for_execution']
            for key in required_keys:
                if key not in validation_results:
                    debug(f"Missing key '{key}' in validation response, adding default",
                          stage="ai_validation", operation="validate_script",
                          context={'missing_key': key})
                    if key == 'syntax_valid':
                        validation_results[key] = True
                    elif key == 'quality_score':
                        # Calculate fallback quality score based on issues and readiness
                        validation_results[key] = _calculate_fallback_quality_score(validation_results)
                    elif key in ['issues_found', 'recommendations']:
                        validation_results[key] = []
                    elif key == 'confidence_rating':
                        validation_results[key] = 'medium'
                    elif key == 'ready_for_execution':
                        validation_results[key] = True

            # Ensure quality_score is within valid range (0-100)
            quality_score = validation_results.get('quality_score', 0)
            if not isinstance(quality_score, (int, float)) or quality_score < 0 or quality_score > 100:
                debug(f"Invalid quality_score value: {quality_score}, calculating fallback",
                      stage="ai_validation", operation="validate_script",
                      context={'invalid_score': quality_score, 'score_type': type(quality_score).__name__})
                validation_results['quality_score'] = _calculate_fallback_quality_score(validation_results)

            # Determine validation status based on issues and readiness
            if not validation_results.get('ready_for_execution', True):
                validation_status = "failed"
            elif validation_results.get('issues_found', []):
                high_severity_issues = [issue for issue in validation_results['issues_found'] if issue.get('severity') == 'high']
                if high_severity_issues:
                    validation_status = "needs_improvement"
                else:
                    validation_status = "good"
            else:
                validation_status = "excellent"

            validation_results['validation_status'] = validation_status

            debug(f"Script validation completed - Status: {validation_status}, Quality Score: {validation_results.get('quality_score', 'N/A')}",
                  stage="ai_validation", operation="validate_script",
                  context={'validation_status': validation_status, 'quality_score': validation_results.get('quality_score', 'N/A')})
            return validation_results

        except json.JSONDecodeError as e:
            debug(f"Failed to parse AI validation response as JSON: {e}",
                  stage="ai_validation", operation="validate_script",
                  context={'error_type': 'json_decode_error', 'error_message': str(e)})
            debug(f"Raw response: {response_text[:500]}...",
                  stage="ai_validation", operation="validate_script",
                  context={'response_preview': response_text[:500]})
            return {
                "syntax_valid": True,
                "quality_score": 50,  # Default middle score for parsing errors
                "issues_found": [{"category": "validation", "severity": "medium", "description": "AI response parsing failed"}],
                "recommendations": ["Review script manually", "Retry validation"],
                "confidence_rating": "low",
                "ready_for_execution": True,
                "validation_status": "needs_improvement",
                "validation_error": f"Failed to parse AI response: {str(e)}"
            }

    except Exception as e:
        debug(f"Error during script validation: {e}",
              stage="ai_validation", operation="validate_script",
              context={'error_type': type(e).__name__, 'error_message': str(e)})
        return {
            "syntax_valid": False,
            "quality_score": 0,  # Lowest score for validation errors
            "issues_found": [{"category": "validation", "severity": "high", "description": f"Validation error: {str(e)}"}],
            "recommendations": ["Review script manually", "Check validation configuration"],
            "confidence_rating": "low",
            "ready_for_execution": False,
            "validation_status": "failed",
            "validation_error": str(e)
        }
