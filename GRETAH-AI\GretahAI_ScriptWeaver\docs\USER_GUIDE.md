# GretahAI ScriptWeaver User Guide

**Complete Step-by-Step Workflow Guide for Test Automation**

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

---

## 📋 Table of Contents

1. [Getting Started](#-getting-started)
2. [Complete Workflow Overview](#-complete-workflow-overview)
3. [Stage-by-Stage Guide](#-stage-by-stage-guide)
4. [Advanced Features](#-advanced-features)
5. [Best Practices](#-best-practices)
6. [Troubleshooting](#-troubleshooting)

## 🚀 Getting Started

### Prerequisites Checklist

Before starting your test automation journey, ensure you have:

- ✅ **Python 3.8+** installed and configured
- ✅ **Google AI API Key** with Gemini 2.0 Flash access
- ✅ **Chrome Browser** (latest version recommended)
- ✅ **Excel file** with properly formatted test cases
- ✅ **Target website URL** accessible for testing

### Quick Start Steps

1. **Launch the Application**
   ```bash
   cd GretahAI_ScriptWeaver
   streamlit run app.py
   ```

2. **Access the Interface**
   - Open your browser to `http://localhost:8501`
   - The application will display the Stage 1 interface

3. **Navigate Using Sidebar**
   - Use the sidebar navigation for direct stage access
   - Visual indicators show stage status and prerequisites

## 🔄 Complete Workflow Overview

### Workflow Phases

```mermaid
graph TD
    A[Stage 1: Upload Excel] --> B[Stage 2: Configure Website]
    B --> C[Stage 3: AI Conversion]
    C --> D[Stage 4: Element Detection]
    D --> E[Stage 5: Test Data]
    E --> F[Stage 6: Script Generation]
    F --> G[Stage 7: Script Execution]
    G --> H[Stage 8: Optimization]
    H --> I[Stage 9: Script Browser]
    I --> J[Stage 10: Template Playground]
    
    C --> C1[Hybrid Editing]
    H --> C[Return to Stage 3]
    J --> K[Template-Based Generation]
```

### Core Workflow (Stages 1-8)
**Primary test automation pipeline from Excel upload to optimized scripts**

### Advanced Features (Stages 9-10)
**Script management, browsing, and template-based generation**

## 📖 Stage-by-Stage Guide

### Stage 1: Excel File Upload & Preview 📁

**Objective**: Upload and validate your test case Excel file

#### Step-by-Step Instructions:

1. **Upload Your Excel File**
   - Click the "Browse files" button
   - Select your Excel file containing test cases
   - Supported formats: `.xlsx`, `.xls`

2. **Preview Test Cases**
   - Review the uploaded test cases in the data table
   - Verify that all required columns are present:
     - Test Case ID
     - Test Case Objective
     - Steps (with Step No, Test Steps, Expected Result)

3. **Validate Format**
   - Ensure test case IDs are unique
   - Check that test steps are clearly described
   - Verify expected results are specific and measurable

#### Expected Outcome:
- ✅ Excel file successfully uploaded and parsed
- ✅ Test cases displayed in interactive table
- ✅ Ready to proceed to Stage 2

#### Common Issues:
- **File format errors**: Ensure Excel file follows the required structure
- **Missing columns**: Add required columns (Test Case ID, Objective, Steps)
- **Empty cells**: Fill in all required test case information

### Stage 2: Website Configuration & API Setup 🌐

**Objective**: Configure target website and AI integration

#### Step-by-Step Instructions:

1. **Enter Website URL**
   - Input the complete URL of your target website
   - Example: `https://example.com/login`
   - Ensure the URL is accessible and loads properly

2. **Configure Google AI API**
   - Enter your Google AI API key
   - The system will validate the key automatically
   - API key is securely stored for the session

3. **Set Detection Preferences**
   - Choose element detection sensitivity
   - Configure matching algorithms
   - Set timeout preferences for element detection

#### Expected Outcome:
- ✅ Website URL validated and accessible
- ✅ Google AI API key configured and tested
- ✅ Detection preferences set
- ✅ Ready for test case conversion

#### Common Issues:
- **Invalid API key**: Verify your Google AI API key and billing status
- **Website access**: Ensure the target website is accessible
- **CORS issues**: Some websites may block automated access

### Stage 3: Test Case Analysis & AI Conversion 🤖

**Objective**: Convert test cases to automation-ready step tables using AI

#### Step-by-Step Instructions:

1. **Select Test Case**
   - Choose a test case from the dropdown list
   - Review the test case objective and steps
   - Ensure the selected test case is complete

2. **AI Conversion Process**
   - Click "Convert Test Case to Step Table"
   - AI analyzes the test case using Gemini 2.0 Flash
   - Conversion typically takes 10-30 seconds

3. **Review Step Table**
   - Examine the generated step table in markdown format
   - Verify that all test steps are properly converted
   - Check that locator strategies are appropriate

4. **Enable Hybrid Editing (Optional)**
   - Toggle hybrid editing to combine AI and manual steps
   - Add custom steps between AI-generated steps
   - Use step templates for common operations

#### Expected Outcome:
- ✅ Test case converted to structured step table
- ✅ Step table reviewed and validated
- ✅ Hybrid editing configured if needed
- ✅ Ready for element detection

#### Hybrid Editing Features:
- **Visual Step Distinction**: AI steps (locked) vs manual steps (editable)
- **Step Templates**: Pre-built templates for common actions
- **Real-Time Validation**: Automatic flow validation with conflict detection

### Stage 4: UI Element Detection & Interactive Selection 🎯

**Objective**: Detect and match UI elements to test steps

#### Step-by-Step Instructions:

1. **Automatic Element Detection**
   - System automatically detects UI elements on the target page
   - Elements are filtered for QA automation relevance
   - Detection results are displayed with confidence scores

2. **Interactive Element Selection**
   - Click "Open Interactive Selector" for manual selection
   - Browser window opens with element highlighting
   - Click on elements to capture precise locators
   - System provides immediate feedback with element details

3. **AI-Powered Element Matching**
   - AI matches detected elements to test steps
   - Review matching suggestions with reasoning
   - Accept or modify matches as needed

4. **Locator Validation**
   - Verify that selected locators are stable and unique
   - Test locators against the target page
   - Choose backup locator strategies if needed

#### Expected Outcome:
- ✅ UI elements detected and cataloged
- ✅ Elements matched to appropriate test steps
- ✅ Locators validated for reliability
- ✅ Ready for test data configuration

#### Interactive Selection Tips:
- **Element Highlighting**: Hover over elements to see highlighting
- **Multiple Strategies**: System captures multiple locator types
- **Validation**: Test locators before proceeding

### Stage 5: Test Data Configuration & Generation 📊

**Objective**: Generate and configure realistic test data

#### Step-by-Step Instructions:

1. **Automatic Data Generation**
   - AI analyzes step context to determine required data
   - Generates realistic test data based on field types
   - Provides appropriate values for common data types

2. **Data Customization**
   - Review generated test data values
   - Modify values to match your testing requirements
   - Add validation rules if needed

3. **Data Type Configuration**
   - Configure data types for each field
   - Set validation patterns (email, phone, etc.)
   - Define data ranges and constraints

4. **Skip Navigation Steps**
   - System automatically skips data generation for navigation steps
   - Focus on form inputs and data entry steps
   - Validate data requirements for each step

#### Expected Outcome:
- ✅ Realistic test data generated for all relevant steps
- ✅ Data values customized for testing requirements
- ✅ Validation rules configured
- ✅ Ready for script generation

#### Data Generation Features:
- **Context-Aware**: Data generation based on step context
- **Realistic Values**: Proper email formats, names, addresses
- **Validation Support**: Built-in validation for common patterns

### Stage 6: Test Script Generation & Review 📝

**Objective**: Generate executable PyTest scripts

#### Step-by-Step Instructions:

1. **Script Generation**
   - Click "Generate Test Script" for the current step
   - AI creates PyTest script using best practices
   - Generation includes error handling and assertions

2. **Script Review**
   - Review generated script with syntax highlighting
   - Verify that locators and test data are properly integrated
   - Check that assertions match expected results

3. **Manual Editing (Optional)**
   - Use in-app editor to modify scripts
   - Add custom logic or error handling
   - Save modifications with version tracking

4. **Script Validation**
   - System validates script syntax
   - Checks for common issues and best practices
   - Provides suggestions for improvements

#### Expected Outcome:
- ✅ PyTest script generated with best practices
- ✅ Script reviewed and validated
- ✅ Manual modifications applied if needed
- ✅ Ready for script execution

#### Script Features:
- **Selenium WebDriver**: Modern browser automation
- **Explicit Waits**: Reliable element detection
- **Error Handling**: Comprehensive exception handling
- **Screenshots**: Automatic capture on failures

### Stage 7: Test Script Execution & Results ▶️

**Objective**: Execute test scripts and analyze results

#### Step-by-Step Instructions:

1. **Script Execution**
   - Click "Execute Test Script"
   - System runs script using enhanced PyTest configuration
   - Real-time execution monitoring with progress indicators

2. **Results Analysis**
   - Review execution results with detailed metrics
   - Examine captured screenshots and logs
   - Analyze performance data and timing

3. **Artifact Collection**
   - Screenshots automatically captured on failures
   - Execution logs saved with detailed information
   - Performance metrics tracked and stored

4. **Next Step Progression**
   - Automatically advance to next step if successful
   - Address any failures before proceeding
   - Option to proceed to optimization after all steps

#### Expected Outcome:
- ✅ Test script executed successfully
- ✅ Results analyzed and validated
- ✅ Artifacts collected and stored
- ✅ Ready for next step or optimization

#### Execution Features:
- **Real-Time Monitoring**: Live execution progress
- **Comprehensive Artifacts**: Screenshots, logs, metrics
- **Performance Tracking**: Execution time and resource usage

### Stage 8: Script Optimization & Consolidation 🔧

**Objective**: Optimize and consolidate all test scripts

#### Step-by-Step Instructions:

1. **Script Consolidation**
   - System combines all individual step scripts
   - Creates cohesive test suite with proper flow
   - Maintains data consistency across steps

2. **AI-Powered Optimization**
   - AI analyzes combined script for improvements
   - Applies best practices and optimization patterns
   - Removes redundancy and improves efficiency

3. **Final Review**
   - Review optimized script with improvements highlighted
   - Verify that all test steps are properly integrated
   - Check that optimization maintains test integrity

4. **Script Download**
   - Download final optimized PyTest script
   - Script includes all necessary imports and fixtures
   - Ready for integration into CI/CD pipelines

#### Expected Outcome:
- ✅ All scripts consolidated into cohesive test suite
- ✅ AI optimization applied with best practices
- ✅ Final script reviewed and validated
- ✅ Optimized script downloaded and ready for use

#### Optimization Features:
- **Best Practices**: Industry-standard patterns applied
- **Efficiency**: Redundancy removal and performance optimization
- **Maintainability**: Clean, readable code structure

### Stage 9: Script Browser & History Management 📜

**Objective**: Manage and browse all generated scripts

#### Step-by-Step Instructions:

1. **Access Script Browser**
   - Navigate to Stage 9 from sidebar (always accessible)
   - Browse all scripts from current and previous sessions
   - Scripts are automatically saved with metadata

2. **Search and Filter**
   - Use search functionality to find specific scripts
   - Filter by test case, optimization status, or date
   - Sort by various criteria (date, size, performance)

3. **Script Comparison**
   - Select multiple scripts for side-by-side comparison
   - View differences with syntax highlighting
   - Compare original vs optimized versions

4. **Script Management**
   - Download individual scripts or bulk export
   - Delete old scripts with confirmation dialogs
   - View detailed metadata and execution history

#### Expected Outcome:
- ✅ All scripts accessible and organized
- ✅ Search and filtering capabilities utilized
- ✅ Script comparisons performed as needed
- ✅ Script management operations completed

### Stage 10: Script Playground & Template Generation 🎮

**Objective**: Generate new scripts using existing scripts as templates

#### Step-by-Step Instructions:

1. **Template Selection**
   - Choose from available optimized scripts as templates
   - Review template metadata and compatibility scores
   - Select templates that match your target test case structure

2. **Target Test Case Selection**
   - Choose the test case you want to generate a script for
   - System displays test case details and requirements
   - Verify compatibility with selected template

3. **AI Gap Analysis**
   - Click "Analyze Template Coverage"
   - AI identifies gaps between template and target test case
   - Review analysis results and compatibility assessment

4. **Gap Filling**
   - Fill in missing information identified by gap analysis
   - Use dynamic forms to provide required data
   - Add custom instructions for specific requirements

5. **Template-Based Generation**
   - Generate new script using template structure
   - AI adapts template to target test case requirements
   - Maintains template best practices and patterns

6. **Script Execution**
   - Execute generated script directly in Stage 10
   - Review execution results and performance metrics
   - Download or save script for future use

#### Expected Outcome:
- ✅ Template selected and analyzed for compatibility
- ✅ Gaps identified and filled with required information
- ✅ New script generated using template patterns
- ✅ Script executed and validated successfully

#### Stage 10 Detailed Workflow

```mermaid
graph TD
    A[Enter Stage 10] --> B[Load Available Templates]
    B --> C[Display Template Selection Interface]
    C --> D[User Selects Template]

    D --> E[Load Template Metadata]
    E --> F[Display Template Preview]
    F --> G[User Selects Target Test Case]

    G --> H[AI Gap Analysis]
    H --> I{Gaps Identified?}
    I -->|Yes| J[Display Gap Filling Form]
    I -->|No| K[Generate Script Directly]

    J --> L[User Fills Missing Data]
    L --> M[Validate Gap Data]
    M --> N{Data Valid?}
    N -->|No| J
    N -->|Yes| K

    K --> O[Generate Template-Based Script]
    O --> P[Display Generated Script]
    P --> Q{User Wants to Execute?}

    Q -->|Yes| R[Execute Script with PyTest]
    Q -->|No| S[Save Script]

    R --> T[Display Execution Results]
    T --> U[Capture Performance Metrics]
    U --> V[Save Script & Results]

    S --> W{Generate Another?}
    V --> W
    W -->|Yes| C
    W -->|No| X[Return to Navigation]

    style A fill:#e1f5fe
    style H fill:#fff3e0
    style O fill:#fff3e0
    style R fill:#ffebee
    style X fill:#c8e6c9
```

## 🚀 Advanced Features

### Hybrid AI-Assisted Editing

**Revolutionary editing system combining AI efficiency with human expertise**

#### Key Features:
- **Visual Step Distinction**: Clear indicators for AI vs manual steps
- **Step Templates**: Pre-built templates for common operations
- **Real-Time Validation**: Automatic flow validation with conflict detection
- **Intelligent Merging**: Smart combination of AI and manual steps

#### How to Use:
1. Enable hybrid editing in Stage 3
2. Add manual steps between AI-generated steps
3. Use step templates for common patterns
4. Validate combined flow before proceeding

### Template-Based Script Generation

**Advanced script generation using proven patterns**

#### Benefits:
- **Proven Patterns**: Use successful scripts as templates
- **Consistency**: Maintain coding standards across projects
- **Efficiency**: Faster script generation with reliable patterns
- **Quality**: Inherit best practices from optimized scripts

#### Workflow:
1. Complete Stages 1-8 to create optimized templates
2. Use Stage 10 for template-based generation
3. Apply templates to new test cases
4. Customize generated scripts as needed

### Performance Monitoring

**Real-time tracking of system performance and optimization**

#### Metrics Tracked:
- **Execution Time**: Script generation and execution timing
- **Resource Usage**: CPU, memory, and network utilization
- **AI Performance**: Token usage and response times
- **Success Rates**: Script generation and execution success rates

#### Access Performance Data:
- View real-time metrics during operations
- Check historical performance in logs
- Use metrics for optimization decisions
- Monitor system health and capacity

## 📋 Best Practices

### Test Case Preparation

1. **Clear Objectives**: Write specific, measurable test objectives
2. **Detailed Steps**: Provide step-by-step instructions with expected results
3. **Consistent Format**: Use consistent naming and structure
4. **Complete Information**: Include all necessary test data and conditions

### Element Selection Strategy

1. **Stable Locators**: Choose locators that won't change frequently
2. **Multiple Strategies**: Use backup locator strategies
3. **Validation**: Test locators before finalizing
4. **Documentation**: Document complex element selection decisions

### Script Optimization

1. **Regular Optimization**: Use Stage 8 optimization for all scripts
2. **Template Creation**: Create templates from successful scripts
3. **Version Control**: Maintain script versions and change history
4. **Performance Testing**: Regularly test script performance

### Workflow Management

1. **Stage Progression**: Complete stages in order for best results
2. **State Management**: Use sidebar navigation for stage jumping
3. **Data Persistence**: Rely on automatic data persistence
4. **Error Recovery**: Use debug mode for troubleshooting

## 🔧 Troubleshooting

### Common Workflow Issues

#### Stage Transition Problems
- **Symptom**: Cannot advance to next stage
- **Solution**: Check prerequisites and complete current stage requirements
- **Debug**: Use sidebar navigation to identify missing requirements

#### Script Generation Failures
- **Symptom**: Generated scripts contain errors or are incomplete
- **Solution**: Review test case format and element selection
- **Debug**: Check AI logs for detailed error information

#### Element Detection Issues
- **Symptom**: Elements not detected or incorrectly matched
- **Solution**: Use interactive element selection for manual control
- **Debug**: Verify website accessibility and element stability

#### Performance Issues
- **Symptom**: Slow response times or application freezing
- **Solution**: Clear logs, restart application, check system resources
- **Debug**: Enable debug mode and monitor performance metrics

### Debug Mode Usage

Enable comprehensive debugging:
```bash
export SCRIPTWEAVER_DEBUG=true
streamlit run app.py
```

Debug mode provides:
- Detailed operation logging
- Stage transition monitoring
- AI request/response tracking
- Performance metrics
- State change tracking

### Getting Support

For enterprise customers with valid support agreements:

**Primary Support Contact**: <EMAIL>

**When Contacting Support, Include**:
- Detailed description of the issue
- Steps to reproduce the problem
- Error logs from relevant directories
- Configuration details (without sensitive information)
- Screenshots or screen recordings if applicable

**Support Response Times**:
- Enterprise customers: Based on support agreement level
- Critical issues: Expedited response for production environments
- General inquiries: Standard business hours response

---

## 📚 Additional Resources

### Documentation Links
- [Developer Guide](DEVELOPER_GUIDE.md) - Technical architecture and development
- [API Documentation](API.md) - Complete API reference
- [Installation Guide](../README.md#-installation--setup) - Setup instructions
- [Changelog](../CHANGELOG.md) - Version history and updates

### Training Resources
- **Video Tutorials**: Available for enterprise customers
- **Best Practices Guide**: Comprehensive workflow optimization
- **Certification Programs**: Professional training and certification
- **Webinar Series**: Regular training sessions and updates

### Community Resources
- **Enterprise Forums**: Exclusive access for licensed customers
- **Knowledge Base**: Searchable database of solutions
- **Feature Requests**: Submit enhancement requests
- **User Groups**: Connect with other enterprise users

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

**Commercial Licensing**: Contact <EMAIL> for licensing inquiries and enterprise partnership opportunities.

**Website**: https://cogniron.com

**Note**: This software requires a valid commercial license for use. Enterprise evaluation licenses are available upon request for qualified customers.
