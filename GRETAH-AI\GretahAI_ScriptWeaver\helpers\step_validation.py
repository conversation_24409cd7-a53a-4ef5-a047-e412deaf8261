"""
Step Validation Helpers for Hybrid AI-Assisted Test Case Editing

This module provides validation functions for ensuring the logical flow and integrity
of combined AI-generated and manual test steps.

Key Features:
- Flow validation for step sequences
- Conflict detection between AI and manual steps
- Logical consistency checks
- Performance impact analysis
- Best practice recommendations
"""

from typing import Dict, List, Any, Tuple, Optional
import logging

logger = logging.getLogger("ScriptWeaver.step_validation")


class StepValidationResult:
    """Container for step validation results."""
    
    def __init__(self):
        self.is_valid = True
        self.errors = []
        self.warnings = []
        self.suggestions = []
        self.performance_issues = []
        self.best_practices = []
    
    def add_error(self, message: str, step_no: str = None):
        """Add a validation error."""
        self.errors.append({
            "message": message,
            "step_no": step_no,
            "severity": "error"
        })
        self.is_valid = False
    
    def add_warning(self, message: str, step_no: str = None):
        """Add a validation warning."""
        self.warnings.append({
            "message": message,
            "step_no": step_no,
            "severity": "warning"
        })
    
    def add_suggestion(self, message: str, step_no: str = None):
        """Add a validation suggestion."""
        self.suggestions.append({
            "message": message,
            "step_no": step_no,
            "severity": "suggestion"
        })
    
    def add_performance_issue(self, message: str, step_no: str = None, impact: str = "medium"):
        """Add a performance-related issue."""
        self.performance_issues.append({
            "message": message,
            "step_no": step_no,
            "impact": impact,
            "severity": "performance"
        })
    
    def add_best_practice(self, message: str, step_no: str = None):
        """Add a best practice recommendation."""
        self.best_practices.append({
            "message": message,
            "step_no": step_no,
            "severity": "best_practice"
        })
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert validation result to dictionary."""
        return {
            "is_valid": self.is_valid,
            "errors": self.errors,
            "warnings": self.warnings,
            "suggestions": self.suggestions,
            "performance_issues": self.performance_issues,
            "best_practices": self.best_practices,
            "total_issues": len(self.errors) + len(self.warnings),
            "total_suggestions": len(self.suggestions) + len(self.best_practices)
        }


def validate_combined_steps(steps: List[Dict[str, Any]]) -> StepValidationResult:
    """
    Comprehensive validation of combined AI and manual steps.
    
    Args:
        steps: List of combined steps to validate
        
    Returns:
        StepValidationResult containing all validation findings
    """
    result = StepValidationResult()
    
    if not steps:
        result.add_error("No steps provided for validation")
        return result
    
    # Run all validation checks
    _validate_step_sequence(steps, result)
    _validate_navigation_flow(steps, result)
    _validate_assertion_coverage(steps, result)
    _validate_wait_strategies(steps, result)
    _validate_data_flow(steps, result)
    _validate_error_handling(steps, result)
    _validate_cleanup_steps(steps, result)
    _validate_performance_impact(steps, result)
    _validate_best_practices(steps, result)
    
    logger.info(f"Step validation completed: {len(result.errors)} errors, {len(result.warnings)} warnings")
    
    return result


def _validate_step_sequence(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate logical sequence of steps."""
    prev_step = None
    
    for i, step in enumerate(steps):
        step_no = step.get("step_no", str(i + 1))
        step_type = step.get("step_type", "")
        action = step.get("action", "").lower()
        
        if prev_step:
            prev_type = prev_step.get("step_type", "")
            prev_action = prev_step.get("action", "").lower()
            
            # Check for invalid sequences
            if prev_type == "teardown" and step_type in ["setup", "ui"]:
                result.add_error(
                    f"Invalid sequence: {step_type} step after teardown step",
                    step_no
                )
            
            # Check for missing setup after teardown
            if prev_type == "teardown" and step_type != "teardown":
                result.add_warning(
                    f"Consider adding setup steps after teardown in step {prev_step.get('step_no')}",
                    step_no
                )
            
            # Check for navigation after form submission
            if "submit" in prev_action and "navigate" in action:
                result.add_suggestion(
                    f"Consider adding wait step after form submission before navigation",
                    step_no
                )
        
        prev_step = step


def _validate_navigation_flow(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate navigation flow and page transitions."""
    has_initial_navigation = False
    navigation_steps = []
    
    for step in steps:
        action = step.get("action", "").lower()
        step_no = step.get("step_no", "")
        
        if any(nav_action in action for nav_action in ["navigate", "open", "goto"]):
            navigation_steps.append(step)
            if not has_initial_navigation:
                has_initial_navigation = True
    
    # Check for initial navigation
    if not has_initial_navigation:
        result.add_warning("No initial navigation step detected. Consider adding navigation to start the test.")
    
    # Check for excessive navigation
    if len(navigation_steps) > 5:
        result.add_performance_issue(
            f"High number of navigation steps ({len(navigation_steps)}) may impact test performance",
            impact="medium"
        )
    
    # Check for navigation without wait
    for i, nav_step in enumerate(navigation_steps):
        step_no = nav_step.get("step_no", "")
        next_step_index = None
        
        # Find the next step after this navigation
        for j, step in enumerate(steps):
            if step.get("step_no") == step_no and j + 1 < len(steps):
                next_step_index = j + 1
                break
        
        if next_step_index:
            next_step = steps[next_step_index]
            next_action = next_step.get("action", "").lower()
            
            if not any(wait_action in next_action for wait_action in ["wait", "sleep", "pause"]):
                result.add_suggestion(
                    f"Consider adding wait step after navigation in step {step_no}",
                    next_step.get("step_no")
                )


def _validate_assertion_coverage(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate assertion and verification coverage."""
    assertion_steps = []
    ui_interaction_steps = []
    
    for step in steps:
        step_type = step.get("step_type", "")
        action = step.get("action", "").lower()
        assertion_type = step.get("assertion_type", "")
        
        if step_type == "assertion" or assertion_type not in ["", "no_error"]:
            assertion_steps.append(step)
        
        if step_type == "ui" and any(action_type in action for action_type in ["click", "type", "select", "upload"]):
            ui_interaction_steps.append(step)
    
    # Check assertion coverage
    if len(assertion_steps) == 0:
        result.add_warning("No assertion steps detected. Consider adding verification steps.")
    
    # Check assertion ratio
    if len(ui_interaction_steps) > 0:
        assertion_ratio = len(assertion_steps) / len(ui_interaction_steps)
        if assertion_ratio < 0.3:  # Less than 30% assertions
            result.add_suggestion(
                f"Low assertion coverage ({assertion_ratio:.1%}). Consider adding more verification steps."
            )


def _validate_wait_strategies(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate wait strategies and timing."""
    wait_steps = []
    long_timeouts = []
    
    for step in steps:
        action = step.get("action", "").lower()
        timeout = step.get("timeout", 0)
        step_no = step.get("step_no", "")
        
        if "wait" in action:
            wait_steps.append(step)
        
        try:
            timeout_val = int(timeout) if timeout else 0
            if timeout_val > 30:
                long_timeouts.append(step)
        except (ValueError, TypeError):
            pass
    
    # Check for excessive waits
    if len(wait_steps) > len(steps) * 0.4:  # More than 40% wait steps
        result.add_performance_issue(
            f"High number of wait steps ({len(wait_steps)}) may slow down test execution",
            impact="high"
        )
    
    # Check for long timeouts
    for step in long_timeouts:
        step_no = step.get("step_no", "")
        timeout = step.get("timeout", 0)
        result.add_performance_issue(
            f"Long timeout ({timeout}s) in step {step_no} may impact test performance",
            step_no,
            impact="medium"
        )


def _validate_data_flow(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate test data flow and dependencies."""
    data_setup_steps = []
    data_usage_steps = []
    
    for step in steps:
        step_type = step.get("step_type", "")
        test_data_param = step.get("test_data_param", "")
        action = step.get("action", "").lower()
        
        if step_type == "data" or "setup" in action:
            data_setup_steps.append(step)
        
        if test_data_param and "{{" in test_data_param:
            data_usage_steps.append(step)
    
    # Check for data usage without setup
    if len(data_usage_steps) > 0 and len(data_setup_steps) == 0:
        result.add_warning("Test data is used but no data setup steps detected.")
    
    # Check for unused data setup
    if len(data_setup_steps) > 0 and len(data_usage_steps) == 0:
        result.add_suggestion("Data setup steps detected but no data usage found.")


def _validate_error_handling(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate error handling and recovery strategies."""
    error_handling_steps = []
    
    for step in steps:
        action = step.get("action", "").lower()
        condition = step.get("condition", "")
        
        if any(error_term in action for error_term in ["handle", "catch", "error", "exception"]):
            error_handling_steps.append(step)
        
        if condition and any(error_term in condition.lower() for error_term in ["error", "fail", "exception"]):
            error_handling_steps.append(step)
    
    # Suggest error handling for critical operations
    critical_actions = ["submit", "upload", "payment", "login"]
    critical_steps = [
        step for step in steps 
        if any(critical in step.get("action", "").lower() for critical in critical_actions)
    ]
    
    if len(critical_steps) > 0 and len(error_handling_steps) == 0:
        result.add_suggestion(
            "Consider adding error handling for critical operations (login, submit, payment, etc.)"
        )


def _validate_cleanup_steps(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate cleanup and teardown steps."""
    teardown_steps = [step for step in steps if step.get("step_type") == "teardown"]
    
    if len(teardown_steps) == 0:
        result.add_suggestion("Consider adding cleanup/teardown steps at the end of the test.")
    
    # Check if teardown steps are at the end
    if teardown_steps:
        last_teardown_index = -1
        for i, step in enumerate(steps):
            if step.get("step_type") == "teardown":
                last_teardown_index = i
        
        if last_teardown_index < len(steps) - 3:  # Teardown not in last 3 steps
            result.add_warning("Teardown steps should typically be at the end of the test.")


def _validate_performance_impact(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate potential performance impacts."""
    # Check for excessive steps
    if len(steps) > 50:
        result.add_performance_issue(
            f"Large number of steps ({len(steps)}) may impact test execution time",
            impact="high"
        )
    
    # Check for inefficient patterns
    consecutive_waits = 0
    for i, step in enumerate(steps):
        action = step.get("action", "").lower()
        
        if "wait" in action:
            consecutive_waits += 1
        else:
            if consecutive_waits > 2:
                result.add_performance_issue(
                    f"Multiple consecutive wait steps detected around step {step.get('step_no')}",
                    step.get("step_no"),
                    impact="medium"
                )
            consecutive_waits = 0


def _validate_best_practices(steps: List[Dict[str, Any]], result: StepValidationResult):
    """Validate adherence to testing best practices."""
    # Check for descriptive step names
    unclear_actions = []
    for step in steps:
        action = step.get("action", "")
        if len(action) < 3 or action.lower() in ["test", "check", "do", "run"]:
            unclear_actions.append(step)
    
    if unclear_actions:
        result.add_best_practice(
            f"Consider using more descriptive action names for {len(unclear_actions)} steps"
        )
    
    # Check for proper locator strategies
    weak_locators = []
    for step in steps:
        locator_strategy = step.get("locator_strategy", "")
        locator = step.get("locator", "")
        
        if locator_strategy == "xpath" and "//" in locator and "contains" not in locator:
            weak_locators.append(step)
    
    if weak_locators:
        result.add_best_practice(
            f"Consider using more robust locator strategies for {len(weak_locators)} steps"
        )
    
    # Check for hardcoded values
    hardcoded_values = []
    for step in steps:
        test_data = step.get("test_data_param", "")
        if test_data and "{{" not in test_data and any(
            pattern in test_data.lower() 
            for pattern in ["test", "example", "123", "password", "admin"]
        ):
            hardcoded_values.append(step)
    
    if hardcoded_values:
        result.add_best_practice(
            f"Consider parameterizing hardcoded test data in {len(hardcoded_values)} steps"
        )


def get_validation_summary(validation_result: StepValidationResult) -> str:
    """
    Generate a human-readable validation summary.
    
    Args:
        validation_result: StepValidationResult instance
        
    Returns:
        String summary of validation results
    """
    result_dict = validation_result.to_dict()
    
    if result_dict["is_valid"] and result_dict["total_issues"] == 0:
        return "✅ All validations passed! Your test flow looks excellent."
    
    summary_parts = []
    
    if result_dict["errors"]:
        summary_parts.append(f"🚨 {len(result_dict['errors'])} errors found")
    
    if result_dict["warnings"]:
        summary_parts.append(f"⚠️ {len(result_dict['warnings'])} warnings")
    
    if result_dict["performance_issues"]:
        summary_parts.append(f"⚡ {len(result_dict['performance_issues'])} performance concerns")
    
    if result_dict["suggestions"]:
        summary_parts.append(f"💡 {len(result_dict['suggestions'])} suggestions")
    
    if result_dict["best_practices"]:
        summary_parts.append(f"📋 {len(result_dict['best_practices'])} best practice recommendations")
    
    return " | ".join(summary_parts) if summary_parts else "✅ No issues found"
