"""
This module provides functionality for generating professional PDF test reports.
It includes a custom FPDF class (`PDFReport`) with enhanced styling, headers,
footers, and methods for adding structured content like tables, charts, and
AI-generated summaries.
The main function `generate_report` orchestrates the process of fetching test
run data from a database, generating visualizations (pie charts, bar charts,
history charts), fetching/generating Root Cause Analysis (RCA), and compiling
everything into a well-formatted PDF document.
    Enhanced PDF Report class derived from FPDF.
    This class provides a standardized structure and styling for test reports,
    including:
    - A professional header with a title and corporate colors.
    - A footer with page numbers.
    - Consistent styling for section and subsection titles.
    - Methods for adding styled summary tables (`add_summary_table`).
    - Methods for embedding charts with captions (`add_chart`).
    - Methods for adding formatted AI analysis summaries for individual tests
      (`add_ai_summary`), highlighting failures.
    - Use of DejaVu fonts for better Unicode character support.

    Generates a comprehensive PDF report for a specific test run.
    This function connects to the specified SQLite database, fetches summary
    data, detailed test results, and historical run data for the given
    timestamp (`run_ts`). It then generates summary metrics, visual charts
    (status distribution, test duration, run history), fetches or generates
    Root Cause Analysis (RCA) for failed tests, and includes AI-generated
    analysis summaries if available. An index with internal links to AI summaries
    is also created. The final report is saved as a PDF file in the specified
    output directory.
    Args:
        run_ts: The timestamp string (e.g., 'YYYYMMDD_HHMMSS') identifying the
                specific test run to report on.
        db_path: The file path to the SQLite database containing the test results.
        output_dir: The directory where the generated PDF report will be saved.
                    Defaults to the 'reports' directory relative to the project base.
    Returns:
        A Path object representing the full path to the generated PDF file.
    Raises:
        ValueError: If no summary data is found for the specified `run_ts`.
        Exception: Propagates exceptions encountered during database access,
                   chart generation, RCA processing, or PDF creation, after logging them
                   via Streamlit messages.
"""

from fpdf import FPDF
from pathlib import Path
from datetime import datetime
import pandas as pd
import tempfile
import matplotlib.pyplot as plt
import traceback
import os
import json
import streamlit as st
import sqlite3
import time

# Import necessary functions from other modules
from helper import format_timestamp_display, perform_rca, extract_json_dict,generate_and_save_summary_if_missing

from sql_lite_db_helpers import (
    get_thread_local_connection,
    close_thread_local_connection,
    build_unified_test_entries_from_db,
    load_test_run_history,
    safe_parse_json,
    find_run_id_by_timestamp,
    save_summary,
    get_test_run_details,
    find_log_files,
    find_artifacts,
    find_run_summary,
)

import re

def strip_markdown(text: str) -> str:
    # remove **bold** and __bold__
    text = re.sub(r'(\*\*|__)(.*?)\1', r'\2', text)
    # remove `code`
    text = re.sub(r'`([^`]+)`', r'\1', text)
    # collapse multiple blank lines
    text = re.sub(r'\n{3,}', '\n\n', text)
    return text

# Define REPORTS_DIR based on expected structure
BASE_DIR = Path(__file__).resolve().parent.parent
REPORTS_DIR = BASE_DIR / "reports"
REPORTS_DIR.mkdir(parents=True, exist_ok=True)

class PDFReport(FPDF):
    """Enhanced PDF Report class with professional header, footer, and styling"""

    def __init__(self):
        super().__init__()
        # Define corporate colors
        self.primary_color = (38, 166, 154)  # Teal
        self.secondary_color = (33, 33, 33)  # Dark gray
        self.light_bg = (245, 245, 245)      # Light gray for alternating rows
        self.accent_color = (255, 87, 34)    # Orange for highlighting failures

        # Add a Unicode-compatible font
        self.add_font("DejaVu", "", "DejaVuSans.ttf", uni=True)
        self.add_font("DejaVu", "B", "DejaVuSans-Bold.ttf", uni=True)
        self.add_font("DejaVu", "I", "DejaVuSans-Oblique.ttf", uni=True)
        self.add_font("DejaVu", "BI", "DejaVuSans-BoldOblique.ttf", uni=True)
        self.set_font("DejaVu", "", 12)  # Set default font to DejaVu

    def header(self):
        # Add a header to each page with logo and title
        self.set_font("DejaVu", "B", 16)
        self.set_text_color(*self.primary_color)
        self.cell(0, 12, "Test Run Report", align="C", ln=True)

        # Add horizontal line under header
        self.ln(2)
        self.set_draw_color(*self.primary_color)
        self.set_line_width(0.8)
        self.line(10, self.get_y(), self.w - 10, self.get_y())
        self.ln(5)

    def footer(self):
        # Add a footer with page numbers
        self.set_y(-15)
        self.set_font("DejaVu", "I", 8)
        self.set_text_color(120, 120, 120)
        self.cell(0, 10, f"Page {self.page_no()}", align="C")

    def add_section_title(self, title):
        """Add a consistently styled section title"""
        self.set_font("DejaVu", "B", 14)
        self.set_text_color(*self.primary_color)
        self.cell(0, 10, title, ln=True)
        self.ln(2)

    def add_subsection_title(self, title):
        """Add a consistently styled subsection title"""
        self.set_font("DejaVu", "B", 12)
        self.set_text_color(*self.secondary_color)
        self.cell(0, 8, title, ln=True)
        self.ln(1)

    def add_summary_table(self, metrics):
        """Add a styled summary metrics table"""
        # Table styling
        col_width = (self.w - self.l_margin - self.r_margin) / 2.2
        line_height = self.font_size * 1.5

        # Table header
        self.set_font("DejaVu", "B", 10)
        self.set_fill_color(*self.primary_color)
        self.set_text_color(255, 255, 255)
        self.cell(col_width, line_height, "Metric", border=1, fill=True)
        self.cell(col_width, line_height, "Value", border=1, ln=True, fill=True)

        # Table body with alternating row colors
        self.set_font("DejaVu", "", 10)
        self.set_text_color(50, 50, 50)

        fill = False
        for key, value in metrics.items():
            fill_color = self.light_bg if fill else (255, 255, 255)
            self.set_fill_color(*fill_color)
            self.cell(col_width, line_height, key, border=1, fill=fill)
            self.cell(col_width, line_height, value, border=1, ln=True, fill=fill)
            fill = not fill

    def add_chart(self, chart_path, caption):
        """Add a chart with caption, centered on page"""
        if chart_path and Path(chart_path).exists():
            # Center the chart
            chart_width = self.w - self.l_margin - self.r_margin - 20
            x_pos = (self.w - chart_width) / 2

            # Add chart image
            self.image(chart_path, x=x_pos, w=chart_width)

            # Add caption
            self.ln(2)
            self.set_font("DejaVu", "I", 9)
            self.set_text_color(80, 80, 80)
            self.cell(0, 6, caption, ln=True, align="C")
            self.ln(6)

    def add_ai_summary(self, test_name, class_name, status, summary_content):
        """Add a nicely formatted AI summary section for a test"""
        # Test header with status indicator
        status_color = self.accent_color if status == "FAILED" else self.primary_color
        self.set_draw_color(*status_color)
        self.set_line_width(0.5)
        self.line(10, self.get_y(), self.w - 10, self.get_y())
        self.ln(2)

        # Test name and status
        self.set_font("DejaVu", "B", 11)
        self.set_text_color(*status_color)
        status_text = "[FAILED]" if status == "FAILED" else "[PASSED]"
        self.cell(0, 6, f"{status_text} {test_name}", ln=True)

        # Class name in smaller text
        self.set_font("DejaVu", "I", 9)
        self.set_text_color(100, 100, 100)
        self.cell(0, 5, f"Class: {class_name}", ln=True)
        self.ln(1)

        # Format the AI summary content
        try:
            if isinstance(summary_content, str):
                summary_dict = safe_parse_json(summary_content)
            else:
                summary_dict = summary_content

            if isinstance(summary_dict, dict):
                for section, content in summary_dict.items():
                    # Section title (Technical, User Experience, etc.)
                    section_title = section.replace('_', ' ').title()
                    self.set_font("DejaVu", "B", 10)
                    self.set_text_color(*self.secondary_color)
                    self.cell(0, 6, section_title, ln=True)

                    # Section content
                    self.set_font("DejaVu", "", 9)
                    self.set_text_color(50, 50, 50)
                    # Use multi_cell for paragraph text with proper wrapping
                    self.multi_cell(0, 5, content, align="L")
                    self.ln(2)
            else:
                # Fallback if not a dictionary
                self.set_font("DejaVu", "", 9)
                self.set_text_color(50, 50, 50)
                self.multi_cell(0, 5, str(summary_content), align="L")
        except Exception as e:
            # Handle any parsing errors
            self.set_font("DejaVu", "", 9)
            self.set_text_color(200, 0, 0)
            self.multi_cell(0, 5, f"Error parsing summary: {str(e)}", align="L")

        # Add space after each summary
        self.ln(5)

    def add_rca_section(self, rca_content):
        """Adds a formatted Root Cause Analysis section with improved readability."""
        self.add_section_title("Root Cause Analysis (RCA)")

        # Attempt to extract JSON only if the input is a string
        if isinstance(rca_content, str):
            parsed_content = extract_json_dict(rca_content)
            # If extraction failed, keep the original string; otherwise, use the parsed dict
            rca_data = parsed_content if parsed_content else rca_content
        elif isinstance(rca_content, dict):
            # If it's already a dict, use it directly
            rca_data = rca_content
        else:
            # Handle other unexpected types by converting to string
            rca_data = str(rca_content)

        # Now, proceed with rendering based on whether rca_data is a dict or string
        if isinstance(rca_data, dict):
            section_number = 1
            main_keys = ("identified_issues", "root_causes", "recommendations")  # Define order

            # Process specific keys first with numbering
            for key in main_keys:
                val = rca_data.get(key)
                if val:
                    title = f"{section_number}. {key.replace('_', ' ').title()}"
                    self.set_font("DejaVu", "B", 11)
                    self.set_text_color(*self.primary_color)
                    self.cell(0, 6, title, ln=True)
                    self.ln(1)  # Add a small space after the title

                    self.set_font("DejaVu", "", 10)
                    self.set_text_color(50, 50, 50)

                    if isinstance(val, (list, tuple)):
                        # Render each list item as a bullet
                        for item in val:
                            self.cell(8)  # Increased indent slightly
                            current_x = self.get_x()
                            self.multi_cell(
                                self.w - self.l_margin - self.r_margin - 8,  # Adjust width for indent
                                5, f"• {item}", align="L"
                            )
                            self.set_x(current_x - 8)  # Reset X position
                            self.ln(0.5)  # Small space between bullet points
                        self.ln(1)  # Add space after the list
                    else:
                        # Render string value as a paragraph
                        self.multi_cell(0, 5, str(val), align="L")
                        self.ln(2)  # Add space after the paragraph

                    section_number += 1
                    self.ln(2)  # Add extra space between main sections

            # Catch any extra keys (without numbering)
            processed_keys = set(main_keys)
            extra_keys = set(rca_data) - processed_keys
            if extra_keys:
                self.set_font("DejaVu", "B", 10)
                self.set_text_color(*self.secondary_color)
                self.cell(0, 6, "Additional Details:", ln=True)
                self.ln(1)

            for key in sorted(list(extra_keys)):  # Sort for consistent order
                val = rca_data[key]
                if val:  # Only print if value is not empty/None
                    self.set_font("DejaVu", "B", 10)
                    self.set_text_color(*self.secondary_color)
                    self.cell(0, 6, f"{key.replace('_', ' ').title()}:", ln=True)
                    self.ln(0.5)  # Small space after title

                    self.set_font("DejaVu", "", 10)
                    self.set_text_color(50, 50, 50)

                    if isinstance(val, (list, tuple)):
                        for item in val:
                            self.cell(8)  # Indent
                            current_x = self.get_x()
                            self.multi_cell(self.w - self.l_margin - self.r_margin - 8, 5, f"- {item}", align="L")
                            self.set_x(current_x - 8)
                            self.ln(0.5)  # Small space between items
                        self.ln(1)  # Space after list
                    else:
                        self.multi_cell(0, 5, str(val), align="L")
                        self.ln(1)  # Space after paragraph
                    self.ln(1)  # Extra space after each extra key-value pair
        else:
            # Fallback to a simple paragraph if rca_data is not a dict (it must be a string here)
            self.set_font("DejaVu", "", 10)
            self.set_text_color(50, 50, 50)
            clean = strip_markdown(rca_data) # Use the string version
            self.multi_cell(0, 5, clean, align="L")
            self.ln(2)

def generate_report(run_ts: str, db_path: str, output_dir: str = str(REPORTS_DIR)) -> Path:
    """Generates a PDF report with summary metrics, charts, history, RCA, and AI summaries for a given test run."""
    conn = None
    pdf_status = st.empty()  # Placeholder for status updates
    pie_chart_path = None
    bar_chart_path = None
    history_chart_path = None
    test_links = {}  # Initialize here
    index_entries = []  # Initialize here
    rca_result = None  # Initialize RCA result

    try:
        pdf_status.info("🔄 Initializing report generation...")

        # --- 1. Fetch Data ---
        pdf_status.info("🔄 Connecting to database and fetching run summary...")
        conn = get_thread_local_connection(db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT total_tests, passed_tests, failed_tests, skipped_tests, duration
            FROM test_runs
            WHERE timestamp = ?
        """, (run_ts,))
        run_summary_data = cursor.fetchone()
        close_thread_local_connection()
        conn = None

        if not run_summary_data:
            raise ValueError(f"No summary data found for run timestamp: {run_ts}")

        total_tests, passed_tests, failed_tests, skipped_tests, duration = run_summary_data
        pass_rate = round((passed_tests / total_tests) * 100, 1) if total_tests > 0 else 0
        duration_str = f"{duration:.2f}s" if duration is not None else "N/A"
        display_ts = format_timestamp_display(run_ts)

        pdf_status.info("🔄 Fetching detailed test entries for the selected run...")
        test_entries = build_unified_test_entries_from_db(db_path, run_ts)
        close_thread_local_connection()

        if not test_entries:
            st.warning(f"No detailed test entries found for run {run_ts}. Some charts and details may be missing.")
            df = pd.DataFrame()
        else:
            df = pd.DataFrame(test_entries)
        pdf_status.info(f"📊 Found {len(df)} detailed test entries for this run.")

        # --- Check for missing AI summaries ---
        missing_ai_summaries = 0
        if not df.empty and 'visual_analysis' in df.columns:
            # Count rows where visual_analysis is None, NaN, empty string, or empty dict
            missing_ai_summaries = (
                df['visual_analysis'].isnull().sum()
                + df[df['visual_analysis'].notnull()]['visual_analysis']
                    .apply(lambda x: not x or (isinstance(x, dict) and not x))
                    .sum()
            )

        if missing_ai_summaries > 0:
            dialog_placeholder = st.empty()
            with dialog_placeholder.container():
                st.warning(
                    f"⚠️ Found {missing_ai_summaries} test case(s) without generated AI summaries. "
                    "These summaries will be missing from the report."
                )
                st.info("How would you like to proceed?")

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.button("Continue without summaries", key="proceed_btn")
                with col2:
                    generate_first = st.button("Generate missing summaries", key="generate_btn")
                with col3:
                    cancel_report = st.button("Cancel report", key="cancel_btn")

            # Handle user choice
            if cancel_report:
                dialog_placeholder.empty()
                st.warning("Report generation canceled by user.")
                return None  # Exit the function

            if generate_first:
                dialog_placeholder.empty()
                # Get missing test cases
                missing_tests = df[
                    df['visual_analysis'].isnull()
                    | df['visual_analysis'].apply(lambda x: not x or (isinstance(x, dict) and not x))
                ]

                progress_bar = st.progress(0)
                status_text = st.empty()


                status_text.info("Generating missing AI summaries...")

                # Bulk generate missing AI summaries
                results = {
                    'generated': 0,
                    'skipped_exists': 0,
                    'skipped_missing_artifacts': 0,
                    'error': 0
                }
                total_missing = len(missing_tests)
                for idx, case in enumerate(missing_tests.to_dict('records')):
                    case_id = case.get('case_id')
                    if not case_id:
                        continue
                    status_text.text(f"Processing {idx+1}/{total_missing}: {case.get('test_name', case_id)}")
                    try:
                        # This function should generate and save only if missing
                        status = generate_and_save_summary_if_missing(
                            case_id, db_path,
                            st.session_state.model,
                            st.session_state.api_key
                        )
                        results[status] = results.get(status, 0) + 1
                    except Exception as err:
                        results['error'] += 1
                        st.error(f"Error generating summary for case {case_id}: {err}")
                    progress_bar.progress((idx + 1) / total_missing)

                # Show bulk‐generation results
                progress_bar.empty()
                status_text.success(
                    f"Bulk generation complete: "
                    f"{results['generated']} generated, "
                    f"{results['skipped_exists']} skipped (already exist), "
                    f"{results['skipped_missing_artifacts']} skipped (no artifacts), "
                    f"{results['error']} errors."
                )

                # Refresh test entries and DataFrame
                test_entries = build_unified_test_entries_from_db(db_path, run_ts)
                close_thread_local_connection()
                df = pd.DataFrame(test_entries)
                time.sleep(1)

                progress_bar.empty()
                status_text.success("✅ AI summaries generated successfully! Continuing with report...")

                # Refresh test data with newly generated summaries
                test_entries = build_unified_test_entries_from_db(db_path, run_ts)
                close_thread_local_connection()
                df = pd.DataFrame(test_entries)

                time.sleep(1)

            # Proceed anyway or after generation
            dialog_placeholder.empty()

        pdf_status.info("🔄 Fetching test run history...")
        history_data_raw = load_test_run_history(db_path, limit=20)
        close_thread_local_connection()

        history_data = []
        if history_data_raw:
            for entry in reversed(history_data_raw):
                passed = entry.get("passed_tests", 0)
                failed = entry.get("failed_tests", 0)
                try:
                    dt_obj = datetime.strptime(entry["timestamp"], "%Y%m%d_%H%M%S")
                except ValueError:
                    dt_obj = datetime.now()
                history_data.append({
                    "datetime": dt_obj,
                    "Timestamp": entry["timestamp"],
                    "Run Time": format_timestamp_display(entry["timestamp"]),
                    "Passed": passed,
                    "Failed": failed,
                })
        history_df = pd.DataFrame(history_data)
        pdf_status.info(f"📈 Found {len(history_df)} history entries.")
        # --- 2. Prepare Output Path ---
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        pdf_filename = output_path / f"report_{run_ts}.pdf"

        # --- 2.5 Fetch or Generate RCA ---
        pdf_status.info("🔄 Checking for existing Root Cause Analysis (RCA)...")
        run_id = find_run_id_by_timestamp(db_path, run_ts)
        close_thread_local_connection()

        if run_id:
            rca_summaries = find_run_summary(db_path, run_id=run_id, summary_type='rca')
            close_thread_local_connection()

            if rca_summaries:
                latest_rca_summary = rca_summaries[0]
                rca_content_str = latest_rca_summary.get('summary_content')
                if rca_content_str:
                    rca_result = safe_parse_json(rca_content_str)
                    if not rca_result: # If JSON parsing failed, use the raw string
                        rca_result = rca_content_str
                    pdf_status.info("✅ Found existing RCA results.")
                else:
                    pdf_status.warning("Found RCA summary record, but content is empty. Will attempt generation.")
                    rca_result = None # Ensure generation is triggered
            else:
                pdf_status.info("ℹ️ No existing RCA found for this run. Attempting generation...")
                rca_result = None # Ensure generation is triggered

            # --- Generate RCA if not found or empty ---
            if rca_result is None:
                pdf_status.info("⚙️ Preparing data for RCA generation...")
                failed_test_details = []
                all_test_cases = get_test_run_details(db_path, run_ts)
                close_thread_local_connection()

                if not all_test_cases:
                    st.warning(f"Could not retrieve test details for run {run_ts} to generate RCA.")
                    rca_result = "Could not retrieve test details for RCA generation."
                else:
                    for case in all_test_cases:
                        if case["result"] == "FAILED":
                            details = {
                                "db_case_id": case["case_id"],
                                "test_name": case["name"],
                                "class_name": case["classname"],
                                "failure_message": case.get("failure_message", ""),
                                "log_snippet": None,
                                "ai_analysis": None
                            }

                            # Find log snippet
                            log_artifact = find_log_files(db_path, case["case_id"])
                            close_thread_local_connection()
                            log_path = log_artifact['path'] if log_artifact else None
                            if log_path and os.path.exists(log_path):
                                try:
                                    with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                                        details["log_snippet"] = f.read(2000) # Limit snippet size
                                except Exception as log_read_err:
                                    details["log_snippet"] = f"Error reading log: {log_read_err}"
                            else:
                                details["log_snippet"] = "Log file not found or not linked."

                            # Find latest AI analysis artifact
                            analysis_artifacts = find_artifacts(db_path, case["case_id"], artifact_type='visual_analysis')
                            close_thread_local_connection()
                            latest_analysis_artifact = analysis_artifacts[0] if analysis_artifacts else None
                            if latest_analysis_artifact:
                                summary_content = latest_analysis_artifact.get('summary_content')
                                if summary_content:
                                    summary_data = safe_parse_json(summary_content)
                                    details['ai_analysis'] = summary_data if summary_data else {"raw_text": summary_content}
                                else:
                                    details['ai_analysis'] = {"error": "AI analysis artifact found but content is missing."}

                            failed_test_details.append(details)

                    if not failed_test_details:
                        st.info(f"No failed tests found in run {run_ts}, skipping RCA generation.")
                        rca_result = "No failed tests found in this run."
                    else:
                        pdf_status.info(f"🤖 Generating RCA based on {len(failed_test_details)} failed test(s)... (This may take time)")

                        # --- Get model and API key from Streamlit session state ---
                        try:
                            model_name = st.session_state.model
                            api_key = st.session_state.api_key
                            # Assuming google_timestamps and google_tokens might be in session state or None
                            google_timestamps = st.session_state.get('google_timestamps', None)
                            google_tokens = st.session_state.get('google_tokens', None)
                            pdf_status.info(f"Using model: {model_name} for RCA.")
                        except AttributeError as e:
                            st.warning(f"Streamlit session state not found for model/API key ({e}). Using defaults.")
                            model_name = "gemini-1.5-flash-latest" # Default model
                            api_key = None # Default API key
                            google_timestamps = None
                            google_tokens = None
                        except Exception as session_err:
                            st.error(f"Error accessing Streamlit session state: {session_err}")
                            # Decide how to handle this - stop or use defaults? Using defaults for now.
                            model_name = "gemini-1.5-flash-latest"
                            api_key = None
                            google_timestamps = None
                            google_tokens = None
                        # --- Perform RCA ---
                        try:
                            rca_result = perform_rca(
                                failed_test_details,
                                model_name=model_name,
                                api_key=api_key,
                                timestamps_list=google_timestamps, # Pass potentially retrieved timestamps
                                token_usage_list=google_tokens    # Pass potentially retrieved tokens
                            )

                            # --- Save generated RCA ---
                            if rca_result and not (isinstance(rca_result, dict) and "error" in rca_result):
                                pdf_status.info("✅ RCA generated successfully. Saving to database...")
                                try:
                                    # Ensure rca_result is stored as a JSON string if it's a dict
                                    rca_content_to_save = json.dumps(rca_result) if isinstance(rca_result, dict) else str(rca_result)
                                    save_summary(db_path, None, model_name, 'rca', rca_content_to_save, run_id=run_id)
                                    close_thread_local_connection()
                                    pdf_status.info("💾 RCA result saved.")
                                except sqlite3.Error as db_err:
                                    st.warning(f"DB Error saving generated RCA: {db_err}")
                                    close_thread_local_connection() # Ensure connection is closed on error
                                except Exception as save_err:
                                    st.warning(f"Error saving generated RCA: {save_err}")
                                    close_thread_local_connection() # Ensure connection is closed on error
                            elif isinstance(rca_result, dict) and "error" in rca_result:
                                st.warning(f"RCA generation failed: {rca_result['error']}")
                                rca_result = f"RCA Generation Error: {rca_result['error']}" # Keep error message for report
                            else:
                                st.warning("RCA generation returned empty or unexpected result.")
                                rca_result = "RCA generation did not produce a result."

                        except sqlite3.Error as db_err:
                            st.error(f"Database error during RCA data preparation: {db_err}")
                            close_thread_local_connection()
                            rca_result = "Error preparing data for RCA."
                        except Exception as general_err:
                            st.error(f"An unexpected error occurred during RCA processing: {general_err}")
                            st.expander("Show RCA Error Details").code(traceback.format_exc())
                            close_thread_local_connection()
                            rca_result = f"Unexpected error during RCA processing: {general_err}"

            # --- End of RCA Generation Block ---

        else: # if run_id:
            st.warning(f"Could not find run ID for timestamp {run_ts}. Cannot fetch or generate RCA.")
            rca_result = "Run ID not found, RCA skipped."

        # --- The original code continues here, starting with chart generation ---
        # Note: The original 'if rca_result is None:' block later in the function
        # is now redundant because RCA generation is handled above.
        # It should be removed or commented out to avoid duplication.

        # --- 3. Generate Charts (in temp dir) ---
        pdf_status.info("🔄 Generating charts with Matplotlib...")
        with tempfile.TemporaryDirectory() as tmpdir:
            tmpdir_path = Path(tmpdir)

            plt.style.use('seaborn-v0_8-whitegrid')
            colors = {
                "PASSED": "#26A69A",
                "FAILED": "#FF5252",
                "SKIPPED": "#FFC107",
                "ERROR": "#FF9800"
            }

            if not df.empty and 'result' in df.columns:
                try:
                    counts = df['result'].value_counts()
                    fig, ax = plt.subplots(figsize=(6, 4))
                    chart_colors = [colors.get(label, '#808080') for label in counts.index]
                    _, texts, autotexts = ax.pie(
                        counts,
                        labels=counts.index,
                        autopct='%1.1f%%',
                        startangle=90,
                        colors=chart_colors,
                        explode=[0.05] * len(counts),
                        shadow=True,
                        wedgeprops={'edgecolor': 'white', 'linewidth': 1}
                    )
                    for text in texts:
                        text.set_fontsize(9)
                    for autotext in autotexts:
                        autotext.set_fontsize(9)
                        autotext.set_fontweight('bold')

                    ax.set_title(f"Test Status Distribution ({display_ts})", fontsize=12, fontweight='bold')
                    fig.tight_layout()
                    pie_path = tmpdir_path / f"pie_{run_ts}.png"
                    fig.savefig(pie_path, bbox_inches='tight', dpi=150)
                    plt.close(fig)
                    pie_chart_path = str(pie_path)
                except Exception as e:
                    if 'fig' in locals(): plt.close(fig)
                    st.warning(f"Could not generate pie chart: {e}")

            if not df.empty and 'duration' in df.columns and df['duration'].notna().any():
                try:
                    top = df.dropna(subset=['duration']).nlargest(10, 'duration')
                    if not top.empty:
                        fig, ax = plt.subplots(figsize=(8, 5))
                        chart_colors = [colors.get(res, '#808080') for res in top['result']]

                        test_names = [name[:30] + '...' if len(name) > 30 else name for name in top['test_name']]

                        bars = ax.barh(test_names, top['duration'], color=chart_colors)
                        ax.invert_yaxis()
                        ax.set_xlabel("Duration (seconds)", fontsize=10)
                        ax.set_title(f"Top {len(top)} Longest Tests ({display_ts})", fontsize=12, fontweight='bold')

                        ax.grid(axis='x', linestyle='--', alpha=0.5)

                        for bar in bars:
                            width = bar.get_width()
                            ax.text(width + 0.3, bar.get_y() + bar.get_height()/2,
                                  f'{width:.2f}s', va='center', fontsize=8)

                        plt.tight_layout()
                        bar_path = tmpdir_path / f"duration_{run_ts}.png"
                        fig.savefig(bar_path, bbox_inches='tight', dpi=150)
                        plt.close(fig)
                        bar_chart_path = str(bar_path)
                except Exception as e:
                    if 'fig' in locals(): plt.close(fig)
                    st.warning(f"Could not generate bar chart: {e}")

            if not history_df.empty:
                try:
                    fig, ax = plt.subplots(figsize=(8, 4))

                    ax.plot(history_df['datetime'], history_df['Passed'],
                            label='Passed', color=colors["PASSED"],
                            marker='o', linewidth=2.5, markersize=7)
                    ax.plot(history_df['datetime'], history_df['Failed'],
                            label='Failed', color=colors["FAILED"],
                            marker='X', linewidth=2.5, markersize=7)

                    import matplotlib.dates as mdates
                    date_format = mdates.DateFormatter('%m/%d %H:%M')
                    ax.xaxis.set_major_formatter(date_format)

                    ax.set_xlabel("Run Date/Time", fontsize=10)
                    ax.set_ylabel("Test Count", fontsize=10)
                    ax.set_title("Test Results History (Recent Runs)", fontsize=12, fontweight='bold')
                    ax.legend(loc='best', framealpha=0.7)
                    ax.grid(True, linestyle='-', alpha=0.3)

                    plt.xticks(rotation=30, ha='right')
                    plt.tight_layout()

                    history_path = tmpdir_path / f"history_{run_ts}.png"
                    fig.savefig(history_path, bbox_inches='tight', dpi=150)
                    plt.close(fig)
                    history_chart_path = str(history_path)
                except Exception as e:
                    if 'fig' in locals(): plt.close(fig)
                    st.warning(f"Could not generate history chart: {e}")

            pdf_status.info("🔄 Assembling professional PDF document...")

            pdf = PDFReport()
            pdf.set_auto_page_break(auto=True, margin=15)
            pdf.add_page()

            pdf.set_font("DejaVu", "", 11)
            pdf.set_text_color(70, 70, 70)
            pdf.cell(0, 8, f"Report for Run: {display_ts}", ln=True)
            pdf.ln(5)

            pdf.add_section_title("Run Summary")
            metrics = {
                "Total Tests": str(total_tests),
                "Passed Tests": str(passed_tests),
                "Failed Tests": str(failed_tests),
                "Skipped Tests": str(skipped_tests or 0),
                "Pass Rate": f"{pass_rate}%",
                "Total Duration": duration_str
            }
            pdf.add_summary_table(metrics)
            pdf.ln(8)

            # --- Create Index Data ---
            if not df.empty and 'visual_analysis' in df.columns and df['visual_analysis'].notna().any():
                ai_df = df[df['visual_analysis'].notna()]
                # Sort index entries: Failed first, then by class name, then by test name
                ai_df_sorted = ai_df.sort_values(by=['result', 'class_name', 'test_name'], ascending=[False, True, True])
                for _, row in ai_df_sorted.iterrows():
                    test_id = f"{row['test_name']}|{row['class_name']}"
                    test_links[test_id] = pdf.add_link()
                    index_entries.append({
                        "test_id": test_id,
                        "test_name": row['test_name'],
                        "class_name": row['class_name'],
                        "status": row['result']
                    })

                # --- Add Index Page ---
                pdf.add_page()
                pdf.add_section_title("Index of AI Summaries")
                pdf.ln(2)

                # Define column widths (adjust as needed)
                page_width = pdf.w - pdf.l_margin - pdf.r_margin
                col_num_width = 15
                col_status_width = 25
                col_name_width = page_width - col_num_width - col_status_width

                # Table Header
                pdf.set_font("DejaVu", "B", 10)
                pdf.set_fill_color(*pdf.primary_color)
                pdf.set_text_color(255, 255, 255)
                line_height = pdf.font_size * 1.5
                pdf.cell(col_num_width, line_height, "#", border=1, fill=True, align="C")
                pdf.cell(col_name_width, line_height, "Test Case", border=1, fill=True, align="C")
                pdf.cell(col_status_width, line_height, "Status", border=1, fill=True, align="C", ln=True)

                # Table Body
                pdf.set_font("DejaVu", "", 9)
                pdf.set_text_color(50, 50, 50)
                fill = False
                default_text_color = (50, 50, 50) # Define default color tuple
                for i, entry in enumerate(index_entries):
                    fill_color = pdf.light_bg if fill else (255, 255, 255)
                    pdf.set_fill_color(*fill_color)

                    # Number column
                    pdf.cell(col_num_width, line_height, str(i + 1), border=1, fill=fill, align="C")

                    # Name column (with link)
                    display_name = f"{entry['test_name']} ({entry['class_name']})"
                    # Add link only to the name cell for clarity
                    pdf.set_text_color(0, 0, 200) # Blue for link
                    pdf.cell(col_name_width, line_height, display_name, border=1, fill=fill, align="L", link=test_links[entry["test_id"]])
                    pdf.set_text_color(*default_text_color) # Reset text color using the defined tuple

                    # Status column
                    status = entry["status"]
                    status_color = pdf.accent_color if status == "FAILED" else pdf.primary_color
                    # No need to save original_text_color if we always reset to default
                    pdf.set_text_color(*status_color)
                    pdf.set_font("DejaVu", "B", 9) # Bold status
                    pdf.cell(col_status_width, line_height, status, border=1, fill=fill, align="C", ln=True)
                    pdf.set_font("DejaVu", "", 9) # Reset font
                    pdf.set_text_color(*default_text_color) # Restore default text color

                    fill = not fill

                pdf.ln(5) # Space after table

            # --- Visual Analysis Page ---
            pdf.add_page()
            pdf.add_section_title("Visual Analysis")

            if history_chart_path:
                pdf.add_chart(history_chart_path, "Test Results History (Recent Runs)")

            if pie_chart_path:
                if pdf.get_y() > pdf.h - 100:
                    pdf.add_page()
                    pdf.add_section_title("Visual Analysis (Continued)")
                pdf.add_chart(pie_chart_path, f"Test Status Distribution ({display_ts})")

            if bar_chart_path:
                if pdf.get_y() > pdf.h - 120:
                    pdf.add_page()
                    pdf.add_section_title("Visual Analysis (Continued)")
                pdf.add_chart(bar_chart_path, f"Top 10 Longest Tests ({display_ts})")

            # --- Performance Metrics Page ---
            # Extract performance metrics from the run data
            performance_metrics = None
            # First try to get performance metrics from the current run summary
            run_id = find_run_id_by_timestamp(db_path, run_ts)
            close_thread_local_connection()

            run_summary = None
            if run_id:
                # use the performance summary, not the RCA one
                run_summary = find_run_summary(db_path, run_id, 'performance')
                close_thread_local_connection()

            if run_summary and run_summary[0].get('summary_content'):
                try:
                    summary_data = safe_parse_json(run_summary[0].get('summary_content', '{}'))
                    if isinstance(summary_data, dict):
                        # if it has a nested "performance" object, use that; otherwise use the dict itself
                        if 'performance' in summary_data and isinstance(summary_data['performance'], dict):
                            performance_metrics = summary_data['performance']
                        else:
                            performance_metrics = summary_data
                except Exception as e:
                    pdf_status.warning(f"Error parsing run summary for performance metrics: {e}")

            # Extract test case level performance metrics from the DataFrame
            test_case_metrics = {}
            if not df.empty and 'performance_metrics' in df.columns:
                # Filter out rows with no performance metrics
                df_with_metrics = df[df['performance_metrics'].notna() & df['performance_metrics'].apply(lambda x: bool(x) if isinstance(x, dict) else False)]

                if not df_with_metrics.empty:
                    # Create a dictionary of test case metrics for easier access
                    for _, row in df_with_metrics.iterrows():
                        test_name = row['test_name']
                        metrics = row['performance_metrics']
                        if isinstance(metrics, dict) and metrics:
                            test_case_metrics[test_name] = metrics

            # Determine which metrics source to use for charts and summary
            metrics_for_charts = {}
            if performance_metrics and any(performance_metrics.values()):
                metrics_for_charts = performance_metrics
                pdf_status.info("Using run-level performance metrics for charts.")
            elif test_case_metrics:
                # Calculate averages from test case metrics if run-level is empty
                pdf_status.info("Calculating average performance metrics from test cases for charts.")
                metric_values = {
                    'execution_time': [],
                    'page_load_time': [],
                    'memory_usage': [],
                    'cpu_usage': [],
                    'network_requests': [],
                    'network_bytes': []
                }
                for test_name, metrics in test_case_metrics.items():
                    for metric_key in metric_values.keys():
                        value = metrics.get(metric_key)
                        if value is not None:
                            metric_values[metric_key].append(value)

                if metric_values['execution_time']:
                    metrics_for_charts['avg_execution_time'] = sum(metric_values['execution_time']) / len(metric_values['execution_time'])
                if metric_values['page_load_time']:
                    avg_page_load = sum(metric_values['page_load_time']) / len(metric_values['page_load_time'])
                    metrics_for_charts['avg_page_load_time'] = avg_page_load
                    metrics_for_charts['max_page_load_time'] = max(metric_values['page_load_time']) # Include max from cases
                if metric_values['memory_usage']:
                    avg_memory = sum(metric_values['memory_usage']) / len(metric_values['memory_usage'])
                    metrics_for_charts['avg_memory_usage'] = avg_memory
                    metrics_for_charts['max_memory_usage'] = max(metric_values['memory_usage']) # Include max from cases
                if metric_values['cpu_usage']:
                    avg_cpu = sum(metric_values['cpu_usage']) / len(metric_values['cpu_usage'])
                    metrics_for_charts['avg_cpu_usage'] = avg_cpu
                    metrics_for_charts['max_cpu_usage'] = max(metric_values['cpu_usage']) # Include max from cases
                if metric_values['network_requests']:
                     metrics_for_charts['total_network_requests'] = sum(metric_values['network_requests']) # Sum for total requests
                if metric_values['network_bytes']:
                     metrics_for_charts['total_network_bytes'] = sum(metric_values['network_bytes']) # Sum for total bytes


            if metrics_for_charts or test_case_metrics: # Check if either source has data
                pdf.add_page()
                pdf.add_section_title("Performance Metrics")

                # Create a temporary directory for performance metrics charts
                # Use a context manager to ensure cleanup
                with tempfile.TemporaryDirectory(prefix="report_perf_") as perf_tmpdir:
                    perf_tmpdir_path = Path(perf_tmpdir)

                    # --- Execution Time Metrics Chart ---
                    # Use metrics_for_charts for the data
                    if any(k in metrics_for_charts for k in ['avg_execution_time', 'max_execution_time']):
                        try:
                            fig_exec, ax_exec = plt.subplots(figsize=(8, 4))
                            metrics = []
                            values = []

                            if 'avg_execution_time' in metrics_for_charts:
                                metrics.append('Avg Execution Time')
                                values.append(metrics_for_charts['avg_execution_time'])
                            if 'max_execution_time' in metrics_for_charts:
                                metrics.append('Max Execution Time')
                                values.append(metrics_for_charts['max_execution_time'])

                            # Use distinct colors for Avg/Max
                            bars = ax_exec.bar(metrics, values, color=['#1f77b4', '#ff7f0e']) # Blue and Orange
                            ax_exec.set_ylabel('Time (seconds)')
                            ax_exec.set_title('Test Execution Time')

                            # Add horizontal grid lines
                            ax_exec.grid(axis='y', linestyle='--', alpha=0.5)

                            # Add value labels on top of bars
                            for bar in bars:
                                height = bar.get_height()
                                ax_exec.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                                        f'{height:.2f}s', ha='center', va='bottom')

                            plt.tight_layout()
                            exec_time_path = perf_tmpdir_path / f"exec_time_{run_ts}.png"
                            fig_exec.savefig(exec_time_path, bbox_inches='tight', dpi=150)
                            plt.close(fig_exec)

                            pdf.add_chart(str(exec_time_path), "Test Execution Time Metrics")
                        except Exception as e:
                            if 'fig_exec' in locals(): plt.close(fig_exec)
                            pdf.set_font("DejaVu", "I", 10)
                            pdf.cell(0, 10, f"Could not generate execution time chart: {e}", ln=True)

                    # --- Page Load Time Metrics Chart ---
                    # Use metrics_for_charts for the data
                    if any(k in metrics_for_charts for k in ['avg_page_load_time', 'max_page_load_time']):
                        if pdf.get_y() > pdf.h - 120:
                            pdf.add_page()
                            pdf.add_section_title("Performance Metrics (Continued)")

                        try:
                            fig_page, ax_page = plt.subplots(figsize=(8, 4))
                            metrics = []
                            values = []

                            if 'avg_page_load_time' in metrics_for_charts:
                                metrics.append('Avg Page Load Time')
                                values.append(metrics_for_charts['avg_page_load_time'])
                            if 'max_page_load_time' in metrics_for_charts:
                                metrics.append('Max Page Load Time')
                                values.append(metrics_for_charts['max_page_load_time'])

                            # Use distinct colors for Avg/Max
                            bars = ax_page.bar(metrics, values, color=['#2ca02c', '#d62728']) # Green and Red
                            ax_page.set_ylabel('Time (milliseconds)')
                            ax_page.set_title('Page Load Time')

                            # Add horizontal grid lines
                            ax_page.grid(axis='y', linestyle='--', alpha=0.5)

                            # Add value labels on top of bars
                            for bar in bars:
                                height = bar.get_height()
                                ax_page.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                                        f'{height:.0f}ms', ha='center', va='bottom')

                            plt.tight_layout()
                            page_load_path = perf_tmpdir_path / f"page_load_{run_ts}.png"
                            fig_page.savefig(page_load_path, bbox_inches='tight', dpi=150)
                            plt.close(fig_page)

                            pdf.add_chart(str(page_load_path), "Page Load Time Metrics")
                        except Exception as e:
                            if 'fig_page' in locals(): plt.close(fig_page)
                            pdf.set_font("DejaVu", "I", 10)
                            pdf.cell(0, 10, f"Could not generate page load time chart: {e}", ln=True)

                        # --- Resource Usage Metrics Charts ---
                        # Use metrics_for_charts for the data
                        if any(k in metrics_for_charts for k in ['avg_memory_usage', 'max_memory_usage', 'avg_cpu_usage', 'max_cpu_usage']):
                            if pdf.get_y() > pdf.h - 120:
                                pdf.add_page()
                                pdf.add_section_title("Performance Metrics (Continued)")

                            # Memory Usage Chart
                            if any(k in metrics_for_charts for k in ['avg_memory_usage', 'max_memory_usage']):
                                try:
                                    fig_mem, ax_mem = plt.subplots(figsize=(8, 4))
                                    metrics = []
                                    values = []

                                    if 'avg_memory_usage' in metrics_for_charts:
                                        metrics.append('Avg Memory Usage')
                                        values.append(metrics_for_charts['avg_memory_usage'])
                                    if 'max_memory_usage' in metrics_for_charts:
                                        metrics.append('Max Memory Usage')
                                        values.append(metrics_for_charts['max_memory_usage'])

                                    # Use distinct colors for Avg/Max
                                    bars = ax_mem.bar(metrics, values, color=['#9467bd', '#8c564b']) # Purple and Brown
                                    ax_mem.set_ylabel('Memory (MB)')
                                    ax_mem.set_title('Memory Usage')

                                    # Add horizontal grid lines
                                    ax_mem.grid(axis='y', linestyle='--', alpha=0.5)

                                    # Add value labels on top of bars
                                    for bar in bars:
                                        height = bar.get_height()
                                        ax_mem.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                                                f'{height:.2f}MB', ha='center', va='bottom')

                                    plt.tight_layout()
                                    memory_path = perf_tmpdir_path / f"memory_{run_ts}.png"
                                    fig_mem.savefig(memory_path, bbox_inches='tight', dpi=150)
                                    plt.close(fig_mem)

                                    pdf.add_chart(str(memory_path), "Memory Usage Metrics")
                                except Exception as e:
                                    if 'fig_mem' in locals(): plt.close(fig_mem)
                                    pdf.set_font("DejaVu", "I", 10)
                                    pdf.cell(0, 10, f"Could not generate memory usage chart: {e}", ln=True)

                            # CPU Usage Chart
                            if any(k in metrics_for_charts for k in ['avg_cpu_usage', 'max_cpu_usage']):
                                if pdf.get_y() > pdf.h - 120:
                                    pdf.add_page()
                                    pdf.add_section_title("Performance Metrics (Continued)")

                                try:
                                    fig_cpu, ax_cpu = plt.subplots(figsize=(8, 4))
                                    metrics = []
                                    values = []

                                    if 'avg_cpu_usage' in metrics_for_charts:
                                        metrics.append('Avg CPU Usage')
                                        values.append(metrics_for_charts['avg_cpu_usage'])
                                    if 'max_cpu_usage' in metrics_for_charts:
                                        metrics.append('Max CPU Usage')
                                        values.append(metrics_for_charts['max_cpu_usage'])

                                    # Use distinct colors for Avg/Max
                                    bars = ax_cpu.bar(metrics, values, color=['#e377c2', '#7f7f7f']) # Pink and Gray
                                    ax_cpu.set_ylabel('CPU Usage (%)')
                                    ax_cpu.set_title('CPU Usage')
                                    ax_cpu.set_ylim(0, 100)  # CPU usage is a percentage

                                    # Add horizontal grid lines
                                    ax_cpu.grid(axis='y', linestyle='--', alpha=0.5)

                                    # Add value labels on top of bars
                                    for bar in bars:
                                        height = bar.get_height()
                                        ax_cpu.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                                                f'{height:.2f}%', ha='center', va='bottom')

                                    plt.tight_layout()
                                    cpu_path = perf_tmpdir_path / f"cpu_{run_ts}.png"
                                    fig_cpu.savefig(cpu_path, bbox_inches='tight', dpi=150)
                                    plt.close(fig_cpu)

                                    pdf.add_chart(str(cpu_path), "CPU Usage Metrics")
                                except Exception as e:
                                    if 'fig_cpu' in locals(): plt.close(fig_cpu)
                                    pdf.set_font("DejaVu", "I", 10)
                                    pdf.cell(0, 10, f"Could not generate CPU usage chart: {e}", ln=True)

                        # --- Network Metrics Charts ---
                        # Use metrics_for_charts for the data
                        if any(k in metrics_for_charts for k in ['total_network_requests', 'total_network_bytes']):
                            if pdf.get_y() > pdf.h - 120:
                                pdf.add_page()
                                pdf.add_section_title("Performance Metrics (Continued)")

                            try:
                                fig_net, (ax_req, ax_bytes) = plt.subplots(1, 2, figsize=(10, 4))

                                # Network Requests
                                if 'total_network_requests' in metrics_for_charts:
                                    ax_req.bar(['Network Requests'], [metrics_for_charts['total_network_requests']], color='#17becf') # Cyan
                                    ax_req.set_ylabel('Count')
                                    ax_req.set_title('Total Network Requests')
                                    ax_req.grid(axis='y', linestyle='--', alpha=0.5)
                                    # Add value label
                                    ax_req.text(0, metrics_for_charts['total_network_requests'] + 0.1,
                                            f"{metrics_for_charts['total_network_requests']}", ha='center', va='bottom')

                                # Network Data
                                if 'total_network_bytes' in metrics_for_charts:
                                    ax_bytes.bar(['Network Data'], [metrics_for_charts['total_network_bytes']], color='#bcbd22') # Olive
                                    ax_bytes.set_ylabel('Data (KB)')
                                    ax_bytes.set_title('Total Network Data Transfer')
                                    ax_bytes.grid(axis='y', linestyle='--', alpha=0.5)
                                    # Add value label
                                    ax_bytes.text(0, metrics_for_charts['total_network_bytes'] + 0.1,
                                            f"{metrics_for_charts['total_network_bytes']}KB", ha='center', va='bottom')

                                plt.tight_layout()
                                network_path = perf_tmpdir_path / f"network_{run_ts}.png"
                                fig_net.savefig(network_path, bbox_inches='tight', dpi=150)
                                plt.close(fig_net)

                                pdf.add_chart(str(network_path), "Network Metrics")
                            except Exception as e:
                                if 'fig_net' in locals(): plt.close(fig_net)
                                pdf.set_font("DejaVu", "I", 10)
                                pdf.cell(0, 10, f"Could not generate network metrics chart: {e}", ln=True)

                # Add performance metrics summary table(s)
                if pdf.get_y() > pdf.h - 120:
                    pdf.add_page()
                    pdf.add_section_title("Performance Metrics Summary")
                else:
                    pdf.add_section_title("Performance Metrics Summary")

                # Display Run-Level Summary if available
                run_level_perf_metrics_table = {}
                if performance_metrics and any(performance_metrics.values()):
                    # Execution Time Metrics
                    if 'avg_execution_time' in performance_metrics:
                        run_level_perf_metrics_table["Avg Execution Time (Run)"] = f"{performance_metrics['avg_execution_time']:.2f}s"
                    if 'max_execution_time' in performance_metrics:
                        run_level_perf_metrics_table["Max Execution Time (Run)"] = f"{performance_metrics['max_execution_time']:.2f}s"

                    # Page Load Time Metrics
                    if 'avg_page_load_time' in performance_metrics:
                        run_level_perf_metrics_table["Avg Page Load Time (Run)"] = f"{performance_metrics['avg_page_load_time']:.0f}ms"
                    if 'max_page_load_time' in performance_metrics:
                        run_level_perf_metrics_table["Max Page Load Time (Run)"] = f"{performance_metrics['max_page_load_time']:.0f}ms"

                    # Resource Usage Metrics
                    if 'avg_memory_usage' in performance_metrics:
                        run_level_perf_metrics_table["Avg Memory Usage (Run)"] = f"{performance_metrics['avg_memory_usage']:.2f}MB"
                    if 'max_memory_usage' in performance_metrics:
                        run_level_perf_metrics_table["Max Memory Usage (Run)"] = f"{performance_metrics['max_memory_usage']:.2f}MB"
                    if 'avg_cpu_usage' in performance_metrics:
                        run_level_perf_metrics_table["Avg CPU Usage (Run)"] = f"{performance_metrics['avg_cpu_usage']:.2f}%"
                    if 'max_cpu_usage' in performance_metrics:
                        run_level_perf_metrics_table["Max CPU Usage (Run)"] = f"{performance_metrics['max_cpu_usage']:.2f}%"

                    # Network Metrics
                    if 'total_network_requests' in performance_metrics:
                        run_level_perf_metrics_table["Total Network Requests (Run)"] = str(performance_metrics['total_network_requests'])
                    if 'total_network_bytes' in performance_metrics:
                        run_level_perf_metrics_table["Total Network Data (Run)"] = f"{performance_metrics['total_network_bytes']}KB"

                    if run_level_perf_metrics_table:
                        pdf.set_font("DejaVu", "B", 10)
                        pdf.set_text_color(*pdf.secondary_color)
                        pdf.cell(0, 8, "Run-Level Summary:", ln=True)
                        pdf.ln(1)
                        pdf.add_summary_table(run_level_perf_metrics_table)
                        pdf.ln(5) # Add space after the first table

                # Display Calculated Average Metrics from Test Cases if available
                if test_case_metrics:
                    calculated_avg_metrics = {}
                    metric_values = {
                        'execution_time': [],
                        'page_load_time': [],
                        'memory_usage': [],
                        'cpu_usage': [],
                        'network_requests': [],
                        'network_bytes': []
                    }

                    # Collect values from all test cases
                    for test_name, metrics in test_case_metrics.items():
                        for metric_key in metric_values.keys():
                            value = metrics.get(metric_key)
                            if value is not None: # Only include non-None values
                                metric_values[metric_key].append(value)

                    # Calculate averages and format
                    if metric_values['execution_time']:
                        avg_exec_time = sum(metric_values['execution_time']) / len(metric_values['execution_time'])
                        calculated_avg_metrics["Avg Execution Time (from cases)"] = f"{avg_exec_time:.2f}s"
                    if metric_values['page_load_time']:
                        avg_page_load = sum(metric_values['page_load_time']) / len(metric_values['page_load_time'])
                        calculated_avg_metrics["Avg Page Load Time (from cases)"] = f"{avg_page_load:.0f}ms"
                    if metric_values['memory_usage']:
                        avg_memory = sum(metric_values['memory_usage']) / len(metric_values['memory_usage'])
                        calculated_avg_metrics["Avg Memory Usage (from cases)"] = f"{avg_memory:.2f}MB"
                    if metric_values['cpu_usage']:
                        avg_cpu = sum(metric_values['cpu_usage']) / len(metric_values['cpu_usage'])
                        calculated_avg_metrics["Avg CPU Usage (from cases)"] = f"{avg_cpu:.2f}%"
                    if metric_values['network_requests']:
                        avg_requests = sum(metric_values['network_requests']) / len(metric_values['network_requests'])
                        calculated_avg_metrics["Avg Network Requests (from cases)"] = f"{avg_requests:.0f}" # Requests are integers
                    if metric_values['network_bytes']:
                        avg_bytes = sum(metric_values['network_bytes']) / len(metric_values['network_bytes'])
                        calculated_avg_metrics["Avg Network Data (from cases)"] = f"{avg_bytes:.2f}KB"

                    if calculated_avg_metrics:
                        # Check if a new page is needed before adding the second table title
                        if pdf.get_y() + 30 > pdf.h - pdf.b_margin:
                             pdf.add_page()
                             pdf.add_section_title("Performance Metrics Summary (Continued)")
                             pdf.ln(2) # Add space after continued title

                        pdf.set_font("DejaVu", "B", 10)
                        pdf.set_text_color(*pdf.secondary_color)
                        pdf.cell(0, 8, "Average Metrics Across Test Cases:", ln=True)
                        pdf.ln(1)
                        pdf.add_summary_table(calculated_avg_metrics)
                        pdf.ln(5) # Add space after the second table
                    # else: # This case is handled by the final 'else' block below if neither table was added
                        # pdf.set_font("DejaVu", "I", 10)
                        # pdf.set_text_color(50, 50, 50)
                        # pdf.cell(0, 10, "No detailed performance metrics available from test cases.", ln=True)


                # If neither run-level nor test-case metrics were added
                if not run_level_perf_metrics_table and not calculated_avg_metrics:
                    pdf.set_font("DejaVu", "I", 10)
                    pdf.set_text_color(50, 50, 50)
                    pdf.cell(0, 10, "No performance metrics available for this run. Run tests with the performance_monitor fixture to collect metrics.", ln=True)


                # Add detailed test case performance metrics table if available
                if test_case_metrics:
                    # Check if a new page is needed *before* adding the title
                    # Use a slightly larger estimate to account for the title itself
                    if pdf.get_y() + 30 > pdf.h - pdf.b_margin:
                        pdf.add_page()
                        # Add "(Continued)" title if this is not the first performance section on this page
                        # (This might be tricky to track precisely, but for simplicity, let's just add the main title)
                        pdf.add_section_title("Test Case Performance Details")
                    else:
                        # Add the main title if it fits on the current page
                        pdf.add_section_title("Test Case Performance Details")

                    pdf.ln(2) # Add space after the title

                    # Create a table with test case performance metrics
                    pdf.set_font("DejaVu", "B", 9)

                    # Define column widths (adjust as needed)
                    col_widths = [80, 25, 25, 25, 25]
                    header = ["Test Case", "Exec Time (s)", "Page Load (ms)", "Memory (MB)", "CPU (%)"]

                    # Draw header
                    pdf.set_fill_color(240, 240, 240)
                    for i, col in enumerate(header):
                        pdf.cell(col_widths[i], 7, col, 1, 0, 'C', True)
                    pdf.ln()

                    # Sort test cases by execution time (descending)
                    sorted_cases = sorted(
                        [(name, metrics) for name, metrics in test_case_metrics.items() if 'execution_time' in metrics],
                        key=lambda x: x[1].get('execution_time', 0),
                        reverse=True
                    )

                    # Limit to top 10 test cases
                    top_cases = sorted_cases[:10]

                    # Draw rows
                    pdf.set_font("DejaVu", "", 8)
                    for test_name, metrics in top_cases:
                        # Truncate test name if too long
                        display_name = test_name[:40] + '...' if len(test_name) > 40 else test_name

                        # Get metrics with proper formatting
                        exec_time = f"{metrics.get('execution_time', 0):.2f}" if 'execution_time' in metrics and metrics.get('execution_time') is not None else "-"
                        page_load = f"{metrics.get('page_load_time', 0):.0f}" if 'page_load_time' in metrics and metrics.get('page_load_time') is not None else "-"
                        memory = f"{metrics.get('memory_usage', 0):.2f}" if 'memory_usage' in metrics and metrics.get('memory_usage') is not None else "-"
                        cpu = f"{metrics.get('cpu_usage', 0):.2f}" if 'cpu_usage' in metrics and metrics.get('cpu_usage') is not None else "-"

                        # Draw cells
                        pdf.cell(col_widths[0], 6, display_name, 1)
                        pdf.cell(col_widths[1], 6, exec_time, 1, 0, 'C')
                        pdf.cell(col_widths[2], 6, page_load, 1, 0, 'C')
                        pdf.cell(col_widths[3], 6, memory, 1, 0, 'C')
                        pdf.cell(col_widths[4], 6, cpu, 1, 0, 'C')
                        pdf.ln()

                    # Add note about the table
                    pdf.set_font("DejaVu", "I", 8)
                    pdf.ln(2)
                    pdf.cell(0, 5, f"Note: Table shows top {len(top_cases)} test cases by execution time.", ln=True)
                else:
                    # Add a note about performance metrics if they're not available
                    # This block is now redundant as the check is done above before adding any tables
                    pass # Keep the pass statement or remove the entire else block if preferred

            else: # If neither metrics_for_charts nor test_case_metrics have data
                pdf.add_page()
                pdf.add_section_title("Performance Metrics")
                pdf.set_font("DejaVu", "I", 10)
                pdf.set_text_color(50, 50, 50)
                pdf.cell(0, 10, "No performance metrics available for this run. Run tests with the performance_monitor fixture to collect metrics.", ln=True)


        # --- Add RCA Section ---
        skip_rca_section = (
            rca_result is None or
            rca_result == "Run ID not found, RCA skipped." or
            rca_result == "No failed tests found in this run." or
            rca_result == "Could not retrieve test details for RCA generation."
        )

        if not skip_rca_section:
            pdf_status.info("Adding Root Cause Analysis (RCA) section...")
            pdf.add_page()
            pdf.add_section_title("Root Cause Analysis (RCA)")
            pdf.ln(2)
            pdf.add_rca_section(rca_result)
        else:
            pdf_status.info("ℹ️ Skipping RCA section as no meaningful RCA data was available.")

        # --- Add AI Summaries Section ---
        ai_summaries_exist = not df.empty \
            and 'visual_analysis' in df.columns \
            and df['visual_analysis'].notna().any()

        if ai_summaries_exist:
            # Check if a new page is needed *before* adding the section title
            # Use a slightly larger estimate to account for the title itself
            if pdf.get_y() + 30 > pdf.h - pdf.b_margin:
                pdf.add_page()

            # Add the main title *after* the potential page break
            pdf.add_section_title("AI Analysis Summaries")
            pdf.ln(2)

            failed_with_summary = df[(df['result'] == 'FAILED') & df['visual_analysis'].notna()]
            other_with_summary = df[(df['result'] != 'FAILED') & df['visual_analysis'].notna()]

            summary_count = 0
            max_summaries = 100

            # Sort combined summaries: Failed first, then by class, then by name
            all_summaries_df = pd.concat([failed_with_summary, other_with_summary])
            all_summaries_df_sorted = all_summaries_df.sort_values(
                by=['result', 'class_name', 'test_name'],
                ascending=[False, True, True]
            )


            for _, test_case in all_summaries_df_sorted.iterrows():
                if summary_count >= max_summaries:
                    pdf.set_font("DejaVu", "I", 9)
                    pdf.cell(0, 5, f"... (truncated - showing first {max_summaries} summaries)", ln=True)
                    break

                test_id = f"{test_case['test_name']}|{test_case['class_name']}"

                # Estimate height *before* adding content
                summary_text = str(test_case.get('visual_analysis', ''))
                # Rough estimate: Header lines + lines for text content
                estimated_height = 25 + (len(summary_text) // 50 + summary_text.count('\n')) * 6 # Adjust multiplier as needed

                # Check if the *current* summary fits on the page
                if pdf.get_y() + estimated_height > pdf.h - pdf.b_margin:
                    pdf.add_page()
                    # Add "(Continued)" title only if this is NOT the first summary
                    if summary_count > 0:
                        pdf.add_section_title("AI Analysis Summaries (Continued)")
                        pdf.ln(2)
                    # If it IS the first summary (summary_count == 0), the main title
                    # was already added on the previous page or at the top of this one.

                # Set the link target *after* potential page break
                if test_id in test_links:
                    # Ensure y-coordinate is correct after potential page break
                    current_y = pdf.get_y()
                    pdf.set_link(test_links[test_id], page=pdf.page_no(), y=current_y)

                # Add the actual summary content
                pdf.add_ai_summary(
                    test_case.get('test_name', 'Unknown Test'),
                    test_case.get('class_name', 'N/A'),
                    test_case.get('result', 'UNKNOWN'),
                    test_case.get('visual_analysis', {})
                )

                summary_count += 1

            if summary_count == 0:
                # This case should ideally not happen if ai_summaries_exist is True,
                # but handle it just in case.
                pdf.set_font("DejaVu", "I", 10)
                pdf.cell(0, 10, "No applicable AI summaries were found for this run.", ln=True)

            pdf_status.info(f"💾 Saving enhanced PDF to {pdf_filename.name}...")
            pdf.output(str(pdf_filename), "F")
            pdf_status.empty()

        return pdf_filename

    except Exception as e:
        pdf_status.empty()
        st.error(f"Error generating PDF report: {e}")
        st.expander("Show Error Details").code(traceback.format_exc())
        raise
    finally:
        pdf_status.empty()
        if conn:
            conn.close()
        close_thread_local_connection()
        plt.close('all')
