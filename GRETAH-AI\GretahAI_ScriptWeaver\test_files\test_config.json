{"application": {"name": "Test Application", "version": "1.0.0", "environment": "testing"}, "database": {"host": "localhost", "port": 5432, "name": "test_db", "username": "test_user", "password": "test_password"}, "api": {"base_url": "https://api.example.com", "timeout": 30, "retry_attempts": 3}, "features": {"file_upload": true, "user_registration": true, "email_notifications": false, "advanced_search": true}, "security": {"max_file_size_mb": 10, "allowed_file_types": ["jpg", "png", "pdf", "docx"], "session_timeout_minutes": 30}, "ui": {"theme": "light", "language": "en", "items_per_page": 20}}