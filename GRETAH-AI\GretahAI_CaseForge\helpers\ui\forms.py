"""
UI Forms Module for GretahAI CaseForge Test Generator

This module contains form rendering functions for the test generator interface.

Functions:
- render_jira_extraction_form: Render the JIRA extraction form
- render_enhancement_form: Render the JIRA enhancement form  
- render_test_generation_controls: Render test generation controls
- render_generate_button: Render the test case generation button
- render_jira_issue_section: Render the JIRA issue details section
- show_extraction_guidance: Show guidance when JIRA issue is not extracted

© 2025 GretahAI Team
"""

import streamlit as st


def render_jira_extraction_form(jira_client):
    """
    Render the JIRA extraction form with input field and extract button.
    
    Args:
        jira_client: JIRA client instance or None
        
    Returns:
        tuple: (case_id, extract_button_pressed, temperature)
    """
    # First, let's create the JIRA ID input and extract button section
    col1, col2 = st.columns([3, 1])
    
    with col1:
        case_id = st.text_input(
            "Enter JIRA User Story ID",
            "TP-1",
            key="jira_case_id_input",
            help="Enter a JIRA User Story ID (e.g., PROJ-123) and click 'Extract User Story' to retrieve and display the story details from JIRA."
        )

    with col2:
        jira_connected = jira_client is not None
        extract_button = st.button(
            "📄 Extract User Story",
            use_container_width=True,
            disabled=not jira_connected,
            key="extract_jira_button",
            help="Enter a JIRA User Story ID (e.g., PROJ-123) and click 'Extract User Story' to retrieve and display the story details from JIRA."
        )
        
        # Add temperature control that's always visible
        temperature = st.slider(
            "Format strictness",
            min_value=0.1,
            max_value=1.0,
            value=0.7,
            step=0.1,
            help="Controls how close the AI stays to the original format:\n\n" +
                 "• Lower (0.1-0.3): Strict formatting, minimal changes\n" +
                 "• Medium (0.4-0.6): Balanced formatting with some improvements\n" +
                 "• Higher (0.7-1.0): More creative reformatting"
        )
        st.session_state["format_temperature"] = temperature
        
        # Show appropriate status messages
        if not jira_connected:
            st.error("JIRA connection failed. Cannot extract issue details.")

    return case_id, extract_button, temperature


def render_enhancement_form(jira_client):
    """
    Render the JIRA enhancement form with enhance button and additional context.
    
    Args:
        jira_client: JIRA client instance or None
        
    Returns:
        tuple: (enhance_button_pressed, additional_context)
    """
    # Check if JIRA issue is available for enhancement
    jira_connected = jira_client is not None
    # Fix: Check both conditions more reliably
    jira_issue_extracted = st.session_state.get("jira_issue_extracted", False)
    jira_issue_available = st.session_state.get("jira_issue", None) is not None
    
    # Both conditions must be true for enhancement to be enabled
    jira_issue_ready = jira_issue_extracted and jira_issue_available
    enhance_button_disabled = not jira_connected or not jira_issue_ready

    # Set contextual tooltip based on button state
    if enhance_button_disabled:
        enhance_tooltip = "Extract a User Story first to enable enhancement functionality. This feature will refine and improve the extracted story using AI analysis."
    else:
        enhance_tooltip = "Click to enhance the extracted user story with AI-powered refinements and INVEST parameter evaluation."

    enhance_button = st.button(
        "✏️ Enhance User Story",
        use_container_width=True,
        disabled=enhance_button_disabled,
        key="enhance_jira_button",
        help=enhance_tooltip
    )

    # Show appropriate status messages
    if not jira_connected:
        st.error("JIRA connection failed. Cannot enhance issue details.")

    additional_context = ""
    
    # Add checkbox for additional context (only show when JIRA issue is ready)
    if jira_connected and jira_issue_ready:
        show_additional_context = st.checkbox(
            "Additional context for enhancement",
            key="show_additional_context_checkbox"
        )

        # Display text area when checkbox is checked
        if show_additional_context:
            additional_context = st.text_area(
                "Enter additional context for enhancement:",
                key="additional_context_text_area",
                height=100,
                help="Provide more details for the AI to consider during enhancement"
            )
            # Store in session state for later use during enhancement
            st.session_state["additional_context_for_enhancement"] = additional_context
        else:
            # Clear the additional context if checkbox is unchecked
            if "additional_context_for_enhancement" in st.session_state:
                st.session_state["additional_context_for_enhancement"] = ""

    return enhance_button, additional_context


def render_test_generation_controls():
    """
    Render the test generation controls including test type selection and number input.
    
    Returns:
        tuple: (test_type, num_scenarios, generate_button_pressed)
    """
    from .components import format_test_type_display_name
    
    # Only show the rest of the form if JIRA issue is extracted
    col_type, col_num = st.columns(2)
    with col_type:
        test_type = st.selectbox(
            "Test Case Type",
            options=["all", "positive", "negative", "security", "performance"],
            format_func=format_test_type_display_name,
            key="test_type_select"
        )
    with col_num:
        num_scenarios = st.number_input("Number of Test cases", min_value=1, max_value=20, value=5, key="num_scenarios_input")

    return test_type, num_scenarios


def render_generate_button(ai_provider, google_api_key):
    """
    Render the test case generation button with appropriate disabled state.
    
    Args:
        ai_provider (str): AI provider name
        google_api_key (str): Google AI API key
        
    Returns:
        bool: Whether generate button was pressed
    """
    # Add the generate button
    disable_generate = (ai_provider == "Cloud" and not google_api_key)
    generate_button = st.button(
        "🚀 Generate Test Cases",
        use_container_width=True,
        disabled=disable_generate,
        key="test_generator_generate_button"
    )

    # API key requirements are now shown through tooltips and button states
        
    return generate_button


def render_jira_issue_section(jira_issue):
    """
    Render the JIRA issue details section in an expandable container.
    
    Args:
        jira_issue: JIRA issue object to display
        
    Returns:
        None (renders directly to Streamlit UI)
    """
    # Import the enhanced formatting function
    from helpers.jira.operations import format_jira_description
    
    with st.expander("JIRA Issue Details", expanded=True):
        st.write(f"**Issue Key:** {jira_issue.key}")
        st.write(f"**Summary:** {jira_issue.fields.summary}")
        st.write(f"**Status:** {jira_issue.fields.status.name}")
        
        description = jira_issue.fields.description or "No description found."
        st.write("**Description:**")
        
        # Use the enhanced formatting function that supports tables and markdown
        formatted_description = format_jira_description(description)
        st.markdown(formatted_description, unsafe_allow_html=True)


def show_extraction_guidance(extract_button_pressed):
    """
    Show guidance message when JIRA issue is not extracted.

    Args:
        extract_button_pressed (bool): Whether extract button was pressed

    Returns:
        None (displays message to UI)
    """
    # Guidance is now provided through tooltips, so this function is simplified
    pass
