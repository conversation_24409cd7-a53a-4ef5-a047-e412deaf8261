"""
File Dependency Validator UI Component for GretahAI ScriptWeaver

This module provides UI components for validating and displaying file dependency
status across all stages of the ScriptWeaver workflow. It ensures users are
properly informed when file dependencies are missing and provides clear guidance
for resolving dependency issues.

Key Features:
- File dependency status validation
- Error messaging for missing dependencies
- User guidance for resolving file issues
- Integration with StateManager for dependency checks
- Professional UI styling with clear visual indicators
"""

import streamlit as st
import os
from typing import Dict, Any

# Import GRETAH standardized logging
from debug_utils import debug


def render_file_dependency_error(state, target_stage_name: str = "this stage") -> bool:
    """
    Render file dependency error message when dependencies are not satisfied.
    
    This component displays a clear error message when file dependencies are missing,
    preventing users from proceeding until dependencies are resolved.
    
    Args:
        state: StateManager instance
        target_stage_name: Name of the stage that requires dependencies
        
    Returns:
        bool: True if dependencies are satisfied, False if error was displayed
    """
    file_status = state.get_file_dependency_status()
    
    if file_status["dependencies_satisfied"]:
        return True
    
    debug("File dependency error displayed",
          stage="file_validation",
          operation="dependency_error_display",
          context={
              "target_stage": target_stage_name,
              "file_status": file_status
          })
    
    # Display prominent error message
    st.error("🚫 **File Dependency Error**")
    
    # Show specific error message
    if file_status["error_message"]:
        st.write(file_status["error_message"])
    
    # Show current status
    with st.expander("📋 Dependency Status", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**File Upload:**")
            if file_status["has_file_path"]:
                st.success("✅ File path exists")
            else:
                st.error("❌ No file uploaded")
        
        with col2:
            st.write("**File Validation:**")
            if file_status["file_exists"]:
                st.success("✅ File exists on disk")
            else:
                st.error("❌ File not found")
        
        st.write("**Test Cases:**")
        if file_status["has_test_cases"]:
            st.success(f"✅ {file_status['test_cases_count']} test cases found")
        else:
            st.error("❌ No test cases parsed")
    
    # Provide resolution guidance
    st.markdown("### 💡 How to Fix This")
    
    if not file_status["has_file_path"]:
        st.info("""
        **No Excel file uploaded:**
        1. Navigate to **Stage 1: Upload Excel File**
        2. Use the file uploader to select your test case Excel file
        3. Wait for the file to be processed successfully
        4. Return to this stage once the file is uploaded
        """)
    elif not file_status["file_exists"]:
        st.warning("""
        **File was removed or moved:**
        1. Navigate to **Stage 1: Upload Excel File**
        2. Re-upload your Excel file using the file uploader
        3. Ensure the file is not deleted or moved after upload
        4. Return to this stage once the file is re-uploaded
        """)
    elif not file_status["has_test_cases"]:
        st.warning("""
        **No test cases found in file:**
        1. Check that your Excel file has the required columns:
           - Test Case ID, Test Case Objective, Step No, Test Steps, Expected Result
        2. Ensure there is actual data in these columns
        3. Navigate to **Stage 1** to re-upload a corrected file
        4. Return to this stage once test cases are properly parsed
        """)
    
    # Add navigation button to Stage 1
    if st.button("🔄 Go to Stage 1 (File Upload)", key="goto_stage1_button", type="primary"):
        from state_manager import StateStage
        if state.advance_to(StateStage.STAGE1_UPLOAD, f"User navigated from {target_stage_name} to fix file dependencies"):
            st.rerun()
        else:
            st.error("Failed to navigate to Stage 1")
    
    return False


def render_file_dependency_warning(state) -> None:
    """
    Render a warning message for potential file dependency issues.
    
    This component shows a less prominent warning when file dependencies
    might be at risk but haven't failed yet.
    
    Args:
        state: StateManager instance
    """
    file_status = state.get_file_dependency_status()
    
    # Only show warning if file exists but has issues
    if file_status["has_file_path"] and file_status["file_exists"] and not file_status["has_test_cases"]:
        st.warning("⚠️ **File Warning:** Test cases could not be parsed from the uploaded file. Some features may not work correctly.")
        
        debug("File dependency warning displayed",
              stage="file_validation",
              operation="dependency_warning_display",
              context={"file_status": file_status})


def render_file_status_indicator(state, compact: bool = False) -> None:
    """
    Render a compact file status indicator for sidebar or header use.
    
    Args:
        state: StateManager instance
        compact: If True, render in compact mode for sidebar
    """
    file_status = state.get_file_dependency_status()
    
    if compact:
        # Compact mode for sidebar
        if file_status["dependencies_satisfied"]:
            st.success(f"📁 File: {file_status['test_cases_count']} test cases")
        else:
            st.error("📁 File: Missing or invalid")
    else:
        # Full mode for main content
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            if file_status["dependencies_satisfied"]:
                file_name = os.path.basename(file_status["file_path"]) if file_status["file_path"] else "Unknown"
                st.success(f"📁 **{file_name}** - {file_status['test_cases_count']} test cases")
            else:
                st.error("📁 **File dependency error** - Upload required")
        
        with col2:
            if file_status["has_file_path"]:
                st.write(f"**Status:** {'✅ Valid' if file_status['dependencies_satisfied'] else '❌ Invalid'}")
        
        with col3:
            if file_status["file_exists"]:
                st.write(f"**Size:** {_get_file_size_display(file_status['file_path'])}")


def _get_file_size_display(file_path: str) -> str:
    """
    Get human-readable file size display.
    
    Args:
        file_path: Path to the file
        
    Returns:
        str: Human-readable file size
    """
    try:
        if file_path and os.path.exists(file_path):
            size_bytes = os.path.getsize(file_path)
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            else:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
    except Exception:
        pass
    return "Unknown"


def validate_stage_dependencies(state, stage_name: str, required_stage_number: int = 2) -> bool:
    """
    Validate that stage dependencies are satisfied before allowing stage access.
    
    This function should be called at the beginning of each stage that requires
    file dependencies to ensure the user cannot proceed without proper setup.
    
    Args:
        state: StateManager instance
        stage_name: Name of the current stage for logging
        required_stage_number: Minimum stage number that requires file dependencies
        
    Returns:
        bool: True if dependencies satisfied, False if validation failed
    """
    current_stage_num = state.current_stage.get_stage_number()
    
    # Stages 1 doesn't require file dependencies
    if current_stage_num < required_stage_number:
        return True
    
    file_status = state.get_file_dependency_status()
    
    if not file_status["dependencies_satisfied"]:
        debug("Stage access blocked due to file dependency validation failure",
              stage="file_validation",
              operation="stage_access_blocked",
              context={
                  "stage_name": stage_name,
                  "current_stage_number": current_stage_num,
                  "required_stage_number": required_stage_number,
                  "file_status": file_status
              })
        
        # Display error and block access
        render_file_dependency_error(state, stage_name)
        return False
    
    debug("Stage access allowed - file dependencies satisfied",
          stage="file_validation",
          operation="stage_access_allowed",
          context={
              "stage_name": stage_name,
              "current_stage_number": current_stage_num,
              "file_status": file_status
          })
    
    return True
