"""
Stage 10 Navigation Components for GretahAI ScriptWeaver

Navigation buttons, workflow indicators, footer, and stage transitions components.
Extracted from stage10_components.py for better maintainability.
"""

import streamlit as st
from typing import Dict, List, Any, Optional

# Import GRETAH standardized logging
from debug_utils import debug


def render_stage10_footer():
    """
    Render the Stage 10 footer.
    """
    st.markdown("---")
    st.markdown("### 🎮 Script Playground")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **🔄 Always Accessible**: Available at any time.

        **🎯 Template-Based**: Uses optimized script patterns.
        """)

    with col2:
        st.info("""
        **🤖 AI-Powered**: Google AI adaptation.

        **⚡ Independent**: Doesn't affect workflow.
        """)


def render_workflow_navigation():
    """
    Render the workflow navigation section.

    Returns:
        tuple: (stage1_clicked, stage8_clicked, stage9_clicked)
    """
    with st.expander("🧭 Navigation", expanded=False):
        col1, col2, col3 = st.columns(3)

        stage1_clicked = False
        stage8_clicked = False
        stage9_clicked = False

        with col1:
            stage1_clicked = st.button(
                "📁 Stage 1",
                use_container_width=True,
                key="stage10_nav_stage1"
            )

        with col2:
            stage8_clicked = st.button(
                "🔧 Stage 8",
                use_container_width=True,
                key="stage10_nav_stage8"
            )

        with col3:
            stage9_clicked = st.button(
                "📜 Stage 9",
                use_container_width=True,
                key="stage10_nav_stage9"
            )

        return stage1_clicked, stage8_clicked, stage9_clicked
