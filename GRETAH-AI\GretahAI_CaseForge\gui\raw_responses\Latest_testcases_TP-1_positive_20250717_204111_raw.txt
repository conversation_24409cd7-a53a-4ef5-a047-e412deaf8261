```json
[
  {
    "scenario_name": "Successful Login After Account Unlock",
    "type": "positive",
    "prerequisites": "User should have their account temporarily locked due to 3 failed login attempts and subsequently unlocked by an administrator.",
    "Test Case Objective": "Verify user can successfully log in after their account has been unlocked following a lockout.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the User ID field", "expected_result": "Username should be successfully entered in the User ID field."},
      {"action": "Verify if user is able to enter valid password in the Password field", "expected_result": "Password should be successfully entered in the Password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be successfully logged in and redirected to the home page."}
    ]
  },
  {
    "scenario_name": "Valid Login with Correct Credentials",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify user can log in successfully with valid username and password.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the User ID field", "expected_result": "Username should be successfully entered in the User ID field."},
      {"action": "Verify if user is able to enter valid password in the Password field", "expected_result": "Password should be successfully entered in the Password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be successfully logged in and redirected to the home page."},
      {"action": "Verify if user is able to view the user's dashboard upon successful login", "expected_result": "User's dashboard should be displayed."}
    ]
  },
  {
    "scenario_name": "Login with Case-Sensitive Password",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment, with a password containing both uppercase and lowercase letters.",
    "Test Case Objective": "Verify user can log in successfully with correct case-sensitive password.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the User ID field", "expected_result": "Username should be successfully entered in the User ID field."},
      {"action": "Verify if user is able to enter the correct case-sensitive password in the Password field", "expected_result": "Password should be successfully entered in the Password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be successfully logged in and redirected to the home page."}
    ]
  },
  {
    "scenario_name": "Successful Login after Password Reset",
    "type": "positive",
    "prerequisites": "User should have recently reset their password through the password reset process.",
    "Test Case Objective": "Verify user can successfully log in using the newly reset password.",
    "steps": [
      {"action": "Verify if user is able to enter their valid username in the User ID field", "expected_result": "Username should be successfully entered in the User ID field."},
      {"action": "Verify if user is able to enter the newly reset password in the Password field", "expected_result": "Password should be successfully entered in the Password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be successfully logged in and redirected to the home page."}
    ]
  },
  {
    "scenario_name": "Login After Session Timeout",
    "type": "positive",
    "prerequisites": "User should have a valid session that has timed out due to inactivity.",
    "Test Case Objective": "Verify user can successfully log in after their previous session has timed out.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the User ID field", "expected_result": "Username should be successfully entered in the User ID field."},
      {"action": "Verify if user is able to enter valid password in the Password field", "expected_result": "Password should be successfully entered in the Password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be successfully logged in and redirected to the home page."}
    ]
  }
]
```