"""
Helper functions for generating and displaying diffs between scripts.

This module provides functions for:
1. Generating HTML diffs between step-specific and merged scripts
2. Highlighting the differences between scripts
3. Analyzing which parts of the merged script came from previous steps
"""

import difflib
import re
import logging
from typing import Tuple, List, Dict, Optional

# Set up logging
logger = logging.getLogger(__name__)

def generate_html_diff(step_specific_script: str, merged_script: str) -> str:
    """
    Generate an HTML diff between the step-specific script and the merged script.

    Args:
        step_specific_script (str): The script for the current step only
        merged_script (str): The merged script combining all steps

    Returns:
        str: HTML representation of the diff
    """
    try:
        # Split the scripts into lines
        step_specific_lines = step_specific_script.splitlines()
        merged_lines = merged_script.splitlines()

        # Generate the diff
        diff = difflib.HtmlDiff(tabsize=4, wrapcolumn=80)
        html_diff = diff.make_file(
            step_specific_lines,
            merged_lines,
            fromdesc="Step-Specific Script",
            todesc="Merged Script"
        )

        # Customize the HTML diff for better integration with Streamlit
        # Remove the default styles that might conflict with Streamlit
        html_diff = html_diff.replace(
            '<style type="text/css">',
            '<style type="text/css">\n/* Custom styles for Streamlit integration */\n'
        )

        # Add custom styles for better visibility in Streamlit
        html_diff = html_diff.replace(
            '</style>',
            'table.diff {width: 100%; border-collapse: collapse; font-family: monospace; font-size: 14px;}\n'
            'table.diff td {padding: 3px 5px; vertical-align: top; white-space: pre-wrap;}\n'
            'table.diff .diff_add {background-color: #aaffaa; color: #000000;}\n'
            'table.diff .diff_chg {background-color: #ffff77; color: #000000;}\n'
            'table.diff .diff_sub {background-color: #ffaaaa; color: #000000;}\n'
            'table.diff th {background-color: #e0e0e0; padding: 5px; text-align: center;}\n'
            '</style>'
        )

        return html_diff
    except Exception as e:
        logger.error(f"Error generating HTML diff: {e}")
        return f"<p>Error generating diff: {str(e)}</p>"

def analyze_script_origins(step_specific_script: str, merged_script: str) -> Dict[int, str]:
    """
    Analyze which parts of the merged script came from previous steps vs. the current step.

    Args:
        step_specific_script (str): The script for the current step only
        merged_script (str): The merged script combining all steps

    Returns:
        Dict[int, str]: Dictionary mapping line numbers to origin ('current' or 'previous')
    """
    try:
        # Split the scripts into lines
        step_specific_lines = step_specific_script.splitlines()
        merged_lines = merged_script.splitlines()

        # Initialize the origins dictionary
        origins = {}

        # Use difflib to compare the scripts
        matcher = difflib.SequenceMatcher(None, step_specific_lines, merged_lines)

        # Process the opcodes to determine the origin of each line
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                # Lines that are in both scripts came from the current step
                for i in range(j1, j2):
                    origins[i] = 'current'
            elif tag == 'insert' or tag == 'replace':
                # Lines that are only in the merged script came from previous steps
                for i in range(j1, j2):
                    origins[i] = 'previous'

        return origins
    except Exception as e:
        logger.error(f"Error analyzing script origins: {e}")
        return {}

def generate_annotated_script(merged_script: str, origins: Dict[int, str]) -> str:
    """
    Generate an annotated version of the merged script with origin indicators.

    Args:
        merged_script (str): The merged script combining all steps
        origins (Dict[int, str]): Dictionary mapping line numbers to origin

    Returns:
        str: Annotated script with origin indicators
    """
    try:
        # Split the script into lines
        lines = merged_script.splitlines()

        # Add annotations to each line
        annotated_lines = []
        for i, line in enumerate(lines):
            origin = origins.get(i, 'unknown')
            if origin == 'current':
                # Lines from the current step
                annotated_lines.append(f"{line}  # [Current Step]")
            elif origin == 'previous':
                # Lines from previous steps
                annotated_lines.append(f"{line}  # [Previous Step]")
            else:
                # Unknown origin
                annotated_lines.append(f"{line}  # [Unknown Origin]")

        return '\n'.join(annotated_lines)
    except Exception as e:
        logger.error(f"Error generating annotated script: {e}")
        return merged_script

def generate_colored_html_script(merged_script: str, origins: Dict[int, str]) -> str:
    """
    Generate an HTML version of the merged script with color-coded origins.

    Args:
        merged_script (str): The merged script combining all steps
        origins (Dict[int, str]): Dictionary mapping line numbers to origin

    Returns:
        str: HTML script with color-coded origins
    """
    try:
        # Split the script into lines
        lines = merged_script.splitlines()

        # Generate HTML with color-coded lines
        html_lines = ['<pre style="font-family: monospace; font-size: 14px; line-height: 1.5;">']

        for i, line in enumerate(lines):
            origin = origins.get(i, 'unknown')

            # Escape HTML special characters
            escaped_line = line.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

            if origin == 'current':
                # Lines from the current step - blue
                html_lines.append(f'<span style="background-color: #e6f3ff; display: block;">{escaped_line}</span>')
            elif origin == 'previous':
                # Lines from previous steps - light green
                html_lines.append(f'<span style="background-color: #e6ffe6; display: block;">{escaped_line}</span>')
            else:
                # Unknown origin - gray
                html_lines.append(f'<span style="background-color: #f0f0f0; display: block;">{escaped_line}</span>')

        html_lines.append('</pre>')
        return '\n'.join(html_lines)
    except Exception as e:
        logger.error(f"Error generating colored HTML script: {e}")
        return f"<pre>{merged_script}</pre>"


def generate_feedback_comparison_diff(original_script: str, regenerated_script: str, validation_feedback: dict) -> str:
    """
    Generate a specialized diff view for feedback loop comparisons.

    This function creates an enhanced diff that highlights how validation feedback
    influenced the regenerated script, with annotations for specific improvements.

    Args:
        original_script (str): The original script before feedback
        regenerated_script (str): The regenerated script after feedback
        validation_feedback (dict): The validation feedback that influenced regeneration

    Returns:
        str: HTML representation of the feedback-aware diff
    """
    try:
        # Split scripts into lines
        original_lines = original_script.splitlines()
        regenerated_lines = regenerated_script.splitlines()

        # Create a custom HTML diff with feedback annotations
        html_content = ['<div style="font-family: monospace; font-size: 14px;">']

        # Add header with feedback summary
        html_content.append('<div style="background-color: #f0f8ff; padding: 10px; margin-bottom: 20px; border-radius: 5px;">')
        html_content.append('<h3 style="margin: 0 0 10px 0;">🔄 Feedback Loop Comparison</h3>')
        html_content.append(f'<p><strong>Quality Score:</strong> {validation_feedback.get("quality_score", "N/A")}/100</p>')
        html_content.append(f'<p><strong>Issues Addressed:</strong> {len(validation_feedback.get("issues_found", []))}</p>')
        html_content.append(f'<p><strong>Recommendations Applied:</strong> {len(validation_feedback.get("recommendations", []))}</p>')
        html_content.append('</div>')

        # Generate side-by-side diff
        diff = difflib.HtmlDiff(tabsize=4, wrapcolumn=80)
        html_diff = diff.make_file(
            original_lines,
            regenerated_lines,
            fromdesc="🔴 Original Script (Before Feedback)",
            todesc="🟢 Regenerated Script (After Feedback)",
            context=True,
            numlines=2
        )

        # Enhance the diff with feedback-specific styling
        enhanced_diff = html_diff.replace(
            '<style type="text/css">',
            '''<style type="text/css">
            /* Enhanced styles for feedback loop comparison */
            .diff_header {background-color: #e6f3ff; font-weight: bold; padding: 8px;}
            .diff_next {background-color: #f0f0f0; padding: 4px;}
            '''
        )

        # Add improvement annotations
        improvements_section = '''
        <div style="background-color: #f0fff0; padding: 15px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #28a745;">
            <h4 style="margin: 0 0 10px 0; color: #28a745;">✅ Key Improvements Implemented</h4>
            <ul style="margin: 0; padding-left: 20px;">
        '''

        # Analyze and add specific improvements
        if 'XPath' in original_script and 'CSS_SELECTOR' in regenerated_script:
            improvements_section += '<li><strong>Locator Strategy:</strong> Replaced generic XPath with specific CSS selectors</li>'

        if 'find_element' in original_script and 'wait.until' in regenerated_script:
            improvements_section += '<li><strong>Wait Conditions:</strong> Added explicit WebDriverWait for reliable element interactions</li>'

        if 'test_data' not in original_script and 'test_data' in regenerated_script:
            improvements_section += '<li><strong>Test Data:</strong> Integrated test_data fixture for dynamic values</li>'

        if 'try:' not in original_script and 'try:' in regenerated_script:
            improvements_section += '<li><strong>Error Handling:</strong> Added try/except with screenshot capture</li>'

        improvements_section += '</ul></div>'

        # Combine all parts
        html_content.append(enhanced_diff)
        html_content.append(improvements_section)
        html_content.append('</div>')

        return '\n'.join(html_content)

    except Exception as e:
        logger.error(f"Error generating feedback comparison diff: {e}")
        return f"<p>Error generating feedback comparison: {str(e)}</p>"


def analyze_feedback_implementation(original_script: str, regenerated_script: str, validation_feedback: dict) -> dict:
    """
    Analyze how validation feedback was implemented in the regenerated script.

    Args:
        original_script (str): The original script
        regenerated_script (str): The regenerated script
        validation_feedback (dict): The validation feedback

    Returns:
        dict: Analysis of implemented improvements
    """
    try:
        analysis = {
            'implemented_recommendations': [],
            'addressed_issues': [],
            'quality_metrics': {},
            'improvement_score': 0
        }

        # Check for locator improvements
        if 'XPath' in original_script and 'CSS_SELECTOR' in regenerated_script:
            analysis['implemented_recommendations'].append({
                'category': 'locators',
                'description': 'Replaced XPath selectors with CSS selectors',
                'impact': 'high'
            })
            analysis['improvement_score'] += 20

        # Check for wait condition improvements
        original_waits = original_script.count('WebDriverWait')
        regenerated_waits = regenerated_script.count('WebDriverWait')
        if regenerated_waits > original_waits:
            analysis['implemented_recommendations'].append({
                'category': 'waits',
                'description': f'Added {regenerated_waits - original_waits} explicit wait conditions',
                'impact': 'high'
            })
            analysis['improvement_score'] += 25

        # Check for test data integration
        if 'test_data' not in original_script and 'test_data' in regenerated_script:
            analysis['implemented_recommendations'].append({
                'category': 'test_data',
                'description': 'Integrated test_data fixture for dynamic values',
                'impact': 'medium'
            })
            analysis['improvement_score'] += 15

        # Check for error handling
        if 'try:' not in original_script and 'try:' in regenerated_script:
            analysis['implemented_recommendations'].append({
                'category': 'error_handling',
                'description': 'Added comprehensive error handling with screenshots',
                'impact': 'medium'
            })
            analysis['improvement_score'] += 20

        # Calculate quality metrics
        analysis['quality_metrics'] = {
            'lines_added': len(regenerated_script.splitlines()) - len(original_script.splitlines()),
            'xpath_selectors_removed': original_script.count('XPATH') - regenerated_script.count('XPATH'),
            'css_selectors_added': regenerated_script.count('CSS_SELECTOR') - original_script.count('CSS_SELECTOR'),
            'wait_conditions_added': regenerated_waits - original_waits,
            'assertions_improved': regenerated_script.count('assert') - original_script.count('assert')
        }

        return analysis

    except Exception as e:
        logger.error(f"Error analyzing feedback implementation: {e}")
        return {'error': str(e)}
