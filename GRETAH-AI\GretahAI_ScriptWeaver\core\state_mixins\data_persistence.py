"""
Data Persistence Mixin for GretahAI ScriptWeaver State Management.

This module contains the data persistence functionality extracted from StateManager
as part of Phase 3 of the parallel refactoring approach using mixin pattern.

The DataPersistenceMixin provides methods for:
- Saving and loading step data to/from JSON storage
- Real-time JSON updates for single source of truth
- Step context and completed steps persistence
- Current step retrieval from JSON storage

This mixin is designed to be used with the StateManager class to provide
JSON data persistence capabilities while maintaining clean separation of concerns.

© 2025 Cogniron All Rights Reserved.
"""

from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime

# Import structured debug function for GRETAH logging compliance
from debug_utils import debug


class DataPersistenceMixin:
    """
    Mixin class providing JSON data persistence functionality for StateManager.
    
    This mixin contains all methods related to saving and loading step data,
    step context, and completed steps to/from persistent JSON storage.
    
    The mixin assumes the following state fields exist in the implementing class:
    - selected_test_case: Optional[Dict[str, Any]]
    - step_table_json: List[Dict[str, Any]]
    - total_steps: int
    - hybrid_editing_enabled: bool
    - step_insertion_points: Dict[str, List[Dict[str, Any]]]
    - step_context: Dict[str, Any]
    - completed_steps: List[Any]
    """

    def save_step_data_to_json(self, step_data: List[Dict[str, Any]], metadata: Dict[str, Any] = None) -> bool:
        """
        Save step data to persistent JSON storage.

        Args:
            step_data: List of step dictionaries to save
            metadata: Additional metadata about the step data

        Returns:
            bool: True if saved successfully, False otherwise
        """
        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            debug("Cannot save step data: no test case selected",
                  stage="state_management", operation="validation_warning")
            return False

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            debug("Cannot save step data: test case has no ID",
                  stage="state_management", operation="validation_warning")
            return False

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            # Prepare metadata
            save_metadata = metadata or {}
            save_metadata.update({
                'test_case_objective': self.selected_test_case.get('Test Case Objective', ''),
                'hybrid_editing_enabled': self.hybrid_editing_enabled,
                'has_manual_steps': bool(self.step_insertion_points),
                'save_timestamp': datetime.now().isoformat()
            })

            success = storage.save_step_data(test_case_id, step_data, save_metadata)

            if success:
                debug("Successfully saved step data to JSON",
                      stage="state_management", operation="data_saving",
                      context={'test_case_id': test_case_id, 'step_count': len(step_data)})
            else:
                debug("Failed to save step data to JSON",
                      stage="state_management", operation="error_handling",
                      context={'test_case_id': test_case_id})

            return success

        except Exception as e:
            debug("Error saving step data to JSON",
                  stage="state_management", operation="error_handling",
                  context={'test_case_id': test_case_id, 'error': str(e)})
            return False

    def load_step_data_from_json(self) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """
        Load step data from persistent JSON storage.

        Returns:
            Tuple of (step_data, metadata) or None if not found
        """
        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            debug("Cannot load step data: no test case selected",
                  stage="state_management", operation="validation_warning")
            return None

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            debug("Cannot load step data: test case has no ID",
                  stage="state_management", operation="validation_warning")
            return None

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            result = storage.load_step_data(test_case_id)

            if result:
                step_data, metadata = result
                debug("Successfully loaded step data from JSON",
                      stage="state_management", operation="data_loading",
                      context={'test_case_id': test_case_id, 'step_count': len(step_data),
                              'source': metadata.get('source', 'unknown')})
            else:
                debug("No step data found in JSON",
                      stage="state_management", operation="data_loading",
                      context={'test_case_id': test_case_id})

            return result

        except Exception as e:
            debug("Error loading step data from JSON",
                  stage="state_management", operation="error_handling",
                  context={'test_case_id': test_case_id, 'error': str(e)})
            return None

    def update_step_data_in_json(self, step_data: List[Dict[str, Any]], operation: str, additional_metadata: Dict[str, Any] = None) -> bool:
        """
        Immediately update step data in JSON storage after any modification.
        This ensures JSON files remain the single source of truth.

        Args:
            step_data: Updated step data to save
            operation: Description of the operation that triggered the update
            additional_metadata: Additional metadata to include

        Returns:
            bool: True if update was successful, False otherwise
        """
        # Prepare metadata
        metadata = {
            'source': 'real_time_update',
            'operation': operation,
            'update_timestamp': datetime.now().isoformat(),
            'step_count': len(step_data)
        }

        if additional_metadata:
            metadata.update(additional_metadata)

        # Save to JSON storage
        success = self.save_step_data_to_json(step_data, metadata)

        if success:
            # Update in-memory working copy
            self.step_table_json = step_data.copy()
            self.total_steps = len(step_data)
            debug("Successfully updated JSON storage",
                  stage="state_management", operation="data_saving",
                  context={'operation': operation, 'step_count': len(step_data)})
        else:
            debug("Failed to update JSON storage",
                  stage="state_management", operation="error_handling",
                  context={'operation': operation})

        return success

    def save_step_context_to_json(self) -> bool:
        """
        Save step context and completed steps to JSON storage for persistence.

        Returns:
            bool: True if saved successfully, False otherwise
        """
        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            return False

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            return False

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            # Load existing step data
            result = storage.load_step_data(test_case_id)
            if not result:
                return False

            step_data, metadata = result

            # Add step context and completed steps to metadata
            metadata['step_context'] = getattr(self, 'step_context', {})
            metadata['completed_steps'] = getattr(self, 'completed_steps', [])
            metadata['step_context_updated'] = datetime.now().isoformat()

            # Save back to storage
            success = storage.save_step_data(test_case_id, step_data, metadata)

            if success:
                debug("Successfully saved step context to JSON",
                      stage="state_management", operation="data_saving",
                      context={'test_case_id': test_case_id,
                              'completed_steps_count': len(getattr(self, 'completed_steps', [])),
                              'step_context_count': len(getattr(self, 'step_context', {}))})

            return success

        except Exception as e:
            debug("Error saving step context to JSON",
                  stage="state_management", operation="error_handling",
                  context={'test_case_id': test_case_id, 'error': str(e)})
            return False

    def restore_step_context_from_json(self) -> bool:
        """
        Restore step context and completed steps from JSON storage.

        Returns:
            bool: True if restored successfully, False otherwise
        """
        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            return False

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            return False

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            # Load step data with metadata
            result = storage.load_step_data(test_case_id)
            if not result:
                return False

            step_data, metadata = result

            # Restore step context and completed steps from metadata
            if 'step_context' in metadata:
                self.step_context = metadata['step_context']
                debug("Restored step_context from JSON",
                      stage="state_management", operation="data_loading",
                      context={'test_case_id': test_case_id, 'step_context_count': len(self.step_context)})

            if 'completed_steps' in metadata:
                self.completed_steps = metadata['completed_steps']
                debug("Restored completed_steps from JSON",
                      stage="state_management", operation="data_loading",
                      context={'test_case_id': test_case_id, 'completed_steps_count': len(self.completed_steps)})

            return True

        except Exception as e:
            debug("Error restoring step context from JSON",
                  stage="state_management", operation="error_handling",
                  context={'test_case_id': test_case_id, 'error': str(e)})
            return False

    def get_current_step_from_json(self, step_index: int) -> Optional[Dict[str, Any]]:
        """
        Get a specific step from JSON storage by index.

        Args:
            step_index: Index of the step to retrieve

        Returns:
            Step dictionary or None if not found
        """
        try:
            step_data = self.get_effective_step_table()
            if 0 <= step_index < len(step_data):
                return step_data[step_index]
            else:
                return None
        except ValueError as e:
            debug(f"Failed to get step from JSON",
                  stage="state_management", operation="error_handling",
                  context={'step_index': step_index, 'error': str(e)})
            return None

    def get_step_data_from_json(self, step_no: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific step from JSON storage by step number.

        Args:
            step_no: Step number to retrieve

        Returns:
            Step dictionary or None if not found
        """
        try:
            step_data = self.get_effective_step_table()

            # Search for step by step_no
            for step in step_data:
                if str(step.get('step_no', '')) == str(step_no):
                    debug(f"Found step data for step {step_no}",
                          stage="state_management", operation="data_retrieval",
                          context={'step_no': step_no, 'step_keys': list(step.keys())})
                    return step

            debug(f"No step found with step_no {step_no}",
                  stage="state_management", operation="data_retrieval",
                  context={'step_no': step_no, 'available_steps': [str(s.get('step_no', '')) for s in step_data]})
            return None

        except ValueError as e:
            debug(f"Failed to get step data from JSON",
                  stage="state_management", operation="error_handling",
                  context={'step_no': step_no, 'error': str(e)})
            return None

    def update_single_step_in_json(self, step_no: str, updates: Dict[str, Any], operation: str) -> bool:
        """
        Update a specific step in JSON storage with new data.

        Args:
            step_no: Step number to update
            updates: Dictionary of fields to update
            operation: Description of the operation

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            # Get current step data
            step_data = self.get_effective_step_table()

            # Find and update the specific step
            step_updated = False
            for step in step_data:
                if str(step.get('step_no', '')) == str(step_no):
                    step.update(updates)
                    step_updated = True
                    debug(f"Updated step {step_no} in JSON",
                          stage="state_management", operation="data_update",
                          context={'step_no': step_no, 'updated_fields': list(updates.keys())})
                    break

            if not step_updated:
                debug(f"Step {step_no} not found for update",
                      stage="state_management", operation="validation_warning",
                      context={'step_no': step_no, 'available_steps': [str(s.get('step_no', '')) for s in step_data]})
                return False

            # Save updated step data
            return self.update_step_data_in_json(step_data, operation, {'updated_step': step_no})

        except Exception as e:
            debug(f"Failed to update step {step_no} in JSON",
                  stage="state_management", operation="error_handling",
                  context={'step_no': step_no, 'error': str(e)})
            return False
