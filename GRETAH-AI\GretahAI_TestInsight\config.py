import streamlit as st
"""
Configuration module for Gretah AI TestInsight Pro Streamlit application.
This module provides utility functions for configuring the Streamlit application's
appearance, including page setup and CSS loading.
Functions:
    load_css(file_name): Loads custom CSS from a file into the Streamlit app.
    setup_page_config(): Sets the Streamlit page configuration and loads custom CSS.
Note:
    The application now uses automatic theme detection based on the user's browser/OS
    preference using CSS @media (prefers-color-scheme) queries.
"""

def load_css(file_name):
    """Loads CSS from a file into the Streamlit app."""
    try:
        with open(file_name) as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS file not found: {file_name}")

def setup_page_config():
    """Sets the Streamlit page configuration and loads custom CSS."""
    st.set_page_config(page_title="Gretah AI TestInsight Pro", layout="wide")
    load_css("style.css")


