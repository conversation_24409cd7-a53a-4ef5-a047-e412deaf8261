```json
[
  {
    "scenario_name": "Invalid Username Login Lockout",
    "type": "negative",
    "prerequisites": "User should have access to the login page and should not be locked out.",
    "Test Case Objective": "Verify that the system locks the user account after three unsuccessful login attempts with an invalid username.",
    "steps": [
      {"action": "Verify if user is able to enter an invalid username and a valid password.", "expected_result": "The system should display an 'Invalid credentials' error message."},
      {"action": "Verify if user is able to attempt login with the same invalid username and valid password three times consecutively.", "expected_result": "The system should display an error message indicating that the account is locked."},
      {"action": "Verify if user is able to attempt to log in again immediately after the lockout message is displayed.", "expected_result": "The system should continue to display the account locked message and prevent login attempts."},
      {"action": "Verify if user is able to reset password after being locked out.", "expected_result": "User should be able to reset the password using the 'Forgot Password' flow and regain access."}
    ]
  },
  {
    "scenario_name": "Invalid Password Login Lockout",
    "type": "negative",
    "prerequisites": "User should have access to the login page and valid credentials for the test environment.",
    "Test Case Objective": "Verify that the system locks the user account after three unsuccessful login attempts with a valid username and invalid password.",
    "steps": [
      {"action": "Verify if user is able to enter a valid username and an invalid password.", "expected_result": "The system should display an 'Invalid credentials' error message."},
      {"action": "Verify if user is able to attempt login with the valid username and different invalid passwords three times consecutively.", "expected_result": "The system should display an error message indicating that the account is locked."},
      {"action": "Verify if user is able to attempt to log in using correct credentials immediately after the lockout message is displayed.", "expected_result": "The system should continue to display the account locked message and prevent login attempts."}
    ]
  },
  {
    "scenario_name": "Case Sensitivity of Username",
    "type": "negative",
    "prerequisites": "User should have access to the login page and a valid username.",
    "Test Case Objective": "Verify that the system correctly handles case sensitivity for the username during login attempts and locks out after multiple failed case variations.",
    "steps": [
      {"action": "Verify if user is able to enter a valid username with incorrect capitalization (e.g., 'UsErNaMe' instead of 'username') and a valid password.", "expected_result": "The system should display an 'Invalid credentials' error message."},
      {"action": "Verify if user is able to attempt login three times consecutively with different capitalization variations of the valid username and the correct password.", "expected_result": "The system should display an error message indicating that the account is locked."},
      {"action": "Verify if user is able to attempt to log in with the correct username and password after the account is locked due to case-sensitivity failures.", "expected_result": "The system should display an account locked error message and prevent login."}
    ]
  },
  {
    "scenario_name": "SQL Injection Attempt in Username Field",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system prevents SQL injection attacks via the username field and handles invalid SQL injection attempts gracefully.",
    "steps": [
      {"action": "Verify if user is able to enter a username containing SQL injection characters (e.g., 'username' OR '1'='1') and a valid password.", "expected_result": "The system should display an 'Invalid credentials' error message, or a generic error message and not execute the SQL injection."},
      {"action": "Verify if user is able to attempt login three times consecutively with different SQL injection strings in the username field and a valid password.", "expected_result": "The system should display an error message after three attempts, or a generic error message, without compromising the database."},
      {"action": "Verify if user is able to check the system logs for any indication of successful SQL injection after the attempts.", "expected_result": "The system logs should not show any evidence of successful SQL injection or unauthorized database access."}
    ]
  },
  {
    "scenario_name": "Leading/Trailing Spaces in Username",
    "type": "negative",
    "prerequisites": "User should have access to the login page and a valid username and password.",
    "Test Case Objective": "Verify that the system handles usernames with leading or trailing spaces correctly and locks the account after multiple failed attempts.",
    "steps": [
      {"action": "Verify if user is able to enter a valid username with leading spaces and the correct password.", "expected_result": "The system should display an 'Invalid credentials' error message."},
      {"action": "Verify if user is able to enter a valid username with trailing spaces and the correct password.", "expected_result": "The system should display an 'Invalid credentials' error message."},
      {"action": "Verify if user is able to attempt login three times consecutively using the username with leading or trailing spaces.", "expected_result": "The system should display an error message indicating that the account is locked."},
      {"action": "Verify if user is able to log in with the correct username and password after the lockout occurs.", "expected_result": "The system should display an account locked error message."}
    ]
  }
]
```