"""
JIRA Operations Module for GretahAI CaseForge

This module provides JIRA integration capabilities including ticket operations,
description enhancement, and test case management.

Functions:
- save_enhanced_jira_description: Save enhanced JIRA descriptions
- parse_enhanced_jira_description: Parse enhanced descriptions
- verify_jira_enhanced_description: Verify JIRA description enhancements

© 2025 GretahAI Team
"""

import os
import json
from datetime import datetime
from typing import Optional, Dict, Any


def save_enhanced_jira_description(jira_id, enhanced_description, timestamp=None):
    """
    Saves enhanced JIRA description to local storage for future reference.
    
    This function stores AI-enhanced JIRA ticket descriptions locally to enable
    reuse and maintain consistency across test case generation sessions. It creates
    a structured storage system for enhanced descriptions with metadata.

    Args:
        jira_id (str): JIRA ticket identifier (e.g., "TP-1", "STORY-123")
        enhanced_description (str): AI-enhanced description content
        timestamp (str, optional): Custom timestamp for the enhancement. 
                                  Defaults to None (uses current time).

    Returns:
        bool: True if save was successful, False otherwise

    Storage Structure:
        - Creates `enhanced_descriptions/` directory if it doesn't exist
        - Saves each enhancement as JSON file named by JIRA ID
        - Includes metadata: timestamp, original description, enhancement details
        - Supports versioning for multiple enhancements of same ticket

    File Format:
        ```json
        {
            "jira_id": "TP-1",
            "enhanced_description": "Enhanced content...",
            "timestamp": "2025-01-15 14:30:22",
            "version": 1,
            "metadata": {
                "enhancement_type": "ai_generated",
                "character_count": 1250
            }
        }
        ```

    Error Handling:
        - Creates directory structure if missing
        - Handles file system permissions issues
        - Manages JSON serialization errors
        - Returns False on any failure without raising exceptions

    Use Cases:
        - Storing AI-enhanced JIRA descriptions
        - Maintaining enhancement history
        - Enabling description reuse across sessions
        - Supporting audit trails for enhancements

    Example:
        success = save_enhanced_jira_description(
            "TP-1",
            "Enhanced description with better test scenarios...",
            "2025-01-15 14:30:22"
        )
        if success:
            print("Enhancement saved successfully")
    """
    try:
        # Create enhanced_descriptions directory if it doesn't exist
        enhanced_dir = "enhanced_descriptions"
        os.makedirs(enhanced_dir, exist_ok=True)
        
        # Use current timestamp if none provided
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Create the data structure
        enhancement_data = {
            "jira_id": jira_id,
            "enhanced_description": enhanced_description,
            "timestamp": timestamp,
            "version": 1,
            "metadata": {
                "enhancement_type": "ai_generated",
                "character_count": len(enhanced_description)
            }
        }
        
        # Save to file
        filename = os.path.join(enhanced_dir, f"{jira_id}_enhanced.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(enhancement_data, f, indent=2, ensure_ascii=False)
        
        return True
        
    except Exception as e:
        print(f"Error saving enhanced description for {jira_id}: {e}")
        return False


def parse_enhanced_jira_description(enhanced_description):
    """
    Parses enhanced JIRA description to extract structured information.
    
    This function analyzes AI-enhanced JIRA descriptions to extract key components
    such as acceptance criteria, business rules, user stories, and technical requirements.
    It provides structured data for improved test case generation.

    Args:
        enhanced_description (str): Enhanced JIRA description content

    Returns:
        dict: Parsed description components with structure:
            {
                "acceptance_criteria": ["criteria1", "criteria2", ...],
                "business_rules": ["rule1", "rule2", ...],
                "user_stories": ["story1", "story2", ...],
                "technical_requirements": ["req1", "req2", ...],
                "edge_cases": ["case1", "case2", ...],
                "raw_content": "original_content"
            }

    Parsing Strategy:
        - Uses pattern matching to identify structured sections
        - Extracts bulleted and numbered lists
        - Identifies keywords like "acceptance criteria", "business rules"
        - Handles various formatting styles and markdown
        - Preserves original content for fallback

    Section Detection:
        - **Acceptance Criteria**: Key testable requirements
        - **Business Rules**: Business logic constraints
        - **User Stories**: User-focused functionality descriptions
        - **Technical Requirements**: Technical specifications
        - **Edge Cases**: Boundary and exceptional scenarios

    Error Handling:
        - Returns structured format even for unstructured input
        - Handles missing sections gracefully
        - Preserves original content in all cases
        - Manages encoding and special character issues

    Use Cases:
        - Improving test case generation prompts
        - Extracting testable components
        - Structuring requirements for AI models
        - Supporting requirements analysis

    Example:
        parsed = parse_enhanced_jira_description(enhanced_desc)
        print(f"Found {len(parsed['acceptance_criteria'])} acceptance criteria")
        print(f"Found {len(parsed['business_rules'])} business rules")
    """
    try:
        # Initialize the parsed structure
        parsed = {
            "acceptance_criteria": [],
            "business_rules": [],
            "user_stories": [],
            "technical_requirements": [],
            "edge_cases": [],
            "raw_content": enhanced_description
        }
        
        # Convert to lowercase for pattern matching
        content_lower = enhanced_description.lower()
        lines = enhanced_description.split('\n')
        
        current_section = None
        
        for line in lines:
            line_stripped = line.strip()
            line_lower = line_stripped.lower()
            
            # Skip empty lines
            if not line_stripped:
                continue
            
            # Detect section headers
            if any(keyword in line_lower for keyword in ['acceptance criteria', 'acceptance criterion']):
                current_section = 'acceptance_criteria'
                continue
            elif any(keyword in line_lower for keyword in ['business rule', 'business logic']):
                current_section = 'business_rules'
                continue
            elif any(keyword in line_lower for keyword in ['user story', 'user stories']):
                current_section = 'user_stories'
                continue
            elif any(keyword in line_lower for keyword in ['technical requirement', 'technical spec']):
                current_section = 'technical_requirements'
                continue
            elif any(keyword in line_lower for keyword in ['edge case', 'corner case', 'boundary']):
                current_section = 'edge_cases'
                continue
            
            # Extract bulleted or numbered items
            if current_section and (line_stripped.startswith('-') or 
                                   line_stripped.startswith('*') or 
                                   line_stripped.startswith('•') or
                                   any(line_stripped.startswith(f"{i}.") for i in range(1, 10))):
                # Clean up the bullet/number
                clean_item = line_stripped
                for prefix in ['-', '*', '•']:
                    if clean_item.startswith(prefix):
                        clean_item = clean_item[1:].strip()
                        break
                
                # Remove numbers
                import re
                clean_item = re.sub(r'^\d+\.\s*', '', clean_item)
                
                if clean_item:
                    parsed[current_section].append(clean_item)
        
        return parsed
        
    except Exception as e:
        print(f"Error parsing enhanced description: {e}")
        return {
            "acceptance_criteria": [],
            "business_rules": [],
            "user_stories": [],
            "technical_requirements": [],
            "edge_cases": [],
            "raw_content": enhanced_description
        }


def verify_jira_enhanced_description(jira_id, enhanced_description=None):
    """
    Verifies and validates JIRA enhanced description quality and completeness.
    
    This function performs quality checks on enhanced JIRA descriptions to ensure
    they meet standards for test case generation. It validates content structure,
    completeness, and provides recommendations for improvement.

    Args:
        jira_id (str): JIRA ticket identifier for context
        enhanced_description (str, optional): Description to verify. 
                                            Defaults to None (loads from storage).

    Returns:
        dict: Verification results with structure:
            {
                "is_valid": bool,
                "quality_score": float,  # 0.0 to 1.0
                "issues": ["issue1", "issue2", ...],
                "suggestions": ["suggestion1", "suggestion2", ...],
                "metrics": {
                    "character_count": int,
                    "word_count": int,
                    "section_count": int,
                    "criteria_count": int
                }
            }

    Validation Criteria:
        - **Content Length**: Adequate detail for test generation
        - **Structure**: Presence of key sections (acceptance criteria, etc.)
        - **Clarity**: Clear and actionable requirements
        - **Completeness**: Sufficient detail for comprehensive testing
        - **Testability**: Requirements are testable and measurable

    Quality Metrics:
        - Character and word count analysis
        - Section structure evaluation
        - Acceptance criteria completeness
        - Business rule clarity
        - Edge case coverage

    Issues Detection:
        - Missing acceptance criteria
        - Vague or ambiguous requirements
        - Insufficient detail for testing
        - Poor structure or formatting
        - Missing edge cases or business rules

    Suggestions Generation:
        - Specific recommendations for improvement
        - Missing section identification
        - Content enhancement suggestions
        - Structure improvement advice

    Use Cases:
        - Quality assurance for enhanced descriptions
        - Identifying areas for improvement
        - Validating AI enhancement quality
        - Supporting manual review processes

    Example:
        verification = verify_jira_enhanced_description("TP-1", enhanced_desc)
        if verification["is_valid"]:
            print(f"Quality score: {verification['quality_score']:.2f}")
        else:
            print("Issues found:", verification["issues"])
    """
    try:
        # Load from storage if description not provided
        if enhanced_description is None:
            enhanced_dir = "enhanced_descriptions"
            filename = os.path.join(enhanced_dir, f"{jira_id}_enhanced.json")
            
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    enhanced_description = data.get("enhanced_description", "")
            else:
                return {
                    "is_valid": False,
                    "quality_score": 0.0,
                    "issues": ["Enhanced description not found in storage"],
                    "suggestions": ["Generate enhanced description first"],
                    "metrics": {
                        "character_count": 0,
                        "word_count": 0,
                        "section_count": 0,
                        "criteria_count": 0
                    }
                }
        
        # Parse the description for analysis
        parsed = parse_enhanced_jira_description(enhanced_description)
        
        # Calculate metrics
        character_count = len(enhanced_description)
        word_count = len(enhanced_description.split())
        section_count = sum(1 for section in parsed.values() if isinstance(section, list) and section)
        criteria_count = len(parsed.get("acceptance_criteria", []))
        
        # Initialize validation results
        issues = []
        suggestions = []
        quality_score = 0.0
        
        # Content length validation
        if character_count < 100:
            issues.append("Description too short (less than 100 characters)")
            suggestions.append("Add more detailed requirements and acceptance criteria")
        elif character_count < 300:
            quality_score += 0.1
            suggestions.append("Consider adding more detail for comprehensive testing")
        else:
            quality_score += 0.3
        
        # Word count validation
        if word_count < 20:
            issues.append("Insufficient detail (less than 20 words)")
        else:
            quality_score += 0.2
        
        # Acceptance criteria validation
        if criteria_count == 0:
            issues.append("No acceptance criteria found")
            suggestions.append("Add clear, testable acceptance criteria")
        elif criteria_count < 3:
            quality_score += 0.2
            suggestions.append("Consider adding more acceptance criteria for comprehensive coverage")
        else:
            quality_score += 0.3
        
        # Section structure validation
        if section_count < 2:
            issues.append("Limited section structure")
            suggestions.append("Add sections like business rules, user stories, or edge cases")
        else:
            quality_score += 0.2
        
        # Overall validation
        is_valid = len(issues) == 0 and quality_score >= 0.6
        
        return {
            "is_valid": is_valid,
            "quality_score": min(quality_score, 1.0),
            "issues": issues,
            "suggestions": suggestions,
            "metrics": {
                "character_count": character_count,
                "word_count": word_count,
                "section_count": section_count,
                "criteria_count": criteria_count
            }
        }
        
    except Exception as e:
        return {
            "is_valid": False,
            "quality_score": 0.0,
            "issues": [f"Verification error: {str(e)}"],
            "suggestions": ["Check description format and try again"],
            "metrics": {
                "character_count": 0,
                "word_count": 0,
                "section_count": 0,
                "criteria_count": 0
            }
        }
