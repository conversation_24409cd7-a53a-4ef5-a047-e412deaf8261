```json
[
  {
    "scenario_name": "Invalid Username and Password Lockout",
    "type": "negative",
    "prerequisites": "User should have a registered account in the system.",
    "Test Case Objective": "Verify that the system locks the user account after three unsuccessful login attempts with an invalid username and password.",
    "steps": [
      {"action": "Verify if user is able to enter an invalid username and password combination and submit the login form.", "expected_result": "An 'Invalid credentials' error message should be displayed."},
      {"action": "Verify if user is able to repeat the previous step two more times with different invalid username and password combinations.", "expected_result": "An 'Invalid credentials' error message should be displayed after each attempt."},
      {"action": "Verify if user is able to attempt a fourth login with valid credentials after three failed attempts.", "expected_result": "An 'Account locked' error message should be displayed, and the user should be unable to log in."},
      {"action": "Verify if user is able to attempt a login with a SQL injection.", "expected_result": "An 'Invalid credentials' error message should be displayed."}
    ]
  },
  {
    "scenario_name": "Case Sensitivity of Username",
    "type": "negative",
    "prerequisites": "User should have a registered account with a specific username in the system.",
    "Test Case Objective": "Verify that the system rejects login attempts with incorrect capitalization of the username after three tries.",
    "steps": [
      {"action": "Verify if user is able to enter a username with incorrect capitalization (e.g., 'UsErNaMe' instead of 'Username') and the correct password.", "expected_result": "An 'Invalid credentials' error message should be displayed."},
      {"action": "Verify if user is able to repeat the previous step two more times, each time with a different capitalization error in the username.", "expected_result": "An 'Invalid credentials' error message should be displayed after each attempt."},
      {"action": "Verify if user is able to attempt a fourth login with the correctly capitalized username and valid password after three failed attempts with incorrect capitalization.", "expected_result": "An 'Account locked' error message should be displayed, and the user should be unable to log in."}
    ]
  },
  {
    "scenario_name": "Empty Username and Password Fields",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system handles login attempts with empty username and password fields and displays appropriate error messages.",
    "steps": [
      {"action": "Verify if user is able to attempt a login with both username and password fields left empty.", "expected_result": "An error message indicating that both fields are required should be displayed."},
      {"action": "Verify if user is able to attempt a login with only the username field left empty and a valid password entered.", "expected_result": "An error message indicating that the username field is required should be displayed."},
      {"action": "Verify if user is able to attempt a login with only the password field left empty and a valid username entered.", "expected_result": "An error message indicating that the password field is required should be displayed."}
    ]
  },
  {
    "scenario_name": "Username with Special Characters",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system handles login attempts with special characters in the username field.",
    "steps": [
      {"action": "Verify if user is able to enter a username containing special characters (e.g., !@#$%^&*) and a valid password.", "expected_result": "An 'Invalid credentials' error message should be displayed."},
      {"action": "Verify if user is able to repeat the previous step two more times with different sets of special characters in the username.", "expected_result": "An 'Invalid credentials' error message should be displayed after each attempt."},
      {"action": "Verify if user is able to attempt a fourth login with a valid username and password after three failed attempts with usernames containing special characters.", "expected_result": "An 'Account locked' error message should be displayed, and the user should be unable to log in."}
    ]
  },
  {
    "scenario_name": "Password with Leading/Trailing Spaces",
    "type": "negative",
    "prerequisites": "User should have a registered account in the system.",
    "Test Case Objective": "Verify that the system does not allow login with password containing leading or trailing spaces.",
    "steps": [
      {"action": "Verify if user is able to enter a valid username and a password with a leading space.", "expected_result": "An 'Invalid credentials' error message should be displayed."},
      {"action": "Verify if user is able to enter a valid username and a password with a trailing space.", "expected_result": "An 'Invalid credentials' error message should be displayed."},
      {"action": "Verify if user is able to repeat the previous steps two more times.", "expected_result": "An 'Invalid credentials' error message should be displayed after each attempt."},
       {"action": "Verify if user is able to attempt a fourth login with a valid username and password after three failed attempts with passwords with spaces.", "expected_result": "An 'Account locked' error message should be displayed, and the user should be unable to log in."}
    ]
  }
]
```