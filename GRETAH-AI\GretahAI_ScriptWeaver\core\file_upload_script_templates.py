"""
File Upload Script Templates for GretahAI ScriptWeaver

This module provides script generation templates and utilities for handling
file upload functionality in generated PyTest scripts.

Key Features:
- Selenium file upload automation templates
- File path resolution utilities
- File type specific handling
- Error handling for file operations

© 2025 Cogniron All Rights Reserved.
"""

import os
from pathlib import Path
from typing import Dict, Any, List, Optional

# Import GRETAH standardized logging
from debug_utils import debug


class FileUploadScriptTemplates:
    """
    Provides templates and utilities for generating file upload automation scripts.
    
    Handles different file upload scenarios and generates appropriate Selenium
    automation code for file upload testing.
    """
    
    @staticmethod
    def get_file_upload_template(file_metadata: Dict[str, Any], step_action: str) -> str:
        """
        Generate a file upload template based on file metadata and step action.
        
        Args:
            file_metadata: File metadata dictionary
            step_action: Description of the test step action
            
        Returns:
            Python code template for file upload automation
        """
        file_category = file_metadata.get('file_category', 'unknown')
        original_name = file_metadata.get('original_name', 'unknown')
        file_path = file_metadata.get('absolute_path', '')
        
        # Generate appropriate template based on file category
        if file_category == 'images':
            return FileUploadScriptTemplates._get_image_upload_template(file_metadata, step_action)
        elif file_category == 'documents':
            return FileUploadScriptTemplates._get_document_upload_template(file_metadata, step_action)
        elif file_category == 'data_files':
            return FileUploadScriptTemplates._get_data_file_upload_template(file_metadata, step_action)
        else:
            return FileUploadScriptTemplates._get_generic_upload_template(file_metadata, step_action)
    
    @staticmethod
    def _get_image_upload_template(file_metadata: Dict[str, Any], step_action: str) -> str:
        """Generate template for image file uploads."""
        file_path = file_metadata.get('absolute_path', '')
        original_name = file_metadata.get('original_name', 'image.jpg')
        
        return f'''
        # File upload for image: {original_name}
        try:
            # Locate the file input element
            file_input = browser.find_element(By.CSS_SELECTOR, "input[type='file']")
            
            # Verify file exists before upload
            upload_file_path = r"{file_path}"
            if not os.path.exists(upload_file_path):
                raise FileNotFoundError(f"Upload file not found: {{upload_file_path}}")
            
            # Upload the image file
            file_input.send_keys(upload_file_path)
            
            # Wait for upload to complete (adjust timeout as needed)
            WebDriverWait(browser, 30).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Optional: Verify upload success by checking for success message or uploaded file preview
            # You may need to adjust this based on the specific application behavior
            time.sleep(2)  # Allow time for upload processing
            
            print(f"Successfully uploaded image: {original_name}")
            
        except FileNotFoundError as e:
            print(f"File not found error: {{e}}")
            raise
        except Exception as e:
            print(f"Error uploading image file: {{e}}")
            raise
        '''
    
    @staticmethod
    def _get_document_upload_template(file_metadata: Dict[str, Any], step_action: str) -> str:
        """Generate template for document file uploads."""
        file_path = file_metadata.get('absolute_path', '')
        original_name = file_metadata.get('original_name', 'document.pdf')
        
        return f'''
        # File upload for document: {original_name}
        try:
            # Locate the file input element
            file_input = browser.find_element(By.CSS_SELECTOR, "input[type='file']")
            
            # Verify file exists before upload
            upload_file_path = r"{file_path}"
            if not os.path.exists(upload_file_path):
                raise FileNotFoundError(f"Upload file not found: {{upload_file_path}}")
            
            # Upload the document file
            file_input.send_keys(upload_file_path)
            
            # Wait for upload to complete
            WebDriverWait(browser, 60).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Optional: Wait for upload progress indicator to disappear
            try:
                WebDriverWait(browser, 30).until_not(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".upload-progress, .uploading"))
                )
            except:
                pass  # Progress indicator might not be present
            
            print(f"Successfully uploaded document: {original_name}")
            
        except FileNotFoundError as e:
            print(f"File not found error: {{e}}")
            raise
        except Exception as e:
            print(f"Error uploading document file: {{e}}")
            raise
        '''
    
    @staticmethod
    def _get_data_file_upload_template(file_metadata: Dict[str, Any], step_action: str) -> str:
        """Generate template for data file uploads."""
        file_path = file_metadata.get('absolute_path', '')
        original_name = file_metadata.get('original_name', 'data.csv')
        
        return f'''
        # File upload for data file: {original_name}
        try:
            # Locate the file input element
            file_input = browser.find_element(By.CSS_SELECTOR, "input[type='file']")
            
            # Verify file exists before upload
            upload_file_path = r"{file_path}"
            if not os.path.exists(upload_file_path):
                raise FileNotFoundError(f"Upload file not found: {{upload_file_path}}")
            
            # Upload the data file
            file_input.send_keys(upload_file_path)
            
            # Wait for upload and processing to complete
            WebDriverWait(browser, 120).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # For data files, there might be additional processing time
            # Wait for any processing indicators to disappear
            try:
                WebDriverWait(browser, 60).until_not(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".processing, .importing, .loading"))
                )
            except:
                pass  # Processing indicator might not be present
            
            print(f"Successfully uploaded data file: {original_name}")
            
        except FileNotFoundError as e:
            print(f"File not found error: {{e}}")
            raise
        except Exception as e:
            print(f"Error uploading data file: {{e}}")
            raise
        '''
    
    @staticmethod
    def _get_generic_upload_template(file_metadata: Dict[str, Any], step_action: str) -> str:
        """Generate generic template for file uploads."""
        file_path = file_metadata.get('absolute_path', '')
        original_name = file_metadata.get('original_name', 'file.txt')
        
        return f'''
        # File upload for: {original_name}
        try:
            # Locate the file input element
            file_input = browser.find_element(By.CSS_SELECTOR, "input[type='file']")
            
            # Verify file exists before upload
            upload_file_path = r"{file_path}"
            if not os.path.exists(upload_file_path):
                raise FileNotFoundError(f"Upload file not found: {{upload_file_path}}")
            
            # Upload the file
            file_input.send_keys(upload_file_path)
            
            # Wait for upload to complete
            WebDriverWait(browser, 30).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            time.sleep(2)  # Allow time for upload processing
            
            print(f"Successfully uploaded file: {original_name}")
            
        except FileNotFoundError as e:
            print(f"File not found error: {{e}}")
            raise
        except Exception as e:
            print(f"Error uploading file: {{e}}")
            raise
        '''
    
    @staticmethod
    def get_file_upload_imports() -> str:
        """Get required imports for file upload scripts."""
        return '''import os
from pathlib import Path
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
import time'''
    
    @staticmethod
    def get_file_verification_template(file_metadata: Dict[str, Any]) -> str:
        """
        Generate template for verifying file upload success.
        
        Args:
            file_metadata: File metadata dictionary
            
        Returns:
            Python code template for upload verification
        """
        original_name = file_metadata.get('original_name', 'file')
        
        return f'''
        # Verify file upload success
        try:
            # Method 1: Check for success message
            try:
                success_message = WebDriverWait(browser, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".success, .uploaded, .complete"))
                )
                print("Upload success message found")
            except:
                print("No success message found (this may be normal)")
            
            # Method 2: Check for uploaded file in file list
            try:
                uploaded_file_element = WebDriverWait(browser, 10).until(
                    EC.presence_of_element_located((By.XPATH, f"//span[contains(text(), '{original_name}')]"))
                )
                print(f"Uploaded file '{original_name}' found in file list")
            except:
                print("Uploaded file not found in file list (this may be normal)")
            
            # Method 3: Check for file preview or thumbnail
            try:
                file_preview = browser.find_element(By.CSS_SELECTOR, ".file-preview, .thumbnail, .preview")
                print("File preview/thumbnail found")
            except:
                print("No file preview found (this may be normal)")
                
        except Exception as e:
            print(f"Error during upload verification: {{e}}")
            # Don't raise here as verification failure doesn't necessarily mean upload failed
        '''
    
    @staticmethod
    def enhance_script_with_file_upload(script_content: str, test_data: Dict[str, Any]) -> str:
        """
        Enhance an existing script with file upload functionality.
        
        Args:
            script_content: Original script content
            test_data: Test data containing file upload information
            
        Returns:
            Enhanced script with file upload capabilities
        """
        try:
            # Find file upload entries in test data
            file_uploads = {}
            for key, value in test_data.items():
                if isinstance(value, dict) and value.get('type') == 'file_upload':
                    step_no = value.get('step_no', 'unknown')
                    file_uploads[step_no] = value
            
            if not file_uploads:
                return script_content
            
            # Add required imports if not present
            imports = FileUploadScriptTemplates.get_file_upload_imports()
            if 'import os' not in script_content:
                script_content = imports + '\n\n' + script_content
            
            # Enhance each test step function with file upload code
            enhanced_script = script_content
            
            for step_no, file_metadata in file_uploads.items():
                # Find the test function for this step
                function_pattern = f"def test_step{step_no}_"
                if function_pattern in enhanced_script:
                    # Generate file upload template
                    upload_template = FileUploadScriptTemplates.get_file_upload_template(
                        file_metadata, f"Step {step_no} file upload"
                    )
                    
                    # Insert file upload code into the function
                    # This is a simplified approach - in practice, you might need more sophisticated parsing
                    function_start = enhanced_script.find(function_pattern)
                    if function_start != -1:
                        # Find the end of the try block in the function
                        try_block_start = enhanced_script.find("try:", function_start)
                        if try_block_start != -1:
                            # Insert file upload code after the try statement
                            insertion_point = enhanced_script.find("\n", try_block_start) + 1
                            enhanced_script = (
                                enhanced_script[:insertion_point] +
                                upload_template +
                                enhanced_script[insertion_point:]
                            )
            
            debug("Enhanced script with file upload functionality",
                  stage="file_upload", operation="script_enhancement",
                  context={'file_upload_count': len(file_uploads)})
            
            return enhanced_script
            
        except Exception as e:
            debug(f"Error enhancing script with file upload: {e}",
                  stage="file_upload", operation="script_enhancement_error",
                  context={'error': str(e)})
            return script_content
    
    @staticmethod
    def get_file_upload_locator_suggestions(step_action: str) -> List[str]:
        """
        Get suggested locators for file upload elements based on step action.
        
        Args:
            step_action: Description of the test step action
            
        Returns:
            List of suggested CSS selectors for file input elements
        """
        # Common file input selectors
        common_selectors = [
            "input[type='file']",
            "input[type='file'][accept*='image']",
            "input[type='file'][accept*='document']",
            ".file-upload input",
            ".upload-area input[type='file']",
            "#file-upload",
            "#upload-input",
            "[data-testid='file-upload']",
            ".dropzone input[type='file']"
        ]
        
        # Add specific selectors based on step action
        action_lower = step_action.lower()
        specific_selectors = []
        
        if 'profile' in action_lower or 'avatar' in action_lower:
            specific_selectors.extend([
                "input[type='file'][accept*='image']",
                "#profile-picture-upload",
                ".avatar-upload input[type='file']"
            ])
        
        if 'document' in action_lower or 'pdf' in action_lower:
            specific_selectors.extend([
                "input[type='file'][accept*='pdf']",
                "input[type='file'][accept*='document']",
                ".document-upload input[type='file']"
            ])
        
        if 'import' in action_lower or 'data' in action_lower:
            specific_selectors.extend([
                "input[type='file'][accept*='csv']",
                "input[type='file'][accept*='excel']",
                ".data-import input[type='file']"
            ])
        
        # Combine and deduplicate
        all_selectors = specific_selectors + common_selectors
        return list(dict.fromkeys(all_selectors))  # Remove duplicates while preserving order
