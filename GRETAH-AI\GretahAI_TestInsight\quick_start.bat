@echo off
echo ========================================
echo   GretahAI TestInsight Management Script
echo ========================================
echo.

REM Get the directory where this script is located
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo Current directory: %CD%
echo.

:menu
echo Available options:
echo 1. Install dependencies only
echo 2. Run application (install if needed)
echo 3. Update dependencies
echo 4. Clean environment (remove venv)
echo 5. Check system requirements
echo 6. Exit
echo.
set /p choice="Please select an option (1-6): "

if "%choice%"=="1" goto install
if "%choice%"=="2" goto run
if "%choice%"=="3" goto update
if "%choice%"=="4" goto clean
if "%choice%"=="5" goto check
if "%choice%"=="6" goto exit
echo Invalid choice. Please try again.
echo.
goto menu

:check
echo ========================================
echo   System Requirements Check
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo Please install Python 3.13 or later from https://python.org
    echo.
    pause
    goto menu
) else (
    echo ✅ Python version:
    python --version
)

REM Check pip
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: pip is not available
) else (
    echo ✅ pip is available
)

REM Check if virtual environment exists
if exist "venv" (
    echo ✅ Virtual environment exists
) else (
    echo ⚠️  Virtual environment not found
)

REM Check internet connectivity
ping -n 1 google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Internet connection available
) else (
    echo ⚠️  Internet connection may not be available
)

REM Check if Ollama is running (optional)
echo Checking Ollama status...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Ollama is running - Local AI models available
) else (
    echo ⚠️  Ollama is not running - Only Google AI will be available
    echo To use local models, install and start Ollama from https://ollama.ai
)

echo.
pause
goto menu

:install
echo ========================================
echo   Installing Dependencies
echo ========================================
echo.

call :check_python
if %errorlevel% neq 0 goto menu

call :setup_venv
if %errorlevel% neq 0 goto menu

call :install_deps
if %errorlevel% neq 0 goto menu

echo.
echo ✅ Installation completed successfully!
echo.
pause
goto menu

:update
echo ========================================
echo   Updating Dependencies
echo ========================================
echo.

call :check_python
if %errorlevel% neq 0 goto menu

if not exist "venv" (
    echo Virtual environment not found. Creating new one...
    call :setup_venv
    if %errorlevel% neq 0 goto menu
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Upgrading pip...
python -m pip install --upgrade pip

echo Updating all packages...
pip install --upgrade -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to update dependencies
    pause
    goto menu
)

echo ✅ Dependencies updated successfully!
echo.
pause
goto menu

:clean
echo ========================================
echo   Cleaning Environment
echo ========================================
echo.

if exist "venv" (
    echo Removing virtual environment...
    rmdir /s /q venv
    echo ✅ Virtual environment removed successfully!
) else (
    echo ⚠️  No virtual environment found to clean
)

echo.
pause
goto menu

:run
echo ========================================
echo   Running GretahAI TestInsight
echo ========================================
echo.

call :check_python
if %errorlevel% neq 0 goto menu

if not exist "venv" (
    echo Virtual environment not found. Installing dependencies first...
    call :setup_venv
    if %errorlevel% neq 0 goto menu
    call :install_deps
    if %errorlevel% neq 0 goto menu
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo ========================================
echo   Starting GretahAI TestInsight...
echo ========================================
echo.
echo The application will open in your default web browser.
echo To stop the application, press Ctrl+C in this window.
echo.

REM Start the Streamlit application
streamlit run GretahAI_TestInsight.py

REM Keep window open if there's an error
if %errorlevel% neq 0 (
    echo.
    echo ❌ ERROR: Failed to start the application
    pause
)
goto menu



:exit
echo Goodbye!
exit /b 0

REM ========================================
REM Helper Functions
REM ========================================

:check_python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo Please install Python 3.13 or later from https://python.org
    exit /b 1
)
exit /b 0

:setup_venv
echo Creating virtual environment...
python -m venv venv
if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to create virtual environment
    exit /b 1
)
echo ✅ Virtual environment created successfully!
exit /b 0

:install_deps
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to activate virtual environment
    exit /b 1
)

echo Upgrading pip...
python -m pip install --upgrade pip

echo Installing requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to install requirements
    echo Please check your internet connection and try again
    exit /b 1
)
echo ✅ Requirements installed successfully!
exit /b 0
