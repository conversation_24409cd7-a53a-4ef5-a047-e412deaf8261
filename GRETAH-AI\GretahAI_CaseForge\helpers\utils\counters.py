"""
Test Case Counter Module for GretahAI CaseForge

This module handles test case ID generation, counter management,
and numbering logic for consistent test case identification.

Functions:
- reset_test_case_counter: Reset the global counter
- set_test_case_counter: Set counter to specific value
- get_test_case_counter: Get current counter value
- get_highest_test_case_id: Find highest existing test case ID

© 2025 GretahAI Team
"""

import os
from typing import Optional


def reset_test_case_counter():
    """
    Resets the global test case counter to start numbering from 1.
    
    This function reinitializes the test case numbering system, typically used
    when starting fresh test case generation or when clearing existing test data.
    It ensures consistent test case ID generation across the application.

    Global Variables Modified:
        Sets internal counter state to 0 (next ID will be 1)

    Use Cases:
        - Starting new test case generation session
        - After clearing database or Excel files
        - Resetting numbering for new JIRA issues
        - Testing and development scenarios

    Thread Safety:
        Function is designed for single-threaded use. In multi-threaded
        environments, additional synchronization may be required.

    Example:
        reset_test_case_counter()
        # Next generated test case will be TC_001
    """
    # Import at module level to access shared global state
    import helpers
    helpers._test_case_counter = 0
    print(f"Test case counter reset to 0.")


def set_test_case_counter(value):
    """
    Sets the test case counter to a specific value for custom numbering sequences.
    
    This function allows manual control over test case ID generation by setting
    the counter to a specific value. Useful for continuing numbering from existing
    test cases or implementing custom numbering schemes.

    Args:
        value (int): The counter value to set. Next generated test case ID will be value + 1

    Global Variables Modified:
        Updates internal counter state to specified value

    Use Cases:
        - Continuing numbering from existing test cases
        - Implementing custom ID schemes
        - Coordinating with external test management systems
        - Batch test case generation with specific ranges

    Validation:
        - Should provide non-negative integer values
        - Values are used directly without additional validation

    Example:
        set_test_case_counter(10)
        # Next generated test case will be TC_011
        
        set_test_case_counter(0)
        # Next generated test case will be TC_001
    """
    # Import at module level to access shared global state
    import helpers
    helpers._test_case_counter = value


def get_test_case_counter():
    """
    Retrieves the current value of the test case counter.
    
    This function returns the current state of the test case numbering system,
    allowing other components to understand what the next test case ID will be
    and to make decisions about numbering continuation.

    Returns:
        int: Current counter value. Next test case ID will be counter + 1

    Use Cases:
        - Checking current numbering state
        - Determining next test case ID
        - Coordinating numbering across multiple operations
        - Debugging numbering issues

    Example:
        current = get_test_case_counter()
        print(f"Next test case will be TC_{current + 1:03d}")
        
        # If counter is 5, output would be: "Next test case will be TC_006"
    """
    # Import at module level to access shared global state
    import helpers
    return helpers._test_case_counter


def get_highest_test_case_id(jira_id, test_type=None):
    """
    Retrieves the highest test case ID number for a specific JIRA issue and test type.
    
    This function analyzes existing test cases to find the highest numbered test case ID,
    enabling intelligent continuation of numbering sequences when adding new test cases.
    It supports both database and Excel file data sources.

    Args:
        jira_id (str): JIRA issue key to search within (e.g., "TP-1", "STORY-123")
        test_type (str, optional): Test type to filter by ("positive", "negative", 
                                  "security", "performance", "all"). Defaults to None.

    Returns:
        int: Highest test case number found, or 0 if no test cases exist

    Data Source Strategy:
        1. Database Primary: Uses Test_case_db_helper to query database
        2. Excel Fallback: Searches Excel files if database unavailable
        3. Number Extraction: Parses test case IDs to extract numeric portions
        4. Maximum Detection: Finds highest number across all matching test cases

    Test Case ID Format:
        - Expected format: TC_XXX where XXX is a 3-digit number
        - Examples: TC_001, TC_010, TC_100
        - Extracts numeric portion for comparison
        - Handles various numbering schemes

    Database Integration:
        - Uses get_highest_test_case_id_number() from database helper
        - Filters by JIRA ID and test type automatically
        - Handles database connection failures gracefully
        - Returns accurate counts from live data

    Excel File Processing:
        - Searches Test_cases folder for relevant files
        - Reads Excel files using pandas
        - Processes Test Case ID column
        - Extracts and compares numeric values

    Error Handling:
        - Returns 0 if no test cases found
        - Handles database connection failures
        - Manages missing or corrupted files
        - Gracefully processes invalid ID formats

    Use Cases:
        - Continuing test case numbering sequences
        - Preventing ID conflicts in new generation
        - Calculating next available test case number        - Validating numbering consistency

    Example:
        # Get highest ID for specific JIRA and test type
        highest = get_highest_test_case_id("TP-1", "positive")
        next_id = highest + 1
        print(f"Next test case should be TC_{next_id:03d}")
        
        # Get highest across all test types
        highest = get_highest_test_case_id("TP-1")
        print(f"Highest existing test case number: {highest}")
    """
    try:
        # First, try to get from database
        try:
            import db_helper as db
            
            # Use database helper to get highest ID across all test types
            highest_id = db.get_highest_test_case_id_number(db.DATABASE_PATH, jira_id, "all")
            
            if highest_id is not None:
                return highest_id
                
        except Exception as db_error:
            print(f"Database lookup failed, falling back to file search: {db_error}")
        
        # Fallback to Excel file search
        from helpers.file import get_latest_test_case_file
        import pandas as pd
        import re
        
        # Get the latest test case file
        file_path, file_name = get_latest_test_case_file(jira_id, test_type)
        
        if not file_path or not os.path.exists(file_path):
            return 0
        
        # Read the Excel file
        df = pd.read_excel(file_path)
        
        if df.empty or 'Test Case ID' not in df.columns:
            return 0
        
        # Extract numeric parts from test case IDs
        highest_num = 0
        
        for test_case_id in df['Test Case ID'].dropna():
            if isinstance(test_case_id, str):
                # Extract number from format like "TC_001", "TC_123", etc.
                match = re.search(r'TC_(\d+)', test_case_id)
                if match:
                    num = int(match.group(1))
                    highest_num = max(highest_num, num)
        
        return highest_num
        
    except Exception as e:
        print(f"Error getting highest test case ID for {jira_id}: {e}")
        return 0
