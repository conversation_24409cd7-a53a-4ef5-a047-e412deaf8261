"""
Database decorators and utility functions for the GretahAI CaseForge system.

This module provides the retry decorator and other utility functions used across
the database helper package for handling database lock contention and error recovery.
"""

import sqlite3
import time
import random
import functools


def retry_on_db_lock(max_attempts=5, initial_wait=0.1):
    """
    Decorator to retry database operations when SQLite database is locked.
    
    This decorator implements exponential backoff with jitter to handle database
    lock contention in multi-threaded environments. It automatically retries
    failed operations due to database locks and manages connection cleanup.

    Args:
        max_attempts (int, optional): Maximum number of retry attempts. Defaults to 5.
        initial_wait (float, optional): Initial wait time between retries in seconds. 
                                       Will be increased exponentially. Defaults to 0.1.

    Returns:
        function: Decorated function with retry logic

    Raises:
        sqlite3.OperationalError: If all retry attempts fail due to database locks
        Exception: Any other exception from the wrapped function

    Example:
        @retry_on_db_lock(max_attempts=10, initial_wait=0.2)
        def my_database_function():
            # Database operations here
            pass
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e).lower() and attempt < max_attempts - 1:
                        # Calculate wait time with exponential backoff and jitter
                        wait_time = initial_wait * (2 ** attempt) + random.uniform(0, 0.1)
                        print(f"Database locked, retrying in {wait_time:.2f} seconds... (attempt {attempt + 1}/{max_attempts})")
                        time.sleep(wait_time)
                        continue
                    else:
                        print(f"Database error after {attempt + 1} attempts: {e}")
                        raise
                except Exception as e:
                    print(f"Non-database error in {func.__name__}: {e}")
                    raise
            return None
        return wrapper
    return decorator
