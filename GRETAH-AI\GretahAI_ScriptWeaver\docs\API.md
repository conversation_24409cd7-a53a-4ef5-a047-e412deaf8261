# GretahAI ScriptWeaver API Documentation

**Complete API Reference for Developers and Integrators**

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

---

## 📋 Table of Contents

- [Core APIs](#-core-apis)
- [State Management API](#-state-management-api)
- [AI Integration API](#-ai-integration-api)
- [Stage APIs (1-10)](#-stage-apis-1-10)
- [UI Component APIs](#-ui-component-apis)
- [Template Management API](#-template-management-api)
- [Performance Monitoring API](#-performance-monitoring-api)
- [Data Storage APIs](#-data-storage-apis)
- [Utility APIs](#-utility-apis)
- [Error Handling](#-error-handling)

## 🔧 Core APIs

### Complete Stage Function Imports

All stage functions are imported from the unified `stages` package:

```python
from stages import (
    stage1_main,                   # Stage 1: Excel File Upload & Preview
    stage2_main,                   # Stage 2: Website Configuration & API Setup
    stage3_main,                   # Stage 3: Test Case Analysis & AI Conversion
    stage4_main,                   # Stage 4: UI Element Detection & Interactive Selection
    stage5_main,                   # Stage 5: Test Data Configuration & Generation
    stage6_main,                   # Stage 6: Test Script Generation & Review
    stage7_main,                   # Stage 7: Test Script Execution & Results
    stage8_main,                   # Stage 8: Script Optimization & Consolidation
    stage9_main,                   # Stage 9: Script Browser & History Management
    stage10_main                   # Stage 10: Script Playground & Template Generation
)
```

### Application Entry Point

```python
def main() -> None:
    """
    Main application entry point that initializes Streamlit and state management.

    Features:
    - Streamlit page configuration
    - StateManager initialization
    - Stage routing and navigation
    - Error handling and recovery
    """
```

## 🗃️ State Management API

### StateManager Class

The central state management class for the entire application.

```python
@dataclass
class StateManager:
    """Centralized state management for GretahAI ScriptWeaver"""

    # Core workflow state
    current_stage: StateStage = StateStage.STAGE1
    excel_file: Optional[pd.DataFrame] = None
    website_url: str = ""
    google_api_key: str = ""

    # Test case and step data
    selected_test_case: Optional[Dict] = None
    step_table: Optional[List[Dict]] = None
    current_step_index: int = 0

    # Advanced features
    hybrid_editing_enabled: bool = False
    performance_monitoring: bool = True
    debug_mode: bool = False

    # Stage 10 specific state
    selected_template_id: Optional[str] = None
    gap_analysis_results: Optional[Dict] = None
    template_based_script: Optional[str] = None
```

### StateStage Enum

```python
class StateStage(Enum):
    """Enumeration of all application stages"""
    STAGE1 = "stage1"    # Excel File Upload & Preview
    STAGE2 = "stage2"    # Website Configuration & API Setup
    STAGE3 = "stage3"    # Test Case Analysis & AI Conversion
    STAGE4 = "stage4"    # UI Element Detection & Interactive Selection
    STAGE5 = "stage5"    # Test Data Configuration & Generation
    STAGE6 = "stage6"    # Test Script Generation & Review
    STAGE7 = "stage7"    # Test Script Execution & Results
    STAGE8 = "stage8"    # Script Optimization & Consolidation
    STAGE9 = "stage9"    # Script Browser & History Management
    STAGE10 = "stage10"  # Script Playground & Template Generation
```

### StateManager Methods

#### `advance_stage(target_stage: StateStage) -> bool`
Advance to the specified stage with validation.

**Parameters:**
- `target_stage` (StateStage): The target stage to advance to

**Returns:**
- `bool`: True if advancement was successful, False otherwise

**Example:**
```python
state = st.session_state.state_manager
success = state.advance_stage(StateStage.STAGE3)
if success:
    st.success("Advanced to Stage 3")
```

#### `reset_stage_data(stage: StateStage) -> None`
Reset data for a specific stage.

**Parameters:**
- `stage` (StateStage): The stage to reset data for

#### `validate_prerequisites(stage: StateStage) -> bool`
Validate prerequisites for a specific stage.

**Parameters:**
- `stage` (StateStage): The stage to validate prerequisites for

**Returns:**
- `bool`: True if prerequisites are met, False otherwise

## 🤖 AI Integration API

### Core AI Functions

#### `generate_llm_response(prompt: str, request_type: str, context: Optional[Dict] = None) -> Dict[str, Any]`
Generate AI response using Google AI with comprehensive logging.

**Parameters:**
- `prompt` (str): The prompt to send to the AI
- `request_type` (str): Type of request for logging purposes
- `context` (Optional[Dict]): Additional context for the request

**Returns:**
- `Dict[str, Any]`: AI response with metadata

**Raises:**
- `AIProcessingError`: If AI request fails
- `ValidationError`: If input validation fails

**Example:**
```python
from core.ai import generate_llm_response

response = generate_llm_response(
    prompt="Generate a test script for login functionality",
    request_type="script_generation",
    context={"test_case_id": "TC_001"}
)
```

#### `clean_llm_response(response_text: str) -> str`
Clean AI response by removing markdown code blocks.

**Parameters:**
- `response_text` (str): Raw AI response text

**Returns:**
- `str`: Cleaned response text

**Example:**
```python
from core.ai_helpers import clean_llm_response

cleaned_script = clean_llm_response(ai_response["content"])
```

### Template-Based AI Functions

#### `generate_template_based_script_prompt(template_script: Dict, target_test_case: Dict) -> str`
Generate prompt for template-based script generation.

**Parameters:**
- `template_script` (Dict): Template script data
- `target_test_case` (Dict): Target test case data

**Returns:**
- `str`: Generated prompt for AI

#### `analyze_template_compatibility(template: Dict, test_case: Dict) -> Dict[str, Any]`
Analyze compatibility between template and test case.

**Parameters:**
- `template` (Dict): Template data
- `test_case` (Dict): Test case data

**Returns:**
- `Dict[str, Any]`: Compatibility analysis results

## Core Module Functions

### AI Integration (core/ai.py)

```python
from core.ai import (
    generate_llm_response,         # Main AI API interface
    optimize_script_with_ai,       # Script optimization
    merge_scripts_with_ai          # Script merging
)

def generate_llm_response(
    prompt: str,
    context: str = "",
    model: str = "gemini-2.0-flash",
    max_tokens: int = 4000
) -> str:
    """
    Generate AI response with comprehensive logging.

    Args:
        prompt: The main prompt for the AI
        context: Additional context information
        model: AI model to use
        max_tokens: Maximum response tokens

    Returns:
        str: AI-generated response

    Raises:
        Exception: API errors, authentication issues
    """
```

### Element Detection (core/element_detection.py)

```python
from core.element_detection import (
    detect_elements,               # Automatic element detection
    save_detected_elements         # Save elements to JSON
)

def detect_elements(url: str, options: dict = None) -> list:
    """
    Detect UI elements from a webpage.

    Args:
        url: Target webpage URL
        options: Detection configuration options

    Returns:
        list: Detected UI elements with metadata
    """
```



## State Manager API

The StateManager provides centralized state management:

```python
from state_manager import StateManager

# Get state instance
state = StateManager.get(st)

# State update methods
state.update_step_progress(current_step_index=1, step_ready_for_script=True)
state.reset_step_state(confirm=True, reason="User requested reset")
state.reset_test_case_state(confirm=True, reason="Starting new test case")
```

### StateManager Methods

```python
class StateManager:
    def update_step_progress(
        self,
        current_step_index: int,
        step_ready_for_script: bool = False
    ) -> None:
        """Update step progression state."""

    def reset_step_state(
        self,
        confirm: bool = False,
        reason: str = ""
    ) -> None:
        """Reset current step state."""

    def reset_test_case_state(
        self,
        confirm: bool = False,
        reason: str = ""
    ) -> None:
        """Reset entire test case state."""

    def get_current_step(self) -> dict:
        """Get current step information."""

    def set_current_step(self, step_data: dict) -> None:
        """Set current step data."""
```

## Helper Functions

### Pure Helper Functions (helpers_pure.py)

```python
from helpers_pure import (
    safe_get_list_item,           # Safe list access
    validate_file_extension,      # File validation
    format_timestamp,             # Time formatting
    sanitize_filename             # Filename sanitization
)
```

### Test Data Management (test_data_manager.py)

```python
from test_data_manager import (
    generate_test_data,           # Generate realistic test data
    validate_test_data,           # Validate data formats
    save_test_data               # Save data to files
)
```

## Configuration API

### Configuration Management (core/config.py)

```python
from core.config import (
    load_config,                  # Load configuration
    save_config,                  # Save configuration
    get_api_key                   # Get API keys securely
)

def load_config(config_path: str = "config.json") -> dict:
    """
    Load application configuration.

    Args:
        config_path: Path to configuration file

    Returns:
        dict: Configuration settings
    """
```

## Error Handling

All API functions follow consistent error handling patterns:

```python
try:
    result = api_function(parameters)
except SpecificException as e:
    logger.error(f"Specific error in function: {e}")
    raise
except Exception as e:
    logger.error(f"Unexpected error: {e}", exc_info=True)
    raise
```

## Logging Integration

All API functions include comprehensive logging:

```python
import logging
logger = logging.getLogger("ScriptWeaver.module_name")

def api_function():
    logger.info("Function started")
    # Function implementation
    logger.info("Function completed successfully")
```

## Type Hints and Validation

All API functions include proper type hints:

```python
from typing import List, Dict, Optional, Union

def typed_function(
    required_param: str,
    optional_param: Optional[int] = None,
    list_param: List[Dict[str, str]] = None
) -> Union[str, None]:
    """Function with comprehensive type hints."""
```

For more detailed implementation examples, see [DEVELOPMENT.md](DEVELOPMENT.md).

---

## Commercial API Development & Enterprise Support

**GretahAI ScriptWeaver is proprietary commercial software developed by Cogniron.**

### Enterprise API Services

**Primary Contact**: <EMAIL>

**Commercial API Development Services Available**:
- Custom API development and integration
- Enterprise-grade API security and authentication
- Advanced AI model integration and optimization
- Professional API documentation and training
- Dedicated technical support with SLA guarantees
- API performance optimization and monitoring

### Enterprise Integration Support

- Custom module development for specific business needs
- Advanced workflow automation and optimization
- Enterprise-grade security and compliance features
- Multi-tenant API deployment and configuration
- Custom reporting and analytics integration

### Contact Information

- **Website**: https://cogniron.com
- **Primary Contact**: <EMAIL>
- **Commercial Licensing**: Contact for pricing and enterprise licensing options
- **Enterprise Support**: Dedicated support packages available for commercial customers

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized copying, distribution, modification, or use of this software is strictly prohibited.

**Commercial Licensing**: Contact <EMAIL> for licensing inquiries and enterprise API development partnerships.

**Note**: This software requires a valid commercial license for use. All API development and customization requires a signed enterprise agreement.
