"""
Core database functionality for the GretahAI CaseForge system.

This module provides the foundational database operations including connection management,
schema handling, and decorators for the GretahAI CaseForge application.
"""

from .connection import (
    get_thread_local_connection,
    close_thread_local_connection,
    close_connection,
    DATABASE_PATH
)

from .decorators import retry_on_db_lock

from .schema import (
    migrate_database,
    init_db,
    backup_database,
    detect_schema_version,
    migrate_old_to_new_schema,
    verify_schema_completeness,
    standardize_schema,
    verify_and_update_schema,
    update_database_schema,
    update_existing_test_cases
)

__all__ = [
    # Connection management
    'get_thread_local_connection',
    'close_thread_local_connection',
    'close_connection',
    'DATABASE_PATH',
    
    # Decorators
    'retry_on_db_lock',
    
    # Schema management
    'migrate_database',
    'init_db',
    'backup_database',
    'detect_schema_version',
    'migrate_old_to_new_schema',
    'verify_schema_completeness',
    'standardize_schema',
    'verify_and_update_schema',
    'update_database_schema',
    'update_existing_test_cases'
]
