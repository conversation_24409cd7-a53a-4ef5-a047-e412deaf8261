"""
Data operations for the GretahAI CaseForge database system.

This module handles data retrieval, processing, and query operations for
test cases, test runs, and related data.
"""

import sqlite3
import pandas as pd
from datetime import datetime
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_cases_from_database(database_path, jira_id, dashboard_test_type=None, user_name=None):
    """
    Retrieves test cases from the database and returns them as a formatted DataFrame.
    
    This function is the primary method for reading test case data from the database.
    It handles the complex logic of retrieving both original and edited versions of
    test cases, merging test case headers with their associated test steps, and
    formatting the data for display in the application interface.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier to filter test cases
        dashboard_test_type (str, optional): Filter by test type ("positive", "negative", 
                                            "security", "performance", "all"). Defaults to None.
        user_name (str, optional): Filter by user who created the test cases. Defaults to None.

    Returns:
        pandas.DataFrame: Formatted DataFrame with columns:
            - Timestamp: When test case was created
            - Project: Associated project name
            - Feature: Feature being tested
            - User Story ID: JIRA ticket ID
            - Test Case ID: Unique test case identifier
            - Test Case Objective: Purpose of the test
            - Prerequisite: Prerequisites for test execution
            - Step No: Step sequence number (first step per test case)
            - Test Steps: Detailed step description
            - Expected Result: Expected outcome
            - Actual Result: Actual execution result
            - Test Status: Current status of test step
            - Priority: Test case priority level
            - Defect ID: Associated defect identifier
            - Comments: Additional notes
            - Test Type: Type of testing performed
            - Test Group: Logical grouping of tests

    Data Processing Logic:
        1. Retrieves JIRA issue database ID from jira_id parameter
        2. Gets unique test case IDs for the specified filters
        3. For each test case, checks for edited versions first
        4. Falls back to original versions if no edited version exists
        5. Retrieves and deduplicates test steps for each test case
        6. Formats first step with test case header information
        7. Adds remaining steps as continuation rows
        8. Handles cases with no steps by creating header-only rows

    Edited Version Priority:
        - Always prefers edited versions over original versions
        - Searches for edited versions by user and test case ID
        - Uses timestamp-based ordering for latest versions
        - Falls back to original version if no edited version found

    Data Deduplication:
        - Removes duplicate test steps by step number
        - Ensures only unique steps are included per test case
        - Logs when duplicate steps are encountered

    Error Handling:
        - Returns empty DataFrame on database errors
        - Logs detailed error information
        - Properly closes database connections
        - Handles missing or corrupted data gracefully

    Example:
        # Get all positive test cases for a JIRA ticket
        df = get_test_cases_from_database(db_path, 'TP-1', 'positive')
        
        # Get test cases created by specific user
        df = get_test_cases_from_database(db_path, 'TP-1', user_name='john_doe')
        
        # Get all test cases regardless of type
        df = get_test_cases_from_database(db_path, 'TP-1', 'all')
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get or create the JIRA issue ID from the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if not result:
            print(f"No JIRA issue found for {jira_id}, creating one")
            # Create a new JIRA issue
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, "", "", "")
            )
            # Commit immediately to ensure the JIRA issue is created
            conn.commit()
            print(f"Committed JIRA issue creation")

            # Get the ID of the newly created JIRA issue
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()
            if not result:
                print(f"Failed to create JIRA issue for {jira_id}")
                return pd.DataFrame()

        db_jira_id = result[0]
        print(f"Using JIRA issue with ID {db_jira_id} for {jira_id}")

        # Normalize dashboard_test_type to lowercase if it exists
        normalized_test_type = dashboard_test_type.lower() if dashboard_test_type else "all"

        # First, get all test case IDs for this JIRA ID and test type
        if normalized_test_type != "all":
            if user_name:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = ? AND user_name = ?
                       ORDER BY test_case_id""",
                    (db_jira_id, normalized_test_type, user_name)
                )
            else:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = ?
                       ORDER BY test_case_id""",
                    (db_jira_id, normalized_test_type)
                )
        else:
            if user_name:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = 'all' AND user_name = ?
                       ORDER BY test_case_id""",
                    (db_jira_id, user_name)
                )
            else:
                cursor.execute(
                    """SELECT DISTINCT test_case_id
                       FROM test_cases
                       WHERE jira_issue_id = ? AND dashboard_test_type = 'all'
                       ORDER BY test_case_id""",
                    (db_jira_id,)
                )

        test_case_ids = [row[0] for row in cursor.fetchall()]

        if not test_case_ids:
            print(f"No test cases found for JIRA ID {jira_id}" + (f" and test type {normalized_test_type}" if normalized_test_type else ""))
            return pd.DataFrame()

        print(f"Found {len(test_case_ids)} unique test case IDs")

        # For each test case ID, get the edited version if it exists, otherwise get the original
        all_test_cases = []

        for tc_id in test_case_ids:
            # Check if there's an edited version of this test case
            if normalized_test_type != "all":
                if user_name:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                           AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, normalized_test_type, user_name, tc_id)
                    )
                else:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                           AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, normalized_test_type, tc_id)
                    )
            else:
                if user_name:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                           AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, user_name, tc_id)
                    )
                else:
                    cursor.execute(
                        """SELECT tc.*, ji.jira_id as jira_issue_id
                           FROM test_cases tc
                           JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                           WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                           AND tc.test_case_id = ? AND tc.is_edited = 1
                           ORDER BY tc.timestamp DESC LIMIT 1""",
                        (db_jira_id, tc_id)
                    )

            edited_tc = cursor.fetchone()

            if edited_tc:
                # Use the edited version
                print(f"Using edited version of test case {tc_id}")
                all_test_cases.append(edited_tc)
            else:
                # No edited version, get the original
                if normalized_test_type != "all":
                    if user_name:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                               AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, normalized_test_type, user_name, tc_id)
                        )
                    else:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = ?
                               AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, normalized_test_type, tc_id)
                        )
                else:
                    if user_name:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                               AND tc.user_name = ? AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, user_name, tc_id)
                        )
                    else:
                        cursor.execute(
                            """SELECT tc.*, ji.jira_id as jira_issue_id
                               FROM test_cases tc
                               JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                               WHERE tc.jira_issue_id = ? AND tc.dashboard_test_type = 'all'
                               AND tc.test_case_id = ? AND tc.is_edited = 0
                               ORDER BY tc.timestamp DESC LIMIT 1""",
                            (db_jira_id, tc_id)
                        )

                original_tc = cursor.fetchone()
                if original_tc:
                    print(f"Using original version of test case {tc_id}")
                    all_test_cases.append(original_tc)

        if not all_test_cases:
            print(f"No test cases found after filtering for JIRA ID {jira_id}")
            return pd.DataFrame()

        # Convert to list of dictionaries for DataFrame
        test_cases_list = []

        for tc in all_test_cases:
            # Check if this is an edited test case
            is_edited = tc["is_edited"] == 1 if "is_edited" in tc else False
            print(f"Processing test case {tc['test_case_id']} (edited: {is_edited})")

            # Get test steps for this test case
            cursor.execute(
                "SELECT * FROM test_steps WHERE test_case_id = ? ORDER BY step_number",
                (tc["id"],)
            )
            steps = cursor.fetchall()

            # Remove duplicate steps by tracking step numbers we've already seen
            unique_steps = []
            seen_step_numbers = set()

            for step in steps:
                step_num = step["step_number"]
                if step_num not in seen_step_numbers:
                    seen_step_numbers.add(step_num)
                    unique_steps.append(step)
                else:
                    print(f"Skipping duplicate step {step_num} for test case {tc['test_case_id']}")

            # Use the deduplicated steps
            steps = unique_steps
            print(f"Found {len(steps)} unique steps for test case {tc['test_case_id']}")            # For the first step, combine it with the test case header
            if steps and len(steps) > 0:
                first_step = steps[0]
                test_case_row = {
                    "Timestamp": tc["timestamp"],
                    "Project": tc["project"],
                    "Feature": tc["feature"],
                    "User Story ID": tc["jira_id"],  # Use jira_id instead of jira_issue_id
                    "Test Case ID": tc["test_case_id"],
                    "Test Case Objective": tc["test_case_objective"],
                    "Prerequisite": tc["prerequisite"],
                    "Step No": str(first_step["step_number"]),  # Include first step number
                    "Test Steps": first_step["test_step"],      # Include first test step
                    "Expected Result": first_step["expected_result"],  # Include first expected result
                    "Actual Result": first_step["actual_result"],
                    "Test Status": first_step["test_status"],
                    "Priority": tc["priority"],
                    "Defect ID": first_step["defect_id"],
                    "Comments": first_step["comments"],
                    "Test Type": tc["test_type"],
                    "Test Group": tc["test_group"],
                    # Add AI-related fields
                    "ai_modified": tc.get("ai_modified", 0),
                    "modification_source": tc.get("modification_source", "manual"),
                    "ai_model_used": tc.get("ai_model_used", ""),
                    "ai_modification_timestamp": tc.get("ai_modification_timestamp", ""),
                    "ai_modification_user": tc.get("ai_modification_user", ""),
                    "ai_user_query": tc.get("ai_user_query", "")
                }
                test_cases_list.append(test_case_row)

                # Create rows for the remaining steps (starting from the second step)
                for step in steps[1:]:
                    test_cases_list.append({
                        "Timestamp": "",
                        "Project": "",
                        "Feature": "",
                        "User Story ID": "",
                        "Test Case ID": "",
                        "Test Case Objective": "",
                        "Prerequisite": "",
                        "Step No": str(step["step_number"]),
                        "Test Steps": step["test_step"],
                        "Expected Result": step["expected_result"],
                        "Actual Result": step["actual_result"],
                        "Test Status": step["test_status"],
                        "Priority": "",
                        "Defect ID": step["defect_id"],
                        "Comments": step["comments"],
                        "Test Type": "",
                        "Test Group": "",
                        # Add AI-related fields (inherit from test case header)
                        "ai_modified": tc.get("ai_modified", 0),
                        "modification_source": tc.get("modification_source", "manual"),
                        "ai_model_used": tc.get("ai_model_used", ""),
                        "ai_modification_timestamp": tc.get("ai_modification_timestamp", ""),
                        "ai_modification_user": tc.get("ai_modification_user", ""),
                        "ai_user_query": tc.get("ai_user_query", "")
                    })
            else:
                # If there are no steps, just add the test case header
                test_case_row = {
                    "Timestamp": tc["timestamp"],
                    "Project": tc["project"],
                    "Feature": tc["feature"],
                    "User Story ID": tc["jira_id"],  # Use jira_id instead of jira_issue_id
                    "Test Case ID": tc["test_case_id"],
                    "Test Case Objective": tc["test_case_objective"],
                    "Prerequisite": tc["prerequisite"],
                    "Step No": "",
                    "Test Steps": "",
                    "Expected Result": "",
                    "Actual Result": "",
                    "Test Status": "",
                    "Priority": tc["priority"],
                    "Defect ID": "",
                    "Comments": "",
                    "Test Type": tc["test_type"],
                    "Test Group": tc["test_group"],
                    # Add AI-related fields
                    "ai_modified": tc.get("ai_modified", 0),
                    "modification_source": tc.get("modification_source", "manual"),
                    "ai_model_used": tc.get("ai_model_used", ""),
                    "ai_modification_timestamp": tc.get("ai_modification_timestamp", ""),
                    "ai_modification_user": tc.get("ai_modification_user", ""),
                    "ai_user_query": tc.get("ai_user_query", "")
                }
                test_cases_list.append(test_case_row)

        # Create DataFrame
        df = pd.DataFrame(test_cases_list)
        print(f"Created DataFrame with {len(df)} rows")
        return df
    except sqlite3.Error as e:
        print(f"Error getting test cases from database: {e}")
        return pd.DataFrame()
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_latest_generated_test_cases(database_path, jira_id, dashboard_test_type=None, user_name=None):
    """
    Retrieves the most recently generated test cases for a JIRA ID.
    
    This function fetches test cases from the latest test run, providing
    access to the most current version of test cases for a given JIRA ticket.
    It's essential for displaying current test case state in the UI.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier (e.g., "TP-1", "STORY-123")
        dashboard_test_type (str, optional): Test type filter. Defaults to None.
        user_name (str, optional): User name filter. Defaults to None.

    Returns:
        pandas.DataFrame or None: DataFrame containing the latest test cases
        Returns None if no test cases found or on error.

    Process Flow:
        1. Finds the latest test run for the specified criteria
        2. Retrieves test cases associated with that test run
        3. Returns formatted DataFrame with test case data

    Example:
        latest_df = get_latest_generated_test_cases(db_path, "TP-1", "positive")
        if latest_df is not None:
            print(f"Latest test cases: {len(latest_df)} rows")
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # First, get the latest test run
        conditions = ["tr.jira_id = ?"]
        values = [jira_id]
        
        if dashboard_test_type and dashboard_test_type.lower() != "all":
            conditions.append("tr.test_type = ?")
            values.append(dashboard_test_type.lower())
            
        if user_name:
            conditions.append("tr.user_name = ?")
            values.append(user_name)
            
        latest_run_query = f"""
            SELECT id FROM test_runs tr
            WHERE {' AND '.join(conditions)}
            ORDER BY timestamp DESC 
            LIMIT 1
        """
        
        cursor.execute(latest_run_query, values)
        latest_run = cursor.fetchone()
        
        if not latest_run:
            print(f"No test runs found for {jira_id}")
            return None
            
        test_run_id = latest_run['id']
        
        # Now get test cases for this test run
        test_cases_query = """
            SELECT DISTINCT
                tc.test_case_id as "Test Case ID",
                tc.test_case_objective as "Test Case Objective",
                tc.prerequisite as "Prerequisite",
                tc.priority as "Priority",
                tc.test_type as "Test Type",
                tc.test_group as "Test Group",
                tc.project as "Project",
                tc.feature as "Feature",
                tc.timestamp as "Timestamp",
                tc.dashboard_test_type as "Dashboard Test Type",
                tc.user_name as "User Name",
                tc.test_run_id as "Test Run ID",
                tc.is_edited as "Is Edited",
                ts.step_number as "Step No",
                ts.test_step as "Test Steps",
                ts.expected_result as "Expected Result",
                ts.actual_result as "Actual Result",
                ts.test_status as "Test Status",
                ts.defect_id as "Defect ID",
                ts.comments as "Comments"
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.test_run_id = ?
            ORDER BY tc.test_case_id, ts.step_number
        """
        
        cursor.execute(test_cases_query, (test_run_id,))
        results = cursor.fetchall()
        
        if not results:
            print(f"No test cases found for test run {test_run_id}")
            return None
            
        # Convert to DataFrame
        df = pd.DataFrame([dict(row) for row in results])
        
        print(f"Retrieved {len(df)} latest test case rows for {jira_id}")
        return df
        
    except sqlite3.Error as e:
        print(f"Error retrieving latest test cases: {e}")
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_all_test_cases_for_user(database_path, user_name):
    """
    Retrieves all test cases created by a specific user across all JIRA IDs.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        user_name (str): Name of the user whose test cases to retrieve

    Returns:
        pandas.DataFrame or None: DataFrame containing all test cases for the user
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        query = """
            SELECT DISTINCT
                tc.jira_id as "JIRA ID",
                tc.test_case_id as "Test Case ID",
                tc.test_case_objective as "Test Case Objective",
                tc.prerequisite as "Prerequisite",
                tc.priority as "Priority",
                tc.test_type as "Test Type",
                tc.test_group as "Test Group",
                tc.project as "Project",
                tc.feature as "Feature",
                tc.timestamp as "Timestamp",
                tc.dashboard_test_type as "Dashboard Test Type",
                tc.user_name as "User Name",
                tc.test_run_id as "Test Run ID",
                tc.is_edited as "Is Edited",
                ts.step_number as "Step No",
                ts.test_step as "Test Steps",
                ts.expected_result as "Expected Result",
                ts.actual_result as "Actual Result",
                ts.test_status as "Test Status",
                ts.defect_id as "Defect ID",
                ts.comments as "Comments",
                tc.ai_modified,
                tc.modification_source,
                tc.ai_model_used,
                tc.ai_modification_timestamp,
                tc.ai_modification_user,
                tc.ai_user_query
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.user_name = ?
            ORDER BY tc.jira_id, tc.test_case_id, ts.step_number
        """
        
        cursor.execute(query, (user_name,))
        results = cursor.fetchall()
        
        if not results:
            print(f"No test cases found for user {user_name}")
            return None
            
        # Convert to DataFrame
        df = pd.DataFrame([dict(row) for row in results])
        
        print(f"Retrieved {len(df)} test case rows for user {user_name}")
        return df
        
    except sqlite3.Error as e:
        print(f"Error retrieving test cases for user: {e}")
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_cases_by_test_run(database_path, test_run_id):
    """
    Retrieves all test cases associated with a specific test run.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        test_run_id (int): ID of the test run

    Returns:
        pandas.DataFrame or None: DataFrame containing test cases for the test run
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        query = """
            SELECT DISTINCT
                tc.test_case_id as "Test Case ID",
                tc.test_case_objective as "Test Case Objective",
                tc.prerequisite as "Prerequisite",
                tc.priority as "Priority",
                tc.test_type as "Test Type",
                tc.test_group as "Test Group",
                tc.project as "Project",
                tc.feature as "Feature",
                tc.timestamp as "Timestamp",
                tc.dashboard_test_type as "Dashboard Test Type",
                tc.user_name as "User Name",
                tc.test_run_id as "Test Run ID",
                tc.is_edited as "Is Edited",
                ts.step_number as "Step No",
                ts.test_step as "Test Steps",
                ts.expected_result as "Expected Result",
                ts.actual_result as "Actual Result",
                ts.test_status as "Test Status",
                ts.defect_id as "Defect ID",
                ts.comments as "Comments",
                tc.ai_modified,
                tc.modification_source,
                tc.ai_model_used,
                tc.ai_modification_timestamp,
                tc.ai_modification_user,
                tc.ai_user_query
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.test_run_id = ?
            ORDER BY tc.test_case_id, ts.step_number
        """
        
        cursor.execute(query, (test_run_id,))
        results = cursor.fetchall()
        
        if not results:
            print(f"No test cases found for test run {test_run_id}")
            return None
            
        # Convert to DataFrame
        df = pd.DataFrame([dict(row) for row in results])
        
        print(f"Retrieved {len(df)} test case rows for test run {test_run_id}")
        return df
        
    except sqlite3.Error as e:
        print(f"Error retrieving test cases by test run: {e}")
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_cases_for_test_run(database_path, test_run_id):
    """
    Alternative function name for get_test_cases_by_test_run for compatibility.
    """
    return get_test_cases_by_test_run(database_path, test_run_id)


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_latest_generated_test_cases_original(database_path, jira_id, test_type, user_name=None):
    """
    Original version of get_latest_generated_test_cases for backward compatibility.
    """
    return get_latest_generated_test_cases(database_path, jira_id, test_type, user_name)


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def fix_all_test_type(database_path, jira_id="TP-10", tc_id="TC_006"):
    """
    Utility function to fix test type inconsistencies in the database.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ID to fix
        tc_id (str): Test case ID to fix

    Returns:
        bool: True if successful, False otherwise
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Update test cases table
        cursor.execute(
            "UPDATE test_cases SET test_type = 'all' WHERE jira_id = ? AND test_case_id = ?",
            (jira_id, tc_id)
        )
        
        # Update test steps table
        cursor.execute(
            """UPDATE test_steps SET dashboard_test_type = 'all' 
               WHERE test_case_id IN (
                   SELECT id FROM test_cases WHERE jira_id = ? AND test_case_id = ?
               )""",
            (jira_id, tc_id)
        )
        
        conn.commit()
        print(f"Fixed test type for {jira_id} - {tc_id}")
        return True
        
    except sqlite3.Error as e:
        print(f"Error fixing test type: {e}")
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_cases_by_filters(db_path, jira_id=None, test_type=None, start_date=None, end_date=None, user_name=None):
    """
    Retrieves test cases with multiple filter options.
    
    Args:
        db_path (str): Absolute path to the SQLite database file
        jira_id (str, optional): JIRA ID filter
        test_type (str, optional): Test type filter
        start_date (str, optional): Start date filter (YYYY-MM-DD format)
        end_date (str, optional): End date filter (YYYY-MM-DD format)
        user_name (str, optional): User name filter

    Returns:
        pandas.DataFrame or None: DataFrame containing filtered test cases
    """
    conn = None
    try:
        conn = sqlite3.connect(db_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Build dynamic query with proper test_type filtering
        conditions = []
        values = []
        
        if jira_id:
            conditions.append("tc.jira_id = ?")
            values.append(jira_id)
            
        # Fix: Properly filter by test_type using test_runs table
        if test_type and test_type.lower() != "all":
            conditions.append("tr.test_type = ?")
            values.append(test_type.lower())
        elif test_type and test_type.lower() == "all":
            # Only get test cases from test runs that have test_type='all'
            conditions.append("tr.test_type = 'all'")
            
        if user_name:
            conditions.append("tc.user_name = ?")
            values.append(user_name)
            
        if start_date:
            conditions.append("DATE(tc.timestamp) >= ?")
            values.append(start_date)
            
        if end_date:
            conditions.append("DATE(tc.timestamp) <= ?")
            values.append(end_date)
            
        # Modified query to join with test_runs table for proper filtering
        base_query = """
            SELECT
                tc.jira_id as "JIRA ID",
                CASE
                    WHEN tc.test_run_id IS NOT NULL
                    THEN tc.test_case_id || '_R' || tc.test_run_id
                    ELSE tc.test_case_id
                END as "Test Case ID",
                tc.test_case_id as "Original Test Case ID",
                tc.test_case_objective as "Test Case Objective",
                tc.prerequisite as "Prerequisite",
                tc.priority as "Priority",
                tc.test_type as "Test Type",
                tc.test_group as "Test Group",
                tc.project as "Project",
                tc.feature as "Feature",
                tc.timestamp as "Timestamp",
                tc.dashboard_test_type as "Dashboard Test Type",
                tc.user_name as "User Name",
                tc.test_run_id as "Test Run ID",
                tc.is_edited as "Is Edited",
                ts.step_number as "Step No",
                ts.test_step as "Test Steps",
                ts.expected_result as "Expected Result",
                ts.actual_result as "Actual Result",
                ts.test_status as "Test Status",
                ts.defect_id as "Defect ID",
                ts.comments as "Comments",
                tc.ai_modified,
                tc.modification_source,
                tc.ai_model_used,
                tc.ai_modification_timestamp,
                tc.ai_modification_user,
                tc.ai_user_query
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            LEFT JOIN test_runs tr ON tc.test_run_id = tr.id
        """
        
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)
            
        base_query += " ORDER BY tc.test_run_id, tc.test_case_id, ts.step_number"
        
        cursor.execute(base_query, values)
        results = cursor.fetchall()
        
        if not results:
            print("No test cases found for the given filters")
            return pd.DataFrame()
            
        # Convert to DataFrame
        df = pd.DataFrame([dict(row) for row in results])
        
        print(f"Retrieved {len(df)} filtered test case rows")
        return df
        
    except sqlite3.Error as e:
        print(f"Error retrieving filtered test cases: {e}")
        return pd.DataFrame()
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_unified_filtered_test_cases(db_path, filters=None, sort_order='desc'):
    """
    Enhanced filtering function for unified interface with comprehensive filter support and configurable sorting.

    Args:
        db_path (str): Absolute path to the SQLite database file
        filters (dict): Dictionary containing filter criteria:
            - jira_id (str, optional): JIRA ID filter
            - test_type (str, optional): Test type filter ('all', 'positive', 'negative', etc.)
            - test_status (str, optional): Test execution status filter ('all', 'Pass', 'Fail', 'Not Executed', etc.)
            - date_start (str, optional): Start date filter (YYYY-MM-DD format)
            - date_end (str, optional): End date filter (YYYY-MM-DD format)
            - ai_generated (bool, optional): Filter for AI generated test cases
            - enhanced_status (bool, optional): Filter for enhanced JIRA stories
            - user_name (str, optional): User name filter
            - is_edited (bool, optional): Filter for edited test cases
            - current_run_id (int, optional): Filter for specific test run ID (for precise generation filtering)
            - timestamp_start (str, optional): Precise start timestamp for tight filtering (YYYY-MM-DD HH:MM:SS)
            - timestamp_end (str, optional): Precise end timestamp for tight filtering (YYYY-MM-DD HH:MM:SS)
            - generation_session_id (str, optional): Unique generation session identifier
            - test_case_id_range_start (int, optional): Start of test case ID range for current session (e.g., 46 for TC_046)
            - test_case_id_range_end (int, optional): End of test case ID range for current session (e.g., 55 for TC_055)
        sort_order (str): Sorting order for results:
            - 'desc': Descending order (newest first) - for dashboard display
            - 'asc': Ascending order (oldest first) - for export functionality

    Returns:
        pandas.DataFrame: DataFrame containing filtered test cases with all necessary columns
    """
    if filters is None:
        filters = {}

    conn = None
    try:
        # Force fresh database connection every time - no connection pooling
        from datetime import datetime
        query_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        print(f"🔗 [{query_timestamp}] Opening fresh database connection: {db_path}")

        conn = sqlite3.connect(db_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        print(f"✅ [{query_timestamp}] Database connection established successfully")

        # Build dynamic query conditions
        conditions = []
        values = []

        # JIRA ID filter
        if filters.get('jira_id') and filters['jira_id'] != 'All':
            conditions.append("tc.jira_id = ?")
            values.append(filters['jira_id'])

        # Test type filter
        if filters.get('test_type') and filters['test_type'] != 'all':
            conditions.append("tr.test_type = ?")
            values.append(filters['test_type'].lower())

        # Precise timestamp filtering (takes precedence over date range)
        if filters.get('timestamp_start') and filters.get('timestamp_end'):
            # Use tight timestamp bounds for precise generation session filtering
            conditions.append("tc.timestamp >= ?")
            values.append(filters['timestamp_start'])
            conditions.append("tc.timestamp <= ?")
            values.append(filters['timestamp_end'])
        else:
            # Fallback to date range filters
            if filters.get('date_start'):
                conditions.append("DATE(tc.timestamp) >= ?")
                values.append(filters['date_start'])

            if filters.get('date_end'):
                conditions.append("DATE(tc.timestamp) <= ?")
                values.append(filters['date_end'])

        # User name filter
        if filters.get('user_name'):
            conditions.append("tc.user_name = ?")
            values.append(filters['user_name'])

        # AI Generated filter (based on test_run existence and type)
        if filters.get('ai_generated') is not None:
            if filters['ai_generated']:
                # AI generated cases typically have test_run_id
                conditions.append("tc.test_run_id IS NOT NULL")
            else:
                # Manual cases might not have test_run_id or have specific markers
                conditions.append("tc.test_run_id IS NULL")

        # Enhanced status filter (check if JIRA issue has enhanced description)
        if filters.get('enhanced_status') is not None:
            if filters['enhanced_status']:
                conditions.append("ji.enhanced_description IS NOT NULL AND ji.enhanced_description != ''")
            else:
                conditions.append("(ji.enhanced_description IS NULL OR ji.enhanced_description = '')")

        # Is edited filter
        if filters.get('is_edited') is not None:
            conditions.append("tc.is_edited = ?")
            values.append(1 if filters['is_edited'] else 0)

        # Current run ID filter (for precise generation filtering)
        if filters.get('current_run_id') is not None:
            conditions.append("tc.test_run_id = ?")
            values.append(filters['current_run_id'])

        # Test Case ID range filtering (primary strategy for current session detection)
        if filters.get('test_case_id_range_start') is not None and filters.get('test_case_id_range_end') is not None:
            # Use Test Case ID range for precise current session filtering
            start_id = filters['test_case_id_range_start']
            end_id = filters['test_case_id_range_end']

            # Build SQL conditions for Test Case ID range (TC_046 to TC_055 format)
            conditions.append("CAST(SUBSTR(tc.test_case_id, 4) AS INTEGER) >= ?")
            values.append(start_id)
            conditions.append("CAST(SUBSTR(tc.test_case_id, 4) AS INTEGER) <= ?")
            values.append(end_id)

            print(f"🎯 [{query_timestamp}] Applied Test Case ID range filter: TC_{start_id:03d} to TC_{end_id:03d}")
            print(f"📊 [{query_timestamp}] SQL conditions added for ID range filtering")
        else:
            print(f"ℹ️ [{query_timestamp}] Test Case ID range filtering not applied (filters not available)")

        # First, get the test case headers that match the filters
        header_conditions = []
        header_values = []

        # Build conditions for test case headers
        if filters.get('jira_id') and filters['jira_id'] != 'All':
            header_conditions.append("tc.jira_id = ?")
            header_values.append(filters['jira_id'])

        if filters.get('test_type') and filters['test_type'] != 'all':
            header_conditions.append("COALESCE(tr.test_type, tc.dashboard_test_type) = ?")
            header_values.append(filters['test_type'].lower())

        # Unified filters (removed redundant feature, project, test_group as they're covered by JIRA User Story ID)
        if filters.get('priority') and filters['priority'] != 'all':
            header_conditions.append("tc.priority = ?")
            header_values.append(filters['priority'])

        if filters.get('user_name') and filters['user_name'] != 'all':
            header_conditions.append("tc.user_name = ?")
            header_values.append(filters['user_name'])

        # Test Status filter - filter by test execution status
        if filters.get('test_status') and filters['test_status'] != 'all':
            # Since test_status is in test_steps table, we need to join and filter
            header_conditions.append("EXISTS (SELECT 1 FROM test_steps ts WHERE ts.test_case_id = tc.id AND ts.test_status = ?)")
            header_values.append(filters['test_status'])

        # Precise timestamp filtering (takes precedence over date range)
        if filters.get('timestamp_start') and filters.get('timestamp_end'):
            # Use tight timestamp bounds for precise generation session filtering
            header_conditions.append("tc.timestamp >= ?")
            header_values.append(filters['timestamp_start'])
            header_conditions.append("tc.timestamp <= ?")
            header_values.append(filters['timestamp_end'])
        else:
            # Fallback to date range filters
            if filters.get('date_start'):
                header_conditions.append("DATE(tc.timestamp) >= ?")
                header_values.append(filters['date_start'])

            if filters.get('date_end'):
                header_conditions.append("DATE(tc.timestamp) <= ?")
                header_values.append(filters['date_end'])

        if filters.get('ai_generated') is not None:
            if filters['ai_generated']:
                header_conditions.append("tc.test_run_id IS NOT NULL")
            else:
                header_conditions.append("tc.test_run_id IS NULL")

        if filters.get('enhanced_status') is not None:
            if filters['enhanced_status']:
                header_conditions.append("ji.enhanced_description IS NOT NULL AND ji.enhanced_description != ''")
            else:
                header_conditions.append("(ji.enhanced_description IS NULL OR ji.enhanced_description = '')")

        if filters.get('is_edited') is not None:
            header_conditions.append("tc.is_edited = ?")
            header_values.append(1 if filters['is_edited'] else 0)

        # Current run ID filter (for precise generation filtering)
        if filters.get('current_run_id') is not None:
            header_conditions.append("tc.test_run_id = ?")
            header_values.append(filters['current_run_id'])

        # Test Case ID range filtering (primary strategy for current session detection)
        if filters.get('test_case_id_range_start') is not None and filters.get('test_case_id_range_end') is not None:
            # Use Test Case ID range for precise current session filtering
            start_id = filters['test_case_id_range_start']
            end_id = filters['test_case_id_range_end']

            # Build SQL conditions for Test Case ID range (TC_046 to TC_055 format)
            header_conditions.append("CAST(SUBSTR(tc.test_case_id, 4) AS INTEGER) >= ?")
            header_values.append(start_id)
            header_conditions.append("CAST(SUBSTR(tc.test_case_id, 4) AS INTEGER) <= ?")
            header_values.append(end_id)

        # Get test case headers first
        header_query = """
            SELECT DISTINCT
                tc.id as tc_internal_id,
                tc.jira_id as "JIRA ID",
                tc.test_case_id as "Test Case ID",
                tc.test_case_objective as "Test Case Objective",
                tc.prerequisite as "Prerequisite",
                tc.priority as "Priority",
                COALESCE(tr.test_type, tc.dashboard_test_type, tc.test_type, 'unknown') as "Test Type",
                tc.test_group as "Test Group",
                tc.project as "Project",
                tc.feature as "Feature",
                tc.timestamp as "Timestamp",
                tc.dashboard_test_type as "Dashboard Test Type",
                tc.user_name as "User Name",
                tc.test_run_id as "Test Run ID",
                tc.is_edited as "Is Edited",
                CASE WHEN tc.test_run_id IS NOT NULL THEN 1 ELSE 0 END as "AI Generated",
                CASE WHEN ji.enhanced_description IS NOT NULL AND ji.enhanced_description != '' THEN 1 ELSE 0 END as "Enhanced Status",
                tc.ai_modified,
                tc.modification_source,
                tc.ai_model_used,
                tc.ai_modification_timestamp,
                tc.ai_modification_user,
                tc.ai_user_query
            FROM test_cases tc
            LEFT JOIN test_runs tr ON tc.test_run_id = tr.id
            LEFT JOIN jira_issues ji ON tc.jira_issue_id = ji.id
        """

        if header_conditions:
            header_query += " WHERE " + " AND ".join(header_conditions)

        # Apply configurable sorting order
        if sort_order.lower() == 'asc':
            header_query += " ORDER BY tc.timestamp ASC, tc.test_case_id"
            print(f"📈 [{query_timestamp}] Applied ascending sort order (oldest first) for export")
        else:
            header_query += " ORDER BY tc.timestamp DESC, tc.test_case_id"
            print(f"📉 [{query_timestamp}] Applied descending sort order (newest first) for display")

        # Log the query execution for debugging
        print(f"🔍 [{query_timestamp}] Executing database query with {len(header_values)} parameters")
        print(f"📝 Query conditions: {len(header_conditions)} filters applied")

        cursor.execute(header_query, header_values)
        test_case_headers = cursor.fetchall()

        print(f"📊 [{query_timestamp}] Query returned {len(test_case_headers)} test case headers")

        if not test_case_headers:
            print("No test cases found for the given filters")
            return pd.DataFrame()

        # Now build the complete test case data with steps (matching original format)
        test_cases_list = []

        for tc_header in test_case_headers:
            tc_internal_id = tc_header["tc_internal_id"]

            # Get test steps for this test case
            cursor.execute(
                "SELECT * FROM test_steps WHERE test_case_id = ? ORDER BY step_number",
                (tc_internal_id,)
            )
            steps = cursor.fetchall()

            # Remove duplicate steps by tracking step numbers
            unique_steps = []
            seen_step_numbers = set()

            for step in steps:
                step_num = step["step_number"]
                if step_num not in seen_step_numbers:
                    seen_step_numbers.add(step_num)
                    unique_steps.append(step)

            steps = unique_steps

            # Create rows in the same format as original function
            if steps and len(steps) > 0:
                # First row: test case header + first step (match original column structure exactly)
                first_step = steps[0]
                test_case_row = {
                    "Timestamp": tc_header["Timestamp"],
                    "Project": tc_header["Project"],
                    "Feature": tc_header["Feature"],
                    "User Story ID": tc_header["JIRA ID"],  # Use "User Story ID" as in original
                    "Test Case ID": tc_header["Test Case ID"],  # Use original format
                    "Test Case Objective": tc_header["Test Case Objective"],
                    "Prerequisite": tc_header["Prerequisite"],
                    "Step No": str(first_step["step_number"]),
                    "Test Steps": first_step["test_step"],
                    "Expected Result": first_step["expected_result"],
                    "Actual Result": first_step["actual_result"],
                    "Test Status": first_step["test_status"],
                    "Priority": tc_header["Priority"],
                    "Defect ID": first_step["defect_id"],
                    "Comments": first_step["comments"],
                    "Test Type": tc_header["Test Type"],
                    "Test Group": tc_header["Test Group"],
                    "ai_modified": tc_header["ai_modified"],
                    "modification_source": tc_header["modification_source"],
                    "ai_model_used": tc_header["ai_model_used"],
                    "ai_modification_timestamp": tc_header["ai_modification_timestamp"],
                    "ai_modification_user": tc_header["ai_modification_user"],
                    "ai_user_query": tc_header["ai_user_query"]
                }
                test_cases_list.append(test_case_row)

                # Additional rows: remaining steps (empty header fields, match original structure)
                for step in steps[1:]:
                    test_cases_list.append({
                        "Timestamp": "",
                        "Project": "",
                        "Feature": "",
                        "User Story ID": "",
                        "Test Case ID": "",
                        "Test Case Objective": "",
                        "Prerequisite": "",
                        "Step No": str(step["step_number"]),
                        "Test Steps": step["test_step"],
                        "Expected Result": step["expected_result"],
                        "Actual Result": step["actual_result"],
                        "Test Status": step["test_status"],
                        "Priority": "",
                        "Defect ID": step["defect_id"],
                        "Comments": step["comments"],
                        "Test Type": "",
                        "Test Group": "",
                        "ai_modified": tc_header["ai_modified"],
                        "modification_source": tc_header["modification_source"],
                        "ai_model_used": tc_header["ai_model_used"],
                        "ai_modification_timestamp": tc_header["ai_modification_timestamp"],
                        "ai_modification_user": tc_header["ai_modification_user"],
                        "ai_user_query": tc_header["ai_user_query"]
                    })
            else:
                # Test case without steps (match original structure)
                test_case_row = {
                    "Timestamp": tc_header["Timestamp"],
                    "Project": tc_header["Project"],
                    "Feature": tc_header["Feature"],
                    "User Story ID": tc_header["JIRA ID"],  # Use "User Story ID" as in original
                    "Test Case ID": tc_header["Test Case ID"],
                    "Test Case Objective": tc_header["Test Case Objective"],
                    "Prerequisite": tc_header["Prerequisite"],
                    "Step No": "",
                    "Test Steps": "",
                    "Expected Result": "",
                    "Actual Result": "",
                    "Test Status": "",
                    "Priority": tc_header["Priority"],
                    "Defect ID": "",
                    "Comments": "",
                    "Test Type": tc_header["Test Type"],
                    "Test Group": tc_header["Test Group"],
                    "ai_modified": tc_header["ai_modified"],
                    "modification_source": tc_header["modification_source"],
                    "ai_model_used": tc_header["ai_model_used"],
                    "ai_modification_timestamp": tc_header["ai_modification_timestamp"],
                    "ai_modification_user": tc_header["ai_modification_user"],
                    "ai_user_query": tc_header["ai_user_query"]
                }
                test_cases_list.append(test_case_row)

        # Convert to DataFrame
        df = pd.DataFrame(test_cases_list)

        print(f"✅ [{query_timestamp}] Successfully retrieved {len(df)} filtered test case rows")
        print(f"🔗 [{query_timestamp}] Closing database connection")
        return df

    except sqlite3.Error as e:
        print(f"❌ [{query_timestamp}] Database error retrieving unified filtered test cases: {e}")
        return pd.DataFrame()
    finally:
        if conn:
            try:
                conn.close()
                print(f"🔒 [{query_timestamp}] Database connection closed successfully")
            except Exception as close_error:
                print(f"⚠️ [{query_timestamp}] Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_filter_options(db_path):
    """
    Get available filter options from the database for dropdown population.
    Enhanced version based on database analysis to provide comprehensive filter options.

    Args:
        db_path (str): Absolute path to the SQLite database file

    Returns:
        dict: Dictionary containing available options for each filter type
    """
    conn = None
    try:
        conn = sqlite3.connect(db_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        filter_options = {}

        # Get unique JIRA IDs (sorted by frequency)
        cursor.execute("""
            SELECT jira_id, COUNT(*) as count
            FROM test_cases
            WHERE jira_id IS NOT NULL AND jira_id != ''
            GROUP BY jira_id
            ORDER BY count DESC, jira_id
        """)
        filter_options['jira_ids'] = [row[0] for row in cursor.fetchall()]

        # Get unique test types from test_cases (more accurate than test_runs)
        cursor.execute("""
            SELECT test_type, COUNT(*) as count
            FROM test_cases
            WHERE test_type IS NOT NULL AND test_type != ''
            GROUP BY test_type
            ORDER BY count DESC
        """)
        test_types_data = cursor.fetchall()
        filter_options['test_types'] = [row[0] for row in test_types_data]

        # Also get test types from test_runs for dashboard compatibility
        cursor.execute("""
            SELECT DISTINCT test_type
            FROM test_runs
            WHERE test_type IS NOT NULL AND test_type != ''
            ORDER BY test_type
        """)
        dashboard_test_types = [row[0] for row in cursor.fetchall()]
        filter_options['dashboard_test_types'] = dashboard_test_types

        # Get unique priorities (with frequency) - exclude null, empty, and 'None' values
        cursor.execute("""
            SELECT priority, COUNT(*) as count
            FROM test_cases
            WHERE priority IS NOT NULL
            AND priority != ''
            AND priority != 'None'
            AND TRIM(priority) != ''
            GROUP BY priority
            ORDER BY count DESC
        """)
        priorities_data = cursor.fetchall()
        filter_options['priorities'] = [row[0] for row in priorities_data]

        # Get unique test groups (with frequency)
        cursor.execute("""
            SELECT test_group, COUNT(*) as count
            FROM test_cases
            WHERE test_group IS NOT NULL AND test_group != ''
            GROUP BY test_group
            ORDER BY count DESC
        """)
        test_groups_data = cursor.fetchall()
        filter_options['test_groups'] = [row[0] for row in test_groups_data]

        # Get unique features (with frequency)
        cursor.execute("""
            SELECT feature, COUNT(*) as count
            FROM test_cases
            WHERE feature IS NOT NULL AND feature != ''
            GROUP BY feature
            ORDER BY count DESC
        """)
        features_data = cursor.fetchall()
        filter_options['features'] = [row[0] for row in features_data]

        # Get unique projects (with frequency)
        cursor.execute("""
            SELECT project, COUNT(*) as count
            FROM test_cases
            WHERE project IS NOT NULL AND project != ''
            GROUP BY project
            ORDER BY count DESC
        """)
        projects_data = cursor.fetchall()
        filter_options['projects'] = [row[0] for row in projects_data]

        # Get unique users (with frequency)
        cursor.execute("""
            SELECT user_name, COUNT(*) as count
            FROM test_cases
            WHERE user_name IS NOT NULL AND user_name != ''
            GROUP BY user_name
            ORDER BY count DESC
        """)
        users_data = cursor.fetchall()
        filter_options['users'] = [row[0] for row in users_data]

        # Get unique test statuses from test_steps (with frequency)
        cursor.execute("""
            SELECT test_status, COUNT(*) as count
            FROM test_steps
            WHERE test_status IS NOT NULL AND test_status != ''
            GROUP BY test_status
            ORDER BY count DESC
        """)
        test_statuses_data = cursor.fetchall()
        filter_options['test_statuses'] = [row[0] for row in test_statuses_data]

        # If no test statuses exist, provide the 4 standard options
        if not filter_options['test_statuses']:
            filter_options['test_statuses'] = ['Not Run', 'Pass', 'Fail', 'Blocked']

        # Get date range (earliest and latest timestamps)
        cursor.execute("SELECT MIN(DATE(timestamp)), MAX(DATE(timestamp)) FROM test_cases")
        date_range = cursor.fetchone()
        filter_options['date_range'] = {
            'min_date': date_range[0] if date_range[0] else None,
            'max_date': date_range[1] if date_range[1] else None
        }

        # Get statistics for better filter recommendations
        cursor.execute("SELECT COUNT(*) FROM test_cases")
        total_test_cases = cursor.fetchone()[0]

        filter_options['statistics'] = {
            'total_test_cases': total_test_cases,
            'test_types_count': len(filter_options['test_types']),
            'priorities_count': len(filter_options['priorities']),
            'test_groups_count': len(filter_options['test_groups']),
            'features_count': len(filter_options['features']),
            'projects_count': len(filter_options['projects']),
            'users_count': len(filter_options['users']),
            'jira_ids_count': len(filter_options['jira_ids']),
            'test_statuses_count': len(filter_options['test_statuses'])
        }

        print(f"✅ Filter options loaded: {filter_options['statistics']}")
        return filter_options

    except sqlite3.Error as e:
        print(f"Error retrieving filter options: {e}")
        return {
            'jira_ids': [],
            'test_types': ['POSITIVE', 'NEGATIVE', 'SECURITY', 'PERFORMANCE'],
            'dashboard_test_types': ['positive', 'negative', 'security', 'performance'],
            'priorities': ['High', 'Medium', 'Low'],
            'test_groups': [],
            'features': [],
            'projects': [],
            'users': [],
            'test_statuses': ['Not Run', 'Pass', 'Fail', 'Blocked'],
            'date_range': {'min_date': None, 'max_date': None},
            'statistics': {}
        }
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def cleanup_invalid_priority_values(database_path: str) -> tuple[int, str]:
    """
    Clean up invalid priority values in the database.

    This function:
    1. Sets 'None' string values to 'Medium' (default)
    2. Sets empty string values to 'Medium' (default)
    3. Sets NULL values to 'Medium' (default)

    Args:
        database_path: Path to the SQLite database

    Returns:
        tuple: (number_of_updated_records, status_message)
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60)
        cursor = conn.cursor()

        # Count records that need updating
        cursor.execute("""
            SELECT COUNT(*) FROM test_cases
            WHERE priority IS NULL
            OR priority = ''
            OR priority = 'None'
            OR TRIM(priority) = ''
        """)
        count_before = cursor.fetchone()[0]

        if count_before == 0:
            return 0, "No invalid priority values found"

        # Update invalid priority values to 'Medium' (default)
        cursor.execute("""
            UPDATE test_cases
            SET priority = 'Medium'
            WHERE priority IS NULL
            OR priority = ''
            OR priority = 'None'
            OR TRIM(priority) = ''
        """)

        updated_count = cursor.rowcount
        conn.commit()

        return updated_count, f"Successfully updated {updated_count} records with invalid priority values"

    except Exception as e:
        if conn:
            conn.rollback()
        return 0, f"Error cleaning up priority values: {str(e)}"
    finally:
        if conn:
            conn.close()


def save_test_cases_to_database(database_path, test_cases_df, jira_id, test_type, user_name, test_run_id=None):
    """
    Save test cases dataframe to the database.
    
    FUNCTION TYPE: DATABASE FUNCTION
    
    Handles the complete process of storing test case data in the database,
    including validation, formatting, and error handling.

    Args:
        database_path (str): Path to the SQLite database file
        test_cases_df (pandas.DataFrame): Dataframe containing test cases
        jira_id (str): JIRA issue ID
        test_type (str): Type of test cases
        user_name (str): Name of the user saving the test cases
        test_run_id (int, optional): ID of the associated test run

    Returns:
        tuple: (success: bool, message: str)
            - success: True if save operation successful
            - message: Success message or error description

    Usage Example:
        success, message = save_test_cases_to_database(
            db_path, df, "TP-1", "positive", "john_doe", test_run_id
        )
        if success:
            st.success(message)
        else:
            st.error(message)
    """
    try:
        # Validate the dataframe
        if test_cases_df.empty:
            return False, "Cannot save empty dataframe to database"
        
        # Check for required columns
        required_columns = ["Test Case ID", "Test Case Objective"]
        missing_columns = [col for col in required_columns if col not in test_cases_df.columns]
        if missing_columns:
            return False, f"Missing required columns: {', '.join(missing_columns)}"        # Save the test cases to database using the correct function
        from ..models.test_cases import save_test_cases_to_database as _save_test_cases_to_database
        test_run_id_result = _save_test_cases_to_database(
            database_path,
            jira_id,
            test_cases_df,
            test_type,
            user_name,
            test_run_id,
            is_edited=False
        )
        
        if test_run_id_result is not None:
            success = True
            message = f"Successfully saved {len(test_cases_df)} test cases to database (Test Run ID: {test_run_id_result})"
        else:
            success = False
            message = "Failed to save test cases to database"

        return success, message

    except Exception as e:
        error_msg = f"Database error while saving test cases: {str(e)}"
        return False, error_msg


def get_latest_test_cases(database_path, jira_id, test_type, user_name):
    """
    Retrieve the most recent test cases for a given JIRA ID and test type.
    
    FUNCTION TYPE: DATABASE FUNCTION
    
    Fetches test cases from the database, handling both individual test types
    and "all" test type scenarios with proper user filtering.

    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str): JIRA issue ID to fetch test cases for
        test_type (str): Type of test cases to retrieve
        user_name (str): Name of the user to filter test cases

    Returns:
        pandas.DataFrame: Dataframe containing the retrieved test cases
                         Empty dataframe if no test cases found

    Usage Example:
        df = get_latest_test_cases(db_path, "TP-1", "positive", "john_doe")
        if not df.empty:
            st.dataframe(df)
        else:
            st.info("No test cases found")
    """
    try:
        # For "all" test type, we need special handling
        if test_type == "all":
            # First try to get the test run with type "all"
            from ..models.test_runs import get_latest_test_run_id
            latest_test_run_id = get_latest_test_run_id(
                database_path, jira_id, "all", user_name
            )

            if latest_test_run_id:
                # Get test cases for this test run
                latest_df = get_test_cases_for_test_run(database_path, latest_test_run_id)
                if not latest_df.empty:
                    return latest_df

            # If no "all" test run found, try to get all test cases for this JIRA ID
            return _get_all_test_cases_for_jira_id(database_path, jira_id, user_name)
        else:
            # For specific test types
            from ..models.test_runs import get_latest_test_run_id
            latest_test_run_id = get_latest_test_run_id(
                database_path, jira_id, test_type, user_name
            )

            if latest_test_run_id:
                return get_test_cases_for_test_run(database_path, latest_test_run_id)
            else:
                # Fallback to getting test cases by JIRA ID and test type
                return _get_test_cases_by_jira_and_type(database_path, jira_id, test_type, user_name)

    except Exception as e:
        if 'st' in globals():
            import streamlit as st
            st.error(f"Error retrieving test cases: {str(e)}")
        return pd.DataFrame()


def _get_all_test_cases_for_jira_id(database_path, jira_id, user_name):
    """
    Internal helper to get all test cases for a JIRA ID regardless of type.
    
    FUNCTION TYPE: DATABASE HELPER FUNCTION
    
    Private function to retrieve all test cases associated with a JIRA ID,
    used as fallback when specific test runs are not found.

    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str): JIRA issue ID
        user_name (str): User name for filtering

    Returns:
        pandas.DataFrame: All test cases for the JIRA ID
    """
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        query = """
        SELECT
            tc.id,
            tc.test_case_id as "Test Case ID",
            tc.test_case_objective as "Test Case Objective",
            tc.feature as "Feature",
            tc.prerequisite as "Prerequisite",
            tc.test_group as "Test Group",
            tc.priority as "Priority",
            '' as "Test Status",
            '' as "Notes",
            tc.dashboard_test_type,
            tc.jira_id as "User Story ID",
            tc.timestamp as "Timestamp",
            tc.user_name,
            'header' as "row_type",
            ts.step_number as "Step No",
            ts.test_step as "Test Steps",
            ts.expected_result as "Expected Result",
            '' as "Actual Result",
            '' as "Defect ID",
            '' as "Comments",
            tc.is_edited,
            tc.test_type as "Test Type",
            tc.project as "Project",
            tc.test_run_id,
            tc.ai_modified,
            tc.modification_source,
            tc.ai_model_used,
            tc.ai_modification_timestamp,
            tc.ai_modification_user,
            tc.ai_user_query
        FROM test_cases tc
        LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
        WHERE tc.jira_id = ? AND tc.user_name = ?
        ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number
        """

        cursor.execute(query, (jira_id, user_name))
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()

        if rows:
            return pd.DataFrame(rows, columns=columns)
        else:
            return pd.DataFrame()

    except Exception as e:
        if 'st' in globals():
            import streamlit as st
            st.error(f"Database error retrieving all test cases: {str(e)}")
        return pd.DataFrame()


def _get_test_cases_by_jira_and_type(database_path, jira_id, test_type, user_name):
    """
    Internal helper to get test cases by JIRA ID and specific test type.
    
    FUNCTION TYPE: DATABASE HELPER FUNCTION
    
    Private function to retrieve test cases filtered by JIRA ID and test type,
    used when test run information is not available.

    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str): JIRA issue ID
        test_type (str): Specific test type to filter
        user_name (str): User name for filtering

    Returns:
        pandas.DataFrame: Test cases matching the criteria
    """
    try:
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        query = """
        SELECT
            tc.id,
            tc.test_case_id as "Test Case ID",
            tc.test_case_objective as "Test Case Objective",
            tc.feature as "Feature",
            tc.prerequisite as "Prerequisite",
            tc.test_group as "Test Group",
            tc.priority as "Priority",
            '' as "Test Status",
            '' as "Notes",
            tc.dashboard_test_type,
            tc.jira_id as "User Story ID",
            tc.timestamp as "Timestamp",
            tc.user_name,
            'header' as "row_type",
            ts.step_number as "Step No",
            ts.test_step as "Test Steps",
            ts.expected_result as "Expected Result",
            '' as "Actual Result",
            '' as "Defect ID",
            '' as "Comments",
            tc.is_edited,
            tc.test_type as "Test Type",
            tc.project as "Project",
            tc.ai_modified,
            tc.modification_source,
            tc.ai_model_used,
            tc.ai_modification_timestamp,
            tc.ai_modification_user,
            tc.ai_user_query
        FROM test_cases tc
        LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
        WHERE tc.jira_id = ? AND tc.dashboard_test_type = ? AND tc.user_name = ?
        ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number
        """

        cursor.execute(query, (jira_id, test_type, user_name))
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()

        if rows:
            return pd.DataFrame(rows, columns=columns)
        else:
            return pd.DataFrame()

    except Exception as e:
        if 'st' in globals():
            import streamlit as st
            st.error(f"Database error retrieving test cases by type: {str(e)}")
        return pd.DataFrame()


def get_most_recent_test_cases_exact(database_path, jira_id, test_type, user_name):
    """
    Get the most recent test cases using the exact backup logic.
    
    This function replicates the exact backup file logic for the "Most Recent Test Cases" tab,
    including direct SQL queries and proper data formatting.
    
    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str): JIRA issue ID to fetch test cases for
        test_type (str): Type of test cases to retrieve
        user_name (str): Name of the user to filter test cases
        
    Returns:
        pandas.DataFrame: Dataframe containing the retrieved test cases in backup format
    """
    import pandas as pd
    
    try:
        # For "all" test type, we need a special approach to get all test cases
        if test_type == "all":
            # First try to get the test run with type "all"
            from ..models.test_runs import get_latest_test_run_id
            latest_test_run_id = get_latest_test_run_id(database_path, jira_id, "all", user_name)

            if latest_test_run_id:
                # For "all" test runs, get all test cases associated with this test run ID
                # regardless of their individual dashboard_test_type
                query = """
                SELECT
                    tc.id,
                    tc.test_case_id as "Test Case ID",
                    tc.test_case_objective as "Test Case Objective",
                    tc.feature as "Feature",
                    tc.prerequisite as "Prerequisite",
                    tc.test_group as "Test Group",
                    tc.priority as "Priority",
                    '' as "Test Status",
                    '' as "Notes",
                    tc.dashboard_test_type,
                    tc.jira_id as "User Story ID",
                    tc.timestamp as "Timestamp",
                    tc.user_name,
                    'header' as "row_type",
                    ts.step_number as "Step No",
                    ts.test_step as "Test Steps",
                    ts.expected_result as "Expected Result",
                    '' as "Actual Result",
                    '' as "Defect ID",
                    '' as "Comments",
                    tc.is_edited,
                    tc.test_type as "Test Type",
                    tc.project as "Project",
                    tc.test_run_id,
                    tc.ai_modified,
                    tc.modification_source,
                    tc.ai_model_used,
                    tc.ai_modification_timestamp,
                    tc.ai_modification_user,
                    tc.ai_user_query
                FROM test_cases tc
                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                WHERE tc.test_run_id = ? AND tc.user_name = ?
                ORDER BY tc.test_case_id, ts.step_number
                """

                conn = get_thread_local_connection(database_path)
                cursor = conn.cursor()
                cursor.execute(query, (latest_test_run_id, user_name))

                # Convert the results to a DataFrame
                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()

                if rows:
                    latest_df = pd.DataFrame(rows, columns=columns)
                else:
                    # If no test cases found with test_run_id, fall back to getting all test cases for this JIRA ID
                    query = """
                    SELECT
                        tc.id,
                        tc.test_case_id as "Test Case ID",
                        tc.test_case_objective as "Test Case Objective",
                        tc.feature as "Feature",
                        tc.prerequisite as "Prerequisite",
                        tc.test_group as "Test Group",
                        tc.priority as "Priority",
                        '' as "Test Status",
                        '' as "Notes",
                        tc.dashboard_test_type,
                        tc.jira_id as "User Story ID",
                        tc.timestamp as "Timestamp",
                        tc.user_name,
                        'header' as "row_type",
                        ts.step_number as "Step No",
                        ts.test_step as "Test Steps",
                        ts.expected_result as "Expected Result",
                        '' as "Actual Result",
                        '' as "Defect ID",
                        '' as "Comments",
                        tc.is_edited,
                        tc.test_run_id
                    FROM test_cases tc
                    LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                    WHERE tc.jira_id = ? AND tc.user_name = ?
                    ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number
                    """

                    cursor.execute(query, (jira_id, user_name))

                    # Convert the results to a DataFrame
                    columns = [description[0] for description in cursor.description]
                    rows = cursor.fetchall()

                    if rows:
                        latest_df = pd.DataFrame(rows, columns=columns)
                    else:
                        latest_df = pd.DataFrame()
            else:
                # If no test run with type "all" found, try to find test runs for individual test types
                all_test_types = ["positive", "negative", "security", "performance"]
                combined_df = pd.DataFrame()

                # Get the latest test run ID for each test type
                for tt in all_test_types:
                    from ..models.test_runs import get_latest_test_run_id
                    tt_test_run_id = get_latest_test_run_id(database_path, jira_id, tt, user_name)

                    if tt_test_run_id:
                        tt_df = get_test_cases_for_test_run(database_path, tt_test_run_id)

                        if not tt_df.empty:
                            # Append to the combined dataframe
                            combined_df = pd.concat([combined_df, tt_df], ignore_index=True)

                if not combined_df.empty:
                    latest_df = combined_df
                else:
                    # If still no test cases found, try to get all test cases for this JIRA ID
                    query = """
                    SELECT
                        tc.id,
                        tc.test_case_id as "Test Case ID",
                        tc.test_case_objective as "Test Case Objective",
                        tc.feature as "Feature",
                        tc.prerequisite as "Prerequisite",
                        tc.test_group as "Test Group",
                        tc.priority as "Priority",
                        '' as "Test Status",
                        '' as "Notes",
                        tc.dashboard_test_type,
                        tc.jira_id as "User Story ID",
                        tc.timestamp as "Timestamp",
                        tc.user_name,
                        'header' as "row_type",
                        ts.step_number as "Step No",
                        ts.test_step as "Test Steps",
                        ts.expected_result as "Expected Result",
                        '' as "Actual Result",
                        '' as "Defect ID",
                        '' as "Comments",
                        tc.is_edited,
                        tc.test_run_id,
                        tc.ai_modified,
                        tc.modification_source,
                        tc.ai_model_used,
                        tc.ai_modification_timestamp,
                        tc.ai_modification_user,
                        tc.ai_user_query
                    FROM test_cases tc
                    LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                    WHERE tc.jira_id = ? AND tc.user_name = ?
                    ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number
                    """

                    conn = get_thread_local_connection(database_path)
                    cursor = conn.cursor()
                    cursor.execute(query, (jira_id, user_name))

                    # Convert the results to a DataFrame
                    columns = [description[0] for description in cursor.description]
                    rows = cursor.fetchall()

                    if rows:
                        latest_df = pd.DataFrame(rows, columns=columns)
                    else:
                        latest_df = pd.DataFrame()
        else:
            # For specific test types, use the normal flow
            # Get the latest test run ID for this JIRA ID and test type
            from ..models.test_runs import get_latest_test_run_id
            latest_test_run_id = get_latest_test_run_id(database_path, jira_id, test_type, user_name)

            if latest_test_run_id:
                # Try to get test cases using the test run ID
                try:
                    conn = get_thread_local_connection(database_path)
                    cursor = conn.cursor()

                    # Query for test cases with this test run ID
                    query = """
                    SELECT
                        tc.id,
                        tc.test_case_id as "Test Case ID",
                        tc.test_case_objective as "Test Case Objective",
                        tc.feature as "Feature",
                        tc.prerequisite as "Prerequisite",
                        tc.test_group as "Test Group",
                        tc.priority as "Priority",
                        '' as "Test Status",
                        '' as "Notes",
                        tc.dashboard_test_type,
                        tc.jira_id as "User Story ID",
                        tc.timestamp as "Timestamp",
                        tc.user_name,
                        'header' as "row_type",
                        ts.step_number as "Step No",
                        ts.test_step as "Test Steps",
                        ts.expected_result as "Expected Result",
                        '' as "Actual Result",
                        '' as "Defect ID",
                        '' as "Comments",
                        tc.is_edited,
                        tc.test_type as "Test Type",
                        tc.project as "Project",
                        tc.ai_modified,
                        tc.modification_source,
                        tc.ai_model_used,
                        tc.ai_modification_timestamp,
                        tc.ai_modification_user,
                        tc.ai_user_query
                    FROM test_cases tc
                    LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                    WHERE tc.test_run_id = ?
                    ORDER BY tc.test_case_id, ts.step_number
                    """

                    cursor.execute(query, (latest_test_run_id,))

                    # Convert the results to a DataFrame
                    columns = [description[0] for description in cursor.description]
                    rows = cursor.fetchall()

                    if rows:
                        latest_df = pd.DataFrame(rows, columns=columns)
                    else:
                        # Fallback to getting test cases by JIRA ID and test type
                        # Query for test cases with this JIRA ID and test type
                        query = """
                        SELECT
                            tc.id,
                            tc.test_case_id as "Test Case ID",
                            tc.test_case_objective as "Test Case Objective",
                            tc.feature as "Feature",
                            tc.prerequisite as "Prerequisite",
                            tc.test_group as "Test Group",
                            tc.priority as "Priority",
                            '' as "Test Status",
                            '' as "Notes",
                            tc.dashboard_test_type,
                            tc.jira_id as "User Story ID",
                            tc.timestamp as "Timestamp",
                            tc.user_name,
                            'header' as "row_type",
                            ts.step_number as "Step No",
                            ts.test_step as "Test Steps",
                            ts.expected_result as "Expected Result",
                            '' as "Actual Result",
                            '' as "Defect ID",
                            '' as "Comments",
                            tc.is_edited,
                            tc.test_type as "Test Type",
                            tc.project as "Project",
                            tc.ai_modified,
                            tc.modification_source,
                            tc.ai_model_used,
                            tc.ai_modification_timestamp,
                            tc.ai_modification_user,
                            tc.ai_user_query
                        FROM test_cases tc
                        LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                        WHERE tc.jira_id = ?
                        """

                        # Add test type filter if not 'all'
                        params = [jira_id]
                        if test_type.lower() != 'all':
                            query += " AND tc.dashboard_test_type = ?"
                            params.append(test_type.lower())

                        # Add user filter
                        if user_name:
                            query += " AND tc.user_name = ?"
                            params.append(user_name)

                        # Order by timestamp to get the most recent test cases
                        query += " ORDER BY tc.timestamp DESC, tc.test_case_id, ts.step_number"

                        cursor.execute(query, params)

                        # Convert the results to a DataFrame
                        columns = [description[0] for description in cursor.description]
                        rows = cursor.fetchall()

                        if rows:
                            latest_df = pd.DataFrame(rows, columns=columns)
                        else:
                            latest_df = pd.DataFrame()
                except Exception as e:
                    latest_df = pd.DataFrame()
            else:
                # If no test run found, try getting test cases by JIRA ID and test type
                latest_df = pd.DataFrame()

                try:
                    conn = get_thread_local_connection(database_path)
                    cursor = conn.cursor()

                    # Query for test cases with this JIRA ID and test type
                    query = """
                    SELECT tc.*, ji.jira_id as jira_issue_id
                    FROM test_cases tc
                    JOIN jira_issues ji ON tc.jira_issue_id = ji.id
                    WHERE tc.jira_id = ? AND tc.dashboard_test_type = ?
                    ORDER BY tc.timestamp DESC"""
                    cursor.execute(query, (jira_id, test_type))
                    
                    # Get the results and convert to DataFrame
                    columns = [description[0] for description in cursor.description]
                    rows = cursor.fetchall()
                    
                    if rows:
                        latest_df = pd.DataFrame(rows, columns=columns)
                    else:
                        latest_df = pd.DataFrame()
                        
                except Exception as e:
                    latest_df = pd.DataFrame()
                    if conn:
                        conn.close()

        return latest_df if 'latest_df' in locals() else pd.DataFrame()

    except Exception as e:
        return pd.DataFrame()


def process_backup_style_test_data(raw_df):
    """
    Process raw SQL test case data into the backup-style display format.
    
    This function takes the raw SQL results and formats them exactly like the backup file,
    creating proper test case header rows followed by step rows.
    
    Args:
        raw_df (pandas.DataFrame): Raw SQL results from get_most_recent_test_cases_exact
        
    Returns:
        pandas.DataFrame: Formatted dataframe ready for display
    """
    import pandas as pd
    
    # First, ensure we have all the necessary columns including AI tracking columns
    required_columns = [
        "Timestamp", "Project", "Feature", "User Story ID", "Test Case ID",
        "Test Case Objective", "Prerequisite", "Step No", "Test Steps",
        "Expected Result", "Test Status", "Actual Result", "Priority",
        "Defect ID", "Comments", "Test Type", "Test Group",
        "ai_modified", "modification_source", "ai_model_used",
        "ai_modification_timestamp", "ai_modification_user", "ai_user_query"
    ]
    
    # Create a new dataframe with the required columns
    display_df = pd.DataFrame(columns=required_columns)
    
    # Check if raw_df is empty or doesn't have 'Test Case ID' column
    if raw_df.empty or 'Test Case ID' not in raw_df.columns:
        return display_df
    
    # Get all unique test case IDs from raw data
    unique_test_cases = raw_df['Test Case ID'].dropna().unique()

    # For each test case, format the data properly to show all steps
    for tc_id in unique_test_cases:
        # Get all rows for this test case from raw data
        tc_rows = raw_df[raw_df["Test Case ID"] == tc_id].copy()
        if tc_rows.empty:
            continue

        # Sort by step number to ensure proper order
        if "Step No" in tc_rows.columns:
            tc_rows = tc_rows.sort_values("Step No")

        # Get the header row (first row with this test case ID)
        header_row = tc_rows.iloc[0]

        # Create the first row with header information and first step
        header_data = {}
        for col in required_columns:
            if col in header_row:
                header_data[col] = header_row[col]
            elif col == "Test Status":
                header_data[col] = ""
            elif col == "Actual Result":
                header_data[col] = ""
            elif col == "Defect ID":
                header_data[col] = ""
            elif col == "Comments":
                header_data[col] = ""
            else:
                header_data[col] = ""

        # Add the header row to the display dataframe
        display_df = pd.concat([display_df, pd.DataFrame([header_data])], ignore_index=True)

        # Add all remaining steps for this test case
        for _, step_row in tc_rows.iloc[1:].iterrows():
            if pd.isna(step_row.get('Step No')) or step_row.get('Step No') == "":
                continue

            # Create a new row for the step with empty values for header fields
            step_data = {col: "" for col in required_columns}

            # Only fill in step-specific columns
            step_data["Step No"] = step_row.get('Step No', "")
            step_data["Test Steps"] = step_row.get('Test Steps', "")
            step_data["Expected Result"] = step_row.get('Expected Result', "")
            step_data["Test Status"] = ""
            step_data["Actual Result"] = ""
            step_data["Defect ID"] = ""
            step_data["Comments"] = ""

            # Add the step row to the display dataframe
            display_df = pd.concat([display_df, pd.DataFrame([step_data])], ignore_index=True)
    
    return display_df


def count_test_cases_in_database_output(output_file, test_type):
    """
    Count test cases from a database output file reference.
    
    FUNCTION TYPE: DATABASE HELPER FUNCTION
    
    Parses database URL references and counts the actual number of
    test cases stored for a specific JIRA ID and test type.

    Args:
        output_file (str): Database URL in format "database://jira_id/test_type/latest"
        test_type (str): Test type to count cases for

    Returns:
        int: Number of test cases found, or 0 if error/not found

    Usage Example:
        count = count_test_cases_in_database_output(
            "database://TP-1/positive/latest", "positive"
        )
        st.info(f"Found {count} test cases in database")
    """
    try:
        if not output_file or not output_file.startswith("database://"):
            return 0

        # Extract JIRA ID and test type from the database URL
        parts = output_file.split("/")
        if len(parts) >= 4:
            db_jira_id = parts[2]
            db_test_type = parts[3]

            # Get the test cases from the database
            df = get_test_cases_from_database(
                "test_cases_v2.db", db_jira_id, test_type  # Use default database path
            )

            # Count unique test case IDs
            if not df.empty and "Test Case ID" in df.columns:
                valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                unique_test_case_ids = valid_test_case_ids.unique()
                return len(unique_test_case_ids)

        return 0

    except Exception as e:
        if 'st' in globals():
            import streamlit as st
            st.error(f"Error counting test cases from database: {str(e)}")
        return 0


def get_test_cases_for_export(db_path, filters=None):
    """
    Get test cases specifically formatted for export with ascending chronological order.

    This function is optimized for export operations, providing test cases sorted
    in ascending chronological order (oldest first) for timeline tracking and
    historical analysis purposes.

    Args:
        db_path (str): Absolute path to the SQLite database file
        filters (dict): Dictionary containing filter criteria (same as get_unified_filtered_test_cases)

    Returns:
        pandas.DataFrame: DataFrame containing filtered test cases sorted chronologically (oldest first)

    Example:
        # Get test cases for export with chronological order
        filters = {
            'jira_id': 'TP-123',
            'test_type': 'positive',
            'test_case_id_range_start': 46,
            'test_case_id_range_end': 55
        }
        df_export = get_test_cases_for_export(db_path, filters)
    """
    return get_unified_filtered_test_cases(db_path, filters, sort_order='asc')
