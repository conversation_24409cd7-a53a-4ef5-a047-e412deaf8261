"""
Locator Conflict Resolution for GretahAI ScriptWeaver

This module provides heuristic preprocessing to resolve conflicts between
step data locators and element matches data before sending to AI generation.

Key Features:
- Prioritizes manually selected elements over automated matches
- Uses reliability-based locator strategy ranking (NAME > CSS > XPATH > ID)
- Resolves conflicts by choosing highest confidence score with priority order
- Creates unified locator recommendations for AI consumption
- Comprehensive logging for debugging and transparency
- Extracts name attributes from CSS selectors and XPath expressions

The resolver eliminates AI confusion by preprocessing conflicting locator
information and providing a single, authoritative locator recommendation
following the priority order: name > css > xpath > id.
"""

import json
import time
import os
from typing import Dict, Any, List, Union
from enum import Enum
from dataclasses import dataclass
from debug_utils import debug

class LocatorStrategy(Enum):
    """Enum representing different locator strategies with their string values."""
    ID = "id"
    CSS = "css"
    CSS_SELECTOR = "css_selector"
    NAME = "name"
    CLASS = "class"
    XPATH = "xpath"
    TAG = "tag"
    TEXT = "text"
    URL = "url" 
    NONE = "none"
    
    @classmethod
    def from_string(cls, value: str) -> 'LocatorStrategy':
        """Convert string to enum member, defaulting to NONE for unknown values."""
        try:
            return cls(value.lower()) if value else cls.NONE
        except ValueError:
            return cls.NONE
            
    def __str__(self) -> str:
        return self.value

# Locator strategy reliability ranking (higher is better)
# Aligned with priority order: NAME > CSS > XPATH > ID
# This matches the _PREFERRED_ORDER = ["name", "css", "xpath", "id"]
LOCATOR_STRATEGY_RELIABILITY = {
    LocatorStrategy.NAME: 100,      # Highest priority - most stable
    LocatorStrategy.CSS: 80,        # Second priority - good balance
    LocatorStrategy.CSS_SELECTOR: 80,
    LocatorStrategy.XPATH: 60,      # Third priority - can be fragile
    LocatorStrategy.ID: 50,         # Fourth priority - can change
    LocatorStrategy.CLASS: 40,      # Lower priority - often dynamic
    LocatorStrategy.TAG: 30,        # Low priority - not specific
    LocatorStrategy.TEXT: 35,       # Low priority - can change
    LocatorStrategy.URL: 90,        # For navigation steps
    LocatorStrategy.NONE: 10        # Lowest priority
}

@dataclass
class ElementMatch:
    """Represents a matched element with its score and selection status."""
    element: Dict[str, Any]
    score: float
    manually_selected: bool = False
    
@dataclass
class ResolutionResult:
    """Represents the result of locator conflict resolution."""
    resolved_locator_strategy: str
    resolved_locator: str
    resolution_reason: str
    confidence_score: float
    original_step_locator: Dict[str, str]
    original_element_matches: List[Dict[str, Any]]
    conflict_detected: bool


# 1️⃣  preference table – put stable attributes first, empty IDs last
_PREFERRED_ORDER = ["name", "css", "xpath", "id"]

# Debug logging configuration
DEBUG_LOG_DIR = "logs/locator_resolution_debug"
DEBUG_ENABLED = True

def _log_comprehensive_debug(
    operation: str,
    test_case_id: str,
    step_no: str,
    data: Dict[str, Any],
    timestamp: str = None
) -> None:
    """
    Log comprehensive debug information for locator resolution troubleshooting.

    Args:
        operation: The operation being performed (input, processing, output)
        test_case_id: Test case identifier
        step_no: Step number
        data: Data to log
        timestamp: Optional timestamp (generated if not provided)
    """
    if not DEBUG_ENABLED:
        return

    try:
        if timestamp is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S") + f"_{int(time.time() * 1000) % 1000:03d}"

        # Create debug log directory if it doesn't exist
        os.makedirs(DEBUG_LOG_DIR, exist_ok=True)

        # Create unique log file for this resolution attempt and operation
        log_filename = f"locator_resolution_{test_case_id}_{step_no}_{timestamp}_{operation}.json"
        log_filepath = os.path.join(DEBUG_LOG_DIR, log_filename)

        # Prepare log entry
        log_entry = {
            "timestamp": timestamp,
            "test_case_id": test_case_id,
            "step_no": step_no,
            "operation": operation,
            "data": data
        }

        # Write to file
        with open(log_filepath, 'w', encoding='utf-8') as f:
            json.dump(log_entry, f, indent=2, default=str)

        # Also log to standard debug system
        debug(
            f"Comprehensive debug logged: {operation}",
            stage="locator_resolution_debug",
            operation=f"debug_log_{operation}",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "log_file": log_filename,
                "data_keys": list(data.keys()) if isinstance(data, dict) else "non_dict"
            }
        )

    except Exception as e:
        debug(
            f"Error writing comprehensive debug log: {e}",
            stage="locator_resolution_debug",
            operation="debug_log_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "operation": operation
            }
        )


def resolve_locator_conflicts(
    step_data: Dict[str, Any],
    element_matches: List[Dict[str, Any]],
    step_no: str,
    test_case_id: str = "unknown"
) -> Dict[str, Any]:
    """
    Resolve conflicts between step data locators and element matches.
    
    This function implements heuristic preprocessing to eliminate conflicting
    locator information before sending data to AI generation prompts.
    
    Args:
        step_data: Step data from JSON storage with locator_strategy and locator
        element_matches: List of element matches with selector, xpath, score, manually_selected
        step_no: Step number for logging context
        test_case_id: Test case ID for logging context
        
    Returns:
        Dict containing resolved locator information with:
        - resolved_locator_strategy: The chosen locator strategy
        - resolved_locator: The chosen locator value
        - resolution_reason: Explanation of why this locator was chosen
        - confidence_score: Confidence in the resolution (0.0-1.0)
        - original_step_locator: Original step data locator for reference
        - original_element_matches: Original element matches for reference
        - conflict_detected: Boolean indicating if conflicts were found
    """
    try:
        # Generate unique timestamp for this resolution attempt
        resolution_timestamp = time.strftime("%Y%m%d_%H%M%S") + f"_{int(time.time() * 1000) % 1000:03d}"

        # 📊 COMPREHENSIVE INPUT LOGGING
        input_data = {
            "resolution_id": f"{test_case_id}_{step_no}_{resolution_timestamp}",
            "step_data": {
                "locator_strategy": step_data.get('locator_strategy', ''),
                "locator": step_data.get('locator', ''),
                "full_step_data": step_data
            },
            "element_matches": {
                "count": len(element_matches),
                "matches": []
            },
            "context": {
                "test_case_id": test_case_id,
                "step_no": step_no,
                "preferred_order": _PREFERRED_ORDER,
                "reliability_scores": {str(k): v for k, v in LOCATOR_STRATEGY_RELIABILITY.items()}
            }
        }

        # Log detailed element match data
        for i, match in enumerate(element_matches):
            element = match.get('element', {})
            match_data = {
                "index": i,
                "score": match.get('score', 0),
                "manually_selected": match.get('manually_selected', False),
                "element_data": {
                    "selector": element.get('selector', ''),
                    "xpath": element.get('xpath', ''),
                    "attributes": element.get('attributes', {}),
                    "all_element_keys": list(element.keys())
                },
                "raw_match": match
            }
            input_data["element_matches"]["matches"].append(match_data)

        _log_comprehensive_debug("INPUT", test_case_id, step_no, input_data, resolution_timestamp)

        debug(
            f"Starting locator conflict resolution for test case {test_case_id}, step {step_no}",
            stage="locator_resolution",
            operation="conflict_resolution_start",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "step_locator_strategy": step_data.get('locator_strategy', ''),
                "step_locator": step_data.get('locator', ''),
                "element_matches_count": len(element_matches),
                "resolution_id": input_data["resolution_id"]
            }
        )

        # ─────────────────────────────
        # 2️⃣  convert element data to locator format and scrub invalid locators
        # ─────────────────────────────
        processing_data = {
            "conversion_results": [],
            "filtered_matches": [],
            "conversion_summary": {
                "total_input": len(element_matches),
                "old_format_count": 0,
                "new_format_count": 0,
                "successful_conversions": 0,
                "failed_conversions": 0,
                "filtered_out": 0
            }
        }

        valid_matches = []
        for i, m in enumerate(element_matches):
            conversion_result = {
                "index": i,
                "input_match": m,
                "conversion_type": None,
                "output_match": None,
                "success": False,
                "reason": None
            }

            # Enhanced format detection: Always prioritize element analysis when element data is available
            # Check multiple possible locations for element data
            element = None
            if 'element_data' in m:
                element = m['element_data']
            elif 'element' in m:
                element = m['element']
            elif 'raw_match' in m and 'element' in m['raw_match']:
                element = m['raw_match']['element']
            else:
                element = {}

            has_element_data = bool(element and (element.get('attributes') or element.get('selector') or element.get('xpath')))
            has_old_format = 'locator_strategy' in m and 'locator' in m

            # Priority 1: Element analysis (when element data is available)
            if has_element_data:
                conversion_result["conversion_type"] = "element_analysis_priority"
                processing_data["conversion_summary"]["new_format_count"] += 1

                debug(
                    f"Prioritizing element analysis for element {i} (element data available)",
                    stage="locator_resolution",
                    operation="element_analysis_priority",
                    context={
                        "element_index": i,
                        "has_element_data": has_element_data,
                        "has_old_format": has_old_format,
                        "element_keys": list(element.keys()),
                        "attributes_available": bool(element.get('attributes')),
                        "name_attribute": element.get('attributes', {}).get('name'),
                        "original_strategy": m.get('locator_strategy'),
                        "original_locator": m.get('locator')
                    }
                )

                converted_match = _convert_element_to_locator_match(element, m, resolution_timestamp, test_case_id, step_no, i)

                if converted_match and converted_match.get('locator'):
                    # Element analysis successful - use the result
                    conversion_result["success"] = True
                    conversion_result["output_match"] = converted_match
                    conversion_result["reason"] = f"element_analysis_to_{converted_match.get('locator_strategy', 'unknown')}"
                    valid_matches.append(converted_match)
                    processing_data["conversion_summary"]["successful_conversions"] += 1

                    debug(
                        f"Element analysis successful for element {i} - selected {converted_match.get('locator_strategy')} strategy",
                        stage="locator_resolution",
                        operation="element_analysis_success",
                        context={
                            "element_index": i,
                            "converted_strategy": converted_match.get('locator_strategy'),
                            "converted_locator": converted_match.get('locator'),
                            "original_strategy": m.get('locator_strategy'),
                            "original_locator": m.get('locator'),
                            "priority_improvement": converted_match.get('locator_strategy') in _PREFERRED_ORDER and m.get('locator_strategy', '').lower() in _PREFERRED_ORDER and _PREFERRED_ORDER.index(converted_match.get('locator_strategy')) < _PREFERRED_ORDER.index(m.get('locator_strategy', '').lower()) if converted_match.get('locator_strategy') and m.get('locator_strategy') else False
                        }
                    )
                elif has_old_format:
                    # Element analysis failed but old format data is available - fallback to old format
                    conversion_result["conversion_type"] = "old_format_fallback"
                    conversion_result["reason"] = "element_analysis_failed_using_old_format"

                    loc = (m.get("locator") or "").strip()
                    strat = (m.get("locator_strategy") or "").lower()

                    if not loc:
                        conversion_result["reason"] = "empty_locator_after_element_analysis_failed"
                        processing_data["conversion_summary"]["filtered_out"] += 1
                    else:
                        conversion_result["success"] = True
                        conversion_result["output_match"] = m
                        valid_matches.append(m)
                        processing_data["conversion_summary"]["successful_conversions"] += 1

                        debug(
                            f"Element analysis failed for element {i} - falling back to old format {strat} strategy",
                            stage="locator_resolution",
                            operation="old_format_fallback",
                            context={
                                "element_index": i,
                                "fallback_strategy": strat,
                                "fallback_locator": loc,
                                "element_analysis_failed": True
                            }
                        )
                else:
                    # Both element analysis and old format failed
                    conversion_result["reason"] = "both_element_analysis_and_old_format_failed"
                    processing_data["conversion_summary"]["failed_conversions"] += 1

                    debug(
                        f"Both element analysis and old format failed for element {i}",
                        stage="locator_resolution",
                        operation="conversion_complete_failure",
                        context={
                            "element_index": i,
                            "element_data": element,
                            "has_old_format": has_old_format,
                            "failure_reason": "No valid locator found in either element data or old format"
                        }
                    )

            # Priority 2: Old format only (when no element data is available)
            elif has_old_format:
                conversion_result["conversion_type"] = "old_format_validation"
                processing_data["conversion_summary"]["old_format_count"] += 1

                loc = (m.get("locator") or "").strip()
                strat = (m.get("locator_strategy") or "").lower()

                if not loc:
                    conversion_result["reason"] = "empty_locator"
                    processing_data["conversion_summary"]["filtered_out"] += 1
                elif strat == "id" and loc == "":
                    conversion_result["reason"] = "empty_id_locator"
                    processing_data["conversion_summary"]["filtered_out"] += 1
                else:
                    conversion_result["success"] = True
                    conversion_result["output_match"] = m
                    conversion_result["reason"] = "old_format_valid"
                    valid_matches.append(m)
                    processing_data["conversion_summary"]["successful_conversions"] += 1

                    debug(
                        f"Using old format validation for element {i} (no element data available)",
                        stage="locator_resolution",
                        operation="old_format_only",
                        context={
                            "element_index": i,
                            "strategy": strat,
                            "locator": loc,
                            "reason": "no_element_data_available"
                        }
                    )

            # Priority 3: Neither element data nor old format available
            else:
                conversion_result["conversion_type"] = "no_valid_data"
                conversion_result["reason"] = "no_element_data_or_old_format"
                processing_data["conversion_summary"]["failed_conversions"] += 1

                debug(
                    f"No valid data found for element {i} - skipping",
                    stage="locator_resolution",
                    operation="no_valid_data",
                    context={
                        "element_index": i,
                        "has_element_data": has_element_data,
                        "has_old_format": has_old_format,
                        "match_keys": list(m.keys())
                    }
                )

            processing_data["conversion_results"].append(conversion_result)

        element_matches = valid_matches      # use the cleaned list

        # Log the processing results
        processing_data["final_valid_matches"] = element_matches
        _log_comprehensive_debug("PROCESSING_CONVERSION", test_case_id, step_no, processing_data, resolution_timestamp)

        debug(
            f"Completed locator validation - filtered {len(element_matches)} valid matches",
            stage="locator_resolution",
            operation="locator_validation",
            context={
                "valid_matches_count": len(element_matches),
                "test_case_id": test_case_id,
                "step_no": step_no
            }
        )

        # extract step-data locator *after* cleaning
        step_locator_strategy = (step_data or {}).get('locator_strategy', '').lower()
        step_locator          = (step_data or {}).get('locator', '').strip()

        # default result (fallback to step data)
        result = ResolutionResult(
            resolved_locator_strategy=step_locator_strategy,
            resolved_locator=step_locator,
            resolution_reason='No element matches available – using step data locator',
            confidence_score=0.8,
            original_step_locator={'strategy': step_locator_strategy, 'locator': step_locator},
            original_element_matches=element_matches,
            conflict_detected=False
        )

        # if still no element matches, keep step data BUT STILL LOG COMPREHENSIVE OUTPUT
        if not element_matches:
            debug(
                "No element matches after validation - using step data locator",
                stage="locator_resolution",
                operation="fallback_to_step_data",
                context={
                    "test_case_id": test_case_id,
                    "step_no": step_no,
                    "step_locator_strategy": step_locator_strategy,
                    "step_locator": step_locator
                }
            )

            # 📊 STILL LOG COMPREHENSIVE OUTPUT FOR EMPTY MATCHES CASE
            empty_scoring_data = {
                "candidates": [],
                "scoring_logic": {
                    "preferred_order": _PREFERRED_ORDER,
                    "base_score_multiplier": 10,
                    "unknown_strategy_base": 5,
                    "formula": "base_priority_score + element_score"
                },
                "selection_result": {
                    "error": "No valid candidates after element conversion",
                    "fallback_used": True,
                    "fallback_strategy": step_locator_strategy,
                    "fallback_locator": step_locator,
                    "fallback_reason": "All element conversions failed or no elements provided"
                }
            }
            _log_comprehensive_debug("PROCESSING_SCORING", test_case_id, step_no, empty_scoring_data, resolution_timestamp)

            # Log comprehensive output for fallback case
            fallback_output_data = {
                "final_result": {
                    "resolved_locator_strategy": result.resolved_locator_strategy,
                    "resolved_locator": result.resolved_locator,
                    "confidence_score": result.confidence_score,
                    "resolution_reason": result.resolution_reason,
                    "conflict_detected": result.conflict_detected
                },
                "comparison_with_input": {
                    "original_step_strategy": step_data.get('locator_strategy', ''),
                    "original_step_locator": step_data.get('locator', ''),
                    "strategy_changed": False,  # Using step data unchanged
                    "locator_changed": False,   # Using step data unchanged
                    "improvement_analysis": {
                        "fallback_reason": "No element matches after conversion",
                        "priority_logic_bypassed": True
                    }
                },
                "decision_summary": {
                    "total_candidates_evaluated": 0,
                    "selection_method": "step_data_fallback",
                    "priority_order_used": _PREFERRED_ORDER,
                    "why_this_choice": f"No valid element candidates - using step data {step_locator_strategy} strategy"
                },
                "fallback_analysis": {
                    "reason": "empty_element_matches_after_conversion",
                    "original_element_count": len(input_data["element_matches"]["matches"]),
                    "conversion_failures": processing_data["conversion_summary"]["failed_conversions"],
                    "filtered_out": processing_data["conversion_summary"]["filtered_out"]
                }
            }
            _log_comprehensive_debug("OUTPUT", test_case_id, step_no, fallback_output_data, resolution_timestamp)

            return vars(result)

        # ─────────────────────────────
        # 3️⃣  ALWAYS PROCESS ELEMENT MATCHES WHEN AVAILABLE
        #     Priority-based scoring to find the best locator strategy
        #     regardless of whether conflicts are detected
        # ─────────────────────────────

        debug(
            f"Processing {len(element_matches)} element matches for priority-based selection",
            stage="locator_resolution",
            operation="priority_processing_start",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "element_count": len(element_matches),
                "step_strategy": step_locator_strategy,
                "step_locator": step_locator
            }
        )

        # 📊 COMPREHENSIVE PRIORITY SCORING LOGGING
        scoring_data = {
            "candidates": [],
            "scoring_logic": {
                "preferred_order": _PREFERRED_ORDER,
                "base_score_multiplier": 10,
                "unknown_strategy_base": 5,
                "formula": "base_priority_score + element_score"
            },
            "selection_result": {}
        }

        # CRITICAL FIX: Check for manually selected elements first (highest priority)
        manually_selected_matches = [m for m in element_matches if m.get('manually_selected', False)]

        if manually_selected_matches:
            debug(
                f"Found manually selected elements - applying highest priority rule",
                stage="locator_resolution",
                operation="manual_selection_priority",
                context={
                    "test_case_id": test_case_id,
                    "step_no": step_no,
                    "manually_selected_count": len(manually_selected_matches),
                    "total_matches": len(element_matches)
                }
            )

            # If multiple manual selections, pick highest score
            if len(manually_selected_matches) > 1:
                best_manual_match = max(manually_selected_matches, key=lambda m: m.get('score', 0))
                debug(
                    f"Selected highest scoring manually selected element",
                    stage="locator_resolution",
                    operation="manual_selection_scoring",
                    context={
                        "test_case_id": test_case_id,
                        "step_no": step_no,
                        "selected_score": best_manual_match.get('score', 0),
                        "candidates_count": len(manually_selected_matches)
                    }
                )
            else:
                best_manual_match = manually_selected_matches[0]

            # Use the manually selected element directly
            manual_strat = best_manual_match["locator_strategy"].lower()
            manual_loc = best_manual_match["locator"]
            manual_score = best_manual_match.get("score", 0)

            result.resolved_locator_strategy = manual_strat
            result.resolved_locator = manual_loc
            result.confidence_score = 1.0  # Perfect confidence for manual selection
            result.resolution_reason = f"Manually selected element (highest priority) - {manual_strat} strategy"
            result.conflict_detected = len(element_matches) > 1

            debug(
                f"Using manually selected element - overriding all other logic",
                stage="locator_resolution",
                operation="manual_selection_override",
                context={
                    "manual_strategy": manual_strat,
                    "manual_locator": manual_loc,
                    "manual_score": manual_score,
                    "step_strategy": step_locator_strategy,
                    "step_locator": step_locator,
                    "override_reason": "manually_selected_highest_priority"
                }
            )

            # Log the manual selection result
            scoring_data["selection_result"] = {
                "selected_strategy": manual_strat,
                "selected_locator": manual_loc,
                "selected_score": manual_score,
                "confidence_score": 1.0,
                "selection_reason": "Manually selected element (highest priority)",
                "manually_selected": True,
                "alternatives_count": len(element_matches) - 1,
                "conflict_detected": len(element_matches) > 1
            }

            _log_comprehensive_debug("PROCESSING_SCORING", test_case_id, step_no, scoring_data, resolution_timestamp)

            # Skip the normal scoring logic and return the manual selection
            output_data = {
                "final_result": {
                    "resolved_locator_strategy": result.resolved_locator_strategy,
                    "resolved_locator": result.resolved_locator,
                    "confidence_score": result.confidence_score,
                    "resolution_reason": result.resolution_reason,
                    "conflict_detected": result.conflict_detected
                },
                "manual_selection_override": {
                    "manually_selected": True,
                    "override_applied": True,
                    "selected_from_count": len(manually_selected_matches),
                    "total_candidates": len(element_matches)
                }
            }
            _log_comprehensive_debug("OUTPUT", test_case_id, step_no, output_data, resolution_timestamp)

            return vars(result)

        ranked = []
        for i, m in enumerate(element_matches):
            strat = m["locator_strategy"].lower()
            loc   = m["locator"]
            element_score = m.get("score", 0)

            # Calculate priority-based score
            if strat in _PREFERRED_ORDER:
                priority_index = _PREFERRED_ORDER.index(strat)
                base_score = (len(_PREFERRED_ORDER) - priority_index) * 10
                priority_explanation = f"Priority {priority_index + 1} of {len(_PREFERRED_ORDER)} (higher is better)"
            else:
                base_score = 5
                priority_explanation = "Unknown strategy - low base score"

            total_score = base_score + element_score

            # Calculate reliability score for comparison
            element_data = m.get('element', {})
            reliability_score = _calculate_locator_reliability(element_data) if element_data else 0

            # Check for enhanced locators and boost score if available
            enhanced_css = element_data.get('enhanced_css_selector', '') if element_data else ''
            enhanced_xpath = element_data.get('enhanced_xpath', '') if element_data else ''
            ancestors = element_data.get('ancestors', []) if element_data else []

            # Apply enhanced locator bonus to total score
            enhanced_bonus = 0
            if enhanced_css and enhanced_css != element_data.get('selector', '') and ancestors:
                enhanced_bonus += 15  # Significant bonus for enhanced CSS with ancestors
            if enhanced_xpath and enhanced_xpath != element_data.get('xpath', '') and ancestors:
                enhanced_bonus += 10  # Bonus for enhanced XPath with ancestors

            total_score_with_enhancement = total_score + enhanced_bonus

            candidate_info = {
                "index": i,
                "strategy": strat,
                "locator": loc,
                "element_score": element_score,
                "base_score": base_score,
                "total_score": total_score_with_enhancement,
                "enhanced_bonus": enhanced_bonus,
                "reliability_score": reliability_score,
                "priority_explanation": priority_explanation,
                "manually_selected": m.get('manually_selected', False),
                "element_attributes": element_data.get('attributes', {}) if element_data else {},
                "enhanced_css_selector": enhanced_css,
                "enhanced_xpath": enhanced_xpath,
                "ancestor_count": len(ancestors),
                "full_match": m
            }

            scoring_data["candidates"].append(candidate_info)
            ranked.append((total_score_with_enhancement, strat, loc, m))

        # Find the best candidate from element matches
        if ranked:
            score, strat, loc, raw = max(ranked, key=lambda x: x[0])

            # Find the selected candidate in our detailed data
            selected_candidate = None
            for candidate in scoring_data["candidates"]:
                if (candidate["total_score"] == score and
                    candidate["strategy"] == strat and
                    candidate["locator"] == loc):
                    selected_candidate = candidate
                    break

            # PRIORITY OVERRIDE: Check if we have enhanced locators with ancestor IDs
            enhanced_locator_override = _check_for_enhanced_locator_override(raw, selected_candidate)
            if enhanced_locator_override:
                # Use enhanced locator with maximum confidence
                score = 150  # Higher than any normal score
                strat = enhanced_locator_override['strategy']
                loc = enhanced_locator_override['locator']

                debug(
                    f"Enhanced locator override applied: {strat} = {loc}",
                    stage="locator_resolution",
                    operation="enhanced_locator_override",
                    context={
                        "original_strategy": selected_candidate["strategy"] if selected_candidate else "unknown",
                        "original_locator": selected_candidate["locator"] if selected_candidate else "unknown",
                        "enhanced_strategy": strat,
                        "enhanced_locator": loc,
                        "ancestor_count": enhanced_locator_override.get('ancestor_count', 0)
                    }
                )

            # Compare element-based result with step data
            step_score = 0
            if step_locator_strategy in _PREFERRED_ORDER:
                step_priority_index = _PREFERRED_ORDER.index(step_locator_strategy)
                step_score = (len(_PREFERRED_ORDER) - step_priority_index) * 10 + 8  # Add 8 for step data reliability

            # Choose the better option: element analysis vs step data
            use_element_result = score > step_score

            if use_element_result:
                scoring_data["selection_result"] = {
                    "selected_strategy": strat,
                    "selected_locator": loc,
                    "selected_score": score,
                    "confidence_score": score / 100.0,
                    "selected_candidate": selected_candidate,
                    "alternatives_count": len(ranked) - 1,
                    "conflict_detected": len(ranked) > 1 or score != step_score,
                    "selection_reason": f"{strat} preferred (element analysis score {score} vs step data score {step_score})",
                    "comparison_with_step_data": {
                        "step_strategy": step_locator_strategy,
                        "step_locator": step_locator,
                        "step_score": step_score,
                        "element_score": score,
                        "element_preferred": True,
                        "score_difference": score - step_score
                    }
                }

                result.resolved_locator_strategy = strat
                result.resolved_locator = loc
                result.confidence_score = score / 100.0
                result.resolution_reason = f"{strat} preferred (element analysis score {score} vs step data score {step_score})"
                result.conflict_detected = len(ranked) > 1 or score != step_score

                debug(
                    f"Element analysis result preferred over step data",
                    stage="locator_resolution",
                    operation="element_preferred",
                    context={
                        "element_strategy": strat,
                        "element_score": score,
                        "step_strategy": step_locator_strategy,
                        "step_score": step_score,
                        "score_improvement": score - step_score
                    }
                )
            else:
                # Step data is better or equal - use step data but log the comparison
                scoring_data["selection_result"] = {
                    "selected_strategy": step_locator_strategy,
                    "selected_locator": step_locator,
                    "selected_score": step_score,
                    "confidence_score": step_score / 100.0,
                    "selected_candidate": None,
                    "alternatives_count": len(ranked),
                    "conflict_detected": False,
                    "selection_reason": f"{step_locator_strategy} preferred (step data score {step_score} vs element analysis score {score})",
                    "comparison_with_step_data": {
                        "step_strategy": step_locator_strategy,
                        "step_locator": step_locator,
                        "step_score": step_score,
                        "element_score": score,
                        "element_preferred": False,
                        "score_difference": step_score - score
                    }
                }

                # Keep the default result (step data)
                result.resolution_reason = f"{step_locator_strategy} preferred (step data score {step_score} vs element analysis score {score})"
                result.confidence_score = step_score / 100.0

                debug(
                    f"Step data preferred over element analysis result",
                    stage="locator_resolution",
                    operation="step_data_preferred",
                    context={
                        "step_strategy": step_locator_strategy,
                        "step_score": step_score,
                        "element_strategy": strat,
                        "element_score": score,
                        "score_difference": step_score - score
                    }
                )

            # Add alternatives to the selection result if element result was chosen
            if use_element_result:
                alternatives = [r for r in ranked if r != (score, strat, loc, raw)]
                if alternatives:
                    scoring_data["selection_result"]["alternatives"] = []
                    for alt_score, alt_strat, alt_loc, alt_raw in alternatives:
                        alt_info = {
                            "strategy": alt_strat,
                            "locator": alt_loc,
                            "score": alt_score,
                            "score_difference": score - alt_score,
                            "why_not_selected": f"Lower total score ({alt_score} vs {score})"
                        }
                        scoring_data["selection_result"]["alternatives"].append(alt_info)

                # Also add step data as an alternative if it was considered
                if step_score > 0:
                    step_alternative = {
                        "strategy": step_locator_strategy,
                        "locator": step_locator,
                        "score": step_score,
                        "score_difference": score - step_score,
                        "why_not_selected": f"Lower step data score ({step_score} vs {score})"
                    }
                    if "alternatives" not in scoring_data["selection_result"]:
                        scoring_data["selection_result"]["alternatives"] = []
                    scoring_data["selection_result"]["alternatives"].append(step_alternative)
        else:
            scoring_data["selection_result"] = {
                "error": "No valid candidates found",
                "fallback_used": True
            }

        # Log the scoring analysis
        _log_comprehensive_debug("PROCESSING_SCORING", test_case_id, step_no, scoring_data, resolution_timestamp)

        # 📊 COMPREHENSIVE OUTPUT LOGGING
        output_data = {
            "final_result": {
                "resolved_locator_strategy": result.resolved_locator_strategy,
                "resolved_locator": result.resolved_locator,
                "confidence_score": result.confidence_score,
                "resolution_reason": result.resolution_reason,
                "conflict_detected": result.conflict_detected
            },
            "comparison_with_input": {
                "original_step_strategy": step_data.get('locator_strategy', ''),
                "original_step_locator": step_data.get('locator', ''),
                "strategy_changed": result.resolved_locator_strategy != step_data.get('locator_strategy', '').lower(),
                "locator_changed": result.resolved_locator != step_data.get('locator', ''),
                "improvement_analysis": {}
            },
            "decision_summary": {
                "total_candidates_evaluated": len(element_matches),
                "selection_method": "priority_based_scoring",
                "priority_order_used": _PREFERRED_ORDER,
                "why_this_choice": f"Selected {result.resolved_locator_strategy} strategy with confidence {result.confidence_score:.3f}"
            }
        }

        # Analyze if this is an improvement over the original
        original_strategy = step_data.get('locator_strategy', '').lower()
        if original_strategy and original_strategy in _PREFERRED_ORDER and result.resolved_locator_strategy in _PREFERRED_ORDER:
            original_priority = _PREFERRED_ORDER.index(original_strategy)
            new_priority = _PREFERRED_ORDER.index(result.resolved_locator_strategy)

            output_data["comparison_with_input"]["improvement_analysis"] = {
                "original_priority_index": original_priority,
                "new_priority_index": new_priority,
                "priority_improved": new_priority < original_priority,  # Lower index = higher priority
                "priority_explanation": f"Changed from priority {original_priority + 1} to {new_priority + 1}"
            }

        # Add specific analysis for the original issue (CSS low confidence)
        if (result.resolved_locator_strategy == 'name' and
            original_strategy == 'css' and
            result.confidence_score > 0.31):
            output_data["original_issue_analysis"] = {
                "issue_resolved": True,
                "description": "Successfully prioritized name-based locator over low-confidence CSS",
                "confidence_improvement": result.confidence_score - 0.31,
                "strategy_improvement": "css -> name"
            }

        _log_comprehensive_debug("OUTPUT", test_case_id, step_no, output_data, resolution_timestamp)

        debug(
            f"Successfully resolved locator conflict - selected '{result.resolved_locator_strategy}' strategy",
            stage="locator_resolution",
            operation="strategy_selection",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "resolved_strategy": result.resolved_locator_strategy,
                "resolved_locator": result.resolved_locator,
                "confidence_score": result.confidence_score,
                "conflict_detected": result.conflict_detected,
                "candidates_evaluated": len(element_matches),
                "resolution_id": input_data["resolution_id"]
            }
        )
        return vars(result)

    except Exception as e:
        # Extract step data safely for error handling
        safe_step_strategy = (step_data or {}).get('locator_strategy', '') if 'step_data' in locals() else ''
        safe_step_locator = (step_data or {}).get('locator', '') if 'step_data' in locals() else ''
        safe_element_matches = element_matches if 'element_matches' in locals() else []
        safe_resolution_timestamp = resolution_timestamp if 'resolution_timestamp' in locals() else time.strftime("%Y%m%d_%H%M%S") + f"_{int(time.time() * 1000) % 1000:03d}"

        debug(
            f"Error during locator conflict resolution - using fallback strategy",
            stage="locator_resolution",
            operation="error_fallback",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "error_line": str(e.__traceback__.tb_lineno) if e.__traceback__ else "unknown",
                "fallback_strategy": "css",
                "fallback_locator": "input:not([type='hidden'])"
            }
        )

        # Log comprehensive error analysis
        error_output_data = {
            "final_result": {
                "resolved_locator_strategy": "css",
                "resolved_locator": "input:not([type='hidden'])",
                "confidence_score": 0.0,
                "resolution_reason": f"Fallback due to {type(e).__name__}: {e}",
                "conflict_detected": True
            },
            "error_analysis": {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "error_line": str(e.__traceback__.tb_lineno) if e.__traceback__ else "unknown",
                "fallback_used": True,
                "original_step_strategy": safe_step_strategy,
                "original_step_locator": safe_step_locator,
                "element_matches_count": len(safe_element_matches)
            },
            "decision_summary": {
                "total_candidates_evaluated": 0,
                "selection_method": "error_fallback",
                "priority_order_used": _PREFERRED_ORDER,
                "why_this_choice": f"Exception occurred during resolution: {type(e).__name__}"
            }
        }
        _log_comprehensive_debug("OUTPUT", test_case_id, step_no, error_output_data, safe_resolution_timestamp)
        return vars(ResolutionResult(
            resolved_locator_strategy="css",
            resolved_locator="input:not([type='hidden'])",
            resolution_reason=f"Fallback due to {type(e).__name__}: {e}",
            confidence_score=0.0,
            original_step_locator={'strategy': safe_step_strategy, 'locator': safe_step_locator},
            original_element_matches=safe_element_matches,
            conflict_detected=True
        ))


def _convert_element_to_locator_match(
    element: Dict[str, Any],
    original_match: Dict[str, Any],
    resolution_timestamp: str = None,
    test_case_id: str = "unknown",
    step_no: str = "unknown",
    element_index: int = -1
) -> Dict[str, Any]:
    """
    Convert element data to locator match format using priority logic.

    This function implements the priority order: name > css > xpath > id
    and converts element data to the expected locator_strategy/locator format.

    ENHANCED: Includes automatic clickability detection and DOM traversal to ensure
    generated locators target clickable elements instead of non-clickable children.

    Args:
        element: Element data with selector, xpath, and attributes
        original_match: Original match data (for score, manually_selected, etc.)

    Returns:
        Dict with locator_strategy and locator fields, or None if no valid locator
    """
    try:
        # Apply clickability transformation before processing locators
        element = _ensure_clickable_element_targeting(element, test_case_id, step_no, element_index)

        selector = element.get('selector', '')
        xpath = element.get('xpath', '')
        attributes = element.get('attributes', {})

        # 📊 DETAILED CONVERSION LOGGING
        conversion_analysis = {
            "element_index": element_index,
            "input_data": {
                "selector": selector,
                "xpath": xpath,
                "attributes": attributes,
                "original_score": original_match.get('score', 0),
                "manually_selected": original_match.get('manually_selected', False)
            },
            "priority_checks": [],
            "selected_strategy": None,
            "selected_locator": None,
            "selection_reason": None
        }

        # Priority 1: NAME attribute (highest priority)
        name_attr = attributes.get('name')
        conversion_analysis["priority_checks"].append({
            "priority": 1,
            "strategy": "name",
            "check": "direct_name_attribute",
            "available": bool(name_attr),
            "value": name_attr,
            "selected": bool(name_attr)
        })

        if name_attr:
            result = {
                'locator_strategy': 'name',
                'locator': name_attr,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "name"
            conversion_analysis["selected_locator"] = name_attr
            conversion_analysis["selection_reason"] = "Direct name attribute found (highest priority)"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 2: CSS selector with name attribute
        css_name_available = selector and '[name=' in selector
        css_name_value = None
        if css_name_available:
            import re
            name_match = re.search(r'\[name=["\']([^"\']+)["\']', selector)
            css_name_value = name_match.group(1) if name_match else None

        conversion_analysis["priority_checks"].append({
            "priority": 2,
            "strategy": "name",
            "check": "css_selector_name_attribute",
            "available": css_name_available,
            "value": css_name_value,
            "selector": selector,
            "selected": bool(css_name_value)
        })

        if css_name_value:
            result = {
                'locator_strategy': 'name',
                'locator': css_name_value,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "name"
            conversion_analysis["selected_locator"] = css_name_value
            conversion_analysis["selection_reason"] = f"Name extracted from CSS selector: {selector}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 3: CSS selector (general) with GRETAH class filtering
        css_available = bool(selector)
        css_is_id = selector.startswith('#') if selector else False

        # Filter out GRETAH internal classes from CSS selector
        filtered_selector = _filter_gretah_classes_from_selector(selector) if selector else selector
        css_strategy = 'id' if css_is_id else 'css'
        css_locator = filtered_selector[1:] if css_is_id else filtered_selector

        # Check if we should use enhanced CSS selector instead
        enhanced_css = element.get('enhanced_css_selector', '')
        if enhanced_css and _is_enhanced_selector_better(enhanced_css, filtered_selector):
            filtered_selector = enhanced_css
            css_locator = enhanced_css
            css_strategy = 'css'

        conversion_analysis["priority_checks"].append({
            "priority": 3,
            "strategy": css_strategy,
            "check": "css_selector_general",
            "available": css_available,
            "value": css_locator,
            "is_id_selector": css_is_id,
            "raw_selector": selector,
            "filtered_selector": filtered_selector,
            "enhanced_css_used": enhanced_css and filtered_selector == enhanced_css,
            "selected": css_available and not css_name_value  # Only if name wasn't found
        })

        if css_available and not css_name_value:  # Only if name wasn't already found
            result = {
                'locator_strategy': css_strategy,
                'locator': css_locator,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = css_strategy
            conversion_analysis["selected_locator"] = css_locator
            conversion_analysis["selection_reason"] = f"CSS selector ({'ID' if css_is_id else 'general'}): {filtered_selector}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 4: XPath with name attribute
        xpath_name_available = xpath and '@name=' in xpath
        xpath_name_value = None
        if xpath_name_available:
            import re
            name_match = re.search(r'@name=["\']([^"\']+)["\']', xpath)
            xpath_name_value = name_match.group(1) if name_match else None

        conversion_analysis["priority_checks"].append({
            "priority": 4,
            "strategy": "name",
            "check": "xpath_name_attribute",
            "available": xpath_name_available,
            "value": xpath_name_value,
            "xpath": xpath,
            "selected": bool(xpath_name_value)
        })

        if xpath_name_value:
            result = {
                'locator_strategy': 'name',
                'locator': xpath_name_value,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "name"
            conversion_analysis["selected_locator"] = xpath_name_value
            conversion_analysis["selection_reason"] = f"Name extracted from XPath: {xpath}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 5: XPath (general)
        xpath_available = bool(xpath)
        xpath_is_id = xpath and '@id=' in xpath
        xpath_id_value = None
        xpath_strategy = None
        xpath_locator = None

        if xpath_available:
            if xpath_is_id:
                import re
                id_match = re.search(r'@id=["\']([^"\']+)["\']', xpath)
                xpath_id_value = id_match.group(1) if id_match else None
                xpath_strategy = 'id'
                xpath_locator = xpath_id_value
            else:
                xpath_strategy = 'xpath'
                xpath_locator = xpath

        conversion_analysis["priority_checks"].append({
            "priority": 5,
            "strategy": xpath_strategy or "xpath",
            "check": "xpath_general",
            "available": xpath_available,
            "value": xpath_locator,
            "is_id_xpath": xpath_is_id,
            "raw_xpath": xpath,
            "selected": xpath_available and not css_name_value and not xpath_name_value
        })

        if xpath_available and not css_name_value and not xpath_name_value:
            result = {
                'locator_strategy': xpath_strategy,
                'locator': xpath_locator,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = xpath_strategy
            conversion_analysis["selected_locator"] = xpath_locator
            conversion_analysis["selection_reason"] = f"XPath ({'ID-based' if xpath_is_id else 'general'}): {xpath}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 6: ID attribute (lowest priority)
        id_attr_available = bool(attributes.get('id'))
        id_attr_value = attributes.get('id')

        conversion_analysis["priority_checks"].append({
            "priority": 6,
            "strategy": "id",
            "check": "id_attribute",
            "available": id_attr_available,
            "value": id_attr_value,
            "selected": id_attr_available and not css_name_value and not xpath_name_value and not css_available and not xpath_available
        })

        if id_attr_available and not css_name_value and not xpath_name_value and not css_available and not xpath_available:
            result = {
                'locator_strategy': 'id',
                'locator': id_attr_value,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "id"
            conversion_analysis["selected_locator"] = id_attr_value
            conversion_analysis["selection_reason"] = f"ID attribute (lowest priority): {id_attr_value}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # No valid locator found - log comprehensive analysis
        conversion_analysis["selected_strategy"] = None
        conversion_analysis["selected_locator"] = None
        conversion_analysis["selection_reason"] = "No valid locator found in any priority check"

        if resolution_timestamp:
            _log_comprehensive_debug(
                f"ELEMENT_CONVERSION_{element_index}_FAILED",
                test_case_id,
                step_no,
                conversion_analysis,
                resolution_timestamp
            )

        debug(
            f"No valid locator found for element",
            stage="locator_resolution",
            operation="element_conversion_failed",
            context={
                "element_keys": list(element.keys()),
                "has_selector": bool(selector),
                "has_xpath": bool(xpath),
                "has_attributes": bool(attributes),
                "element_index": element_index,
                "priority_checks_performed": len(conversion_analysis["priority_checks"])
            }
        )
        return None

    except Exception as e:
        debug(
            f"Error converting element to locator match",
            stage="locator_resolution",
            operation="element_conversion_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e)
            }
        )
        return None


def _ensure_clickable_element_targeting(
    element: Dict[str, Any],
    test_case_id: str = "unknown",
    step_no: str = "unknown",
    element_index: int = -1
) -> Dict[str, Any]:
    """
    Ensure element locators target clickable elements instead of non-clickable children.

    This function implements automatic clickability detection and DOM traversal to prevent
    ElementClickInterceptedException by modifying selectors to target clickable parents
    when the current element is non-clickable.

    Args:
        element: Element data with selector, xpath, attributes, and ancestors
        test_case_id: Test case ID for logging
        step_no: Step number for logging
        element_index: Element index for logging

    Returns:
        Dict: Modified element data with clickable-targeting locators
    """
    try:
        # Define clickable and non-clickable element types
        CLICKABLE_TAGS = {'a', 'button', 'input', 'select', 'textarea', 'option'}
        NON_CLICKABLE_TAGS = {'span', 'div', 'p', 'label', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'ul', 'ol'}

        attributes = element.get('attributes', {})
        # Try multiple locations for tag information
        current_tag = (element.get('tag', '') or
                      element.get('name', '') or
                      attributes.get('tag', '')).lower()
        ancestors = element.get('ancestors', [])

        # Check if current element is clickable
        is_clickable = current_tag in CLICKABLE_TAGS
        is_non_clickable = current_tag in NON_CLICKABLE_TAGS

        debug(
            f"Clickability analysis for element {element_index}",
            stage="locator_resolution",
            operation="clickability_analysis",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "element_index": element_index,
                "current_tag": current_tag,
                "is_clickable": is_clickable,
                "is_non_clickable": is_non_clickable,
                "has_ancestors": bool(ancestors)
            }
        )

        # If element is already clickable, return as-is
        if is_clickable:
            debug(
                f"Element {element_index} is already clickable - no modification needed",
                stage="locator_resolution",
                operation="clickable_element_confirmed",
                context={
                    "tag": current_tag,
                    "element_index": element_index
                }
            )
            return element

        # If element is non-clickable, attempt to use enhanced selectors or find clickable parent
        if is_non_clickable:
            # First, try to use enhanced CSS selector if it targets a clickable element
            enhanced_css = element.get('enhanced_css_selector', '')
            if enhanced_css and _enhanced_selector_targets_clickable_element(enhanced_css):
                debug(
                    f"Using enhanced CSS selector for clickable targeting on element {element_index}",
                    stage="locator_resolution",
                    operation="enhanced_css_clickable_targeting",
                    context={
                        "original_selector": element.get('selector', ''),
                        "enhanced_css": enhanced_css,
                        "element_index": element_index
                    }
                )
                # Extract the clickable part from enhanced CSS selector
                clickable_selector = _extract_clickable_selector_from_enhanced_css(enhanced_css)
                if clickable_selector:
                    modified_element = element.copy()
                    modified_element['selector'] = clickable_selector
                    modified_element['attributes'] = modified_element.get('attributes', {}).copy()
                    modified_element['attributes']['clickability_modified'] = True
                    modified_element['attributes']['original_selector'] = element.get('selector', '')

                    # Preserve viewport and interaction data
                    if 'viewport_status' in element:
                        modified_element['viewport_status'] = element['viewport_status']
                    if 'interaction_requirements' in element:
                        modified_element['interaction_requirements'] = element['interaction_requirements']

                    return modified_element

            # Fallback: attempt DOM traversal to find clickable parent
            if ancestors:
                clickable_ancestor = _find_clickable_ancestor(ancestors, element, test_case_id, step_no, element_index)

                if clickable_ancestor:
                    # Modify element to target the clickable ancestor
                    modified_element = _modify_element_for_clickable_ancestor(
                        element, clickable_ancestor, test_case_id, step_no, element_index
                    )
                    return modified_element
                else:
                    debug(
                        f"No clickable ancestor found for non-clickable element {element_index}",
                        stage="locator_resolution",
                        operation="no_clickable_ancestor",
                        context={
                            "current_tag": current_tag,
                            "ancestor_count": len(ancestors),
                            "element_index": element_index
                        }
                    )

        # For unknown clickability or no ancestors, return original element with warning
        if not is_clickable and not is_non_clickable:
            debug(
                f"Unknown clickability for element {element_index} with tag '{current_tag}' - proceeding with caution",
                stage="locator_resolution",
                operation="unknown_clickability_warning",
                context={
                    "tag": current_tag,
                    "element_index": element_index
                }
            )

        return element

    except Exception as e:
        debug(
            f"Error in clickability analysis for element {element_index}: {e}",
            stage="locator_resolution",
            operation="clickability_analysis_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "element_index": element_index
            }
        )
        # Return original element on error
        return element


def _enhanced_selector_targets_clickable_element(enhanced_css: str) -> bool:
    """
    Check if enhanced CSS selector targets a clickable element.

    Args:
        enhanced_css: Enhanced CSS selector string

    Returns:
        bool: True if selector targets clickable element, False otherwise
    """
    try:
        # Check if selector contains clickable element tags
        clickable_patterns = ['a[', 'button[', 'input[', 'select[', 'textarea[']
        enhanced_css_lower = enhanced_css.lower()

        for pattern in clickable_patterns:
            if pattern in enhanced_css_lower:
                return True

        # Check for standalone clickable tags
        clickable_tags = [' a ', ' button ', ' input ', ' select ', ' textarea ']
        for tag in clickable_tags:
            if tag in enhanced_css_lower:
                return True

        # Check if selector ends with clickable tag
        if enhanced_css_lower.endswith((' a', ' button', ' input', ' select', ' textarea')):
            return True

        return False

    except Exception:
        return False


def _extract_clickable_selector_from_enhanced_css(enhanced_css: str) -> str:
    """
    Extract the clickable part from enhanced CSS selector.

    Args:
        enhanced_css: Enhanced CSS selector string

    Returns:
        str: Clickable selector part, or empty string if not found
    """
    try:
        # Find the last clickable element in the selector chain
        clickable_patterns = [
            (r'(#[^#\s]+\s+a\[[^\]]+\])', 'a'),  # ID + anchor with attributes
            (r'(#[^#\s]+\s+button\[[^\]]+\])', 'button'),  # ID + button with attributes
            (r'(#[^#\s]+\s+input\[[^\]]+\])', 'input'),  # ID + input with attributes
            (r'(#[^#\s]+\s+a)', 'a'),  # ID + anchor
            (r'(#[^#\s]+\s+button)', 'button'),  # ID + button
            (r'(a\[[^\]]+\])', 'a'),  # Anchor with attributes
            (r'(button\[[^\]]+\])', 'button'),  # Button with attributes
        ]

        import re

        for pattern, tag in clickable_patterns:
            matches = re.findall(pattern, enhanced_css, re.IGNORECASE)
            if matches:
                # Return the last (most specific) match
                return matches[-1]

        return ""

    except Exception:
        return ""


def _filter_gretah_classes_from_selector(selector: str) -> str:
    """
    Filter out GRETAH internal CSS classes from a CSS selector.

    Args:
        selector: CSS selector string

    Returns:
        str: Filtered CSS selector with GRETAH classes removed
    """
    if not selector:
        return selector

    try:
        import re

        # Define GRETAH internal class patterns to filter out
        gretah_patterns = [
            r'\.gretah-[^.\s\[\]]+',  # .gretah-* classes
            r'\.highlight-[^.\s\[\]]+',  # .highlight-* classes
            r'\.selected-[^.\s\[\]]+',  # .selected-* classes
        ]

        filtered_selector = selector

        # Remove each GRETAH pattern
        for pattern in gretah_patterns:
            filtered_selector = re.sub(pattern, '', filtered_selector)

        # Clean up any double spaces or trailing/leading spaces
        filtered_selector = re.sub(r'\s+', ' ', filtered_selector).strip()

        # If selector becomes empty or just a tag, return original
        if not filtered_selector or filtered_selector.isspace():
            # Extract just the tag name if possible
            tag_match = re.match(r'^([a-zA-Z]+)', selector)
            if tag_match:
                return tag_match.group(1)
            return selector

        return filtered_selector

    except Exception:
        return selector


def _is_enhanced_selector_better(enhanced_css: str, current_selector: str) -> bool:
    """
    Determine if enhanced CSS selector is better than current selector.

    Args:
        enhanced_css: Enhanced CSS selector
        current_selector: Current CSS selector

    Returns:
        bool: True if enhanced selector should be used
    """
    if not enhanced_css or not current_selector:
        return bool(enhanced_css)

    try:
        # Enhanced selector is better if:
        # 1. It contains ancestor ID references (more reliable)
        # 2. Current selector contains GRETAH classes
        # 3. Enhanced selector has more specific attributes

        has_ancestor_id = '#' in enhanced_css and ' ' in enhanced_css
        current_has_gretah = any(pattern in current_selector.lower()
                               for pattern in ['gretah-', 'highlight-', 'selected-'])

        enhanced_has_attributes = '[' in enhanced_css and ']' in enhanced_css
        current_has_attributes = '[' in current_selector and ']' in current_selector

        # Prefer enhanced if it has ancestor IDs or current has GRETAH classes
        if has_ancestor_id or current_has_gretah:
            return True

        # Prefer enhanced if it has attributes and current doesn't
        if enhanced_has_attributes and not current_has_attributes:
            return True

        return False

    except Exception:
        return False


def _find_clickable_ancestor(
    ancestors: List[Dict[str, Any]],
    original_element: Dict[str, Any],
    test_case_id: str,
    step_no: str,
    element_index: int
) -> Dict[str, Any]:
    """
    Find the nearest clickable ancestor in the DOM hierarchy.

    Args:
        ancestors: List of ancestor elements from nearest to farthest
        original_element: Original element data for context
        test_case_id: Test case ID for logging
        step_no: Step number for logging
        element_index: Element index for logging

    Returns:
        Dict: Clickable ancestor data, or None if no clickable ancestor found
    """
    try:
        CLICKABLE_TAGS = {'a', 'button', 'input', 'select', 'textarea', 'option'}

        for i, ancestor in enumerate(ancestors):
            # Try multiple locations for tag information in ancestors
            ancestor_tag = (ancestor.get('tagName', '') or
                           ancestor.get('tag', '') or
                           ancestor.get('name', '')).lower()
            ancestor_id = ancestor.get('id', '')

            if ancestor_tag in CLICKABLE_TAGS:
                debug(
                    f"Found clickable ancestor for element {element_index}",
                    stage="locator_resolution",
                    operation="clickable_ancestor_found",
                    context={
                        "ancestor_index": i,
                        "ancestor_tag": ancestor_tag,
                        "ancestor_id": ancestor_id,
                        "distance_from_original": i + 1,
                        "element_index": element_index
                    }
                )
                return ancestor

        debug(
            f"No clickable ancestor found in {len(ancestors)} ancestors for element {element_index}",
            stage="locator_resolution",
            operation="no_clickable_ancestor_found",
            context={
                "ancestor_count": len(ancestors),
                "ancestor_tags": [a.get('tag', 'unknown') for a in ancestors[:5]],  # First 5 for brevity
                "element_index": element_index
            }
        )
        return None

    except Exception as e:
        debug(
            f"Error finding clickable ancestor for element {element_index}: {e}",
            stage="locator_resolution",
            operation="find_clickable_ancestor_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "element_index": element_index
            }
        )
        return None


def _modify_element_for_clickable_ancestor(
    original_element: Dict[str, Any],
    clickable_ancestor: Dict[str, Any],
    test_case_id: str,
    step_no: str,
    element_index: int
) -> Dict[str, Any]:
    """
    Modify element data to target clickable ancestor instead of non-clickable child.

    Args:
        original_element: Original element data
        clickable_ancestor: Clickable ancestor to target instead
        test_case_id: Test case ID for logging
        step_no: Step number for logging
        element_index: Element index for logging

    Returns:
        Dict: Modified element data targeting the clickable ancestor
    """
    try:
        # Create a copy of the original element
        modified_element = original_element.copy()

        # Get ancestor information
        ancestor_tag = (clickable_ancestor.get('tagName', '') or
                       clickable_ancestor.get('tag', '') or
                       clickable_ancestor.get('name', '')).lower()
        ancestor_id = clickable_ancestor.get('id', '')
        ancestor_attributes = clickable_ancestor.get('attributes', {})

        # Store original selectors for logging
        original_selector = original_element.get('selector', '')

        # Generate new selectors targeting the clickable ancestor
        new_selectors = _generate_clickable_ancestor_selectors(
            clickable_ancestor, original_element, test_case_id, step_no, element_index
        )

        # Update element data with clickable ancestor targeting
        if new_selectors.get('css_selector'):
            modified_element['selector'] = new_selectors['css_selector']

        if new_selectors.get('xpath'):
            modified_element['xpath'] = new_selectors['xpath']

        if new_selectors.get('enhanced_css'):
            modified_element['enhanced_css_selector'] = new_selectors['enhanced_css']

        if new_selectors.get('enhanced_xpath'):
            modified_element['enhanced_xpath'] = new_selectors['enhanced_xpath']

        # Update attributes to reflect the clickable ancestor
        modified_element['attributes'] = ancestor_attributes.copy()
        modified_element['attributes']['original_tag'] = original_element.get('attributes', {}).get('tag', '')
        modified_element['attributes']['clickability_modified'] = True

        # Preserve viewport and interaction data from original element
        if 'viewport_status' in original_element:
            modified_element['viewport_status'] = original_element['viewport_status']
        if 'interaction_requirements' in original_element:
            modified_element['interaction_requirements'] = original_element['interaction_requirements']

        debug(
            f"Modified element {element_index} to target clickable ancestor",
            stage="locator_resolution",
            operation="element_modified_for_clickability",
            context={
                "original_tag": original_element.get('attributes', {}).get('tag', ''),
                "new_tag": ancestor_tag,
                "original_selector": original_selector,
                "new_selector": new_selectors.get('css_selector', ''),
                "ancestor_id": ancestor_id,
                "element_index": element_index,
                "viewport_data_preserved": 'viewport_status' in original_element,
                "interaction_data_preserved": 'interaction_requirements' in original_element
            }
        )

        return modified_element

    except Exception as e:
        debug(
            f"Error modifying element {element_index} for clickable ancestor: {e}",
            stage="locator_resolution",
            operation="modify_element_for_ancestor_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "element_index": element_index
            }
        )
        # Return original element on error
        return original_element


def _generate_clickable_ancestor_selectors(
    clickable_ancestor: Dict[str, Any],
    original_element: Dict[str, Any],
    test_case_id: str,
    step_no: str,
    element_index: int
) -> Dict[str, str]:
    """
    Generate new selectors targeting the clickable ancestor.

    Args:
        clickable_ancestor: Clickable ancestor element data
        original_element: Original element data for context
        test_case_id: Test case ID for logging
        step_no: Step number for logging
        element_index: Element index for logging

    Returns:
        Dict: New selectors targeting the clickable ancestor
    """
    try:
        selectors = {}

        # Try multiple locations for tag information in clickable ancestor
        ancestor_tag = (clickable_ancestor.get('tagName', '') or
                       clickable_ancestor.get('tag', '') or
                       clickable_ancestor.get('name', '')).lower()
        ancestor_id = clickable_ancestor.get('id', '')
        ancestor_attributes = clickable_ancestor.get('attributes', {})
        ancestors = original_element.get('ancestors', [])

        # Generate CSS selector for clickable ancestor
        if ancestor_id:
            # Use ID-based selector for maximum reliability
            selectors['css_selector'] = f"#{ancestor_id}"

            # Generate enhanced CSS with ancestor context if available
            if len(ancestors) > 1:
                # Find the ancestor of the clickable ancestor for enhanced context
                clickable_ancestor_index = next(
                    (i for i, a in enumerate(ancestors) if a.get('id') == ancestor_id),
                    None
                )
                if clickable_ancestor_index is not None and clickable_ancestor_index < len(ancestors) - 1:
                    parent_ancestor = ancestors[clickable_ancestor_index + 1]
                    parent_id = parent_ancestor.get('id', '')
                    if parent_id:
                        selectors['enhanced_css'] = f"#{parent_id} #{ancestor_id}"
                    else:
                        selectors['enhanced_css'] = f"#{ancestor_id}"
                else:
                    selectors['enhanced_css'] = f"#{ancestor_id}"
            else:
                selectors['enhanced_css'] = f"#{ancestor_id}"

        elif ancestor_tag == 'a':
            # For anchor tags, try href-based selector
            href = ancestor_attributes.get('href', '')
            if href:
                selectors['css_selector'] = f"a[href=\"{href}\"]"
                selectors['enhanced_css'] = f"a[href=\"{href}\"]"
            else:
                selectors['css_selector'] = "a"
                selectors['enhanced_css'] = "a"

        elif ancestor_tag in ['button', 'input']:
            # For buttons and inputs, try type or name-based selectors
            button_type = ancestor_attributes.get('type', '')
            name = ancestor_attributes.get('name', '')

            if name:
                selectors['css_selector'] = f"{ancestor_tag}[name=\"{name}\"]"
                selectors['enhanced_css'] = f"{ancestor_tag}[name=\"{name}\"]"
            elif button_type:
                selectors['css_selector'] = f"{ancestor_tag}[type=\"{button_type}\"]"
                selectors['enhanced_css'] = f"{ancestor_tag}[type=\"{button_type}\"]"
            else:
                selectors['css_selector'] = ancestor_tag
                selectors['enhanced_css'] = ancestor_tag
        else:
            # Generic clickable element selector
            selectors['css_selector'] = ancestor_tag
            selectors['enhanced_css'] = ancestor_tag

        # Generate XPath selectors
        if ancestor_id:
            selectors['xpath'] = f"//*[@id='{ancestor_id}']"
            selectors['enhanced_xpath'] = f"//*[@id='{ancestor_id}']"
        elif ancestor_tag == 'a':
            href = ancestor_attributes.get('href', '')
            if href:
                selectors['xpath'] = f"//a[@href='{href}']"
                selectors['enhanced_xpath'] = f"//a[@href='{href}']"
            else:
                selectors['xpath'] = "//a"
                selectors['enhanced_xpath'] = "//a"
        else:
            selectors['xpath'] = f"//{ancestor_tag}"
            selectors['enhanced_xpath'] = f"//{ancestor_tag}"

        debug(
            f"Generated clickable ancestor selectors for element {element_index}",
            stage="locator_resolution",
            operation="clickable_ancestor_selectors_generated",
            context={
                "ancestor_tag": ancestor_tag,
                "ancestor_id": ancestor_id,
                "css_selector": selectors.get('css_selector', ''),
                "enhanced_css": selectors.get('enhanced_css', ''),
                "xpath": selectors.get('xpath', ''),
                "element_index": element_index
            }
        )

        return selectors

    except Exception as e:
        debug(
            f"Error generating clickable ancestor selectors for element {element_index}: {e}",
            stage="locator_resolution",
            operation="generate_ancestor_selectors_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "element_index": element_index
            }
        )
        return {}


def _calculate_locator_reliability(element: Dict[str, Any]) -> int:
    """
    Calculate reliability score for an element's locator strategies.

    Implements proper priority order: name > css > xpath > id
    Enhanced with ancestor-based locator prioritization for more reliable automation.
    Returns the highest reliability score based on available locator strategies.

    Args:
        element: Element data with selector, xpath, attributes, and ancestors

    Returns:
        int: Reliability score (higher is better)
    """
    try:
        attributes = element.get('attributes', {})
        selector = element.get('selector', '')
        xpath = element.get('xpath', '')
        enhanced_css_selector = element.get('enhanced_css_selector', '')
        enhanced_xpath = element.get('enhanced_xpath', '')
        ancestors = element.get('ancestors', [])

        # Priority 0: Enhanced locators using ancestor IDs (highest reliability when available)
        if enhanced_css_selector and enhanced_css_selector != selector and ancestors:
            debug(
                f"Element has enhanced CSS selector with ancestor ID - highest reliability",
                stage="locator_resolution",
                operation="reliability_enhanced_css_priority",
                context={
                    "enhanced_css_selector": enhanced_css_selector,
                    "ancestor_count": len(ancestors),
                    "nearest_ancestor_id": ancestors[0].get('id') if ancestors else None,
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] + 10
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] + 10  # Higher than name attribute

        if enhanced_xpath and enhanced_xpath != xpath and ancestors:
            debug(
                f"Element has enhanced XPath with ancestor ID - very high reliability",
                stage="locator_resolution",
                operation="reliability_enhanced_xpath_priority",
                context={
                    "enhanced_xpath": enhanced_xpath,
                    "ancestor_count": len(ancestors),
                    "nearest_ancestor_id": ancestors[0].get('id') if ancestors else None,
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] + 5
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] + 5  # Slightly lower than enhanced CSS

        # Priority 1: NAME attribute (highest reliability for direct attributes)
        if attributes.get('name'):
            debug(
                f"Element has name attribute - highest reliability",
                stage="locator_resolution",
                operation="reliability_name_priority",
                context={
                    "name_value": attributes['name'],
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

        # Fast path: Check for data-test* attributes (very high reliability)
        if any(k.startswith('data-test') for k in attributes.keys()):
            return 90  # Very high reliability for test attributes

        # Priority 2: Check if CSS selector contains name attribute
        if selector and '[name=' in selector:
            debug(
                f"CSS selector contains name attribute - high reliability",
                stage="locator_resolution",
                operation="reliability_css_name",
                context={
                    "selector": selector,
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

        # Priority 3: Check if XPath contains name attribute
        if xpath and '@name=' in xpath:
            debug(
                f"XPath contains name attribute - high reliability",
                stage="locator_resolution",
                operation="reliability_xpath_name",
                context={
                    "xpath": xpath,
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] - 5
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] - 5

        # Priority 4: ID attribute
        if attributes.get('id'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID]

        # Evaluate enhanced locators first (higher priority)
        max_score = 0
        if enhanced_css_selector and enhanced_css_selector != selector:
            enhanced_css_score = _evaluate_enhanced_css_selector_reliability(enhanced_css_selector, ancestors)
            max_score = max(max_score, enhanced_css_score)

        if enhanced_xpath and enhanced_xpath != xpath:
            enhanced_xpath_score = _evaluate_enhanced_xpath_reliability(enhanced_xpath, ancestors)
            max_score = max(max_score, enhanced_xpath_score)

        # Evaluate standard CSS selector types
        if selector:
            css_score = _evaluate_css_selector_reliability(selector)
            max_score = max(max_score, css_score)

        # Evaluate standard XPath reliability
        if xpath:
            xpath_score = _evaluate_xpath_reliability(xpath)
            max_score = max(max_score, xpath_score)

        return max_score

    except Exception as e:
        debug(
            f"Error calculating locator reliability - returning default score",
            stage="locator_resolution",
            operation="reliability_calculation_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": 0
            }
        )
        return 0


def _evaluate_css_selector_reliability(selector: str) -> int:
    """
    Evaluate the reliability of a CSS selector.

    Prioritizes name-based selectors according to the priority order.

    Args:
        selector: CSS selector string

    Returns:
        int: Reliability score
    """
    try:
        if not selector:
            return 0

        # Highest priority: Name-based attribute selectors
        if '[name=' in selector:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

        # Fast path: data-test* attributes are highly reliable
        if '[data-testid=' in selector or '[data-test=' in selector:
            return 90

        # ID selectors - lower priority than name
        if selector.startswith('#'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID]

        # ID-based attribute selectors
        if '[id=' in selector:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID] - 5

        # Class selectors are moderately reliable
        if '.' in selector and not selector.startswith('.'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] - 10

        if selector.startswith('.'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CLASS]

        # Tag selectors are less reliable
        if selector.isalpha():
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.TAG]

        # Complex selectors get moderate score
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] - 20

    except Exception as e:
        debug(
            f"Error evaluating CSS selector reliability - returning default score",
            stage="locator_resolution",
            operation="css_reliability_evaluation_error",
            context={
                "selector": selector,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] // 2
            }
        )
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] // 2


def _evaluate_xpath_reliability(xpath: str) -> int:
    """
    Evaluate the reliability of an XPath selector.

    Prioritizes name-based XPath according to the priority order.

    Args:
        xpath: XPath selector string

    Returns:
        int: Reliability score
    """
    try:
        if not xpath:
            return 0

        # Highest priority: Name-based XPath
        if '@name=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] - 5  # Slightly lower than direct name attribute

        # Fast path: data-test* attributes in XPath
        if '@data-testid=' in xpath or '@data-test=' in xpath:
            return 85

        # ID-based XPath - lower priority than name
        if '@id=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID] - 10

        # Text-based XPath is moderately reliable
        if 'text()=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.TEXT]

        # Position-based XPath is less reliable
        if '[' in xpath and ']' in xpath and any(char.isdigit() for char in xpath):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] - 20

        # Generic XPath
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH]

    except Exception as e:
        debug(
            f"Error evaluating XPath reliability - returning default score",
            stage="locator_resolution",
            operation="xpath_reliability_evaluation_error",
            context={
                "xpath": xpath,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] // 2
            }
        )
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] // 2


def _evaluate_enhanced_css_selector_reliability(enhanced_selector: str, ancestors: List[Dict[str, Any]]) -> int:
    """
    Evaluate the reliability of an enhanced CSS selector that leverages ancestor IDs.

    Enhanced selectors that use ancestor IDs are more reliable than standard selectors
    because IDs are typically unique and stable identifiers.

    Args:
        enhanced_selector: Enhanced CSS selector string that may include ancestor IDs
        ancestors: List of ancestor elements with ID attributes

    Returns:
        int: Reliability score (higher is better)
    """
    try:
        if not enhanced_selector or not ancestors:
            return 0

        # Check if the enhanced selector uses ancestor IDs
        ancestor_id_used = False
        for ancestor in ancestors:
            ancestor_id = ancestor.get('id', '')
            if ancestor_id and f"#{ancestor_id}" in enhanced_selector:
                ancestor_id_used = True
                break

        if ancestor_id_used:
            # Enhanced selectors with ancestor IDs get very high reliability
            base_score = LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] + 5

            # Additional bonus for using nearest ancestor (more specific)
            if ancestors and f"#{ancestors[0].get('id', '')}" in enhanced_selector:
                base_score += 5

            debug(
                f"Enhanced CSS selector uses ancestor ID - very high reliability",
                stage="locator_resolution",
                operation="enhanced_css_reliability_evaluation",
                context={
                    "enhanced_selector": enhanced_selector,
                    "ancestor_count": len(ancestors),
                    "reliability_score": base_score
                }
            )
            return base_score

        # If no ancestor ID is used, fall back to standard CSS evaluation
        return _evaluate_css_selector_reliability(enhanced_selector)

    except Exception as e:
        debug(
            f"Error evaluating enhanced CSS selector reliability - returning default score",
            stage="locator_resolution",
            operation="enhanced_css_reliability_evaluation_error",
            context={
                "enhanced_selector": enhanced_selector,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS]
            }
        )
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS]


def _evaluate_enhanced_xpath_reliability(enhanced_xpath: str, ancestors: List[Dict[str, Any]]) -> int:
    """
    Evaluate the reliability of an enhanced XPath that leverages ancestor IDs.

    Enhanced XPaths that use ancestor IDs are more reliable than standard XPaths
    because IDs are typically unique and stable identifiers.

    Args:
        enhanced_xpath: Enhanced XPath string that may include ancestor IDs
        ancestors: List of ancestor elements with ID attributes

    Returns:
        int: Reliability score (higher is better)
    """
    try:
        if not enhanced_xpath or not ancestors:
            return 0

        # Check if the enhanced XPath uses ancestor IDs
        ancestor_id_used = False
        for ancestor in ancestors:
            ancestor_id = ancestor.get('id', '')
            if ancestor_id and f'@id="{ancestor_id}"' in enhanced_xpath:
                ancestor_id_used = True
                break

        if ancestor_id_used:
            # Enhanced XPaths with ancestor IDs get high reliability
            base_score = LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

            # Additional bonus for using nearest ancestor (more specific)
            if ancestors and f'@id="{ancestors[0].get("id", "")}"' in enhanced_xpath:
                base_score += 3

            debug(
                f"Enhanced XPath uses ancestor ID - high reliability",
                stage="locator_resolution",
                operation="enhanced_xpath_reliability_evaluation",
                context={
                    "enhanced_xpath": enhanced_xpath,
                    "ancestor_count": len(ancestors),
                    "reliability_score": base_score
                }
            )
            return base_score

        # If no ancestor ID is used, fall back to standard XPath evaluation
        return _evaluate_xpath_reliability(enhanced_xpath)

    except Exception as e:
        debug(
            f"Error evaluating enhanced XPath reliability - returning default score",
            stage="locator_resolution",
            operation="enhanced_xpath_reliability_evaluation_error",
            context={
                "enhanced_xpath": enhanced_xpath,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH]
            }
        )
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH]


def _check_for_enhanced_locator_override(element_match: Dict[str, Any], selected_candidate: Dict[str, Any]) -> Dict[str, Any]:
    """
    Check if enhanced locators with ancestor IDs should override the selected locator.

    This function implements the priority override logic to ensure enhanced locators
    with ancestor IDs are always preferred over basic selectors for more reliable automation.

    Args:
        element_match: The raw element match data
        selected_candidate: The currently selected candidate information

    Returns:
        Dict containing enhanced locator override information, or None if no override needed
    """
    try:
        if not element_match or not element_match.get('element'):
            return None

        element_data = element_match['element']
        ancestors = element_data.get('ancestors', [])

        # Only override if we have ancestors with IDs
        if not ancestors:
            return None

        enhanced_css = element_data.get('enhanced_css_selector', '')
        enhanced_xpath = element_data.get('enhanced_xpath', '')
        standard_css = element_data.get('selector', '')
        standard_xpath = element_data.get('xpath', '')

        # Check if enhanced CSS is different from standard and uses ancestor ID
        if enhanced_css and enhanced_css != standard_css:
            # Verify it actually uses an ancestor ID
            for ancestor in ancestors:
                ancestor_id = ancestor.get('id', '')
                if ancestor_id and f"#{ancestor_id}" in enhanced_css:
                    return {
                        'strategy': 'css',
                        'locator': enhanced_css,
                        'reason': f'Enhanced CSS selector using ancestor ID: {ancestor_id}',
                        'ancestor_count': len(ancestors),
                        'confidence_boost': 50  # Significant boost for ancestor-based locators
                    }

        # Check if enhanced XPath is different from standard and uses ancestor ID
        if enhanced_xpath and enhanced_xpath != standard_xpath:
            # Verify it actually uses an ancestor ID
            for ancestor in ancestors:
                ancestor_id = ancestor.get('id', '')
                if ancestor_id and f'@id="{ancestor_id}"' in enhanced_xpath:
                    return {
                        'strategy': 'xpath',
                        'locator': enhanced_xpath,
                        'reason': f'Enhanced XPath selector using ancestor ID: {ancestor_id}',
                        'ancestor_count': len(ancestors),
                        'confidence_boost': 45  # High boost for ancestor-based XPath
                    }

        return None

    except Exception as e:
        debug(
            f"Error checking enhanced locator override: {e}",
            stage="locator_resolution",
            operation="enhanced_locator_override_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e)
            }
        )
        return None