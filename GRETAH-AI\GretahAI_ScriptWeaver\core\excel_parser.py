"""
Excel parser module for GretahAI ScriptWeaver.

This module provides functions for parsing Excel files containing test cases.
Implements the actual Excel parsing functionality using pandas.
"""

import os
import logging
import pandas as pd
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.excel_parser")

def parse_excel(file_path: str) -> List[Dict[str, Any]]:
    """
    Parse an Excel file containing test cases.

    Args:
        file_path (str): Path to the Excel file

    Returns:
        list: List of test cases
    """
    print(f"Parsing Excel file: {file_path}")

    try:
        # Read the Excel file
        df = pd.read_excel(file_path, dtype=str)  # Read all columns as strings to avoid NaN issues

        # Clean up the DataFrame - replace NaN with empty strings and strip whitespace
        df = df.fillna('')
        for col in df.columns:
            if df[col].dtype == 'object':
                df[col] = df[col].str.strip()

        # Check if the DataFrame is empty
        if df.empty:
            print("Excel file is empty")
            return []

        # Get the column names
        columns = df.columns.tolist()

        # Check if the required columns are present
        required_columns = ['Test Case ID', 'Test Case Objective', 'Step No', 'Test Steps', 'Expected Result']
        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            print(f"Required columns missing: {', '.join(missing_columns)}")
            return []

        # Initialize variables
        test_cases = []
        test_cases_map = {}
        current_tc_id = None

        # Process each row
        for _, row in df.iterrows():
            tc_id = row['Test Case ID'].strip()
            step_no = row['Step No'].strip()
            test_steps = row['Test Steps'].strip()
            expected_result = row['Expected Result'].strip()

            # Skip completely empty rows
            if not any([tc_id, step_no, test_steps, expected_result]):
                continue

            # If we have a test case ID, this is a new test case
            if tc_id:
                current_tc_id = tc_id
                if tc_id not in test_cases_map:
                    # Create new test case
                    test_cases_map[tc_id] = {
                        'Test Case ID': tc_id,
                        'Test Case Objective': row['Test Case Objective'].strip(),
                        'Test Type': row.get('Test Type', '').strip(),
                        'Feature': row.get('Feature', '').strip(),
                        'Prerequisites': row.get('Prerequisite', '').strip(),
                        'Steps': []
                    }
                    test_cases.append(test_cases_map[tc_id])

            # If we have step information (with or without TC ID)
            if current_tc_id and (step_no or test_steps or expected_result):
                step = {
                    'Step No': step_no or str(len(test_cases_map[current_tc_id]['Steps']) + 1),
                    'Test Steps': test_steps,
                    'Expected Result': expected_result
                }
                test_cases_map[current_tc_id]['Steps'].append(step)

        print(f"Parsed {len(test_cases)} test cases from Excel")
        return test_cases

    except Exception as e:
        print(f"Error parsing Excel file: {e}")
        return []


def parse_excel_legacy(file_path: str) -> List[Dict[str, Any]]:
    """
    Legacy Excel parser that treats each row as a separate test case.

    This function maintains backward compatibility with the old parsing behavior
    where each row in the Excel file was treated as a separate test case.

    Args:
        file_path (str): Path to the Excel file

    Returns:
        list: List of rows as dictionaries (legacy format)
    """
    logger.info(f"Parsing Excel file using legacy format: {file_path}")

    try:
        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"Excel file not found: {file_path}")
            return []

        # Read the Excel file using pandas
        df = pd.read_excel(file_path)

        # Convert DataFrame to list of dictionaries (old behavior)
        test_cases = df.to_dict('records')

        # Clean up the data - remove NaN values and convert to strings
        cleaned_test_cases = []
        for test_case in test_cases:
            cleaned_case = {}
            for key, value in test_case.items():
                # Convert NaN to empty string, everything else to string
                if pd.isna(value):
                    cleaned_case[key] = ""
                else:
                    cleaned_case[key] = str(value)
            cleaned_test_cases.append(cleaned_case)

        logger.info(f"Successfully parsed {len(cleaned_test_cases)} rows using legacy format from {file_path}")
        return cleaned_test_cases

    except Exception as e:
        logger.error(f"Error parsing Excel file {file_path}: {e}")
        return []


def get_test_case_count(test_cases: List[Dict[str, Any]]) -> int:
    """
    Get the actual count of test cases from parsed data.

    This function correctly counts test cases regardless of the parsing format used.

    Args:
        test_cases: List of test cases from parse_excel()

    Returns:
        int: Number of actual test cases
    """
    if not test_cases or not isinstance(test_cases, list):
        return 0

    # Check if this is the new format (test cases with Steps)
    if test_cases and isinstance(test_cases[0], dict) and 'Steps' in test_cases[0]:
        # New format: each item is a test case
        return len(test_cases)
    else:
        # Legacy format: count unique Test Case IDs
        unique_ids = set()
        for item in test_cases:
            if isinstance(item, dict):
                tc_id = item.get('Test Case ID', '').strip()
                if tc_id:
                    unique_ids.add(tc_id)
        return len(unique_ids)
