"""
GretahAI_CaseForge - Unified Interface Configuration

Configuration settings, defaults, and constants for the unified test case interface.
This module centralizes all configuration to ensure consistency and easy maintenance.

Author: GretahAI Development Team
Date: 2025-01-14
Version: 1.0.0
"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List

# Default filter configuration - Enhanced based on database analysis
# Removed feature, project, test_group as they're redundant with JIRA User Story ID
DEFAULT_FILTERS = {
    # Core filters (unified interface)
    'test_type': 'all',
    'priority': 'all',
    'user_name': 'all',
    'test_status': 'all',

    # JIRA and status filters
    'jira_id': 'All',
    'ai_generated': 'All',
    'enhanced_status': 'All',
    'is_edited': None,

    # Date filters (moved to advanced)
    'date_range_days': 30,
    'date_start': None,
    'date_end': None,

    # Legacy/specialized filters
    'current_run_id': None,  # For precise generation run filtering
    'timestamp_start': None,  # For tight timestamp filtering (start)
    'timestamp_end': None,    # For tight timestamp filtering (end)
    'generation_session_id': None,  # Unique identifier for generation session
    'test_case_id_range_start': None,  # Start of test case ID range for current session
    'test_case_id_range_end': None     # End of test case ID range for current session
}

# UI Configuration
UI_CONFIG = {
    'page_title': 'GretahAI CaseForge - Test Cases',
    'page_icon': '🧪',
    'layout': 'wide',
    'table_height': 500,
    'filter_expander_expanded': False,
    'show_count_indicator': True,
    'enable_data_editor': True,
    'enable_export': True
}

# Column configuration for data editor (enhanced for better editability)
EDITABLE_COLUMNS = [
    "Project",           # Now editable for new test cases
    "Feature",
    "Test Case Objective",
    "Prerequisite",
    "Test Steps",
    "Expected Result",
    "Actual Result",     # Now editable for test execution
    "Test Status",
    "Priority",
    "Defect ID",         # Now editable for defect tracking
    "Comments",          # Now editable for additional notes
    "Test Type"          # Now editable for test classification
    # "Test Group" - Removed as requested
]

# Read-only fields that should never be editable (system-generated or protected)
READ_ONLY_COLUMNS = [
    "Timestamp",         # System-generated
    "User Story ID",     # Protected field (from user preferences)
    "Test Case ID",      # System-generated, should not be manually changed
    "Step No"            # System-generated sequence number
]

# Column display order for data table (match original two-tab system exactly)
COLUMN_ORDER = [
    "Timestamp",
    "Project",
    "Feature",
    "User Story ID",
    "Test Case ID",
    "Test Case Objective",
    "Prerequisite",
    "Step No",
    "Test Steps",
    "Expected Result",
    "Actual Result",
    "Test Status",
    "Priority",
    "Defect ID",
    "Comments",
    "Test Type"
    # "Test Group" - Removed as requested
]

# Export configuration
EXPORT_CONFIG = {
    'formats': ['Excel', 'CSV'],
    'default_format': 'Excel',
    'filename_template': 'TestCases_Filtered_{timestamp}',
    'include_timestamp': True,
    'mime_types': {
        'Excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'CSV': 'text/csv'
    },
    'file_extensions': {
        'Excel': 'xlsx',
        'CSV': 'csv'
    }
}

# Filter options configuration
FILTER_OPTIONS = {
    'test_types': {
        'all': 'All Types',
        'positive': 'Positive',
        'negative': 'Negative', 
        'security': 'Security',
        'performance': 'Performance',
        'integration': 'Integration',
        'regression': 'Regression'
    },
    'boolean_options': ['All', 'Yes', 'No'],
    'date_range_presets': {
        'Last 7 days': 7,
        'Last 30 days': 30,
        'Last 90 days': 90,
        'Last 6 months': 180,
        'Last year': 365
    }
}

# Session state keys
SESSION_KEYS = {
    'filters': {
        'test_type': 'unified_filter_test_type',
        'date_start': 'unified_filter_date_start',
        'date_end': 'unified_filter_date_end',
        'ai_generated': 'unified_filter_ai_generated',
        'enhanced_status': 'unified_filter_enhanced_status',
        'jira_id': 'unified_filter_jira_id',
        'user_name': 'unified_filter_user_name',
        'is_edited': 'unified_filter_is_edited',
        'current_run_id': 'unified_filter_current_run_id',
        'timestamp_start': 'unified_filter_timestamp_start',
        'timestamp_end': 'unified_filter_timestamp_end',
        'generation_session_id': 'unified_filter_generation_session_id',
        'test_case_id_range_start': 'unified_filter_test_case_id_range_start',
        'test_case_id_range_end': 'unified_filter_test_case_id_range_end'
    },
    'ui_filters': {
        'test_type': 'unified_ui_filter_test_type',
        'date_start': 'unified_ui_filter_date_start',
        'date_end': 'unified_ui_filter_date_end',
        'ai_generated': 'unified_ui_filter_ai_generated',
        'enhanced_status': 'unified_ui_filter_enhanced_status',
        'jira_id': 'unified_ui_filter_jira_id',
        'user_name': 'unified_ui_filter_user_name',
        'is_edited': 'unified_ui_filter_is_edited',
        'test_case_id_range_start': 'unified_ui_filter_test_case_id_range_start',
        'test_case_id_range_end': 'unified_ui_filter_test_case_id_range_end'
    },
    'export': {
        'ready': 'unified_export_ready',
        'filename': 'unified_export_filename',
        'format': 'unified_export_format',
        'data': 'unified_export_data'
    },
    'cache': {
        'filter_options': 'unified_filter_options',
        'last_refresh': 'unified_last_refresh'
    },
    'generation_tracking': {
        'last_known_test_case_id': 'last_known_test_case_id',
        'current_highest_test_case_id': 'current_highest_test_case_id',
        'generation_start_id': 'generation_start_id',
        'generation_end_id': 'generation_end_id'
    },
    'smart_defaults': {
        'test_type': 'unified_smart_default_test_type',
        'date_start': 'unified_smart_default_date_start',
        'date_end': 'unified_smart_default_date_end',
        'ai_generated': 'unified_smart_default_ai_generated',
        'enhanced_status': 'unified_smart_default_enhanced_status',
        'jira_id': 'unified_smart_default_jira_id',
        'test_case_id_range_start': 'unified_smart_default_test_case_id_range_start',
        'test_case_id_range_end': 'unified_smart_default_test_case_id_range_end'
    }
}

# Help text for UI components
HELP_TEXT = {
    'test_type': 'Filter by test case type (positive, negative, security, etc.)',
    'date_start': 'Filter test cases from this date onwards',
    'date_end': 'Filter test cases up to this date',
    'ai_generated': 'Filter by whether test cases were generated by AI',
    'enhanced_status': 'Filter by whether JIRA story was enhanced with AI',
    'jira_id': 'Filter by specific JIRA User Story ID',
    'user_name': 'Filter by the user who created the test cases',
    'is_edited': 'Filter by whether test cases have been manually edited',
    'refresh': 'Refresh data and filter options',
    'reset': 'Reset all filters to default values',
    'export_format': 'Choose the format for exporting filtered test cases',
    'prepare_export': 'Prepare filtered test cases for download',
    'download': 'Download filtered test cases in selected format'
}

# Error messages
ERROR_MESSAGES = {
    'database_connection': 'Unable to connect to database. Please check the database path and permissions.',
    'no_data': 'No test cases found matching the current filter criteria.',
    'export_failed': 'Failed to prepare export file. Please try again.',
    'invalid_date_range': 'End date must be after start date.',
    'filter_load_failed': 'Unable to load filter options from database.',
    'data_load_failed': 'Error loading test case data from database.'
}

# Success messages
SUCCESS_MESSAGES = {
    'export_prepared': 'Export file prepared successfully',
    'filters_reset': 'All filters have been reset to default values',
    'data_refreshed': 'Data and filter options refreshed successfully'
}

# CSS class names for styling
CSS_CLASSES = {
    'unified_header': 'unified-header',
    'filter_container': 'filter-container',
    'filter_section_header': 'filter-section-header',
    'count_indicator': 'count-indicator',
    'filter_group': 'filter-group',
    'export_section': 'export-section',
    'export_header': 'export-header',
    'data_table_container': 'data-table-container',
    'table_header': 'table-header'
}

def get_default_date_range() -> tuple:
    """
    Get default date range based on configuration.
    
    Returns:
        tuple: (start_date, end_date) as date objects
    """
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=DEFAULT_FILTERS['date_range_days'])
    return start_date, end_date

def get_session_key(category: str, key: str) -> str:
    """
    Get session state key for a specific filter or setting.

    Args:
        category (str): Category of the key ('filters', 'ui_filters', 'smart_defaults', 'export', 'cache')
        key (str): Specific key name

    Returns:
        str: Full session state key
    """
    return SESSION_KEYS.get(category, {}).get(key, f'unified_{category}_{key}')

def get_ui_filter_key(key: str) -> str:
    """
    Get session state key for UI filter controls.

    Args:
        key (str): Filter key name

    Returns:
        str: UI filter session state key
    """
    return get_session_key('ui_filters', key)

def get_applied_filter_key(key: str) -> str:
    """
    Get session state key for applied filters (used for database queries).

    Args:
        key (str): Filter key name

    Returns:
        str: Applied filter session state key
    """
    return get_session_key('filters', key)

def get_smart_default_key(key: str) -> str:
    """
    Get session state key for smart default values.

    Args:
        key (str): Filter key name

    Returns:
        str: Smart default session state key
    """
    return get_session_key('smart_defaults', key)

def get_filter_default(filter_name: str) -> Any:
    """
    Get default value for a specific filter.
    
    Args:
        filter_name (str): Name of the filter
        
    Returns:
        Any: Default value for the filter
    """
    return DEFAULT_FILTERS.get(filter_name)

def get_export_filename(format_type: str, include_timestamp: bool = True) -> str:
    """
    Generate export filename based on configuration.
    
    Args:
        format_type (str): Export format ('Excel' or 'CSV')
        include_timestamp (bool): Whether to include timestamp in filename
        
    Returns:
        str: Generated filename
    """
    template = EXPORT_CONFIG['filename_template']
    extension = EXPORT_CONFIG['file_extensions'][format_type]
    
    if include_timestamp:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{template.format(timestamp=timestamp)}.{extension}"
    else:
        filename = f"{template.replace('_{timestamp}', '')}.{extension}"
    
    return filename

def get_mime_type(format_type: str) -> str:
    """
    Get MIME type for export format.
    
    Args:
        format_type (str): Export format ('Excel' or 'CSV')
        
    Returns:
        str: MIME type string
    """
    return EXPORT_CONFIG['mime_types'].get(format_type, 'application/octet-stream')

def validate_date_range(start_date, end_date) -> bool:
    """
    Validate that date range is logical.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        bool: True if valid, False otherwise
    """
    if start_date and end_date:
        return start_date <= end_date
    return True

def get_filter_help_text(filter_name: str) -> str:
    """
    Get help text for a specific filter.
    
    Args:
        filter_name (str): Name of the filter
        
    Returns:
        str: Help text for the filter
    """
    return HELP_TEXT.get(filter_name, f'Filter by {filter_name}')

def get_error_message(error_type: str) -> str:
    """
    Get error message for a specific error type.
    
    Args:
        error_type (str): Type of error
        
    Returns:
        str: Error message
    """
    return ERROR_MESSAGES.get(error_type, 'An unexpected error occurred.')

def get_success_message(success_type: str) -> str:
    """
    Get success message for a specific success type.
    
    Args:
        success_type (str): Type of success
        
    Returns:
        str: Success message
    """
    return SUCCESS_MESSAGES.get(success_type, 'Operation completed successfully.')

# Configuration validation
def validate_config():
    """
    Validate configuration settings for consistency and completeness.
    
    Returns:
        List[str]: List of validation errors, empty if all valid
    """
    errors = []
    
    # Validate default filters
    if not isinstance(DEFAULT_FILTERS, dict):
        errors.append("DEFAULT_FILTERS must be a dictionary")
    
    # Validate editable columns
    if not isinstance(EDITABLE_COLUMNS, list):
        errors.append("EDITABLE_COLUMNS must be a list")
    
    # Validate export configuration
    if not all(key in EXPORT_CONFIG for key in ['formats', 'mime_types', 'file_extensions']):
        errors.append("EXPORT_CONFIG missing required keys")
    
    # Validate session keys structure
    required_session_categories = ['filters', 'export', 'cache']
    if not all(cat in SESSION_KEYS for cat in required_session_categories):
        errors.append("SESSION_KEYS missing required categories")
    
    return errors

# Initialize configuration validation on import
_config_errors = validate_config()
if _config_errors:
    print(f"Configuration validation errors: {_config_errors}")
