#!/usr/bin/env python3
"""
Monitor URL Tracking Changes

This script monitors the step_data_TC_001.json file for URL tracking updates.
"""

import json
import time
from pathlib import Path

def check_url_tracking():
    """Check the current URL tracking status in the JSON file."""
    json_file = Path("step_data_storage/step_data_TC_001.json")
    
    if not json_file.exists():
        print("❌ JSON file not found")
        return
    
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        step_data = data.get('step_data', [])
        
        print(f"📊 URL Tracking Status at {time.strftime('%H:%M:%S')}")
        print("=" * 50)
        
        for step in step_data:
            step_no = step.get('step_no', 'unknown')
            current_url = step.get('current_url')
            url_timestamp = step.get('url_capture_timestamp')
            url_history = step.get('url_history', [])
            step_execution_urls = step.get('step_execution_urls', {})
            
            print(f"Step {step_no}:")
            print(f"  📍 Current URL: {current_url or 'null'}")
            print(f"  ⏰ Timestamp: {url_timestamp or 'null'}")
            print(f"  📚 History entries: {len(url_history)}")
            print(f"  🔗 End URL: {step_execution_urls.get('end_url', 'null')}")
            print()
            
    except Exception as e:
        print(f"❌ Error reading JSON file: {e}")

if __name__ == "__main__":
    check_url_tracking()
