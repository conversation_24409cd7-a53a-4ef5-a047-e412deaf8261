"""
Test script to verify that the AI modification buttons are working correctly.

This script can be run to check if the session state keys are being managed properly
for the AI modification workflow.
"""
import streamlit as st
import pandas as pd
from datetime import datetime

def test_ai_modification_workflow():
    """Test the AI modification workflow session state management."""
    
    st.title("🔧 AI Modification Button Fix Verification")
    
    # Create sample test data
    sample_data = pd.DataFrame({
        'Test Case ID': ['TC001', 'TC002', 'TC003'],
        'Test Case Name': ['Test Login', 'Test Registration', 'Test Logout'],
        'Test Steps': ['Enter credentials', 'Enter user details', 'Click logout'],
        'Expected Result': ['Login successful', 'User registered', 'User logged out']
    })
    
    tab_key = "test_verification"
    
    st.subheader("📊 Sample Test Data")
    st.dataframe(sample_data)
    
    # Initialize session state
    editor_data_key = f"{tab_key}_editor_data"
    ai_modified_key = f"ai_modified_df_{tab_key}"
    table_updated_key = f"{tab_key}_table_updated_with_ai"
    original_df_key = f"original_{tab_key}_df"
    
    if editor_data_key not in st.session_state:
        st.session_state[editor_data_key] = sample_data.copy()
    if original_df_key not in st.session_state:
        st.session_state[original_df_key] = sample_data.copy()
    
    # Show current session state
    st.subheader("🔍 Session State Debug")
    
    col1, col2 = st.columns(2)
    with col1:
        st.write("**Session State Keys:**")
        relevant_keys = [k for k in st.session_state.keys() if tab_key in k or 'ai_modified' in k]
        for key in relevant_keys:
            st.write(f"- {key}: {type(st.session_state[key])}")
    
    with col2:
        st.write("**AI Update Counter:**")
        counter_key = f"{tab_key}_ai_update_counter"
        current_counter = st.session_state.get(counter_key, 0)
        st.write(f"Current counter: {current_counter}")
    
    # Simulate AI modification
    st.subheader("🤖 Simulate AI Modification")
    
    if st.button("Simulate AI Modification", key="simulate_ai"):
        # Create modified data
        modified_data = sample_data.copy()
        modified_data['Test Steps'] = modified_data['Test Steps'] + " (AI Enhanced)"
        modified_data['Expected Result'] = modified_data['Expected Result'] + " (AI Verified)"
        
        # Store in session state
        st.session_state[ai_modified_key] = modified_data.copy()
        st.success("✅ AI modification simulated!")
        st.info("📋 Now try the 'Update Table with AI Output' button")
    
    # Test the update button logic
    st.subheader("🔄 Test Update Button")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔄 Update Table with AI Output", key=f"update_table_with_ai_output_{tab_key}"):
            if ai_modified_key in st.session_state and st.session_state[ai_modified_key] is not None:
                # Update the data
                st.session_state[editor_data_key] = st.session_state[ai_modified_key].copy()
                st.session_state[table_updated_key] = True
                
                # Increment the counter (this is the key fix)
                counter_key = f"{tab_key}_ai_update_counter"
                st.session_state[counter_key] = st.session_state.get(counter_key, 0) + 1
                
                st.success("✅ Table updated with latest AI modifications.")
                st.rerun()
            else:
                st.warning("No new AI modifications to apply.")

    with col2:
        if st.button("↩️ Undo All Modifications", key=f"undo_all_modifications_{tab_key}"):
            if original_df_key in st.session_state:
                st.session_state[editor_data_key] = st.session_state[original_df_key].copy()
                st.session_state[table_updated_key] = False
                if ai_modified_key in st.session_state:
                    del st.session_state[ai_modified_key]
                
                # Increment the counter (this is the key fix)
                counter_key = f"{tab_key}_ai_update_counter"
                st.session_state[counter_key] = st.session_state.get(counter_key, 0) + 1
                
                st.success("All modifications have been undone. Original data restored.")
                st.rerun()
    
    # Display current data with dynamic key
    st.subheader("📋 Current Data Editor")
    
    # Determine which data to display
    if st.session_state.get(table_updated_key, False) and editor_data_key in st.session_state:
        display_df = st.session_state[editor_data_key]
    else:
        display_df = sample_data
        st.session_state[editor_data_key] = sample_data.copy()

    # Generate dynamic key (this is the key fix)
    ai_update_counter = st.session_state.get(f"{tab_key}_ai_update_counter", 0)
    editor_key = f"test_data_editor_{ai_update_counter}"
    
    st.write(f"**Data Editor Key:** `{editor_key}`")
    
    # Display the data editor
    edited_df = st.data_editor(
        display_df,
        use_container_width=True,
        num_rows="dynamic",
        key=editor_key
    )
    
    # Update session state with current editor data
    st.session_state[editor_data_key] = edited_df.copy()
    
    # Show what changed
    if not edited_df.equals(sample_data):
        st.info("🔄 Data has been modified!")
        with st.expander("Show changes"):
            st.write("**Original Data:**")
            st.dataframe(sample_data)
            st.write("**Current Data:**")
            st.dataframe(edited_df)

if __name__ == "__main__":
    test_ai_modification_workflow()
