"""
Data Processing Module for GretahAI CaseForge

This module handles JSON parsing and DataFrame operations for AI-generated test scenarios.

Functions:
- parse_test_scenarios_json: Parse AI-generated JSON into structured DataFrames

© 2025 GretahAI Team
"""

import pandas as pd
import json
import os
import re
import ast
from datetime import datetime


def fix_missing_commas_in_json_array(text):
    """
    Fixes missing commas between objects in a JSON array.
    Specifically targets the Gemini 1.5 Flash output issue.
    """
    # Only operate if this looks like a JSON array of objects
    # Insert a comma between } and { if not already present, but only inside arrays
    # Handles cases like: ...} { "scenario_name": ...
    # Improved regex: only add comma if } is followed by whitespace and then {
    # but not already followed by a comma
    fixed = re.sub(r'(\})(\s*\n\s*)(\{)', r'\1,\2\3', text)
    fixed = re.sub(r'(\})(\s+)(\{)', r'\1,\2\3', fixed)
    # Also handle cases where }{ is on the same line
    fixed = re.sub(r'\}(\s*)\{', r'},\1{', fixed)
    # Remove trailing commas before closing array
    fixed = re.sub(r',\s*\]', ']', fixed)
    return fixed


def strip_trailing_commas(text):
    """
    Remove trailing commas before closing brackets/braces in JSON-like text.
    """
    text = re.sub(r',\s*([}\]])', r'\1', text)
    return text


def extract_json_array(text):
    """
    Extracts the first JSON array (or object) from a string, ignoring any extra text before/after.
    """
    # Try to find the first [ ... ] or { ... }
    array_match = re.search(r'(\[.*?\])', text, re.DOTALL)
    if array_match:
        return array_match.group(1)
    obj_match = re.search(r'(\{.*\})', text, re.DOTALL)
    if obj_match:
        return obj_match.group(1)
    return text  # fallback: return as-is


def parse_test_scenarios_json(response_text, issue=None, start_id_from=None, continue_numbering=False, dashboard_test_type=None):
    """
    Robustly parse AI-generated JSON (including Gemini 1.5 Flash output) into a DataFrame.
    If parsing fails, return a DataFrame with error info and a preview of the raw response.

    Args:
        response_text (str): Raw text response from AI model containing JSON test scenarios
        issue (jira.Issue, optional): JIRA issue object for metadata extraction. Defaults to None.
        start_id_from (int, optional): Starting number for test case ID generation. 
                                      Defaults to None (uses counter or auto-detection).
        continue_numbering (bool, optional): Whether to continue from existing numbering.
                                           Defaults to False.
        dashboard_test_type (str, optional): Test type for classification ("positive", 
                                           "negative", etc.). Defaults to None.

    Returns:
        pd.DataFrame: Structured DataFrame with columns:
            - Timestamp: Generation timestamp
            - Project: Project name from JIRA
            - Feature: Feature name from JIRA  
            - User Story ID: JIRA issue key
            - Test Case ID: Generated test case identifier (TC_XXX)
            - Test Case Objective: Purpose of the test case
            - Prerequisite: Prerequisites for test execution
            - Step No: Step sequence number within test case
            - Test Steps: Individual step description
            - Expected Result: Expected outcome for the step
            - Actual Result: Empty (for execution phase)
            - Test Status: Empty (for execution phase)
            - Priority: Test case priority level
            - Defect ID: Empty (for defect tracking)
            - Comments: Additional notes
            - Test Type: Type of testing (positive/negative/etc.)
            - Test Group: Logical grouping of tests

    JSON Processing Features:
        - Multiple JSON format support (single object, array, nested)
        - Robust JSON extraction from mixed text responses
        - Flexible schema handling for different AI model outputs
        - Error recovery for malformed JSON responses

    Data Validation and Cleaning:
        - Validates required fields (objective, steps)
        - Cleans and normalizes text content
        - Handles missing or null values
        - Ensures data type consistency

    Test Case ID Generation:
        - Automatic sequential numbering (TC_001, TC_002, etc.)
        - Continuation from existing test cases when specified
        - Database integration for ID conflict prevention
        - Flexible starting points for custom numbering

    JIRA Metadata Integration:
        - Extracts project and feature information from JIRA issue
        - Populates timestamp and user story fields
        - Handles missing JIRA data gracefully
        - Maintains traceability to source requirements

    Step Processing:
        - Distributes test steps across multiple DataFrame rows
        - Maintains test case grouping with proper header rows
        - Handles variable step counts per test case
        - Preserves step sequencing and numbering

    Error Handling:
        - Returns empty DataFrame on critical parsing failures
        - Logs detailed error information for debugging
        - Handles partial JSON responses gracefully
        - Manages encoding and special character issues

    Output Format Compatibility:
        - Creates DataFrame structure compatible with Excel export
        - Supports database storage through db_helper functions
        - Maintains format consistency across different AI providers
        - Enables seamless integration with downstream processes

    Example:
        # Parse AI response with automatic numbering
        ai_response = '{"test_scenarios": [{"objective": "Test login", "steps": [...]}]}'
        df = parse_test_scenarios_json(ai_response, jira_issue, dashboard_test_type="positive")
        
        # Continue numbering from existing test cases
        df = parse_test_scenarios_json(
            ai_response, 
            jira_issue, 
            start_id_from=10, 
            continue_numbering=True
        )
          if not df.empty:
            print(f"Parsed {len(df)} test case rows")
        else:
            print("Failed to parse test scenarios")    """
    # Import here to avoid circular imports
    from ..utils.counters import (
        reset_test_case_counter, get_highest_test_case_id
    )
    
    # Import the main helpers module to access shared global state
    import helpers

    # Always check for the highest test case ID in the main file
    current_jira_id = issue.key if issue else None
    
    # If the JIRA ID has changed, reset the counter
    if current_jira_id and helpers._last_jira_id and current_jira_id != helpers._last_jira_id:
        print(f"JIRA ID changed from {helpers._last_jira_id} to {current_jira_id}. Resetting counter.")
        reset_test_case_counter()
        helpers._last_jira_id = current_jira_id

    # Extract test type from the first scenario if available
    current_test_type = None
    try:
        # Try to parse the JSON to extract the test type
        scenarios = json.loads(response_text)
        if isinstance(scenarios, list) and len(scenarios) > 0:
            # Get the test type from the first scenario
            current_test_type = scenarios[0].get("type", "").lower()
    except:
        # If parsing fails, we'll proceed without a test type
        pass    # If continue_numbering is True, we don't need to check or reset the counter
    # We'll just use the current counter value or the provided start_id_from
    if not continue_numbering and current_jira_id is not None:
        # Find the highest test case ID across ALL test types for this JIRA ID
        # This ensures continuous numbering regardless of test type
        highest_id = get_highest_test_case_id(current_jira_id)
        if highest_id > 0:
            # If we found existing test cases, continue from the highest ID
            helpers._test_case_counter = highest_id
            print(f"Found existing test cases for {current_jira_id}. Continuing from TC_{highest_id+1:03d}")
        else:
            # If no existing test cases for this JIRA ID, reset the counter
            reset_test_case_counter()
            print(f"No existing test cases found for {current_jira_id}. Starting from TC_001")

        helpers._last_jira_id = current_jira_id

    # If a starting ID is provided, update the counter
    if start_id_from is not None:
        helpers._test_case_counter = start_id_from
        print(f"Setting test case counter to {start_id_from} based on provided start_id_from")

    # Generate timestamp for the test cases
    current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Remove potential markdown code block markers if present
    response_text = response_text.strip()
    if not response_text:
        raise ValueError("Empty response received from AI model")

    # Extract JSON from markdown code blocks or mixed content
    # First, try to find JSON within ```json code blocks
    json_block_match = re.search(r'```json\s*\n(.*?)\n```', response_text, re.DOTALL | re.IGNORECASE)
    if json_block_match:
        response_text = json_block_match.group(1).strip()
        print("Extracted JSON from ```json code block")
    elif response_text.startswith("```") and response_text.endswith("```"):
        # Handle generic code blocks
        response_text = "\n".join(response_text.splitlines()[1:-1])
        print("Extracted JSON from generic code block")
    else:
        # Try to extract JSON array from mixed content
        json_array_match = re.search(r'(\[\s*\{.*?\}\s*\])', response_text, re.DOTALL)
        if json_array_match:
            response_text = json_array_match.group(1).strip()
            print("Extracted JSON array from mixed content")

    # --- FIX MISSING COMMAS BETWEEN OBJECTS (Gemini 1.5 Flash bug) ---
    response_text = fix_missing_commas_in_json_array(response_text)
    response_text = strip_trailing_commas(response_text)

    # Try to parse the JSON, with extensive error handling
    try:
        # Try parsing with json.loads
        scenarios = json.loads(response_text)
        print(f"Successfully parsed JSON with {len(scenarios)} scenarios")
    except json.JSONDecodeError as e:
        # If the response starts with "Error from Google AI Studio" or other error message
        if response_text.startswith("Error from"):
            raise ValueError(f"AI model returned an error: {response_text}")

        print(f"JSON parse error at position {e.pos}, line: {e.lineno}, column: {e.colno}")
        print(f"Error message: {e.msg}")
        print(f"First 200 chars of response: {response_text[:200]}")
        print(f"Last 200 chars of response: {response_text[-200:]}")

        # Save the raw response to a file for debugging
        try:
            raw_folder = "raw_responses"
            os.makedirs(raw_folder, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            raw_file_path = os.path.join(raw_folder, f"raw_response_{timestamp}.txt")
            with open(raw_file_path, "w", encoding="utf-8") as f:
                f.write(response_text)
            print(f"Saved raw response to {raw_file_path}")
        except Exception as save_error:
            print(f"Error saving raw response: {save_error}")

        # --- Fallback: Try to forcibly repair JSON array by inserting commas between objects ---
        try:
            # Only attempt if the response looks like a JSON array
            array_match = re.search(r'^\s*\[\s*\{.*\}\s*\]\s*$', response_text, re.DOTALL)
            if array_match:
                repaired = re.sub(r'\}(\s*)\{', r'},\1{', response_text)  # Add comma if missing
                repaired = re.sub(r'\}(\s*)\n(\s*)\{', r'},\1\n\2{', repaired)
                repaired = re.sub(r'(\})(\s+)(\{)', r'\1,\2\3', repaired)
                repaired = re.sub(r',\s*\]', ']', repaired)
                repaired = strip_trailing_commas(repaired)
                try:
                    scenarios = json.loads(repaired)
                    print("Successfully parsed JSON after forced comma repair.")
                except Exception:
                    # Try ast.literal_eval as a last resort
                    try:
                        scenarios = ast.literal_eval(repaired)
                        print("Successfully parsed JSON using ast.literal_eval after forced comma repair.")
                    except Exception:
                        raise
            else:
                raise
        except Exception as repair_error:
            print(f"Failed to repair JSON: {repair_error}")
            # Try to extract JSON if it's embedded in other text
            try:
                # Try to find JSON array in the response using regex
                json_match = re.search(r'\[\s*{.*}\s*\]', response_text, re.DOTALL)
                if json_match:
                    json_text = json_match.group(0)
                    json_text = strip_trailing_commas(json_text)
                    try:
                        scenarios = json.loads(json_text)
                        print("Successfully extracted JSON using regex pattern.")
                    except Exception:
                        # Try ast.literal_eval as a last resort
                        try:
                            scenarios = ast.literal_eval(json_text)
                            print("Successfully extracted JSON using ast.literal_eval from regex pattern.")
                        except Exception:
                            raise ValueError("Could not extract JSON from response")
                else:
                    # Try another approach - look for the first [ and last ]
                    start_idx = response_text.find('[')
                    end_idx = response_text.rfind(']')
                    if (start_idx != -1 and end_idx != -1 and start_idx < end_idx):
                        json_text = response_text[start_idx:end_idx+1]
                        json_text = strip_trailing_commas(json_text)
                        try:
                            scenarios = json.loads(json_text)
                            print("Successfully extracted JSON using bracket positions.")
                        except Exception:
                            # Try ast.literal_eval as a last resort
                            try:
                                scenarios = ast.literal_eval(json_text)
                                print("Successfully extracted JSON using ast.literal_eval from bracket positions.")
                            except Exception:
                                raise ValueError("Could not extract JSON from response")
                    else:
                        raise ValueError("Could not extract JSON from response")
            except Exception:
                # If all parsing attempts fail, provide detailed error
                error_msg = (
                    f"Invalid JSON output: {str(e)}.\n"
                    f"Response does not contain valid JSON. First 100 characters: {response_text[:100]}..."
                )
                # Save the raw response to a file for debugging
                try:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    raw_file_path = os.path.join("Test_cases", f"Latest_testcases_{current_jira_id}_{dashboard_test_type}_{timestamp}_raw.txt")
                    with open(raw_file_path, "w", encoding="utf-8") as f:
                        f.write(response_text)
                    print(f"Saved raw response to {raw_file_path}")
                except Exception as save_error:
                    print(f"Error saving raw response: {save_error}")

                raise ValueError(error_msg)
    # Ensure the output is a list of scenarios
    if not isinstance(scenarios, list):
        raise ValueError(f"Expected a JSON array of scenarios, but got {type(scenarios).__name__}")

    if len(scenarios) == 0:
        raise ValueError("JSON array is empty - no scenarios found")    # Get the highest test case ID from the database across all test types
    try:
        import db_helper as db
        # Get the JIRA ID from the issue
        jira_id = issue.key if issue else ""

        if jira_id:
            # Get the highest test case ID across ALL test types for this JIRA ID
            # This ensures continuous numbering across all test types
            highest_id = db.get_highest_test_case_id_number(db.DATABASE_PATH, jira_id, "all")
            if highest_id > 0:
                print(f"Found highest test case ID {highest_id} for {jira_id} across all test types")
                # The counter will be managed by the parse_json_response function
                # We don't set it here as it's already handled in the calling logic
            else:
                print(f"No existing test cases found for {jira_id}, will continue with existing counter state")
        else:
            print(f"No JIRA ID provided, will continue with existing counter state")
    except Exception as e:
        print(f"Error getting highest test case ID from database: {e}")
        # Don't reset the counter on error - let the existing logic handle it
        print("Continuing with existing counter state despite error")

    # Extract JIRA metadata if available with safe access
    project = ""
    if issue:
        try:
            project_field = issue.raw.get('fields', {}).get('project')
            if project_field and project_field.get('name'):
                project = project_field['name']
        except (KeyError, AttributeError, TypeError) as e:
            print(f"Warning: Could not extract project from JIRA issue: {e}")
            project = ""

    issue_key = issue.key if issue else ""

    # Safely extract test objective
    test_objective = ""
    if issue:
        try:
            if hasattr(issue, 'fields') and hasattr(issue.fields, 'summary'):
                test_objective = issue.fields.summary
            elif 'fields' in issue.raw and 'summary' in issue.raw['fields']:
                test_objective = issue.raw['fields']['summary']
        except (KeyError, AttributeError, TypeError) as e:
            print(f"Warning: Could not extract summary from JIRA issue: {e}")
            test_objective = ""

    # Clean up the feature name by removing any prefix pattern
    feature_name = test_objective
    if feature_name and isinstance(feature_name, str):
        # Process feature name to remove prefixes

        # Try a simpler approach - if it contains "TS 001" or similar pattern, split and take the rest
        if "TS " in feature_name and any(c.isdigit() for c in feature_name):
            # Split by space, find the part that has TS and numbers, and remove everything up to that
            parts = feature_name.split()
            for i, part in enumerate(parts):
                if part == "TS" and i+1 < len(parts) and parts[i+1].isdigit():
                    feature_name = " ".join(parts[i+2:])
                    break

        # If the above didn't work, try regex as a fallback
        if feature_name == test_objective:  # If no change was made
            # Remove common prefix patterns like "TS 001", "FEAT-123", "ID_456", etc.
            feature_name = re.sub(r'^[A-Za-z0-9]+[\s\-_]*\d+\s*', '', feature_name)

        # Feature name has been processed

        # If we accidentally removed everything, use the original
        if not feature_name.strip():
            feature_name = test_objective

    # Safely extract priority from JIRA issue
    priority = ""
    if issue:
        try:
            priority_field = issue.raw.get('fields', {}).get('priority')
            if priority_field and priority_field.get('name'):
                priority = priority_field['name']
        except (KeyError, AttributeError, TypeError) as e:
            print(f"Warning: Could not extract priority from JIRA issue: {e}")
            priority = ""

    parsed_data = []
    for _, scenario in enumerate(scenarios, start=1):
        # Get scenario name (not used but kept for future reference)
        _ = scenario.get("scenario_name", "").strip()
        prerequisite = scenario.get("prerequisites", "").strip()
        steps = scenario.get("steps", [])        # We don't need to get the highest test case ID for each scenario
        # The counter is already set to the highest ID across all test types        # Increment the global counter for each scenario
        helpers._test_case_counter += 1

        # Create Test Case ID with proper formatting (e.g., TC_001, TC_002)
        # Ensure there are no extra spaces in the Test Case ID
        test_case_id = f"TC_{helpers._test_case_counter:03d}".strip()  # Format as TC_001, TC_002, etc.

        # We don't need to check for duplicate test case IDs
        # The counter is already set to the highest ID across all test types

        for step_idx, step in enumerate(steps, start=1):
            instruction = step.get("action", "").strip()
            expected_result = step.get("expected_result", "").strip()

            # Only include Project and Feature for the first step of each test case
            # For subsequent steps, leave these fields blank
            current_project = project if step_idx == 1 else ""
            current_feature = feature_name if step_idx == 1 else ""  # Use cleaned feature name without TS prefix

            # Only include Test Case ID and User Story ID for the first step
            current_test_case_id = test_case_id if step_idx == 1 else ""
            current_user_story_id = issue_key if step_idx == 1 else ""

            # Only include Test Case Objective and Prerequisite for the first step
            # Ensure there are no extra spaces in the Test Case Objective
            # Use the Test Case Objective from the scenario if it's provided, otherwise use the issue summary
            scenario_test_objective = scenario.get("Test Case Objective", "").strip()
            current_test_objective = scenario_test_objective if step_idx == 1 and scenario_test_objective else (test_objective.strip() if step_idx == 1 and test_objective else "")
            current_prerequisite = prerequisite if step_idx == 1 else ""  # Only include for first step

            # Get test type from scenario if available, otherwise use empty string
            test_type = scenario.get("type", "").strip().upper() if scenario.get("type") else ""

            # Determine the group number based on the test type
            group_number = 1  # Default group number
            if test_type == "NEGATIVE":
                group_number = 2
            elif test_type == "SECURITY":
                group_number = 3
            elif test_type == "PERFORMANCE":
                group_number = 4

            # Only include Timestamp in the first step of each test case, like other fields
            current_timestamp_value = current_timestamp if step_idx == 1 else ""

            parsed_data.append({
                "Timestamp": current_timestamp_value,  # Add timestamp as first column
                "Project": current_project,
                "Feature": current_feature,  # Use issue summary as Feature
                "User Story ID": str(current_user_story_id),  # Add User Story ID column with JIRA issue key as string
                "Test Case ID": current_test_case_id,
                "Test Case Objective": current_test_objective,
                "Prerequisite": current_prerequisite,  # Only include prerequisite for first step, otherwise empty string
                "Step No": step_idx,
                "Test Steps": instruction,
                "Expected Result": expected_result,
                "Actual Result": "",
                "Test Status": "",
                "Priority": priority if step_idx == 1 else "",  # Only include Priority in the first step
                "Defect ID": "",
                "Comments": "",
                "Test Type": test_type if step_idx == 1 else "",  # Only include Test Type in the first step
                "Test Group": f"Group {group_number}: {test_type}" if step_idx == 1 else ""  # Include Test Group with test type and group number
            })

    return pd.DataFrame(parsed_data)


def validate_test_case_dataframe(df):
    """
    Validate test case dataframe structure and content.
    
    FUNCTION TYPE: DATA VALIDATION HELPER
    
    Checks if a test case dataframe has the required structure
    and valid data for database operations.

    Args:
        df (pandas.DataFrame): Test case dataframe to validate

    Returns:
        tuple: (is_valid: bool, error_messages: list)
            - is_valid: True if dataframe is valid for processing
            - error_messages: List of validation error messages

    Usage Example:
        is_valid, errors = validate_test_case_dataframe(df)
        if not is_valid:
            for error in errors:
                st.error(error)
    """
    errors = []
    
    if df.empty:
        errors.append("Dataframe is empty")
        return False, errors
    
    # Check for required columns
    required_columns = ["Test Case ID", "Test Case Objective"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        errors.append(f"Missing required columns: {', '.join(missing_columns)}")
    
    # Check for valid test case IDs
    if "Test Case ID" in df.columns:
        valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
        valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
        valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
        
        if len(valid_test_case_ids) == 0:
            errors.append("No valid test case IDs found (expected format: TC_XXX)")
    
    return len(errors) == 0, errors


def count_valid_test_cases(df):
    """
    Count valid test cases in a dataframe.
    
    FUNCTION TYPE: DATA ANALYSIS HELPER
    
    Counts the number of valid test case IDs in a dataframe,
    filtering out empty or invalid entries.

    Args:
        df (pandas.DataFrame): Dataframe containing test cases

    Returns:
        int: Number of valid test cases found

    Usage Example:
        count = count_valid_test_cases(test_cases_df)
        st.info(f"Found {count} valid test cases")
    """
    if df.empty or "Test Case ID" not in df.columns:
        return 0
    
    # Filter out empty strings and count only valid test case IDs (TC_XXX format)
    valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
    valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
    # Only count IDs that match the TC_XXX pattern
    valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
    unique_test_case_ids = valid_test_case_ids.unique()
    
    return len(unique_test_case_ids)


def parse_enhanced_description_json(enhanced_desc):
    """
    Parse enhanced description JSON and format it for display.
    
    FUNCTION TYPE: DATA PROCESSING HELPER
    
    This function attempts to parse enhanced descriptions stored as JSON
    and format them into structured HTML for better readability.

    Args:
        enhanced_desc (str): Enhanced description potentially in JSON format

    Returns:
        str: Formatted HTML string or cleaned text if JSON parsing fails

    Usage Example:
        formatted = parse_enhanced_description_json(enhanced_description)
        st.markdown(formatted, unsafe_allow_html=True)
    """
    try:
        # Parse JSON (remove any markdown code blocks if present)
        if "```json" in enhanced_desc:
            json_text = enhanced_desc.split("```json")[1].split("```")[0].strip()
        elif "```" in enhanced_desc:
            json_text = enhanced_desc.split("```")[1].strip()
        else:
            json_text = enhanced_desc.strip()
        
        # Clean up JSON text to help parsing
        json_text = re.sub(r'[\n\r\t]', ' ', json_text)  # Replace newlines/tabs with spaces
        json_text = re.sub(r',\s*}', '}', json_text)  # Fix trailing commas
        json_text = re.sub(r',\s*]', ']', json_text)  # Fix trailing commas in arrays
        
        enhanced_data = json.loads(json_text)
        
        # Format the JSON data as structured HTML
        formatted_description = '<div class="jira-details">'
        
        # User Story Section
        if "userStory" in enhanced_data and enhanced_data["userStory"]:
            formatted_description += f'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">User Story:</h4>'
            formatted_description += f'<p>{enhanced_data["userStory"]}</p>'
        
        # Description Section
        if "description" in enhanced_data and enhanced_data["description"]:
            formatted_description += f'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Description:</h4>'
            formatted_description += '<ul>'
            for item in enhanced_data["description"]:
                formatted_description += f'<li>{item}</li>'
            formatted_description += '</ul>'
        
        # Acceptance Criteria Section
        if "acceptanceCriteria" in enhanced_data and enhanced_data["acceptanceCriteria"]:
            formatted_description += f'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Acceptance Criteria:</h4>'
            formatted_description += '<ol>'
            for idx, item in enumerate(enhanced_data["acceptanceCriteria"], 1):
                formatted_description += f'<li><strong>{idx}.</strong> {item}</li>'
            formatted_description += '</ol>'
            
        formatted_description += '</div>'
        return formatted_description
        
    except (json.JSONDecodeError, ValueError) as e:
        # If JSON parsing fails, create a human-readable format instead of showing raw JSON
        print(f"Error parsing enhanced description as JSON: {e}")
        
        # First, attempt to clean up the JSON-like string for better display
        clean_text = enhanced_desc
        clean_text = re.sub(r'```json', '', clean_text)
        clean_text = re.sub(r'```', '', clean_text)
        clean_text = re.sub(r'\n+', '<br>', clean_text)  # Replace newlines with <br> tags
        clean_text = re.sub(r'^\s*-\s+', r'<li>', clean_text, flags=re.MULTILINE)  # Replace bullet points
        clean_text = re.sub(r'^\s*\d+\.\s+', r'<li>', clean_text, flags=re.MULTILINE)  # Replace numbered list
        clean_text = re.sub(r'(<li>.*?</li>)', r'<ul>\1</ul>', clean_text, flags=re.DOTALL)  # Wrap in ul tags
        clean_text = re.sub(r'(<ol>.*?</ol>)', r'<ol>\1</ol>', clean_text, flags=re.DOTALL)  # Wrap in ol tags
        
        # Format "User Story:" as a heading
        clean_text = re.sub(r'(User Story:)', r'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">\1</h4>', clean_text)
        
        # Format "Description:" as a heading
        clean_text = re.sub(r'(Description:)', r'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">\1</h4>', clean_text)
        
        # Format "Acceptance Criteria:" as a heading
        clean_text = re.sub(r'(Acceptance Criteria:)', r'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">\1</h4>', clean_text)
        
        # Fix numbered list format in Acceptance Criteria section
        acceptance_criteria_match = re.search(r'Acceptance Criteria:.*?(\d+\.\s+.*?)(?=(User Story:|Description:|$)', clean_text, re.DOTALL)
        if acceptance_criteria_match:
            criteria_content = acceptance_criteria_match.group(1)
            fixed_criteria = re.sub(r'(\d+\.\s+[^0-9]+?)(\d+\.)', r'\1<br>\2', criteria_content)
            clean_text = clean_text.replace(criteria_content, fixed_criteria)
        
        # Format each numbered item with proper styling
        clean_text = re.sub(r'(\d+\.\s+)', r'<br><strong>\1</strong>', clean_text)
        
        # Clean up any potential duplicate <br> tags at the beginning
        clean_text = clean_text.replace('<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Acceptance Criteria:</h4><br>', 
                                      '<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Acceptance Criteria:</h4>')
        
        return clean_text


def format_jira_description(description):
    """
    Format JIRA description text for better UI display.
    
    FUNCTION TYPE: UI FORMATTING HELPER
    
    This function processes raw JIRA description text and applies formatting
    to make it more readable in the UI, including bullet points, headings,
    table conversion, and paragraph formatting.

    Args:
        description (str): Raw JIRA description text

    Returns:
        str: Formatted HTML string ready for display

    Usage Example:
        formatted = format_jira_description(issue.fields.description)
        st.markdown(formatted, unsafe_allow_html=True)
    """
    # Import the function from jira operations to avoid code duplication
    from helpers.jira.operations import format_jira_description as jira_format_description
    return jira_format_description(description)