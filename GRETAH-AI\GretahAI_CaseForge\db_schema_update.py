"""
Database schema migration script.
This script updates the database from the old schema to the new schema.

Usage in PowerShell:
    py db_schema_update.py [path_to_database]

If no database path is provided, it will look for test_cases_v2.db in the current directory.
"""

import os
import sqlite3
import shutil
import time
import sys
from datetime import datetime

# Get the database path from command line argument or use default
if len(sys.argv) > 1:
    DATABASE_PATH = sys.argv[1]
    print(f"Using database path from command line: {DATABASE_PATH}")
else:
    # Default to test_cases_v2.db in the current working directory
    DATABASE_PATH = os.path.join(os.getcwd(), "test_cases_v2.db")
    print(f"Using default database path: {DATABASE_PATH}")

# Verify the database exists before proceeding
if not os.path.exists(DATABASE_PATH):
    print(f"ERROR: Database file not found at {DATABASE_PATH}")
    print("Please specify the correct path to your database file:")
    print("Example: py db_schema_update.py C:\\path\\to\\test_cases_v2.db")
    sys.exit(1)

def backup_database():
    """Creates a backup of the database before migration."""
    if os.path.exists(DATABASE_PATH):
        backup_dir = os.path.join(os.path.dirname(DATABASE_PATH), "backups")
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"database_backup_{timestamp}.db")
        
        # Create the backup
        shutil.copy2(DATABASE_PATH, backup_path)
        print(f"Created database backup at: {backup_path}")
        return backup_path
    return None

def detect_schema_version():
    """Detects the current schema version of the database."""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # Check if the test_runs table has a jira_id column (new schema)
        cursor.execute("PRAGMA table_info(test_runs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if "jira_id" in columns:
            print("Detected new schema (direct JIRA ID in test_runs table)")
            return "new"
        else:
            print("Detected old schema (jira_issue_id foreign key in test_runs table)")
            return "old"
    except sqlite3.Error as e:
        print(f"Error detecting schema version: {e}")
        return "unknown"
    finally:
        if conn:
            conn.close()

def migrate_old_to_new_schema():
    """Migrates the database from the old schema to the new schema."""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH, timeout=60)
        cursor = conn.cursor()
        
        # Enable Foreign Key support
        cursor.execute("PRAGMA foreign_keys = OFF;")  # Temporarily disable for migration
        
        # Start a transaction
        cursor.execute("BEGIN TRANSACTION")
        
        print("Creating backup tables...")
        # Step 1: Create backup tables
        cursor.execute("CREATE TABLE test_runs_backup AS SELECT * FROM test_runs")
        cursor.execute("CREATE TABLE test_cases_backup AS SELECT * FROM test_cases")
        
        # Step 2: Get all JIRA issues
        cursor.execute("SELECT id, jira_id FROM jira_issues")
        jira_issues = {row[0]: row[1] for row in cursor.fetchall()}
        
        # Step 3: Drop and recreate the test_runs table with the new schema
        print("Updating test_runs table schema...")
        cursor.execute("DROP TABLE test_runs")
        cursor.execute('''
        CREATE TABLE test_runs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_type TEXT NOT NULL,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            num_test_cases INTEGER,
            status TEXT DEFAULT 'planned',
            user_name TEXT,
            notes TEXT
        )
        ''')
        
        # Step 4: Migrate data from the backup table to the new table
        print("Migrating test_runs data...")
        cursor.execute("SELECT * FROM test_runs_backup")
        for row in cursor.fetchall():
            jira_issue_id = row[1]  # Assuming jira_issue_id is the second column
            jira_id = jira_issues.get(jira_issue_id, "UNKNOWN")
            
            # Extract values from the row, handling potential missing columns
            test_type = row[2] if len(row) > 2 else ""
            timestamp = row[3] if len(row) > 3 else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            num_test_cases = row[4] if len(row) > 4 else 0
            status = row[5] if len(row) > 5 else "planned"
            user_name = row[6] if len(row) > 6 else ""
            notes = row[7] if len(row) > 7 else ""
            
            cursor.execute('''
            INSERT INTO test_runs (
                id, jira_id, test_type, timestamp, num_test_cases, status, user_name, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                row[0],  # id
                jira_id,
                test_type,
                timestamp,
                num_test_cases,
                status,
                user_name,
                notes
            ))
        
        # Step 5: Update the test_cases table to use the actual JIRA ID
        print("Updating test_cases table...")
        cursor.execute("ALTER TABLE test_cases ADD COLUMN jira_id TEXT")
        
        # Step 6: Update the jira_id column in the test_cases table
        for jira_issue_id, jira_id in jira_issues.items():
            cursor.execute(
                "UPDATE test_cases SET jira_id = ? WHERE jira_issue_id = ?",
                (jira_id, jira_issue_id)
            )
        
        # Step 7: Create new indexes
        print("Creating new indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id)")
        
        # Step 8: Drop the backup tables
        print("Cleaning up backup tables...")
        cursor.execute("DROP TABLE test_runs_backup")
        cursor.execute("DROP TABLE test_cases_backup")
        
        # Re-enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON;")
        
        # Commit the transaction
        cursor.execute("COMMIT")
        print("Schema migration completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Schema migration error: {e}")
        if conn:
            try:
                cursor.execute("ROLLBACK")
            except:
                pass
        return False
    finally:
        if conn:
            conn.close()

def run_migration():
    """Main function to run the migration."""
    print(f"Starting database schema migration for: {DATABASE_PATH}")
    
    # Create a backup first
    backup_path = backup_database()
    if not backup_path:
        print("No existing database found, nothing to migrate.")
        return
    
    # Detect the schema version
    schema_version = detect_schema_version()
    
    if schema_version == "new":
        print("Database already has the latest schema. Standardizing to ensure consistency...")
        success = standardize_schema()
        if success:
            print("Schema standardization completed successfully.")
            # Verify schema completeness after standardization
            verify_schema_completeness()
        else:
            print(f"Schema standardization failed. Restoring from backup: {backup_path}")
            # Wait a moment to ensure connections are closed
            time.sleep(1)
            # Restore from backup
            shutil.copy2(backup_path, DATABASE_PATH)
            print("Database restored from backup.")
    elif schema_version == "old":
        print("Migrating database from old schema to new schema...")
        success = migrate_old_to_new_schema()
        if success:
            print("Migration completed successfully. Standardizing to ensure consistency...")
            standardize_success = standardize_schema()
            if standardize_success:
                print("Schema standardization completed successfully.")
                # Verify schema completeness after standardization
                verify_schema_completeness()
            else:
                print(f"Schema standardization failed. Restoring from backup: {backup_path}")
                # Wait a moment to ensure connections are closed
                time.sleep(1)
                # Restore from backup
                shutil.copy2(backup_path, DATABASE_PATH)
                print("Database restored from backup.")
        else:
            print(f"Migration failed. Restoring from backup: {backup_path}")
            # Wait a moment to ensure connections are closed
            time.sleep(1)
            # Restore from backup
            shutil.copy2(backup_path, DATABASE_PATH)
            print("Database restored from backup.")
    else:
        print("Unknown schema version. Cannot migrate.")

def verify_schema_completeness():
    """Verifies that all required tables and columns exist in the database."""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # Check for required tables
        required_tables = ["test_runs", "test_cases", "test_steps", "jira_issues"]
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if not cursor.fetchone():
                print(f"Missing required table: {table}")
                return False
        
        # Check for required columns in test_runs
        cursor.execute("PRAGMA table_info(test_runs)")
        test_runs_columns = [column[1] for column in cursor.fetchall()]
        required_test_runs_columns = ["id", "jira_id", "test_type", "timestamp", "num_test_cases", "status", "user_name", "notes"]
        for column in required_test_runs_columns:
            if column not in test_runs_columns:
                print(f"Missing required column in test_runs: {column}")
                return False
        
        # Check for required columns in test_cases
        cursor.execute("PRAGMA table_info(test_cases)")
        test_cases_columns = [column[1] for column in cursor.fetchall()]
        required_test_cases_columns = ["id", "jira_id", "test_case_id", "test_case_objective", "dashboard_test_type", "user_name"]
        for column in required_test_cases_columns:
            if column not in test_cases_columns:
                print(f"Missing required column in test_cases: {column}")
                return False
        
        # Check for required columns in test_steps
        cursor.execute("PRAGMA table_info(test_steps)")
        test_steps_columns = [column[1] for column in cursor.fetchall()]
        required_test_steps_columns = ["id", "test_case_id", "step_number", "test_step", "expected_result"]
        for column in required_test_steps_columns:
            if column not in test_steps_columns:
                print(f"Missing required column in test_steps: {column}")
                return False
        
        # Check for required columns in jira_issues
        cursor.execute("PRAGMA table_info(jira_issues)")
        jira_issues_columns = [column[1] for column in cursor.fetchall()]
        required_jira_issues_columns = ["id", "jira_id"]
        for column in required_jira_issues_columns:
            if column not in jira_issues_columns:
                print(f"Missing required column in jira_issues: {column}")
                return False
        
        # Check for required indexes
        required_indexes = [
            "idx_jira_issues_jira_id",
            "idx_test_runs_jira_id",
            "idx_test_cases_jira_id",
            "idx_test_cases_jira_issue_id",
            "idx_test_steps_test_case_id"
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
        indexes = [row[0] for row in cursor.fetchall()]
        
        for index in required_indexes:
            if index not in indexes:
                print(f"Missing required index: {index}")
                # Don't fail for missing indexes, just warn
                print(f"Warning: Missing index {index} - this may affect performance but not functionality")
        
        print("Schema verification complete: All required tables and columns exist")
        return True
    except sqlite3.Error as e:
        print(f"Error verifying schema: {e}")
        return False
    finally:
        if conn:
            conn.close()

def standardize_schema():
    """Standardizes the database schema to ensure consistency across all installations."""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH, timeout=60)
        cursor = conn.cursor()
        
        # Disable foreign keys temporarily
        cursor.execute("PRAGMA foreign_keys = OFF;")
        cursor.execute("BEGIN TRANSACTION")
        
        print("Creating backup of all tables...")
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # Create backup of all tables
        for table in tables:
            cursor.execute(f"CREATE TABLE IF NOT EXISTS {table}_backup AS SELECT * FROM {table}")
        
        print("Recreating tables with standardized schema...")
        
        # Drop and recreate all tables with standardized schema
        # 1. jira_issues table
        cursor.execute("DROP TABLE IF EXISTS jira_issues")
        cursor.execute('''
        CREATE TABLE jira_issues (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT UNIQUE NOT NULL,
            summary TEXT,
            description TEXT,
            status TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            test_run_id INTEGER,
            enhanced_description TEXT,
            enhanced_timestamp TEXT
        )
        ''')
        
        # 2. test_runs table
        cursor.execute("DROP TABLE IF EXISTS test_runs")
        cursor.execute('''
        CREATE TABLE test_runs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_type TEXT NOT NULL,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            num_test_cases INTEGER,
            status TEXT DEFAULT 'planned',
            user_name TEXT,
            notes TEXT
        )
        ''')
        
        # 3. test_cases table
        cursor.execute("DROP TABLE IF EXISTS test_cases")
        cursor.execute('''
        CREATE TABLE test_cases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jira_id TEXT NOT NULL,
            test_case_id TEXT NOT NULL,
            test_case_objective TEXT,
            prerequisite TEXT,
            priority TEXT,
            test_type TEXT,
            test_group TEXT,
            project TEXT,
            feature TEXT,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            is_latest BOOLEAN DEFAULT 1,
            dashboard_test_type TEXT,
            user_name TEXT,
            jira_issue_id INTEGER,
            is_edited BOOLEAN DEFAULT 0,
            test_run_id INTEGER,
            FOREIGN KEY (jira_issue_id) REFERENCES jira_issues(id),
            FOREIGN KEY (test_run_id) REFERENCES test_runs(id)
        )
        ''')
        
        # 4. test_steps table
        cursor.execute("DROP TABLE IF EXISTS test_steps")
        cursor.execute('''
        CREATE TABLE test_steps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            test_case_id INTEGER NOT NULL,
            step_number INTEGER NOT NULL,
            test_step TEXT,
            expected_result TEXT,
            actual_result TEXT,
            test_status TEXT,
            defect_id TEXT,
            comments TEXT,
            dashboard_test_type TEXT,
            user_name TEXT,
            FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
        )
        ''')
        
        # 5. test_case_executions table (if it exists)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_executions_backup'")
        if cursor.fetchone():
            cursor.execute("DROP TABLE IF EXISTS test_case_executions")
            cursor.execute('''
            CREATE TABLE test_case_executions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_run_id INTEGER NOT NULL,
                test_case_id INTEGER NOT NULL,
                execution_date TEXT DEFAULT CURRENT_TIMESTAMP,
                executed_by TEXT,
                status TEXT,
                defect_id TEXT,
                comments TEXT,
                FOREIGN KEY (test_run_id) REFERENCES test_runs(id),
                FOREIGN KEY (test_case_id) REFERENCES test_cases(id)
            )
            ''')
        
        # Migrate data from backup tables to new tables
        print("Migrating data to standardized tables...")
        # 1. Migrate jira_issues data
        cursor.execute("SELECT * FROM jira_issues_backup")
        for row in cursor.fetchall():
            # Get column names from the backup table
            cursor.execute("PRAGMA table_info(jira_issues_backup)")
            backup_columns = [col[1] for col in cursor.fetchall()]
            
            # Create a dictionary of column values
            row_dict = {backup_columns[i]: row[i] for i in range(len(row)) if i < len(backup_columns)}
              # Insert into new table with all 9 columns
            try:
                cursor.execute('''
                INSERT INTO jira_issues (
                    id, jira_id, summary, description, status, created_at, 
                    test_run_id, enhanced_description, enhanced_timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    row_dict.get("id"),
                    row_dict.get("jira_id"),
                    row_dict.get("summary", ""),  # Default empty string for TEXT columns
                    row_dict.get("description", ""),
                    row_dict.get("status", ""),
                    row_dict.get("created_at", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                    row_dict.get("test_run_id"),  # INTEGER can be NULL 
                    row_dict.get("enhanced_description"),  # TEXT can be NULL
                    row_dict.get("enhanced_timestamp")  # TEXT can be NULL
                ))
            except sqlite3.Error as e:
                print(f"Error inserting jira issue - Row data: {row_dict}")
                print(f"Error details: {e}")
                raise
        
        # 2. Migrate test_runs data
        cursor.execute("SELECT * FROM test_runs_backup")
        for row in cursor.fetchall():
            # Adjust column count based on the actual schema
            columns = ["id", "jira_id", "test_type", "timestamp", "num_test_cases", "status", "user_name", "notes"]
            values = []
            for i, col in enumerate(columns):
                if i < len(row):
                    values.append(row[i])
                else:
                    values.append(None)
            
            placeholders = ", ".join(["?"] * len(values))
            cursor.execute(f"INSERT OR IGNORE INTO test_runs VALUES ({placeholders})", values)
        
        # 3. Migrate test_cases data
        cursor.execute("SELECT * FROM test_cases_backup")
        for row in cursor.fetchall():
            # Get column names from the backup table
            cursor.execute("PRAGMA table_info(test_cases_backup)")
            backup_columns = [col[1] for col in cursor.fetchall()]
            
            # Create a dictionary of column values
            row_dict = {backup_columns[i]: row[i] for i in range(len(row)) if i < len(backup_columns)}
            
            # Insert into new table with standardized columns
            cursor.execute('''
            INSERT INTO test_cases (
                id, jira_id, test_case_id, test_case_objective, prerequisite, priority, 
                test_type, test_group, project, feature, timestamp, is_latest, 
                dashboard_test_type, user_name, jira_issue_id, is_edited, test_run_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                row_dict.get("id"),
                row_dict.get("jira_id"),
                row_dict.get("test_case_id", ""),
                row_dict.get("test_case_objective"),
                row_dict.get("prerequisite"),
                row_dict.get("priority"),
                row_dict.get("test_type"),
                row_dict.get("test_group"),
                row_dict.get("project"),
                row_dict.get("feature"),
                row_dict.get("timestamp", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                row_dict.get("is_latest", 1),
                row_dict.get("dashboard_test_type"),
                row_dict.get("user_name"),
                row_dict.get("jira_issue_id"),
                row_dict.get("is_edited", 0),
                row_dict.get("test_run_id")
            ))
        
        # 4. Migrate test_steps data
        cursor.execute("SELECT * FROM test_steps_backup")
        for row in cursor.fetchall():
            # Get column names from the backup table
            cursor.execute("PRAGMA table_info(test_steps_backup)")
            backup_columns = [col[1] for col in cursor.fetchall()]
            
            # Create a dictionary of column values
            row_dict = {backup_columns[i]: row[i] for i in range(len(row)) if i < len(backup_columns)}
            
            # Insert into new table with standardized columns
            cursor.execute('''
            INSERT INTO test_steps (
                id, test_case_id, step_number, test_step, expected_result, 
                actual_result, test_status, defect_id, comments, dashboard_test_type, user_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                row_dict.get("id"),
                row_dict.get("test_case_id"),
                row_dict.get("step_number"),
                row_dict.get("test_step"),
                row_dict.get("expected_result"),
                row_dict.get("actual_result"),
                row_dict.get("test_status"),
                row_dict.get("defect_id"),
                row_dict.get("comments"),
                row_dict.get("dashboard_test_type"),
                row_dict.get("user_name")
            ))
        
        # 5. Migrate test_case_executions data if it exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_case_executions_backup'")
        if cursor.fetchone():
            cursor.execute("SELECT * FROM test_case_executions_backup")
            for row in cursor.fetchall():
                # Get column names from the backup table
                cursor.execute("PRAGMA table_info(test_case_executions_backup)")
                backup_columns = [col[1] for col in cursor.fetchall()]
                
                # Create a dictionary of column values
                row_dict = {backup_columns[i]: row[i] for i in range(len(row)) if i < len(backup_columns)}
                
                # Insert into new table with standardized columns
                cursor.execute('''
                INSERT INTO test_case_executions (
                    id, test_run_id, test_case_id, execution_date, executed_by, 
                    status, defect_id, comments
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    row_dict.get("id"),
                    row_dict.get("test_run_id"),
                    row_dict.get("test_case_id"),
                    row_dict.get("execution_date", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                    row_dict.get("executed_by"),
                    row_dict.get("status"),
                    row_dict.get("defect_id"),
                    row_dict.get("comments")
                ))
        
        # Create all necessary indexes
        print("Creating standardized indexes...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_jira_issues_jira_id ON jira_issues (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_runs_jira_id ON test_runs (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_id ON test_cases (jira_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_jira_issue_id ON test_cases (jira_issue_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_cases_dashboard_test_type ON test_cases (dashboard_test_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_test_case_id ON test_steps (test_case_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_steps_dashboard_test_type ON test_steps (dashboard_test_type)")
        
        # Drop backup tables
        print("Cleaning up backup tables...")
        for table in tables:
            cursor.execute(f"DROP TABLE IF EXISTS {table}_backup")
        
        # Re-enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON;")
        
        # Commit the transaction
        cursor.execute("COMMIT")
        print("Schema standardization completed successfully")
        return True
    except sqlite3.Error as e:
        print(f"Error standardizing schema: {e}")
        if conn and conn.in_transaction:
            conn.rollback()
        raise
    finally:
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")

if __name__ == "__main__":
    # Ask for confirmation before running
    print("This script will update your database schema to the latest version.")
    print("A backup will be created before making any changes.")
    confirm = input("Do you want to proceed? (y/n): ")
    
    if confirm.lower() == 'y':
        run_migration()
    else:
        print("Migration cancelled.")

    # Verify the schema completeness
    if verify_schema_completeness():
        print("Schema verification passed.")
    else:
        print("Schema verification failed. Please check your database schema.")
        
    # Add command-line argument handling
    if len(sys.argv) > 1:
        if sys.argv[1] == "migrate":
            # Just run the migration without asking for confirmation
            print("Running migration in non-interactive mode...")
            run_migration()
        elif sys.argv[1] == "backup":
            # Just create a backup
            backup_path = backup_database()
            if backup_path:
                print(f"Backup created at: {backup_path}")
            else:
                print("Backup failed")
                sys.exit(1)
        elif sys.argv[1] == "verify":
            # Just verify the schema
            if verify_schema_completeness():
                print("Schema verification passed.")
            else:
                print("Schema verification failed.")
                sys.exit(1)
        else:
            print(f"Unknown command: {sys.argv[1]}")
            print("Available commands: migrate, backup, verify")
            sys.exit(1)

def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) < 2:
        print("Please provide a command: migrate, backup, or verify")
        sys.exit(1)
        
    command = sys.argv[1].lower()
    if command == "migrate":
        standardize_schema()
    elif command == "backup":
        backup_database()
    elif command == "verify":
        verify_schema_completeness()
    else:
        print(f"Unknown command: {command}")
        print("Available commands: migrate, backup, verify")
        sys.exit(1)

if __name__ == "__main__":
    main()

