#!/usr/bin/env python3
"""
Test Data Generator for Data-Driven Testing.
This script generates test data files in JSON, YAML, and CSV formats
for various test scenarios, including window management, login functionality,
and dropdown interactions. It also creates a central test configuration file
in JSON and YAML formats.
The script uses command-line arguments to customize the number of test cases
generated for specific scenarios and the desired output format(s).
Key functionalities:
- Generates test data for window opening/management tests.
- Generates test data for login tests (valid, invalid, random cases).
- Generates test data for dropdown selection tests (by text, value, index, JS).
- Creates a `test_config.json` and `test_config.yaml` file with browser settings,
    URLs, default credentials, and test-specific settings.
- Supports outputting test data in JSON, YAML, and CSV formats.
- Allows specifying the output directory for generated files.
Usage:
        python create_test_data.py [--window-tests N] [--login-tests N] [--format {json,yaml,csv,all}] [--output-dir PATH]
Arguments:
        --window-tests N: Number of window test cases to generate (default: 5).
        --login-tests N:  Number of login test cases to generate (default: 10).
        --format:         Output file format(s) (default: 'all').
        --output-dir:     Directory to store generated files (default: './test_data').
Generated Files (in the specified output directory):
- test_config.json: Test configuration in JSON format.
- test_config.yaml: Test configuration in YAML format.
- window_test_data.{json,yaml,csv}: Test data for window tests.
- login_test_data.{json,yaml,csv}: Test data for login tests.
- dropdown_test_data.{json,yaml,csv}: Test data for dropdown tests.
    (Note: CSV format might be less suitable for complex nested data like window tests).
"""

import json
import yaml
import csv
import random
import string
import argparse
from pathlib import Path
from datetime import datetime

# Define directories
TEST_DATA_DIR = Path("test_data")
TEST_DATA_DIR.mkdir(exist_ok=True)

def random_string(length=10):
    """Generate a random string of fixed length"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def generate_window_test_data(num_test_cases=5):
    """Generate test data for window tests"""
    test_cases = []
    
    # Basic test cases
    test_cases.append({
        "name": "basic_window_test",
        "windows_to_open": 1,
        "expected_title": "The Internet",
        "expected_heading": "New Window",
        "timeout": 10,
        "strict_validation": False
    })
    
    test_cases.append({
        "name": "multiple_windows_test",
        "windows_to_open": 3,
        "expected_title": "The Internet",
        "expected_heading": "New Window", 
        "timeout": 5,
        "strict_validation": True
    })
    
    # Generate additional random test cases
    for i in range(num_test_cases - 2):
        test_cases.append({
            "name": f"random_window_test_{i+1}",
            "windows_to_open": random.randint(1, 5),
            "expected_title": random.choice(["The Internet", "New Window", "Windows"]),
            "expected_heading": random.choice(["New Window", "Opening a new window"]),
            "timeout": random.randint(3, 10),
            "strict_validation": random.choice([True, False])
        })
    
    return test_cases

def generate_login_test_data(num_test_cases=10):
    """Generate test data for login tests"""
    valid_credentials = {
        "username": "tomsmith", 
        "password": "SuperSecretPassword!"
    }
    
    test_cases = []
    
    # Valid credentials
    test_cases.append({
        "name": "valid_login",
        "username": valid_credentials["username"],
        "password": valid_credentials["password"],
        "expected_result": "success",
        "expected_message": "You logged into a secure area"
    })
    
    # Invalid username
    test_cases.append({
        "name": "invalid_username",
        "username": "invalid_user",
        "password": valid_credentials["password"],
        "expected_result": "failure",
        "expected_message": "Your username is invalid"
    })
    
    # Invalid password
    test_cases.append({
        "name": "invalid_password",
        "username": valid_credentials["username"],
        "password": "wrong_password",
        "expected_result": "failure",
        "expected_message": "Your password is invalid"
    })
    
    # Empty fields
    test_cases.append({
        "name": "empty_credentials",
        "username": "",
        "password": "",
        "expected_result": "failure",
        "expected_message": "Your username is invalid"
    })
    
    # Generate additional random test cases
    for i in range(num_test_cases - 4):
        # Random invalid credentials
        username = random_string() if random.random() > 0.2 else ""
        password = random_string() if random.random() > 0.2 else ""
        
        # Determine expected result
        if username == valid_credentials["username"] and password == valid_credentials["password"]:
            expected_result = "success"
            expected_message = "You logged into a secure area"
        elif username != valid_credentials["username"]:
            expected_result = "failure"
            expected_message = "Your username is invalid"
        else:
            expected_result = "failure"
            expected_message = "Your password is invalid"
        
        test_cases.append({
            "name": f"random_login_test_{i+1}",
            "username": username,
            "password": password,
            "expected_result": expected_result,
            "expected_message": expected_message
        })
    
    return test_cases

def generate_dropdown_test_data():
    """Generate test data for dropdown tests"""
    return [
        {"name": "select_by_text", "method": "text", "value": "Option 1", "expected": "Option 1"},
        {"name": "select_by_value", "method": "value", "value": "2", "expected": "Option 2"},
        {"name": "select_by_index", "method": "index", "value": 1, "expected": "Option 1"},
        {"name": "select_by_js", "method": "js", "value": "2", "expected": "Option 2"}
    ]

def create_test_config():
    """Create test configuration file"""
    config = {
        "browser": {
            "headless": False,
            "window_size": {
                "width": 1920,
                "height": 1080
            },
            "implicit_wait": 5,
            "page_load_timeout": 30
        },
        "urls": {
            "base_url": "https://the-internet.herokuapp.com",
            "login_page": "/login",
            "windows_page": "/windows",
            "dynamic_content_page": "/dynamic_content"
        },
        "test_data": {
            "username": "tomsmith",
            "password": "SuperSecretPassword!",
            "invalid_credentials": [
                {"username": "invalid", "password": "invalid"},
                {"username": "tomsmith", "password": "wrongpassword"},
                {"username": "admin", "password": "admin"}
            ]
        },
        "test_settings": {
            "screenshot_on_failure": True,
            "retry_count": 2,
            "window_test": {
                "windows_to_open": 3,
                "expected_titles": ["The Internet", "New Window"],
                "expected_texts": ["New Window", "Opening a new window"],
                "validation_timeout": 5
            }
        },
        "generated_on": datetime.now().isoformat()
    }
    
    with open(TEST_DATA_DIR / "test_config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    with open(TEST_DATA_DIR / "test_config.yaml", 'w') as f:
        yaml.dump(config, f)
    
    print(f"Created test configuration: {TEST_DATA_DIR / 'test_config.json'}")
    print(f"Created test configuration: {TEST_DATA_DIR / 'test_config.yaml'}")

def main():
    parser = argparse.ArgumentParser(description='Generate test data for data-driven tests')
    parser.add_argument('--window-tests', type=int, default=5, 
                        help='Number of window test cases to generate')
    parser.add_argument('--login-tests', type=int, default=10, 
                        help='Number of login test cases to generate')
    parser.add_argument('--format', choices=['json', 'yaml', 'csv', 'all'], default='all',
                        help='File format for test data')
    parser.add_argument('--output-dir', default=str(TEST_DATA_DIR),
                        help='Directory to store generated test data')
    
    args = parser.parse_args()
    
    # Use specified output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Create test configuration
    create_test_config()
    
    # Generate window test data
    window_test_data = generate_window_test_data(args.window_tests)
    
    # Generate login test data
    login_test_data = generate_login_test_data(args.login_tests)
    
    # Generate dropdown test data
    dropdown_test_data = generate_dropdown_test_data()
    
    # Save test data in the specified format(s)
    if args.format in ['json', 'all']:
        with open(output_dir / "window_test_data.json", 'w') as f:
            json.dump(window_test_data, f, indent=2)
        with open(output_dir / "login_test_data.json", 'w') as f:
            json.dump(login_test_data, f, indent=2)
        with open(output_dir / "dropdown_test_data.json", 'w') as f:
            json.dump(dropdown_test_data, f, indent=2)
        print(f"Created JSON test data in {output_dir}")
    
    if args.format in ['yaml', 'all']:
        with open(output_dir / "window_test_data.yaml", 'w') as f:
            yaml.dump(window_test_data, f)
        with open(output_dir / "login_test_data.yaml", 'w') as f:
            yaml.dump(login_test_data, f)
        with open(output_dir / "dropdown_test_data.yaml", 'w') as f:
            yaml.dump(dropdown_test_data, f)
        print(f"Created YAML test data in {output_dir}")
    
    if args.format in ['csv', 'all']:
        # Save login test data as CSV
        if login_test_data:
            with open(output_dir / "login_test_data.csv", 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=login_test_data[0].keys())
                writer.writeheader()
                writer.writerows(login_test_data)
            print(f"Created login CSV test data in {output_dir}")
        
        # Save dropdown test data as CSV
        if dropdown_test_data:
            with open(output_dir / "dropdown_test_data.csv", 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=dropdown_test_data[0].keys())
                writer.writeheader()
                writer.writerows(dropdown_test_data)
            print(f"Created dropdown CSV test data in {output_dir}")
            
        # Window test data might be more complex for CSV, but we'll try
        if window_test_data:
            with open(output_dir / "window_test_data.csv", 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=window_test_data[0].keys())
                writer.writeheader()
                writer.writerows(window_test_data)
            print(f"Created window CSV test data in {output_dir}")
    
    print("Test data generation complete!")

if __name__ == '__main__':
    main()
