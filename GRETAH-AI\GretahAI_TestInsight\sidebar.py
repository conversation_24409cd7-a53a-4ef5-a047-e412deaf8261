"""
Renders the Administration section within a collapsible expander in the Streamlit sidebar.
This section provides functionality for resetting the application's data.
It includes options to reset only the database or perform a complete
application reset, which deletes the database, logs, screenshots, output files,
test results, and clears the session state. Both actions require an admin password.
Args:
    DATABASE_PATH (str): The file path to the SQLite database.
    SCREENSHOTS_DIR (str): The directory where screenshots are stored.
    LOGS_DIR (str): The directory where log files are stored.
    debug_output_dir (str): The directory for debug output files.
    raw_output_dir (str): The directory for raw output files.
    clear_database_func (callable): A function that takes the database path
        and clears all tables. Should return True on success, False otherwise.
    close_connection_func (callable): A function to close the database connection.
"""
"""
Renders the main sidebar for the Streamlit application.
Includes the application logo, title, main navigation between 'Execution'
and 'Analysis' sections, an expandable 'Analysis Configuration' section
(for selecting AI engine type, specific model, and managing API keys/config path),
and an expandable 'Administration' section (rendered by `render_admin_section`).
Args:
    DATABASE_PATH (str): The file path to the SQLite database.
    SCREENSHOTS_DIR (str): The directory where screenshots are stored.
    LOGS_DIR (str): The directory where log files are stored.
    debug_output_dir (str): The directory for debug output files.
    raw_output_dir (str): The directory for raw output files.
    clear_database_func (callable): Function passed to `render_admin_section`
        for database clearing.
    close_connection_func (callable): Function passed to `render_admin_section`
        and used locally to close the database connection after operations.
Returns:
    str: The selected section from the main navigation ('Execution' or 'Analysis').
"""


import streamlit as st
import os
import glob
import time
import json

from helper import OFFLINE_MODELS, ONLINE_MODELS, read_api_key_from_config
from sql_lite_db_helpers import get_config_path, update_config_path

# --- Define Logo URL ---
LOGO_URL = "../assets/images/Cogniron_logo.png"
# --- End Define Logo URL ---


def render_admin_section(
    DATABASE_PATH,
    SCREENSHOTS_DIR,
    LOGS_DIR,
    debug_output_dir,
    raw_output_dir,
    clear_database_func,
    close_connection_func
):
    """Renders the Administration section in a collapsed expander."""
    admin_exp = st.sidebar.expander("Administration", expanded=False)

    with admin_exp:
        # Add custom CSS to prevent password field conflicts
        st.markdown("""
        <style>
        .stTextInput > div > div > input[type="password"] {
            position: relative;
            z-index: 1;
        }
        .stTextInput > div {
            margin-bottom: 1rem;
        }
        </style>
        """, unsafe_allow_html=True)
        
        st.markdown("#### Database Management")
        st.warning("⚠️ Caution: This action permanently deletes all test data.")

        # --- Reset Database ---
        password = st.text_input("Admin Password (Database Reset):", type="password", key="db_clear_password")
        if st.button("Reset Database", key="clear_db_button"):
            if password == "password":
                with st.spinner("Resetting database..."):
                    success = clear_database_func(DATABASE_PATH)
                    close_connection_func()
                if success:
                    st.success("✅ Database reset successfully.")
                    st.session_state.pop('test_entries', None)
                    st.session_state.pop('selected_run_timestamp', None)
                    st.rerun()
                else:
                    st.error("❌ Database reset failed. Check logs.")
            elif password:
                st.error("❌ Invalid admin password.")
            else:
                st.warning("⚠️ Admin password required for database reset.")

        st.markdown("---")
        st.markdown("") # Add extra spacing

        # --- Complete Application Reset ---
        st.error("⚠️ **COMPLETE APPLICATION RESET**")
        st.warning("This will delete ALL data: database, logs, screenshots, and results.")

        # Use columns for better layout
        col1, col2 = st.columns([2, 1])
        
        with col1:
            confirm_text = st.text_input(
                "Type 'RESET ALL' to confirm complete reset:", 
                key="confirm_reset_text"
            )
        
        with col2:
            st.write("") # Empty space for alignment
        
        admin_password = st.text_input(
            "Admin Password (Complete Reset):", 
            type="password", 
            key="admin_complete_reset_password"
        )

        if st.button("🧹 Reset Everything", key="complete_reset_button"):
            if confirm_text == "RESET ALL" and admin_password == "password":
                with st.spinner("Performing complete application reset..."):
                    # 1) Clear DB
                    db_success = clear_database_func(DATABASE_PATH)
                    close_connection_func()

                    # 2) Delete screenshots
                    screenshot_count = 0
                    for f in glob.glob(os.path.join(SCREENSHOTS_DIR, "*")):
                        try:
                            os.remove(f)
                            screenshot_count += 1
                        except Exception as e:
                            st.warning(f"Could not delete {f}: {e}")

                    # 3) Delete logs
                    logs_count = 0
                    for f in glob.glob(os.path.join(LOGS_DIR, "**/*"), recursive=True):
                        if os.path.isfile(f):
                            try:
                                os.remove(f)
                                logs_count += 1
                            except Exception as e:
                                st.warning(f"Could not delete {f}: {e}")

                    # 4) Delete debug/raw outputs
                    output_count = 0
                    for d in (debug_output_dir, raw_output_dir):
                        for f in glob.glob(os.path.join(d, "*")):
                            try:
                                os.remove(f)
                                output_count += 1
                            except Exception as e:
                                st.warning(f"Could not delete {f}: {e}")

                    # 5) Delete any pytest XMLs in cwd
                    result_count = 0
                    for f in glob.glob(os.path.join(os.getcwd(), "results_*.xml")):
                        try:
                            os.remove(f)
                            result_count += 1
                        except Exception as e:
                            st.warning(f"Could not delete {f}: {e}")

                    # 6) Clear session state
                    for key in [
                        'test_entries', 
                        'selected_run_timestamp', 
                        'process', 
                        'current_test_run'
                    ]:
                        st.session_state.pop(key, None)
                    st.session_state.process_finished_flag = False

                    # Show summary
                    st.success(
                        "✅ Application reset complete:\n"
                        f"- Database cleared: {'Yes' if db_success else 'No'}\n"
                        f"- {screenshot_count} screenshots deleted\n"
                        f"- {logs_count} log files deleted\n"
                        f"- {output_count} debug/raw output files deleted\n"
                        f"- {result_count} result files deleted\n"
                        f"- Session state reset"
                    )
                    st.balloons()
                    time.sleep(1)
                    st.rerun()
            else:
                st.error("❌ Confirmation text or password incorrect.")


def render_sidebar(
    DATABASE_PATH,
    SCREENSHOTS_DIR,
    LOGS_DIR,
    debug_output_dir,
    raw_output_dir,
    clear_database_func,
    close_connection_func
):
    """Renders the full sidebar with logo, navigation, analysis config, and admin."""
    # Logo
    st.sidebar.image(LOGO_URL, width=250)

    # Title
    st.sidebar.markdown('<h1 class="sidebar-header">Gretah AI TestInsight Pro</h1>', unsafe_allow_html=True)

    # Main nav
    st.sidebar.markdown("## Navigation")
    selected_section = st.sidebar.radio(
        "Go to", ("Execution", "Analysis"),
        key="main_nav",
        label_visibility="collapsed"
    )

    # Analysis Configuration
    st.sidebar.markdown("---")
    with st.sidebar.expander("Analysis Configuration", expanded=False):
        # Determine the selected model type from the radio button
        selected_model_type_display = st.radio(
            "AI Engine", ["Local", "Cloud"],
            index=0 if st.session_state.get("model_type", "Offline") == "Offline" else 1,
            key="analysis_model_type_sidebar"
        )
        current_model_type = "Offline" if selected_model_type_display == "Local" else "Online"

        # Check if the model type changed or if the current model is invalid for the selected type
        previous_model_type = st.session_state.get("model_type")
        current_model = st.session_state.get("model")
        model_needs_reset = False

        if current_model_type != previous_model_type:
            model_needs_reset = True
        elif current_model_type == "Offline" and current_model not in OFFLINE_MODELS:
             model_needs_reset = True
        elif current_model_type == "Online" and current_model not in ONLINE_MODELS:
             model_needs_reset = True

        # Reset the model if necessary
        if model_needs_reset:
            if current_model_type == "Offline":
                st.session_state.model = OFFLINE_MODELS[0] if OFFLINE_MODELS else None
            else: # Online
                st.session_state.model = ONLINE_MODELS[0] if ONLINE_MODELS else None
            st.warning(f"Model reset to default for {current_model_type} engine.") # Optional: Inform user

        # Update the model_type in session state *after* potential model reset
        st.session_state['model_type'] = current_model_type

        # Now render the selectbox using the potentially updated model state
        if st.session_state['model_type'] == "Offline":
            opts = OFFLINE_MODELS
            # Ensure the current model exists in opts before finding index
            current_model_in_state = st.session_state.get("model", opts[0] if opts else None)
            if current_model_in_state not in opts and opts:
                st.session_state.model = opts[0] # Fallback if still invalid somehow
                current_model_in_state = opts[0]

            idx = opts.index(current_model_in_state) if opts and current_model_in_state in opts else 0
            sel = st.selectbox("Select Local Model", opts, index=idx, key="offline_model_sidebar")
            st.session_state.model = sel
            st.session_state.api_key = None # Ensure API key is cleared for local
        else: # Online
            opts = ONLINE_MODELS
            # Ensure the current model exists in opts before finding index
            current_model_in_state = st.session_state.get("model", opts[0] if opts else None)
            if current_model_in_state not in opts and opts:
                st.session_state.model = opts[0] # Fallback if still invalid somehow
                current_model_in_state = opts[0]

            idx = opts.index(current_model_in_state) if opts and current_model_in_state in opts else 0
            sel = st.selectbox("Select Cloud Model", opts, index=idx, key="online_model_sidebar")
            st.session_state.model = sel

            # API config
            config_path = get_config_path(DATABASE_PATH)
            close_connection_func()
            st.subheader("API Configuration")
            st.caption(f"Config path: `{config_path}`")
            st.markdown("##### Update/Test Config Path")

            new_path = st.text_input("New config.json path:", value=config_path or "", key="new_config_path_sidebar")
            c1, c2 = st.columns(2)
            with c1:
                if st.button("Update Path", key="update_config_path_sidebar"):
                    if new_path and os.path.isdir(os.path.dirname(new_path)):
                        ok = update_config_path(DATABASE_PATH, new_path)
                        close_connection_func()
                        st.success("Config path updated." if ok else "Failed to update config path.")
                        st.rerun()
                    else:
                        st.error("Invalid directory for config path.")
            with c2:
                if st.button("Test Path", key="test_config_path_sidebar"):
                    if os.path.exists(new_path):
                        try:
                            data = json.load(open(new_path))
                            if 'google_api_key' in data:
                                st.success("Config valid! Google API key found.")
                            else:
                                st.warning("No 'google_api_key' in config.")
                        except json.JSONDecodeError:
                            st.error("Invalid JSON.")
                    else:
                        st.error("Path not found.")

            # load or prompt for key
            api_key = read_api_key_from_config(config_path)
            if api_key:
                st.session_state.api_key = api_key
                st.success("API key loaded from config.")
            else:
                st.session_state.api_key = None
                st.warning("No Google API key in config.")
                temp = st.text_input(
                    "Enter API Key temporarily", type="password", key="api_key_input_sidebar",
                    help="Use config.json for persistence."
                )
                if temp:
                    st.session_state.api_key = temp
                    st.success("Temporary API key set.")
                else:
                    st.error("API key required for cloud model.")

    # Administration Section (collapsed by default)
    render_admin_section(
        DATABASE_PATH,
        SCREENSHOTS_DIR,
        LOGS_DIR,
        debug_output_dir,
        raw_output_dir,
        clear_database_func,
        close_connection_func
    )

    return selected_section
