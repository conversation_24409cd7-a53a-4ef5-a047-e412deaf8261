"""
Optimization Prevention Components for GretahAI ScriptWeaver

This module provides UI components to prevent accidental re-optimization of 
already processed test cases by implementing confirmation dialogs and visual 
indicators in the test case selection interface.

Key Features:
- Confirmation dialog for re-processing optimized test cases
- Visual indicators for optimization status
- Session state management for user choices
- Integration with existing workflow stages
"""

import streamlit as st
from typing import Dict, Any, Optional, Tuple
from debug_utils import debug


def show_optimization_confirmation_dialog(test_case: Dict[str, Any]) -> Tuple[bool, bool]:
    """
    Show confirmation dialog when user selects an already optimized test case.
    
    Args:
        test_case: Test case dictionary with optimization status
        
    Returns:
        Tuple of (should_proceed, dialog_closed)
    """
    test_case_id = test_case.get('Test Case ID', 'Unknown')
    optimization_status = test_case.get('optimization_status', 'none')
    
    # Create unique key for this test case confirmation
    dialog_key = f"optimization_confirmation_{test_case_id}"
    proceed_key = f"proceed_reoptimization_{test_case_id}"
    cancel_key = f"cancel_reoptimization_{test_case_id}"
    
    # Check if dialog should be shown
    if optimization_status != 'optimized':
        return True, True  # No confirmation needed for non-optimized test cases
    
    # Show confirmation dialog
    st.warning("⚠️ **Re-optimization Confirmation Required**")
    
    with st.container():
        st.markdown(f"""
        **Test Case**: `{test_case_id}`
        
        This test case has already completed the full optimization pipeline. 
        Re-processing will:
        
        - ✅ Generate new scripts and overwrite existing ones
        - ✅ Create new optimization results
        - ⚠️ Previous optimization work will be preserved in script history
        - ⏱️ Consume additional API resources and processing time
        
        **Are you sure you want to re-process this test case?**
        """)
        
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col1:
            proceed_clicked = st.button(
                "🔄 Yes, Re-process",
                key=proceed_key,
                type="primary",
                help="Continue with re-processing this optimized test case"
            )
        
        with col2:
            cancel_clicked = st.button(
                "❌ Cancel",
                key=cancel_key,
                help="Cancel and select a different test case"
            )
        
        with col3:
            st.button(
                "ℹ️ View History",
                key=f"view_history_{test_case_id}",
                help="View optimization history for this test case",
                on_click=lambda tc_id=test_case_id: _show_optimization_history(tc_id)
            )
    
    if proceed_clicked:
        debug(f"User confirmed re-optimization for test case {test_case_id}",
              stage="optimization_prevention", operation="reoptimization_confirmed",
              context={"test_case_id": test_case_id})
        return True, True
    
    if cancel_clicked:
        debug(f"User cancelled re-optimization for test case {test_case_id}",
              stage="optimization_prevention", operation="reoptimization_cancelled",
              context={"test_case_id": test_case_id})
        return False, True
    
    # Dialog still open, waiting for user input
    return False, False


def _show_optimization_history(test_case_id: str):
    """
    Show optimization history for a test case in an expander.
    
    Args:
        test_case_id: Test case ID to show history for
    """
    # Store the request to show history in session state
    if 'show_optimization_history' not in st.session_state:
        st.session_state.show_optimization_history = {}
    
    st.session_state.show_optimization_history[test_case_id] = True


def render_optimization_history_expander(test_case_id: str, state):
    """
    Render optimization history expander if requested.
    
    Args:
        test_case_id: Test case ID
        state: StateManager instance
    """
    if ('show_optimization_history' in st.session_state and 
        test_case_id in st.session_state.show_optimization_history and
        st.session_state.show_optimization_history[test_case_id]):
        
        with st.expander(f"📊 Optimization History - {test_case_id}", expanded=True):
            try:
                # Initialize script storage if needed
                if not hasattr(state, '_script_storage') or state._script_storage is None:
                    state._init_script_storage()
                
                # Get scripts for this test case
                test_case_scripts = state._script_storage.get_scripts_by_test_case(test_case_id)
                
                if not test_case_scripts:
                    st.info("No optimization history found for this test case.")
                    return
                
                # Group scripts by type
                script_types = {}
                for script in test_case_scripts:
                    script_type = script.get('type', 'unknown')
                    if script_type not in script_types:
                        script_types[script_type] = []
                    script_types[script_type].append(script)
                
                # Display script history
                for script_type, scripts in script_types.items():
                    st.markdown(f"**{script_type.title()} Scripts ({len(scripts)})**")
                    
                    for i, script in enumerate(scripts):
                        timestamp = script.get('timestamp', 'Unknown')
                        file_path = script.get('file_path', 'N/A')
                        optimization_status = script.get('optimization_status', 'unknown')
                        
                        st.markdown(f"""
                        - **Script {i+1}**: {timestamp}
                          - File: `{file_path}`
                          - Status: `{optimization_status}`
                        """)
                
            except Exception as e:
                st.error(f"Error loading optimization history: {str(e)}")
                debug(f"Error loading optimization history for {test_case_id}: {e}",
                      stage="optimization_prevention", operation="history_load_error",
                      context={"test_case_id": test_case_id, "error": str(e)})
        
        # Clear the history request after showing
        st.session_state.show_optimization_history[test_case_id] = False


def get_optimization_status_badge(optimization_status: str) -> str:
    """
    Get a visual badge for optimization status.
    
    Args:
        optimization_status: Status string ('optimized', 'partial', 'none')
        
    Returns:
        HTML badge string
    """
    badges = {
        'optimized': '<span style="background-color: #28a745; color: #ffffff; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;" aria-label="Status: Optimized">✅ OPTIMIZED</span>',
        'partial': '<span style="background-color: #ffc107; color: #000000; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;" aria-label="Status: Partially Optimized">⚠️ PARTIAL</span>',
        'none': '<span style="background-color: #6c757d; color: #ffffff; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;" aria-label="Status: New">⭕ NEW</span>'
    }
    
    return badges.get(optimization_status, badges['none'])


def should_show_confirmation_for_test_case(test_case: Dict[str, Any]) -> bool:
    """
    Determine if confirmation dialog should be shown for a test case.
    
    Args:
        test_case: Test case dictionary
        
    Returns:
        bool: True if confirmation is needed
    """
    return test_case.get('is_optimized', False)


def clear_optimization_confirmation_state():
    """
    Clear all optimization confirmation related session state.
    """
    keys_to_clear = [key for key in st.session_state.keys() 
                     if key.startswith(('optimization_confirmation_', 
                                       'proceed_reoptimization_', 
                                       'cancel_reoptimization_'))]
    
    for key in keys_to_clear:
        del st.session_state[key]
    
    if 'show_optimization_history' in st.session_state:
        st.session_state.show_optimization_history.clear()
    
    debug("Cleared optimization confirmation session state",
          stage="optimization_prevention", operation="state_cleared",
          context={"cleared_keys": len(keys_to_clear)})
