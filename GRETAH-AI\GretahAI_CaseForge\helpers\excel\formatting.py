"""
Excel Formatting Module for GretahAI CaseForge

This module provides Excel file creation, formatting, and styling capabilities
for test case documentation and reports.

Functions:
- create_formatted_excel_from_scenarios: Create formatted Excel files from test scenarios
- save_scenarios_to_excel: Save test scenarios to Excel format

© 2025 GretahAI Team
"""

import pandas as pd
import os
import json
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.views import Pane
from typing import Union, Optional
import streamlit as st


def create_formatted_excel_from_scenarios(
    response_text_or_df, 
    output_file_path: str, 
    issue=None, 
    is_dataframe=False, 
    start_id_from=None, 
    continue_numbering=False, 
    save_to_db=True, 
    test_type=None, 
    create_excel=False
) -> None:
    """
    Creates professionally formatted Excel files from test scenarios with advanced styling.
    
    This function is the primary Excel generation engine, creating beautifully formatted
    test case documents with professional styling, proper data organization, and 
    comprehensive metadata. It supports both JSON input and DataFrame input sources.

    Args:
        response_text_or_df (str or pd.DataFrame): Either raw AI response text containing
                                                  JSON test scenarios, or pre-processed DataFrame
        output_file_path (str): Full path where the Excel file should be saved
        issue (jira.Issue, optional): JIRA issue object for metadata extraction. Defaults to None.
        is_dataframe (bool, optional): Whether input is DataFrame (True) or text (False). 
                                      Defaults to False.
        start_id_from (int, optional): Starting number for test case ID generation. 
                                      Defaults to None.
        continue_numbering (bool, optional): Whether to continue from existing numbering.
                                           Defaults to False.
        save_to_db (bool, optional): Whether to save test cases to database. Defaults to True.
        test_type (str, optional): Test type for classification and database storage. 
                                  Defaults to None.
        create_excel (bool, optional): Whether to actually create Excel file. Defaults to False.

    Returns:
        None: Function creates Excel file as side effect
    """
    try:
        # Parse scenarios and create DataFrame
        if is_dataframe:
            df = response_text_or_df  # Use the DataFrame directly
        else:
            try:                # Parse from response text
                from helpers.data import parse_test_scenarios_json
                df = parse_test_scenarios_json(response_text_or_df, issue, start_id_from=start_id_from, continue_numbering=continue_numbering, dashboard_test_type=test_type)
            except ValueError as e:
                # If parsing fails, create a minimal dataframe with error message and standard columns
                print(f"Error parsing test scenarios: {str(e)}")

                # Create a DataFrame with both error info and standard columns to maintain compatibility
                current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                df = pd.DataFrame({
                    "Timestamp": [current_timestamp],  # Add timestamp as first column
                    "Project": ["ERROR"],
                    "Feature": [""],
                    "User Story ID": ["ERROR"],  # Add User Story ID column as string
                    "Test Case ID": ["ERROR-1"],
                    "Test Case Objective": ["Failed to parse AI response"],
                    "Prerequisite": [""],
                    "Step No": [1],
                    "Test Steps": ["Check the AI response for formatting issues"],
                    "Expected Result": ["Properly formatted JSON response"],
                    "Actual Result": [""],
                    "Test Status": ["Failed"],
                    "Priority": ["High"],
                    "Defect ID": [""],
                    "Comments": [str(e)],
                    "Test Type": [""],
                    "Test Group": [""],
                    "Error": ["Failed to parse AI response as JSON. See raw output for details."],
                    "Raw Response Preview": [response_text_or_df[:500] + "..." if len(response_text_or_df) > 500 else response_text_or_df]
                })

        # Save to database if requested and we have a valid issue
        if save_to_db and issue and not df.empty and "Test Case ID" in df.columns:
            try:
                from db_helper import save_test_cases_to_database, DATABASE_PATH
                # Extract the test type from the output file path if not provided
                if not test_type and output_file_path:
                    file_name = os.path.basename(output_file_path)
                    parts = file_name.split('_')
                    if len(parts) >= 4:
                        # Check if it's an ALL file with a specific test type
                        if "ALL" in parts and len(parts) >= 5:
                            # Format: Latest_testcases_TP-1_ALL_positive_timestamp.xlsx
                            test_type = parts[4]
                        else:
                            # Format: Latest_testcases_TP-1_positive_timestamp.xlsx
                            test_type = parts[3]

                # If we still don't have a test type, default to "all"
                if not test_type:
                    test_type = "all"                # Save to database
                print(f"Saving test cases to database for {issue.key} with test type {test_type}...")
                save_test_cases_to_database(DATABASE_PATH, issue.key, df, test_type, user_name="system")
                print(f"Test cases saved to database successfully.")
            except Exception as db_error:
                print(f"Error saving test cases to database: {str(db_error)}")
                if create_excel:
                    print("Continuing with Excel file creation...")
                else:
                    print("Skipping Excel file creation as requested.")
                    return

        # Skip Excel file creation if not requested
        if not create_excel:
            print(f"Skipping Excel file creation as create_excel=False")
            return

        # Ensure the output directory exists before creating Excel file
        output_dir = os.path.dirname(output_file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # Create Excel file
        # Create a new workbook and select the active sheets
        wb = Workbook()
        ws = wb.active

        # Define styles
        header_font = Font(name='Calibri', size=11, bold=True)
        data_font = Font(name='Calibri', size=11)
        header_fill = PatternFill(start_color='B3E5FC', end_color='B3E5FC', fill_type='solid')  # Light blue
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

        # Define border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Add header row with formatting
        for c, column_name in enumerate(df.columns, start=1):
            cell = ws.cell(row=1, column=c, value=column_name)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment
            cell.border = thin_border

        # Write DataFrame to worksheet with formatting
        for r, (_, row) in enumerate(df.iterrows(), start=2):
            for c, value in enumerate(row.values, start=1):
                cell = ws.cell(row=r, column=c, value=value)
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = thin_border

        # Re-apply formatting to all cells to ensure it's not lost during merging
        for row in ws.iter_rows(min_row=2):  # Skip header row
            for cell in row:
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = thin_border

        # Adjust column widths based on the longest cell in each column
        for col in ws.columns:
            max_length = 0
            col_letter = get_column_letter(col[0].column)
            for cell in col:
                try:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Add padding, cap at 50
            ws.column_dimensions[col_letter].width = adjusted_width

        # Freeze the header row (row 1) so it remains visible when scrolling
        # Create a sheet view with a frozen pane
        ws.sheet_view.pane = Pane(ySplit=1, state='frozen', activePane='bottomLeft', topLeftCell='A2')
        ws.sheet_view.selection[0].activeCell = 'A2'
        ws.sheet_view.selection[0].sqref = 'A2'

        # Save the workbook
        wb.save(output_file_path)
        print(f"Formatted Excel file saved to: {output_file_path}")
    except Exception as e:
        print(f"Error creating Excel file: {str(e)}")


def save_scenarios_to_excel(parsed_data, filename="test_scenarios.xlsx"):
    """
    Saves parsed test scenario data to a basic Excel file format.
    
    This function provides a simplified Excel export for test scenarios without
    advanced formatting. It's useful for quick exports and data interchange.

    Args:
        parsed_data (list): List of dictionaries containing test scenario data
        filename (str, optional): Output filename. Defaults to "test_scenarios.xlsx".

    Returns:
        bool: True if save was successful, False otherwise

    Features:
        - Basic Excel format without advanced styling
        - Quick export for data sharing
        - Lightweight alternative to formatted export
        - Compatible with standard spreadsheet applications

    Data Structure Expected:
        Each item in parsed_data should be a dictionary with keys:
        - scenario_name, type, prerequisites, objective, steps, etc.

    Error Handling:
        - Returns False on file save errors
        - Handles data format issues gracefully
        - Manages file permission problems

    Use Cases:
        - Quick data exports
        - Integration with external tools
        - Backup and data interchange
        - Development and testing workflows

    Example:
        success = save_scenarios_to_excel(
            test_scenarios,
            "quick_export.xlsx"
        )
        if success:
            print("Export completed successfully")
    """
    try:
        # Convert to DataFrame
        df = pd.DataFrame(parsed_data)
        
        # Save to Excel
        df.to_excel(filename, index=False)
        return True
        
    except Exception as e:
        print(f"Error saving to Excel: {e}")
        return False
