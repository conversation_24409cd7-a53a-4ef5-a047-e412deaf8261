#!/usr/bin/env python3
"""
GretahAI ScriptWeaver Cleanup Script

This script safely removes empty folders, temporary files, cache files, and other
unnecessary files to clean up the codebase while preserving important data.

© 2025 Cogniron All Rights Reserved.
"""

import os
import shutil
import glob
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Base directory
BASE_DIR = Path(__file__).parent

def cleanup_pycache():
    """Remove all __pycache__ directories and .pyc files."""
    logger.info("Cleaning up Python cache files...")
    
    # Remove __pycache__ directories
    pycache_dirs = list(BASE_DIR.rglob("__pycache__"))
    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            logger.info(f"Removed: {pycache_dir}")
        except Exception as e:
            logger.error(f"Error removing {pycache_dir}: {e}")
    
    # Remove .pyc files
    pyc_files = list(BASE_DIR.rglob("*.pyc"))
    for pyc_file in pyc_files:
        try:
            pyc_file.unlink()
            logger.info(f"Removed: {pyc_file}")
        except Exception as e:
            logger.error(f"Error removing {pyc_file}: {e}")

def cleanup_temporary_files():
    """Remove temporary files and test artifacts."""
    logger.info("Cleaning up temporary files...")
    
    # Patterns for temporary files to remove
    temp_patterns = [
        "temp_*.py",
        "test_*.xml",
        "results_*.xml",
        "stage10_*.xml",
        "template_generated_*.py",
        "*.log",
        "*.tmp",
        "*.temp"
    ]
    
    for pattern in temp_patterns:
        files = list(BASE_DIR.glob(pattern))
        for file in files:
            try:
                file.unlink()
                logger.info(f"Removed temporary file: {file}")
            except Exception as e:
                logger.error(f"Error removing {file}: {e}")

def cleanup_old_screenshots(days_old=30):
    """Remove old screenshot files."""
    logger.info(f"Cleaning up screenshots older than {days_old} days...")
    
    screenshots_dir = BASE_DIR / "screenshots"
    if not screenshots_dir.exists():
        return
    
    cutoff_date = datetime.now() - timedelta(days=days_old)
    
    for screenshot in screenshots_dir.glob("*.png"):
        try:
            file_time = datetime.fromtimestamp(screenshot.stat().st_mtime)
            if file_time < cutoff_date:
                screenshot.unlink()
                logger.info(f"Removed old screenshot: {screenshot}")
        except Exception as e:
            logger.error(f"Error removing {screenshot}: {e}")

def cleanup_old_generated_tests(days_old=30):
    """Remove old generated test files."""
    logger.info(f"Cleaning up generated tests older than {days_old} days...")
    
    generated_tests_dir = BASE_DIR / "generated_tests"
    if not generated_tests_dir.exists():
        return
    
    cutoff_date = datetime.now() - timedelta(days=days_old)
    
    for test_file in generated_tests_dir.glob("*.py"):
        try:
            file_time = datetime.fromtimestamp(test_file.stat().st_mtime)
            if file_time < cutoff_date:
                test_file.unlink()
                logger.info(f"Removed old test file: {test_file}")
        except Exception as e:
            logger.error(f"Error removing {test_file}: {e}")

def cleanup_old_ai_logs(days_old=30):
    """Remove old AI log files."""
    logger.info(f"Cleaning up AI logs older than {days_old} days...")
    
    ai_logs_dir = BASE_DIR / "ai_logs"
    if not ai_logs_dir.exists():
        return
    
    cutoff_date = datetime.now() - timedelta(days=days_old)
    
    for log_subdir in ["requests", "errors", "metrics"]:
        log_dir = ai_logs_dir / log_subdir
        if not log_dir.exists():
            continue
            
        for log_file in log_dir.glob("*"):
            try:
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    log_file.unlink()
                    logger.info(f"Removed old log file: {log_file}")
            except Exception as e:
                logger.error(f"Error removing {log_file}: {e}")

def cleanup_database_temp_files():
    """Remove SQLite temporary files."""
    logger.info("Cleaning up database temporary files...")
    
    db_temp_patterns = ["*.db-shm", "*.db-wal"]
    
    for pattern in db_temp_patterns:
        files = list(BASE_DIR.glob(pattern))
        for file in files:
            try:
                file.unlink()
                logger.info(f"Removed database temp file: {file}")
            except Exception as e:
                logger.error(f"Error removing {file}: {e}")

def cleanup_empty_directories():
    """Remove empty directories."""
    logger.info("Cleaning up empty directories...")
    
    # Get all directories, sorted by depth (deepest first)
    all_dirs = [d for d in BASE_DIR.rglob("*") if d.is_dir()]
    all_dirs.sort(key=lambda x: len(x.parts), reverse=True)
    
    for directory in all_dirs:
        try:
            # Skip important directories even if empty
            skip_dirs = {
                "docs", "core", "stages", "ui_components", "utils", 
                "tests", "api", "ui", "debug_logs"
            }
            
            if directory.name in skip_dirs:
                continue
                
            # Check if directory is empty
            if directory.exists() and not any(directory.iterdir()):
                directory.rmdir()
                logger.info(f"Removed empty directory: {directory}")
        except Exception as e:
            logger.debug(f"Could not remove directory {directory}: {e}")

def get_cleanup_summary():
    """Get a summary of what will be cleaned up."""
    logger.info("Generating cleanup summary...")
    
    summary = {
        "pycache_dirs": len(list(BASE_DIR.rglob("__pycache__"))),
        "pyc_files": len(list(BASE_DIR.rglob("*.pyc"))),
        "temp_files": len(list(BASE_DIR.glob("temp_*.py")) + 
                         list(BASE_DIR.glob("test_*.xml")) + 
                         list(BASE_DIR.glob("results_*.xml"))),
        "db_temp_files": len(list(BASE_DIR.glob("*.db-shm")) + 
                            list(BASE_DIR.glob("*.db-wal"))),
        "screenshots": len(list((BASE_DIR / "screenshots").glob("*.png"))) if (BASE_DIR / "screenshots").exists() else 0,
        "generated_tests": len(list((BASE_DIR / "generated_tests").glob("*.py"))) if (BASE_DIR / "generated_tests").exists() else 0
    }
    
    return summary

def main():
    """Main cleanup function."""
    logger.info("Starting GretahAI ScriptWeaver cleanup...")
    
    # Get summary before cleanup
    before_summary = get_cleanup_summary()
    logger.info(f"Before cleanup: {before_summary}")
    
    try:
        # Perform cleanup operations
        cleanup_pycache()
        cleanup_temporary_files()
        cleanup_database_temp_files()
        cleanup_old_screenshots(days_old=30)
        cleanup_old_generated_tests(days_old=30)
        cleanup_old_ai_logs(days_old=30)
        cleanup_empty_directories()
        
        # Get summary after cleanup
        after_summary = get_cleanup_summary()
        logger.info(f"After cleanup: {after_summary}")
        
        logger.info("Cleanup completed successfully!")
        
    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
