"""
CSV Export functionality for GretahAI CaseForge

This module provides CSV export capabilities for test cases and related data.
Complements the existing Excel export functionality with CSV format support.

Functions:
- create_csv_from_dataframe: Export DataFrame to CSV format
- export_test_cases_to_csv: Export test cases with proper formatting

© 2025 GretahAI Team
"""

import os
import pandas as pd
from datetime import datetime
from pathlib import Path


def create_csv_from_dataframe(df, output_path, include_index=False):
    """
    Export a DataFrame to CSV format with proper formatting.
    
    Args:
        df (pd.DataFrame): DataFrame containing test case data
        output_path (str): Path where CSV file should be saved
        include_index (bool): Whether to include DataFrame index in CSV
        
    Returns:
        bool: True if export successful, False otherwise
        
    Usage Example:
        success = create_csv_from_dataframe(test_cases_df, "output/test_cases.csv")
    """
    try:
        # Ensure the output directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Export DataFrame to CSV
        df.to_csv(output_path, index=include_index, encoding='utf-8-sig')
        
        # Verify file was created successfully
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            print(f"Successfully exported {len(df)} rows to CSV: {output_path}")
            return True
        else:
            print(f"Failed to create CSV file: {output_path}")
            return False
            
    except Exception as e:
        print(f"Error exporting DataFrame to CSV: {e}")
        return False


def export_test_cases_to_csv(test_cases_df, jira_id, test_type, output_dir="csv_exports"):
    """
    Export test cases to CSV format with standardized naming.
    
    Args:
        test_cases_df (pd.DataFrame): DataFrame containing test cases
        jira_id (str): JIRA ticket identifier
        test_type (str): Test type for filename
        output_dir (str): Directory to save CSV files
        
    Returns:
        tuple: (success: bool, file_path: str, error_message: str)
        
    Usage Example:
        success, file_path, error = export_test_cases_to_csv(df, "TP-1", "positive")
    """
    try:
        # Create output directory if it doesn't exist
        csv_dir = Path(output_dir)
        csv_dir.mkdir(exist_ok=True)
        
        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create standardized filename
        if test_type.lower() == "all":
            csv_filename = f"TestCases_{jira_id}_ALL_{timestamp}.csv"
        else:
            csv_filename = f"TestCases_{jira_id}_{test_type.upper()}_{timestamp}.csv"
        
        csv_path = csv_dir / csv_filename
        
        # Export to CSV
        success = create_csv_from_dataframe(test_cases_df, str(csv_path))
        
        if success:
            return True, str(csv_path), ""
        else:
            return False, "", "Failed to create CSV file"
            
    except Exception as e:
        error_message = f"Error exporting test cases to CSV: {str(e)}"
        print(error_message)
        return False, "", error_message


def format_csv_for_external_tools(df):
    """
    Format DataFrame for better compatibility with external tools.
    
    Args:
        df (pd.DataFrame): Input DataFrame
        
    Returns:
        pd.DataFrame: Formatted DataFrame optimized for CSV export
    """
    try:
        # Create a copy to avoid modifying original
        formatted_df = df.copy()
        
        # Clean up data for CSV export
        # Remove any Excel-specific formatting
        for col in formatted_df.columns:
            if formatted_df[col].dtype == 'object':
                # Clean up text fields
                formatted_df[col] = formatted_df[col].astype(str).str.replace('\n', ' | ')
                formatted_df[col] = formatted_df[col].str.replace('\r', '')
                # Replace NaN/None with empty string
                formatted_df[col] = formatted_df[col].replace(['nan', 'None'], '')
        
        return formatted_df
        
    except Exception as e:
        print(f"Error formatting DataFrame for CSV: {e}")
        return df
