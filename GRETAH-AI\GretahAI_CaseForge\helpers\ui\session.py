"""
Session Management Module for GretahAI CaseForge Test Generator

This module handles session state initialization and management
for the test generator interface.

Functions:
- initialize_session_state: Initialize session state variables

© 2025 GretahAI Team
"""

import streamlit as st


def initialize_session_state():
    """
    Initialize session state variables for the test generator.
    
    Returns:
        None (modifies st.session_state)
    """
    # Initialize session state variables
    if "google_request_timestamps" not in st.session_state:
        st.session_state.google_request_timestamps = []

    if "jira_issue_extracted" not in st.session_state:
        st.session_state.jira_issue_extracted = False
        st.session_state.jira_issue = None
    
    # Ensure jira_issue is also initialized to None if jira_issue_extracted is False
    if not st.session_state.jira_issue_extracted and "jira_issue" not in st.session_state:
        st.session_state.jira_issue = None


def load_usage_data():
    """Load Google AI usage data from file for tracking API usage."""
    import os
    import json
    from datetime import datetime
    
    try:
        # Use path relative to the current file
        usage_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "gui", "usage_data.json")
        if os.path.exists(usage_data_path):
            with open(usage_data_path, "r") as f:
                data = json.load(f)
                # Convert timestamps back to datetime objects
                request_timestamps = [datetime.fromisoformat(ts) for ts in data.get("request_timestamps", [])]
                token_usage = [(datetime.fromisoformat(ts), count) for ts, count in data.get("token_usage", [])]
                return request_timestamps, token_usage
        return [], []
    except FileNotFoundError:
        return [], []
    except Exception as e:
        st.error(f"Error loading usage data: {e}")
        return [], []


def save_usage_data():
    """Save Google AI usage data to file for persistence."""
    import os
    import json
    
    try:
        # Convert datetime objects to ISO format strings for JSON serialization
        request_timestamps = [ts.isoformat() for ts in st.session_state.google_request_timestamps]
        token_usage = [(ts.isoformat(), count) for ts, count in st.session_state.google_token_usage]

        data = {
            "request_timestamps": request_timestamps,
            "token_usage": token_usage
        }
        # Use path relative to the current file
        usage_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "gui", "usage_data.json")
        with open(usage_data_path, "w") as f:
            json.dump(data, f)
    except Exception as e:
        st.error(f"Error saving usage data: {e}")
