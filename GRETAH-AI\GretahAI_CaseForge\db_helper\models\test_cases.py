"""
Test case model operations for the GretahAI CaseForge database system.

This module handles CRUD operations for test cases including creation, retrieval,
updating, and ID management for the test case management system.
"""

import sqlite3
import pandas as pd
from datetime import datetime
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_highest_test_case_id_number(database_path, jira_id, dashboard_test_type=None):
    """Gets the highest test case ID number for a specific JIRA ID.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        dashboard_test_type: Dashboard test type (e.g., "positive", "negative", "all")
                            If "all" or None, returns the highest ID across all test types
                            If a specific type, returns the highest ID for that type only

    Returns:
        The highest test case ID number, or 0 if no test cases found
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get or create the JIRA issue ID from the database
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if not result:
            print(f"No JIRA issue found for {jira_id}, creating one")
            # Create a new JIRA issue
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, "", "", "")
            )
            # Commit immediately to ensure the JIRA issue is created
            conn.commit()
            print(f"Committed JIRA issue creation")

            # Get the ID of the newly created JIRA issue
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()
            if not result:
                print(f"Failed to create JIRA issue for {jira_id}")
                return 0

        db_jira_id = result[0]
        print(f"Using JIRA issue with ID {db_jira_id} for {jira_id}")

        # Always get the highest test case ID across all test types for this JIRA ID
        # This ensures that test case IDs are continuous across all test types for a given JIRA ID
        print(f"Getting highest test case ID across all test types for {jira_id}")
        cursor.execute(
            """SELECT DISTINCT test_case_id
               FROM test_cases
               WHERE jira_id = ?""",
            (jira_id,)
        )

        test_case_ids = cursor.fetchall()
        if not test_case_ids:
            return 0

        # Extract the highest test case ID number
        highest_id = 0
        print(f"Found {len(test_case_ids)} test case IDs for {jira_id}")
        for row in test_case_ids:
            tc_id = row[0]
            print(f"Processing test case ID: {tc_id}")
            if isinstance(tc_id, str) and tc_id.startswith("TC_"):
                try:
                    id_num = int(tc_id.split("_")[1])
                    highest_id = max(highest_id, id_num)
                    print(f"Extracted ID number: {id_num}, current highest: {highest_id}")
                except (ValueError, IndexError) as e:
                    print(f"Error extracting ID number from {tc_id}: {e}")
                    pass

        # We're no longer checking for gaps in the sequence
        # Just return the highest ID found

        print(f"Highest test case ID number for {jira_id} across all test types: {highest_id}")
        return highest_id
    except sqlite3.Error as e:
        print(f"Error getting highest test case ID: {e}")
        return 0
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def save_test_cases_to_database(database_path, jira_id, test_cases_df, test_type, user_name=None, test_run_id=None, is_edited=False, ai_metadata=None):
    """
    Saves test cases from a DataFrame to the database with full relationship management.

    This function is the primary method for persisting test case data. It handles the
    complete workflow of saving test cases including JIRA issue management, test run
    creation/association, and both test case headers and detailed test steps.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier (e.g., "TP-1", "STORY-123")
        test_cases_df (pandas.DataFrame): DataFrame containing test case data with columns:
            - Test Case ID: Unique identifier for each test case
            - Test Case Objective: Description of test purpose
            - Test Steps: Individual step descriptions
            - Expected Result: Expected outcome for each step
            - Step No: Step sequence number
            - Priority, Test Type, Test Group, etc.
        test_type (str): Type of testing ("positive", "negative", "security", "performance", "all")
        user_name (str, optional): Name of user creating the test cases. Defaults to None.
        test_run_id (int, optional): ID of existing test run to associate with. Defaults to None.
        is_edited (bool, optional): Whether these are edited versions of existing cases. Defaults to False.
        ai_metadata (dict, optional): AI modification metadata containing:
            - ai_model: AI model used (e.g., "gemini-2.0-flash")
            - user_query: Original user query
            - original_data: Original test case data (JSON string)
            - processing_time: Time taken for AI processing
            - tokens_used: AI tokens consumed

    Returns:
        int: ID of the test run associated with the saved test cases, None if failed

    Process Flow:
        1. Establishes database connection with extended timeout
        2. Creates or retrieves JIRA issue record
        3. Validates or creates test run association
        4. Processes DataFrame to extract test case headers
        5. Inserts/updates test cases with proper relationships
        6. Saves individual test steps with case associations
        7. Updates test run statistics and status
        8. Commits transaction and returns test run ID

    Data Validation:
        - Ensures JIRA issue exists before saving test cases
        - Validates test run belongs to correct JIRA ID
        - Handles duplicate test case IDs appropriately
        - Maintains data integrity across related tables

    Error Handling:
        - Rolls back transactions on any failure
        - Preserves existing transactions if started externally
        - Provides detailed error logging
        - Returns None on any error condition

    Example:
        df = pd.DataFrame({
            'Test Case ID': ['TC_001', 'TC_002'],
            'Test Case Objective': ['Test login', 'Test logout'],
            'Step No': [1, 1],
            'Test Steps': ['Enter credentials', 'Click logout'],
            'Expected Result': ['Login successful', 'User logged out']
        })
        test_run_id = save_test_cases_to_database(
            db_path, 'TP-1', df, 'positive', 'john_doe'        )
    """
    # Initialize the database if it doesn't exist
    from ..core.schema import init_db
    import os
    if not os.path.exists(database_path):
        init_db(database_path)

    # Create a dedicated connection for this function
    conn = None
    try:
        # Connect to the database with a longer timeout
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        conn.execute("PRAGMA busy_timeout=60000")  # 60 second timeout
        cursor = conn.cursor()

        # Get or create the JIRA issue
        # Use a direct query instead of calling another function that might close the connection
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if result:
            db_jira_id = result[0]
            print(f"Found existing JIRA issue with ID {db_jira_id} for {jira_id}")
        else:
            # Create a new JIRA issue
            print(f"Creating new JIRA issue for {jira_id}")
            cursor.execute(
                "INSERT INTO jira_issues (jira_id, summary, description, status) VALUES (?, ?, ?, ?)",
                (jira_id, "", "", "")
            )
            # Commit immediately to ensure the JIRA issue is created
            conn.commit()
            print(f"Committed JIRA issue creation")

            # Get the ID of the newly created JIRA issue
            cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
            result = cursor.fetchone()
            if result:
                db_jira_id = result[0]
                print(f"Created new JIRA issue with ID {db_jira_id} for {jira_id}")
            else:
                raise ValueError(f"Failed to create JIRA issue for {jira_id}")

        # Check if a transaction is already in progress
        cursor.execute("SELECT * FROM sqlite_master LIMIT 1")
        in_transaction = conn.in_transaction

        # Begin transaction only if not already in a transaction
        if not in_transaction:
            cursor.execute("BEGIN TRANSACTION;")
            print("Started new transaction")
        else:
            print("Using existing transaction")

        # Check if a test run already exists for this JIRA ID and test type with the same timestamp
        current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # If a test_run_id was provided, use it
        if test_run_id:
            # Verify that the test run exists and belongs to this JIRA ID
            cursor.execute(
                "SELECT id FROM test_runs WHERE id = ? AND jira_id = ?",
                (test_run_id, jira_id)  # Use the actual JIRA ID, not the database ID
            )
            existing_test_run = cursor.fetchone()

            if existing_test_run:
                print(f"Using provided test run with ID {test_run_id} for {jira_id} ({test_type})")
                # Update the test run status to in_progress
                cursor.execute(
                    "UPDATE test_runs SET status = ? WHERE id = ?",
                    ("in_progress", test_run_id)
                )
            else:
                # If the provided test_run_id doesn't exist or doesn't belong to this JIRA ID,
                # we'll create a new one
                test_run_id = None
                print(f"Provided test run ID {test_run_id} not found or doesn't belong to {jira_id}, creating a new one")

        # If no test_run_id was provided or the provided one was invalid, check for a recent test run
        if not test_run_id:
            cursor.execute(
                """SELECT id FROM test_runs
                   WHERE jira_id = ? AND test_type = ?
                   AND datetime(timestamp) > datetime('now', '-1 minute')
                   ORDER BY timestamp DESC LIMIT 1""",
                (jira_id, test_type.lower())  # Use the actual JIRA ID, not the database ID
            )
            existing_test_run = cursor.fetchone()

            if existing_test_run:
                test_run_id = existing_test_run[0]
                print(f"Using existing test run with ID {test_run_id} for {jira_id} ({test_type})")
                # Update the test run status to in_progress
                cursor.execute(
                    "UPDATE test_runs SET status = ? WHERE id = ?",
                    ("in_progress", test_run_id)
                )
            else:
                # Create a test run record
                # For the "all" test type, we need to handle it differently
                # We'll set num_test_cases to 20 (5 test cases for each of the 4 test types)
                if test_type.lower() == "all":
                    num_test_cases = 20  # 5 test cases for each of the 4 test types
                else:
                    num_test_cases = len(test_cases_df["Test Case ID"].unique())

                cursor.execute(
                    "INSERT INTO test_runs (jira_id, test_type, timestamp, num_test_cases, status, user_name) VALUES (?, ?, ?, ?, ?, ?)",
                    (jira_id, test_type.lower(), current_timestamp, num_test_cases, "in_progress", user_name)  # Use the actual JIRA ID
                )
                test_run_id = cursor.lastrowid
                print(f"Created new test run with ID {test_run_id} for {jira_id} ({test_type})")        # Process each test case
        # Group rows by Test Case ID to ensure steps are associated with the correct test case
        # First, create a dictionary to map Test Case IDs to their database IDs
        test_case_db_ids = {}
        current_test_case_id = None

        # First pass: Insert all test cases
        for _, row in test_cases_df.iterrows():
            tc_id = row.get("Test Case ID")

            # If this is a new test case (has a Test Case ID)
            if pd.notna(tc_id) and tc_id and tc_id not in test_case_db_ids:                # Check if this test case ID already exists in the database
                cursor.execute(
                    "SELECT id FROM test_cases WHERE jira_id = ? AND test_case_id = ? AND dashboard_test_type = ?",
                    (db_jira_id, tc_id, test_type.lower())  # Use db_jira_id to match original behavior
                )
                existing_tc = cursor.fetchone()

                if existing_tc and not is_edited:
                    # If this test case ID already exists and we're not editing, skip it
                    print(f"Test case ID {tc_id} already exists in the database. Skipping.")
                    test_case_db_ids[tc_id] = existing_tc[0]
                    continue

                # If we're editing or the test case doesn't exist, insert/update it
                if existing_tc and is_edited:
                    # Update the existing test case
                    cursor.execute('''
                        UPDATE test_cases SET
                            test_case_objective = ?,
                            prerequisite = ?,
                            priority = ?,
                            test_type = ?,
                            test_group = ?,
                            project = ?,
                            feature = ?,
                            timestamp = ?,
                            is_latest = ?,
                            dashboard_test_type = ?,
                            user_name = ?,
                            test_run_id = ?,
                            is_edited = ?
                        WHERE id = ?
                    ''', (
                        row.get("Test Case Objective", ""),
                        row.get("Prerequisite", ""),
                        row.get("Priority", ""),
                        row.get("Test Type", ""),
                        row.get("Test Group", ""),
                        row.get("Project", ""),
                        row.get("Feature", ""),
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        1,
                        test_type.lower(),
                        user_name,
                        test_run_id,
                        is_edited,
                        existing_tc[0]
                    ))
                    test_case_db_ids[tc_id] = existing_tc[0]
                    print(f"Updated existing test case {tc_id} in the database.")
                else:
                    # Prepare AI tracking values
                    ai_modified = ai_metadata is not None
                    modification_source = 'ai' if ai_modified else 'manual'
                    ai_model_used = ai_metadata.get('ai_model') if ai_metadata else None
                    ai_modification_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S") if ai_modified else None
                    ai_modification_user = user_name if ai_modified else None
                    ai_user_query = ai_metadata.get('user_query') if ai_metadata else None

                    # Insert the test case
                    cursor.execute('''
                        INSERT INTO test_cases (
                            jira_id, test_case_id, test_case_objective, prerequisite,
                            priority, test_type, test_group, project, feature,
                            timestamp, is_latest, dashboard_test_type, user_name, test_run_id, is_edited, jira_issue_id,
                            ai_modified, modification_source, ai_model_used, ai_modification_timestamp,
                            ai_modification_user, ai_user_query
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        jira_id,  # Use the actual JIRA ID, not the database ID
                        tc_id,
                        row.get("Test Case Objective", ""),
                        row.get("Prerequisite", ""),
                        row.get("Priority", ""),
                        row.get("Test Type", ""),
                        row.get("Test Group", ""),
                        row.get("Project", ""),
                        row.get("Feature", ""),
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        1,
                        # Always use the test_type parameter from the function call
                        test_type.lower(),
                        user_name,
                        test_run_id,
                        is_edited,
                        db_jira_id,  # Set the jira_issue_id to reference the jira_issues.id
                        ai_modified,
                        modification_source,
                        ai_model_used,
                        ai_modification_timestamp,
                        ai_modification_user,
                        ai_user_query
                    ))

                    # Get the test case ID in the database and store it in our mapping
                    test_case_db_ids[tc_id] = cursor.lastrowid
                    print(f"Inserted new test case {tc_id} into the database.")

        # Second pass: Insert all test steps with the correct test case ID
        for _, row in test_cases_df.iterrows():
            tc_id = row.get("Test Case ID")

            # If this row has a Test Case ID, update our current test case tracking
            if pd.notna(tc_id) and tc_id:
                current_test_case_id = tc_id

            # If this is a test step (has a Step No) and we have a current test case
            if pd.notna(row.get("Step No")) and current_test_case_id and current_test_case_id in test_case_db_ids:
                # Get the database ID for this test case
                test_case_db_id = test_case_db_ids[current_test_case_id]

                # Insert the test step
                cursor.execute('''
                    INSERT INTO test_steps (
                        test_case_id, step_number, test_step, expected_result,
                        actual_result, test_status, defect_id, comments, dashboard_test_type, user_name
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    test_case_db_id,
                    row.get("Step No", 0),
                    row.get("Test Steps", ""),
                    row.get("Expected Result", ""),
                    row.get("Actual Result", ""),
                    row.get("Test Status", ""),
                    row.get("Defect ID", ""),
                    row.get("Comments", ""),
                    test_type.lower(),
                    user_name
                ))

        # Update the test run status to completed
        if test_run_id:
            # Count the actual number of unique test cases that were successfully inserted
            num_test_cases = len(test_case_db_ids)

            # For "all" test type, verify we have the expected number of test cases
            if test_type.lower() == "all":
                # We should have test cases for each test type (positive, negative, security, performance)
                # Count how many of each type we have
                test_type_counts = {}
                for tc_id, db_id in test_case_db_ids.items():
                    cursor.execute("SELECT test_type FROM test_cases WHERE id = ?", (db_id,))
                    result = cursor.fetchone()
                    if result and result[0]:
                        test_type = result[0].lower()
                        if test_type not in test_type_counts:
                            test_type_counts[test_type] = 0
                        test_type_counts[test_type] += 1

                # Log the counts for each test type
                print(f"Test type counts for test run {test_run_id}: {test_type_counts}")

            cursor.execute(
                "UPDATE test_runs SET status = ?, num_test_cases = ? WHERE id = ?",
                ("completed", num_test_cases, test_run_id)
            )
            print(f"Updated test run {test_run_id} status to completed with {num_test_cases} test cases")

        # Commit the transaction only if we started one
        if not in_transaction and conn.in_transaction:
            conn.commit()
            print("Committed transaction")
        elif in_transaction:
            print("Not committing transaction as it was started elsewhere")
        else:
            # If we're not in a transaction at all, commit any pending changes
            conn.commit()
            print("Committed changes (no explicit transaction)")

        print(f"Saved {len(test_cases_df[test_cases_df['Test Case ID'].notna()]['Test Case ID'].unique())} test cases to database")

        # Log AI modifications to history table if this was an AI modification
        if ai_metadata and test_case_db_ids:
            try:
                import json
                for tc_id, db_id in test_case_db_ids.items():
                    cursor.execute('''
                        INSERT INTO ai_modifications (
                            test_case_id, jira_id, test_case_identifier,
                            user_name, ai_model, user_query,
                            original_data, modified_data, modification_type,
                            processing_time_seconds, tokens_used
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        db_id,
                        jira_id,
                        tc_id,
                        user_name,
                        ai_metadata.get('ai_model', ''),
                        ai_metadata.get('user_query', ''),
                        ai_metadata.get('original_data', ''),
                        ai_metadata.get('modified_data', ''),
                        ai_metadata.get('modification_type', 'content'),
                        ai_metadata.get('processing_time', 0.0),
                        ai_metadata.get('tokens_used', 0)
                    ))
                # Commit the AI history logging
                conn.commit()
                print(f"Logged AI modification history for {len(test_case_db_ids)} test cases")
            except Exception as ai_log_error:
                print(f"Warning: Failed to log AI modification history: {ai_log_error}")
                # Rollback only the AI history part, not the main transaction
                try:
                    conn.rollback()
                    conn.commit()  # Re-commit the main transaction
                except:
                    pass

        # Return the test run ID for reference
        return test_run_id
    except sqlite3.Error as e:
        print(f"Error saving test cases to database: {e}")
        # Only rollback if we started the transaction
        if conn and conn.in_transaction and not in_transaction:
            try:
                conn.rollback()
                print("Rolled back transaction")
            except Exception as rollback_error:
                print(f"Error rolling back transaction: {rollback_error}")
        elif conn and conn.in_transaction and in_transaction:
            print("Not rolling back transaction as it was started elsewhere")
        return None
    except Exception as e:
        print(f"Unexpected error saving test cases to database: {e}")
        # Only rollback if we started the transaction
        if conn and conn.in_transaction and not in_transaction:
            try:
                conn.rollback()
                print("Rolled back transaction")
            except Exception as rollback_error:
                print(f"Error rolling back transaction: {rollback_error}")
        elif conn and conn.in_transaction and in_transaction:
            print("Not rolling back transaction as it was started elsewhere")
        return None
    finally:
        # Always close the connection
        if conn:
            try:
                conn.close()
                print("Database connection closed successfully")
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock()
def update_test_cases_in_database(database_path, df, jira_id, test_type, user_name, is_edited=True, ai_metadata=None):
    """
    Updates existing test cases in the database with new data.

    This function modifies existing test cases with updated information while
    preserving the original data structure and relationships.

    Args:
        database_path (str): Absolute path to the SQLite database file
        df (pandas.DataFrame): DataFrame containing updated test case data
        jira_id (str): JIRA ticket identifier
        test_type (str): Type of testing being updated
        user_name (str): Name of user making the updates
        is_edited (bool, optional): Mark as edited version. Defaults to True.
        ai_metadata (dict, optional): AI modification metadata containing:
            - ai_model: AI model used (e.g., "gemini-2.0-flash")
            - user_query: Original user query
            - original_data: Original test case data (JSON string)
            - processing_time: Time taken for AI processing
            - tokens_used: AI tokens consumed

    Returns:
        tuple: (success: bool, message: str) - Success status and descriptive message
    """
    try:
        # Determine if this is an AI modification
        is_ai_modified = ai_metadata is not None

        test_run_id = save_test_cases_to_database(
            database_path, jira_id, df, test_type, user_name, None, is_edited, ai_metadata
        )

        if test_run_id is not None:
            test_case_count = len(df["Test Case ID"].dropna().unique()) if "Test Case ID" in df.columns else len(df)
            modification_type = "AI-modified" if is_ai_modified else "manually updated"
            return True, f"Successfully {modification_type} {test_case_count} test cases in database"
        else:
            return False, "Failed to update test cases in database"

    except Exception as e:
        return False, f"Error updating test cases: {str(e)}"


@retry_on_db_lock()
def get_ai_modification_history(database_path, jira_id=None, test_case_id=None, limit=50):
    """
    Retrieve AI modification history from the database.

    Args:
        database_path (str): Path to the database
        jira_id (str, optional): Filter by JIRA ID
        test_case_id (str, optional): Filter by specific test case ID
        limit (int): Maximum number of records to return

    Returns:
        list: List of AI modification records
    """
    conn = None
    try:
        from ..core.connection import get_thread_local_connection, close_thread_local_connection
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        query = '''
        SELECT am.*, tc.test_case_id, tc.test_case_objective
        FROM ai_modifications am
        JOIN test_cases tc ON am.test_case_id = tc.id
        WHERE 1=1
        '''
        params = []

        if jira_id:
            query += ' AND am.jira_id = ?'
            params.append(jira_id)

        if test_case_id:
            query += ' AND tc.test_case_id = ?'
            params.append(test_case_id)

        query += ' ORDER BY am.modification_timestamp DESC LIMIT ?'
        params.append(limit)

        cursor.execute(query, params)
        columns = [description[0] for description in cursor.description]
        results = []

        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))

        return results

    except Exception as e:
        print(f"Error retrieving AI modification history: {e}")
        return []
    finally:
        close_thread_local_connection()


@retry_on_db_lock()
def get_ai_modification_statistics(database_path, days=30):
    """
    Get statistics about AI modifications.

    Args:
        database_path (str): Path to the database
        days (int): Number of days to look back

    Returns:
        dict: Statistics about AI usage
    """
    conn = None
    try:
        from ..core.connection import get_thread_local_connection, close_thread_local_connection
        conn = get_thread_local_connection(database_path)
        cursor = conn.cursor()

        # Get modification counts by model
        cursor.execute('''
        SELECT
            ai_model,
            COUNT(*) as modification_count,
            AVG(processing_time_seconds) as avg_processing_time,
            SUM(tokens_used) as total_tokens,
            COUNT(DISTINCT user_name) as unique_users
        FROM ai_modifications
        WHERE modification_timestamp >= date('now', '-{} days')
        GROUP BY ai_model
        '''.format(days))

        model_stats = []
        for row in cursor.fetchall():
            model_stats.append({
                'ai_model': row[0],
                'modification_count': row[1],
                'avg_processing_time': row[2] or 0,
                'total_tokens': row[3] or 0,
                'unique_users': row[4]
            })

        # Get overall statistics
        cursor.execute('''
        SELECT
            COUNT(*) as total_modifications,
            COUNT(DISTINCT jira_id) as unique_jira_ids,
            COUNT(DISTINCT user_name) as unique_users,
            AVG(processing_time_seconds) as avg_processing_time
        FROM ai_modifications
        WHERE modification_timestamp >= date('now', '-{} days')
        '''.format(days))

        overall_row = cursor.fetchone()
        overall_stats = {
            'total_modifications': overall_row[0] if overall_row else 0,
            'unique_jira_ids': overall_row[1] if overall_row else 0,
            'unique_users': overall_row[2] if overall_row else 0,
            'avg_processing_time': overall_row[3] if overall_row else 0
        }

        return {
            'overall': overall_stats,
            'by_model': model_stats,
            'period_days': days
        }

    except Exception as e:
        print(f"Error retrieving AI modification statistics: {e}")
        return {'overall': {}, 'by_model': [], 'period_days': days}
    finally:
        close_thread_local_connection()


@retry_on_db_lock()
def delete_test_case(database_path, test_case_id):
    """
    Deletes a specific test case and all its associated test steps.
    
    Args:
        database_path (str): Absolute path to the SQLite database file
        test_case_id (int): Database ID of the test case to delete

    Returns:
        bool: True if deletion successful, False otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        # Start transaction
        cursor.execute("BEGIN TRANSACTION")

        # Delete test steps first (foreign key dependency)
        cursor.execute("DELETE FROM test_steps WHERE test_case_id = ?", (test_case_id,))
        steps_deleted = cursor.rowcount

        # Delete test case executions
        cursor.execute("DELETE FROM test_case_executions WHERE test_case_id = ?", (test_case_id,))
        executions_deleted = cursor.rowcount

        # Delete the test case
        cursor.execute("DELETE FROM test_cases WHERE id = ?", (test_case_id,))
        case_deleted = cursor.rowcount

        cursor.execute("COMMIT")

        print(f"Deleted test case {test_case_id}: {case_deleted} test case, {steps_deleted} steps, {executions_deleted} executions")
        return True

    except sqlite3.Error as e:
        print(f"Error deleting test case {test_case_id}: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()
