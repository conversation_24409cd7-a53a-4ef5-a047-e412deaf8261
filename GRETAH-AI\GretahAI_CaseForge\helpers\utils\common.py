"""
Common Utilities Module for GretahAI CaseForge

This module provides shared utility functions and common operations
used across the application.

Functions:
- generate_test_scenarios: Main test scenario generation function
- _generate_test_scenarios_internal: Internal generation logic

© 2025 GretahAI Team
"""

import streamlit as st
from datetime import datetime
from typing import Optional, Dict, Any, Union
import pandas as pd
import os


def generate_test_scenarios(
    case_id, 
    test_type, 
    num_scenarios, 
    selected_model, 
    jira_client, 
    ai_provider, 
    google_api_key, 
    is_all_test_types=False, 
    continue_numbering=False, 
    test_run_id=None
):
    """
    Main orchestration function for generating test scenarios using AI models.
    
    This is the primary entry point for test case generation, coordinating between
    JIRA integration, AI model selection, prompt engineering, and output processing.
    It handles the complete workflow from requirement analysis to formatted test case delivery.

    Args:
        case_id (str): JIRA issue key for test case generation (e.g., "TP-1", "STORY-123")
        test_type (str): Type of test scenarios to generate ("positive", "negative", 
                        "security", "performance", "all")
        num_scenarios (int): Number of test scenarios to generate per type
        selected_model (str): AI model identifier (e.g., "mistral", "gemini-2.0-flash")
        jira_client (jira.JIRA): Authenticated JIRA client instance
        ai_provider (str): AI provider selection ("ollama", "google", "openai")
        google_api_key (str): Google AI Studio API key (if using Google provider)
        is_all_test_types (bool, optional): Whether generating all test types. Defaults to False.
        continue_numbering (bool, optional): Whether to continue from existing numbering.
                                           Defaults to False.
        test_run_id (int, optional): Database test run ID for tracking. Defaults to None.

    Returns:
        tuple: (success: bool, result_data: dict, error_message: str)
            - success: True if generation completed successfully
            - result_data: Dictionary containing generated test cases and metadata
            - error_message: Detailed error message if generation failed
    """
    # Start timestamp for performance tracking
    start_time = datetime.now()

    # First get the issue from JIRA
    issue = jira_client.issue(case_id)

    # Check if we should use enhanced description
    use_enhanced = st.session_state.get("use_enhanced_description", False)
    enhanced_description = None

    if use_enhanced:
        # Get enhanced description from database using the existing helper function
        from db_helper import get_jira_issue_enhancement, DATABASE_PATH
        enhanced_description, enhanced_timestamp = get_jira_issue_enhancement(DATABASE_PATH, case_id)
        if enhanced_description:
            print(f"Using enhanced description from database for {case_id} (enhanced on: {enhanced_timestamp})")
        else:
            print(f"Enhanced description requested but not found in database for {case_id}")
    
    # Generate test cases with the appropriate description
    response_text, token_count = _generate_test_scenarios_internal(
        case_id, test_type, num_scenarios, selected_model,
        ai_provider, google_api_key, issue, enhanced_description
    )
    
    return issue, response_text, None, (datetime.now() - start_time).total_seconds(), token_count


def _generate_test_scenarios_internal(
    case_id, 
    test_type, 
    num_scenarios, 
    selected_model, 
    ai_provider, 
    google_api_key, 
    issue, 
    enhanced_description=None
):
    """Internal function to handle the actual test scenario generation.
    Extracted to avoid code duplication between enhanced and original description paths."""
    
    if ai_provider == "Local":
        from helpers.ai import extract_test_info_from_issue, run_ollama_with_chat
        combined_text = extract_test_info_from_issue(issue, test_type=test_type, num_scenarios=num_scenarios, enhanced_description=enhanced_description)
        response_text = run_ollama_with_chat(combined_text, model=selected_model)
        return response_text, None

    elif ai_provider == "Google AI Studio":
        if not google_api_key:
            raise ValueError("Google AI Studio API Key is required.")

        attachment_path = None
        # Check for image attachments that might be useful for test generation
        if hasattr(issue.fields, 'attachment') and issue.fields.attachment:
            for attachment in issue.fields.attachment:
                # Look for image files
                if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg']):
                    # Download and save the image temporarily
                    image_data = attachment.get()
                    temp_dir = "temp_attachments"
                    os.makedirs(temp_dir, exist_ok=True)
                    attachment_path = os.path.join(temp_dir, attachment.filename)
                    with open(attachment_path, "wb") as f:
                        f.write(image_data)
                    break  # Just use the first image found        # Generate the prompt with or without image, passing the enhanced_description
        from helpers.ai import generate_gemini_test_gen_prompt, run_google_ai_studio
        combined_text = generate_gemini_test_gen_prompt(
            issue, 
            test_type=test_type, 
            num_scenarios=num_scenarios, 
            attachment_path=attachment_path,
            enhanced_description=enhanced_description
        )

        # Handle the case where combined_text is a tuple (image, prompt) from generate_gemini_test_gen_prompt
        result = run_google_ai_studio(combined_text, api_key=google_api_key, model=selected_model)

        # Clean up temporary attachment file if it was created
        if attachment_path and os.path.exists(attachment_path):
            try:
                os.remove(attachment_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {attachment_path}: {e}")

        # Check if the result is a tuple (text, token_count)
        if isinstance(result, tuple) and len(result) == 2:
            response_text, token_count = result
        else:
            response_text = str(result)
            token_count = None

        return response_text, token_count

    else:
        raise ValueError("Invalid AI provider selected.")
