"""
JIRA Integration Module for GretahAI CaseForge

This module provides JIRA operations and enhancement capabilities.

Components:
- enhancement: JIRA description enhancement and validation
- operations: JIRA API integration and workflow handling

© 2025 GretahAI Team
"""

from .enhancement import (
    save_enhanced_jira_description,
    parse_enhanced_jira_description,
    verify_jira_enhanced_description
)

from .operations import (
    extract_jira_issue,
    format_jira_description,
    parse_enhanced_description_json,
    handle_jira_extraction_workflow,
    handle_jira_enhancement_workflow,
    convert_jira_table_to_markdown
)

__all__ = [
    'save_enhanced_jira_description',
    'parse_enhanced_jira_description', 
    'verify_jira_enhanced_description',
    'extract_jira_issue',
    'format_jira_description',
    'parse_enhanced_description_json',
    'handle_jira_extraction_workflow',
    'handle_jira_enhancement_workflow',
    'convert_jira_table_to_markdown'
]
