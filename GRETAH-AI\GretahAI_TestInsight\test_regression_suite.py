# Regression Test Suite for the-internet.herokuapp.com

import os
import time
import pytest
from datetime import datetime
from pathlib import Path
import random
import string

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import Action<PERSON>hains

# Constants
BASE_URL = "https://the-internet.herokuapp.com"
TEST_DATA_DIR = Path("test_data")
TEST_DATA_DIR.mkdir(exist_ok=True)

# Helper Functions (copied from original suite for self-containment)
def create_test_file(filename="test_upload_regression.txt", content=None):
    """Create a test file with random content if not provided"""
    filepath = TEST_DATA_DIR / filename
    if content is None:
        content = ''.join(random.choices(string.ascii_letters + string.digits, k=50)) # Smaller content for regression

    with open(filepath, 'w') as f:
        f.write(content)

    return str(filepath.absolute())

class TestUtils:
    @staticmethod
    def wait_for_page_load(driver, timeout=10):
        """Wait for page to finish loading completely"""
        try:
            WebDriverWait(driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
        except TimeoutException:
            print("Warning: Page load timeout reached, continuing test.") # Less strict for regression

# --- Regression Tests ---

# 1. Core Login and Logout Functionality
def test_regression_login_logout(browser):
    """Verify basic login and logout works."""
    try:
        browser.get(f"{BASE_URL}/login")
        TestUtils.wait_for_page_load(browser)

        # Enter valid credentials
        browser.find_element(By.ID, "username").send_keys("tomsmith")
        browser.find_element(By.ID, "password").send_keys("SuperSecretPassword!")
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()

        # Verify successful login message
        WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.success"))
        )
        success_message = browser.find_element(By.CLASS_NAME, "flash.success")
        assert "You logged into a secure area" in success_message.text, "Login success message not found."
        assert "secure" in browser.current_url, "Did not redirect to secure area after login."

        # Verify logout
        logout_button = browser.find_element(By.CSS_SELECTOR, "a.button")
        logout_button.click()

        # Verify successful logout message
        WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "flash.success"))
        )
        logout_message = browser.find_element(By.CLASS_NAME, "flash.success")
        assert "You logged out of the secure area" in logout_message.text, "Logout success message not found."
        assert "login" in browser.current_url, "Did not redirect to login page after logout."

    except Exception as e:
        pytest.fail(f"Regression test_regression_login_logout failed: {e}")

# 2. Checkbox Interaction
def test_regression_checkboxes(browser):
    """Verify checkboxes can be checked and unchecked."""
    try:
        browser.get(f"{BASE_URL}/checkboxes")
        TestUtils.wait_for_page_load(browser)

        checkboxes = browser.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
        assert len(checkboxes) >= 2, "Expected at least 2 checkboxes."

        # Check initial states
        initial_states = [cb.is_selected() for cb in checkboxes]

        # Toggle first checkbox
        checkboxes[0].click()
        assert checkboxes[0].is_selected() != initial_states[0], "Checkbox 1 state did not toggle."

        # Toggle second checkbox
        checkboxes[1].click()
        assert checkboxes[1].is_selected() != initial_states[1], "Checkbox 2 state did not toggle."

        # Toggle back to ensure stability
        checkboxes[0].click()
        assert checkboxes[0].is_selected() == initial_states[0], "Checkbox 1 did not toggle back correctly."
        checkboxes[1].click()
        assert checkboxes[1].is_selected() == initial_states[1], "Checkbox 2 did not toggle back correctly."

    except Exception as e:
        pytest.fail(f"Regression test_regression_checkboxes failed: {e}")

# 3. Dropdown Selection
def test_regression_dropdown(browser):
    """Verify dropdown selection works."""
    try:
        browser.get(f"{BASE_URL}/dropdown")
        TestUtils.wait_for_page_load(browser)

        dropdown_element = browser.find_element(By.ID, "dropdown")
        select = Select(dropdown_element)

        # Select Option 1 by visible text
        select.select_by_visible_text("Option 1")
        assert select.first_selected_option.text == "Option 1", "Failed to select Option 1 by text."

        # Select Option 2 by value
        select.select_by_value("2")
        assert select.first_selected_option.text == "Option 2", "Failed to select Option 2 by value."

    except Exception as e:
        pytest.fail(f"Regression test_regression_dropdown failed: {e}")

# 4. Basic File Upload
def test_regression_file_upload(browser):
    """Verify basic file upload functionality."""
    try:
        test_file_path = create_test_file()
        test_filename = os.path.basename(test_file_path)

        browser.get(f"{BASE_URL}/upload")
        TestUtils.wait_for_page_load(browser)

        # Upload the file
        browser.find_element(By.ID, "file-upload").send_keys(test_file_path)
        browser.find_element(By.ID, "file-submit").click()

        # Verify upload success
        WebDriverWait(browser, 15).until(
            EC.presence_of_element_located((By.ID, "uploaded-files"))
        )
        success_header = browser.find_element(By.CSS_SELECTOR, "h3")
        assert "File Uploaded!" in success_header.text, "Upload success header not found."

        uploaded_files_text = browser.find_element(By.ID, "uploaded-files").text
        assert test_filename in uploaded_files_text, f"Uploaded filename '{test_filename}' not found in '{uploaded_files_text}'."

    except Exception as e:
        pytest.fail(f"Regression test_regression_file_upload failed: {e}")
    finally:
        # Clean up test file
        if 'test_file_path' in locals() and os.path.exists(test_file_path):
            os.remove(test_file_path)

# 5. Dynamic Controls - Basic Enable/Disable Check
def test_regression_dynamic_controls_enable_disable(browser):
    """Verify basic enable/disable functionality of dynamic controls."""
    try:
        browser.get(f"{BASE_URL}/dynamic_controls")
        TestUtils.wait_for_page_load(browser)

        input_field = browser.find_element(By.CSS_SELECTOR, "input[type=text]")
        assert not input_field.is_enabled(), "Input should be initially disabled."

        # Click Enable
        browser.find_element(By.XPATH, "//button[contains(text(),'Enable')]").click()
        WebDriverWait(browser, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type=text]"))
        )
        assert browser.find_element(By.CSS_SELECTOR, "input[type=text]").is_enabled(), "Input failed to enable."
        assert "enabled" in browser.find_element(By.ID, "message").text.lower(), "Enable message incorrect."

        # Click Disable
        browser.find_element(By.XPATH, "//button[contains(text(),'Disable')]").click()
        WebDriverWait(browser, 10).until(
            EC.invisibility_of_element_located((By.ID, "loading")) # Wait for loading to finish
        )
        # Add a small wait for the disabled attribute to be reliably set
        time.sleep(0.5)
        assert not browser.find_element(By.CSS_SELECTOR, "input[type=text]").is_enabled(), "Input failed to disable."
        assert "disabled" in browser.find_element(By.ID, "message").text.lower(), "Disable message incorrect."

    except Exception as e:
        pytest.fail(f"Regression test_regression_dynamic_controls_enable_disable failed: {e}")
