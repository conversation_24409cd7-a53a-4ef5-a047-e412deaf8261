'''
    Renders the "Analysis Suite" section of the Streamlit application.
    This function creates the UI for analyzing test results, focusing on
    log summarization, failure investigation, and root cause analysis (RCA).
    It presents two main tabs: "Failure Investigation" and "Root Cause Analysis".
    **Failure Investigation Tab:**
    - Allows users to select a specific test run for detailed analysis.
    - Provides filtering options for runs based on contained classes.
    - Offers filtering within the selected run by test class, status, search term,
      or showing only failed tests.
    - Displays summary metrics (total, passed, failed, duration) for the selected run.
    - Presents visualizations:
        - Bar chart of the longest running tests (Duration Analysis).
        - Pie chart showing the distribution of test statuses (Status Distribution).
    - Shows a filterable data grid of test results.
    - Includes a "Detailed Inspector" section:
        - Allows bulk generation of missing AI summaries for all filtered tests or
          only the failed ones within the filter.
        - (Implicitly) Enables viewing detailed information for individual tests
          (handled by `render_test_case_details` when an expander is opened,
           though the direct call isn't in this function).
    **Root Cause Analysis (RCA) Tab:**
    - Allows users to select a test run.
    - Provides a button to trigger an AI-driven RCA process specifically on the
      failed tests within the selected run.
    - Fetches necessary details for failed tests (failure messages, log snippets,
      existing AI analysis).
    - Calls the `perform_rca` helper function to generate the analysis.
    - Displays the generated RCA findings.
    - Saves the RCA results to the database, linked to the specific run.
    - Offers a download button for the generated RCA report.
    **Dependencies:**
    - Streamlit (`st`) for UI elements.
    - Pandas (`pd`) for data manipulation.
    - Plotly Express (`px`) for charting.
    - Database helper functions (`load_test_run_history`, `get_test_run_details`,
      `build_unified_test_entries_from_db`, `find_run_id_by_timestamp`,
      `save_summary`, `get_all_unique_classnames`, `get_classnames_for_run`, etc.)
      to interact with the SQLite database (`DATABASE_PATH`).
    - AI helper functions (`perform_rca`, `generate_and_save_summary_if_missing`)
      to interact with the selected AI model.
    - Utility functions (`format_timestamp_display`).
    - Session state (`st.session_state`) for API keys, selected model, and UI state.
    '''

import streamlit as st
# import subprocess # Moved to execution_view.py
import os
# import time # No longer needed here
# import glob # No longer needed here
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
# import signal # Moved to execution_view.py
# from google import genai # No longer needed here
import json
import sqlite3
# import plotly.express as px # No longer needed here
# import re # Moved to execution_view.py
# import traceback # No longer needed here


# Import helper functions
# from ollama import chat, ChatResponse # No longer needed here
# import threading # No longer needed here
# import queue # No longer needed here
# from st_aggrid import AgGrid, GridOptionsBuilder, JsCode # No longer needed here
from PIL import Image

from helper import (
    OFFLINE_MODELS,
    ONLINE_MODELS,
    MAX_RPM,
    request_queue,
    api_lock,
    estimate_tokens,
    get_log_files,
    is_binary_file,
    read_log_file,
    check_directory_contents,
    list_latest_logs,
    get_log_status,
    format_file_size,
    generate_csv,
    get_log_info,
    # parse_junit_xml, # Moved to execution_view.py
    track_google_request,
    calculate_usage_metrics,
    save_usage_data,
    load_usage_data,
    run_google_ai_studio,
    generate_ai_failure_summary,
    generate_log_summary_prompt,
    # generate_rca_prompt, # No longer needed here
    # perform_rca, # Moved to analysis_view.py
    save_rca_history,
    read_api_key_from_config,
    format_timestamp_display,
    generate_and_save_summary_if_missing,
    # generate_comparison_summary, # Moved to execution_view.py
    # extract_json_dict # Moved to analysis_view.py
)

# Import database helper functions
from sql_lite_db_helpers import (
    init_db,
    get_config_path,
    update_config_path,
    # save_test_results, # Moved to execution_view.py
    # load_test_run_history, # Moved to analysis_view.py
    # get_test_run_details, # Moved to analysis_view.py
    # compare_test_results, # Moved to execution_view.py
    # build_unified_test_entries_from_db, # Moved to analysis_view.py
    safe_parse_json,
    get_thread_local_connection,
    close_thread_local_connection,
    clear_database,
    # find_artifacts, # Moved to analysis_view.py
    # find_log_files, # Moved to analysis_view.py
    find_page_source_files,
    find_and_link_artifact,
    # find_run_id_by_timestamp, # Moved to analysis_view.py
    save_summary,
    # get_comparison_summary, # Moved to execution_view.py
    # get_classnames_for_run, # Moved to analysis_view.py
    # find_previous_run_with_class, # Moved to execution_view.py
    # find_case_id, # Moved to execution_view.py
    # get_single_test_case_details, # Moved to execution_view.py
    # get_all_unique_classnames # Moved to analysis_view.py
)

# Import new modular components
import config as app_config
import init_env
import sidebar as app_sidebar
# from report_generator import generate_report # Moved to execution_view.py
from execution_view import render_test_execution # Import the moved function
from analysis_view import render_log_summarization # Import the new function


# ================================
# Initialize Environment & Config
# ================================
# Set page config and load CSS
app_config.setup_page_config()

# Define and create directories, define DB path
init_env.initialize_directories()
DATABASE_PATH = init_env.DATABASE_PATH  # Get DB path from init_env
SCREENSHOTS_DIR = init_env.SCREENSHOTS_DIR
LOGS_DIR = init_env.LOGS_DIR
PAGE_SOURCE_DIR = init_env.PAGE_SOURCE_DIR
raw_output_dir = init_env.RAW_OUTPUT_DIR
debug_output_dir = init_env.DEBUG_OUTPUT_DIR
REPORTS_DIR = os.path.join(init_env.BASE_DIR, "reports")
Path(REPORTS_DIR).mkdir(parents=True, exist_ok=True)  # Ensure reports directory exists

# Initialize session state variables (needs st)
init_env.setup_session_state()

# Database setup
# Initialize the database at the start
init_db(DATABASE_PATH)

# Ensure the database has the performance_metrics column
from sql_lite_db_helpers import validate_and_fix_schema
validate_and_fix_schema(DATABASE_PATH)

close_thread_local_connection()  # Close initial connection if opened by init_db

# ================================
# Automatic Theme Detection
# ================================
# The application now uses automatic theme detection based on browser/OS preference
# No manual toggle needed - CSS @media queries handle theme switching automatically

# ================================
# Utility Functions (Keep DB related ones here for now)
# ================================
# Functions using streamlit components remain here
def get_config_path_local():
    """Get the config file path from the database, adapted for local use."""
    return get_config_path(DATABASE_PATH)

def update_config_path_local(new_path):
    """Update the config file path in the database, adapted for local use."""
    return update_config_path(DATABASE_PATH, new_path) # Added return statement


# ================================
# Reusable UI Components
# ================================
def render_test_case_details(test_case_data, unique_key_prefix, is_nested_in_expander=False):
    """
    Renders the detailed view for a single test case, including artifacts and AI analysis.

    Args:
        test_case_data (dict): Dictionary containing all details for the test case.
                               Expected keys: 'db_case_id', 'test_name', 'class_name', 'result',
                               'duration', 'failure_message', 'screenshot_path',
                               'page_source_path', 'log_path', 'visual_analysis', 'performance_metrics'.
        unique_key_prefix (str): A unique prefix for Streamlit widget keys within this instance.
        is_nested_in_expander (bool, optional): Flag indicating if this component is rendered inside an expander.
                                               Defaults to False.
    """
    if not test_case_data:
        st.warning("No test case data provided.")
        return

    st.write(f"**Class:** {test_case_data.get('class_name', 'N/A')}")
    duration_str = f"{test_case_data['duration']:.3f}s" if pd.notna(test_case_data.get('duration')) else "N/A"
    st.write(f"**Duration:** {duration_str}")
    st.write(f"**Result:** {test_case_data.get('result', 'Unknown')}")

    # Define tabs for resources
    resource_tabs = st.tabs(["Failure Details", "Screenshot", "Page Source", "AI Analysis", "Performance Metrics"])

    with resource_tabs[0]:  # Failure Details
        failure_msg = test_case_data.get("failure_message")
        if failure_msg:
            st.error("Failure Information:")
            st.code(failure_msg)
        elif test_case_data.get("result") == "PASSED":
            st.success("Test passed validation.")
        else:
            st.info("No failure message recorded for this test.")

    with resource_tabs[1]:  # Screenshot
        screenshot_path = test_case_data.get("screenshot_path")
        if screenshot_path and os.path.exists(screenshot_path):
            try:
                img = Image.open(screenshot_path)
                st.image(img, caption=f"Evidence: {test_case_data.get('test_name', 'Screenshot')}")
            except Exception as img_err:
                st.error(f"Error loading screenshot: {img_err}")
        elif screenshot_path:
            st.warning(f"Screenshot file missing at path: {screenshot_path}")
        else:
            st.info("No screenshot linked or found for this test.")

    with resource_tabs[2]:  # Page Source
        page_source_path = test_case_data.get("page_source_path")
        if page_source_path and os.path.exists(page_source_path):
            try:
                with open(page_source_path, 'r', encoding='utf-8', errors='ignore') as f:
                    page_source = f.read()
                st.download_button(
                    "Download Full Page Source", page_source,
                    file_name=f"page_source_{test_case_data.get('test_name', 'test')}.html", mime="text/html",
                    key=f"{unique_key_prefix}_dl_ps"  # Unique key
                )
                preview_length = 1500
                st.code(page_source[:preview_length] + ("..." if len(page_source) > preview_length else ""), language="html")
            except Exception as ps_err:
                st.error(f"Error reading page source: {ps_err}")
        elif page_source_path:
            st.warning(f"Page source file not found at path: {page_source_path}")
        else:
            st.info("No page source linked or found for this test.")

    with resource_tabs[3]:  # AI Analysis
        ai_analysis = test_case_data.get("visual_analysis")  # This comes from the fetched data

        # Check resource availability for generation
        screenshot_path = test_case_data.get("screenshot_path")
        page_source_path = test_case_data.get("page_source_path")
        log_path = test_case_data.get("log_path")  # Ensure log_path is fetched

        screenshot_exists = screenshot_path and os.path.exists(screenshot_path)
        page_source_exists = page_source_path and os.path.exists(page_source_path)
        log_exists = log_path and os.path.exists(log_path)

        can_generate = screenshot_exists and page_source_exists and log_exists

        # Display existing analysis
        if ai_analysis:
            st.markdown("#### 💡 AI Analysis ")
            if isinstance(ai_analysis, dict):
                if "error" in ai_analysis:
                    st.error(f"Previous analysis attempt failed: {ai_analysis.get('error', 'Unknown error')}")
                    if "raw_response" in ai_analysis:
                        if is_nested_in_expander:
                            st.markdown("#### Raw Response")
                            st.code(ai_analysis['raw_response'])
                        else:
                            st.expander("Show Raw Response").code(ai_analysis['raw_response'])
                else:
                    perspectives_list = list(ai_analysis.items())
                    if len(perspectives_list) > 1:
                        tab_names = [p.replace("_", " ").title() for p, _ in perspectives_list]
                        tabs = st.tabs(tab_names)
                        for i, (perspective, content) in enumerate(perspectives_list):
                            with tabs[i]:
                                st.markdown(str(content))
                    elif len(perspectives_list) == 1:
                        st.markdown(str(perspectives_list[0][1]))
                    else:
                        st.info("AI analysis data is empty.")

                    try:
                        ai_analysis_json = json.dumps(ai_analysis, indent=2)
                        st.download_button(
                            "📥 Export Analysis as JSON",
                            data=ai_analysis_json,
                            file_name=f"{test_case_data.get('test_name', 'test')}_ai_analysis.json",
                            mime="application/json",
                            key=f"{unique_key_prefix}_dl_ai_analysis"  # Unique key
                        )
                    except Exception as json_err:
                        st.warning(f"Could not serialize AI analysis for download: {json_err}")
            else:
                st.write(str(ai_analysis))
        else:
            st.info("AI analysis not yet generated or found in database.")

        # Offer generation/regeneration if possible
        if can_generate:
            gen_button_enabled = st.session_state['model_type'] == "Online" and bool(st.session_state.api_key)

            if st.session_state['model_type'] == "Offline":
                st.warning("Using local model. Cloud API key will be ignored.")

            button_text = "Regenerate AI Analysis" if ai_analysis else "Generate AI Analysis"
            button_key = f"{unique_key_prefix}_regen_ai" if ai_analysis else f"{unique_key_prefix}_gen_ai"  # Unique key

            if st.button(button_text, key=button_key, disabled=not gen_button_enabled):
                with st.spinner("Analyzing screenshot, page source, and log..."):
                    try:
                        current_screenshot_path = screenshot_path
                        current_page_source_path = page_source_path
                        current_log_path = log_path

                        with open(current_page_source_path, 'r', encoding='utf-8', errors='ignore') as f: page_source = f.read()
                        with open(current_log_path, 'r', encoding='utf-8', errors='ignore') as f: log_content = f.read()

                        analysis_result = generate_ai_failure_summary(
                            current_screenshot_path, page_source, log_content, test_case_data.get("test_name", "Unknown Test"),
                            model_name=st.session_state.model, api_key=st.session_state.api_key
                        )

                        analysis_content_str = None
                        try:
                            if isinstance(analysis_result, str):
                                if analysis_result.strip().startswith('{') and analysis_result.strip().endswith('}'):
                                    parsed_result = safe_parse_json(analysis_result)
                                    if parsed_result:
                                        analysis_result = parsed_result
                                        analysis_content_str = json.dumps(analysis_result)
                                    else:
                                        analysis_content_str = json.dumps({"raw_text": analysis_result, "parse_error": True})
                                else:
                                    analysis_content_str = json.dumps({"raw_text": analysis_result})
                            elif isinstance(analysis_result, dict):
                                analysis_content_str = json.dumps(analysis_result)
                            else:
                                analysis_content_str = json.dumps({"raw_text": str(analysis_result)})

                            db_case_id = test_case_data.get('db_case_id')
                            if db_case_id:
                                save_summary(DATABASE_PATH, db_case_id, st.session_state.model, 'visual_analysis', analysis_content_str)
                                st.success("AI analysis generated/regenerated and saved to database.")
                                close_thread_local_connection()
                                # --- Use unique_key_prefix directly for session state update ---
                                session_state_key_to_update = unique_key_prefix
                                if session_state_key_to_update in st.session_state:
                                    st.session_state[session_state_key_to_update]['visual_analysis'] = analysis_result
                                else:
                                    st.warning(f"Session state key {session_state_key_to_update} not found before update, creating.")
                                    if session_state_key_to_update not in st.session_state:
                                        st.session_state[session_state_key_to_update] = test_case_data
                                    st.session_state[session_state_key_to_update]['visual_analysis'] = analysis_result

                                st.rerun()
                                # --- End session state update fix ---
                            else:
                                st.error("Cannot save analysis: Missing database case ID.")

                        except sqlite3.Error as db_err:
                            st.error(f"DB Error saving AI analysis: {db_err}")
                            close_thread_local_connection()
                        except Exception as save_err:
                            st.error(f"Error processing or saving AI analysis: {save_err}")
                            st.error("Generated analysis could not be saved, but is displayed below:")
                            if isinstance(analysis_result, dict): st.json(analysis_result)
                            else: st.code(str(analysis_result), language="text")
                            close_thread_local_connection()

                    except FileNotFoundError as fnf_err:
                        st.error(f"Error reading resource file: {fnf_err}. Cannot generate analysis.")
                    except Exception as gen_err:
                        st.error(f"Error during AI analysis generation: {gen_err}")
                        import traceback
                        if is_nested_in_expander:
                            st.markdown("#### Detailed Error Information")
                            st.code(traceback.format_exc())
                        else:
                            st.expander("Show detailed error information", expanded=False).code(traceback.format_exc())
                        try:
                            db_case_id = test_case_data.get('db_case_id')
                            if db_case_id:
                                error_content_str = json.dumps({"error": str(gen_err)})
                                save_summary(DATABASE_PATH, db_case_id, st.session_state.model, 'visual_analysis', error_content_str)
                                close_thread_local_connection()
                            else:
                                st.warning("Could not save error state to DB: Missing case ID.")
                        except Exception as save_error_err:
                            st.warning(f"Could not save error state to DB: {save_error_err}")
                            close_thread_local_connection()

        else:  # Cannot generate
            st.warning("Cannot generate AI analysis: Missing required resources.")
            missing = []
            if not screenshot_exists: missing.append("Screenshot")
            if not page_source_exists: missing.append("Page Source")
            if not log_exists: missing.append("Log File")
            if missing:
                st.markdown(f"**Missing:** {', '.join(missing)}")

    with resource_tabs[4]:  # Performance Metrics
        performance_metrics = test_case_data.get("performance_metrics", {})
        if performance_metrics and isinstance(performance_metrics, dict):
            # Create columns for metrics display
            col1, col2 = st.columns(2)

            # Execution metrics
            with col1:
                st.markdown("#### Execution Metrics")
                if "execution_time" in performance_metrics:
                    st.metric("Execution Time", f"{performance_metrics['execution_time']:.3f} s")
                if "page_load_time" in performance_metrics:
                    st.metric("Page Load Time", f"{performance_metrics['page_load_time']:.0f} ms")

            # Resource metrics
            with col2:
                st.markdown("#### Resource Usage")
                if "memory_usage" in performance_metrics:
                    st.metric("Memory Usage", f"{performance_metrics['memory_usage']:.2f} MB")
                if "cpu_usage" in performance_metrics:
                    st.metric("CPU Usage", f"{performance_metrics['cpu_usage']:.2f}%")

            # Network metrics
            st.markdown("#### Network Metrics")
            col3, col4 = st.columns(2)
            with col3:
                if "network_requests" in performance_metrics:
                    st.metric("Network Requests", f"{int(performance_metrics['network_requests'])}")
            with col4:
                if "network_bytes" in performance_metrics:
                    st.metric("Network Data", f"{performance_metrics['network_bytes']:.2f} KB")

            # Show all metrics as JSON for debugging/advanced users
            if is_nested_in_expander:
                # If already in an expander, just show the JSON with a header
                st.markdown("#### All Performance Metrics (JSON)")
                st.json(performance_metrics)
            else:
                # If not in an expander, use an expander for the JSON
                with st.expander("All Performance Metrics (JSON)"):
                    st.json(performance_metrics)
        else:
            st.info("No performance metrics available for this test case. Run tests with the performance_monitor fixture to collect metrics.")

# ================================
# Main Navigation: Render Section Based on Sidebar Choice
# ================================
selected_section = app_sidebar.render_sidebar(
    DATABASE_PATH=DATABASE_PATH,
    SCREENSHOTS_DIR=SCREENSHOTS_DIR,
    LOGS_DIR=LOGS_DIR,
    debug_output_dir=debug_output_dir,
    raw_output_dir=raw_output_dir,
    clear_database_func=clear_database,
    close_connection_func=close_thread_local_connection
)

if selected_section == "Execution":
    # Call the imported function, passing necessary arguments
    render_test_execution(
        st_obj=st,
        db_path=DATABASE_PATH,
        base_dir=init_env.BASE_DIR,
        logs_dir=LOGS_DIR,
        reports_dir=REPORTS_DIR,
        debug_dir=debug_output_dir,
        render_test_case_details_func=render_test_case_details # Pass the function itself
    )
elif selected_section == "Analysis":
    # Call the imported function, passing necessary arguments
    render_log_summarization(
        st_obj=st,
        db_path=DATABASE_PATH,
        render_test_case_details_func=render_test_case_details # Pass the function itself
    )

# ================================
# Footer
# ================================

# Commercial Footer with comprehensive licensing information
st.markdown("""
    <div class="testinsight-footer">
        <div style="margin-bottom: 10px;">
            <strong>GretahAI TestInsight Pro</strong> v2.1.0 | © 2025 Cogniron. All Rights Reserved.
        </div>
        <div class="confidential-text">
            <strong>PROPRIETARY COMMERCIAL SOFTWARE</strong> - This software is proprietary and confidential.
        </div>
        <div class="confidential-text">
            <strong>Commercial Licensing:</strong> Valid commercial license required for all use.
            Unauthorized copying, distribution, or use is strictly prohibited.
        </div>
        <div class="confidential-text">
            <strong>Enterprise Support:</strong> <a href="mailto:<EMAIL>" class="confidential-link"><EMAIL></a> |
            <strong>Website:</strong> <a href="https://cogniron.com" target="_blank" class="confidential-link">cogniron.com</a>
        </div>
    </div>
""", unsafe_allow_html=True)

close_thread_local_connection()
