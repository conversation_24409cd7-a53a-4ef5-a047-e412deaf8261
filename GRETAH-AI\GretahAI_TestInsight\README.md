# GretahAI TestInsight

[![Cogniron Logo](https://cogniron.com/wp-content/uploads/2024/10/image-69.png)](https://cogniron.com/)

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

---

**GretahAI TestInsight** is a Streamlit web application that offers test execution monitoring, AI-powered log analysis, interactive test report visualization, and Root Cause Analysis (RCA). This commercial software supports testing teams in understanding test results, diagnosing failures, and gaining insights from execution logs.

**Development Status**: This software is under active development and is suitable for development and testing environments. Enterprise deployment requires additional configuration, testing, and validation.

*(Optional: Add a screenshot of the application dashboard here)*

## Features

*   **Test Suite Execution & Automation:** Supports uploading and running `pytest` suites from the UI with log monitoring.
*   **Test Results Comparison:** Parses JUnit XML reports and compares runs to identify regressions, fixes, and new tests with trend visualization.
*   **AI-Powered Log Summarization:** Generates summaries of log files using:
    *   **Offline Models:** Via locally running Ollama (e.g., Llama 3).
    *   **Online Models:** Via Google AI Studio (Gemini models).
*   **Interactive Test Analysis Dashboards:** Provides visualization of JUnit XML results with charts (duration, pass/fail rates) and detailed tables.
*   **Unified Test View:** Correlates test cases from XML reports with logs, screenshots, page source, and AI summaries in a single view.
*   **AI-Enhanced Root Cause Analysis (RCA):** Utilizes AI to analyze failed test details (logs, summaries, failure messages, visual analysis) and suggest potential root causes.
*   **Multi-Perspective Visual Analysis:** Generates AI insights on failures by analyzing screenshots, page source, and logs together (requires Google AI).

## Installation

**Prerequisites:**

*   Python 3.13+
*   Ollama (for offline models) - [Download](https://ollama.com/)

**Steps:**

1.  **Clone the repository (optional):**
    ```bash
    git clone <repository_url>
    cd GretahAI_TestInsight # Navigate to your project directory
    ```
2.  **Create & Activate Virtual Environment:**
    ```bash
    python -m venv venv
    # Windows: .\venv\Scripts\activate
    # macOS/Linux: source venv/bin/activate
    ```
3.  **Navigate to the Application Directory:**
    ```bash
    cd GretahAI_TestInsight
    ```
4.  **Install Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
5.  **Configure API Keys:**
    *   **Google AI Studio (Online Models):**
        *   Get an API key from [Google AI Studio](https://aistudio.google.com/).
        *   Create `config.json` in the repository root with:
            ```json
            { "api_key": "YOUR_GOOGLE_AI_STUDIO_API_KEY" }
            ```
        *   Alternatively, upload `config.json` via the UI or paste the key in the sidebar (less secure).
    *   **Ollama (Offline Models):**
        *   Ensure Ollama is running.
        *   Pull desired models: `ollama pull llama3`

## Getting Started

1.  **Navigate to the Application Directory:**
    ```bash
    cd GretahAI_TestInsight
    ```
2.  **Activate Virtual Environment:** (See Installation Step 2)
3.  **Run the Streamlit App:**
    ```bash
    streamlit run GretahAI_TestInsight.py
    ```
4.  **Open the URL** provided by Streamlit in your browser.
5.  **Use the Sidebar** to navigate between "Test Execution" and "Test Analysis".
6.  **Configure Settings** (Model Type, API Key/Ollama status) in the sidebar.
7.  **Follow UI prompts** to upload files, execute tests, load reports, and perform analysis/RCA.

## Directory Structure

```
GretahAI_TestInsight/
├── GretahAI_TestInsight.py  # Main Streamlit script
├── helper.py                # Helper functions
├── execution_view.py        # Test execution view module
├── analysis_view.py         # Test analysis view module
├── conftest.py              # Pytest configuration/fixtures
├── test_suite.py            # Example test suite
├── config.json              # (Optional) API keys
├── logs/test_logs/          # Test execution logs stored here
├── raw_outputs/             # AI summaries, RCA reports, history
├── screenshots/             # Test screenshots stored here
├── requirements.txt         # Dependencies
└── README.md                # This file
```


## Usage Examples

1.  **Execute a Test Suite:**
    *   Go to "Test Execution".
    *   Upload `test_suite.py`.
    *   Click "▶️ Execute Test Suite".
    *   Monitor logs in the "Execution Logs" tab.
    *   View results in the "Test Results Comparison" tab.
2.  **Analyze a Test Report:**
    *   Go to "Test Analysis".
    *   Configure AI model (Offline/Online) in the sidebar.
    *   In the "Test Report Analysis" tab, upload or specify the path to `results.xml`.
    *   Explore metrics, charts, and the "Unified View" for detailed test info.
3.  **Perform RCA:**
    *   Ensure a report is loaded in "Test Report Analysis".
    *   Go to the "Root Cause Analysis" tab.
    *   Click "Perform RCA".
    *   Review the AI-generated findings.

## Troubleshooting

*   **Ollama Connection Issues:** Ensure Ollama is running and the selected model is pulled (`ollama list`). Check firewall settings.
*   **Google API Key Errors:** Verify the key is correct, active, and properly configured (`config.json` or UI input). Check API usage limits.
*   **File Not Found:** Double-check paths provided for logs, XML reports. Ensure files exist in the expected locations (`Autotest/logs/test_logs`, `Autotest/screenshots`).
*   **Missing Screenshots/Logs in UI:** Verify the filename matching logic in `build_unified_test_entries` within `GretahAI_TestInsight.py` aligns with how `conftest.py` saves files.
*   **Slow Performance:** AI inference takes time. Offline performance depends on local hardware.

## FAQs

*   **Q: Can I use models other than Llama 3 with Ollama?**
    *   A: Yes, any model pulled in your local Ollama instance should appear in the "Offline" model dropdown.
*   **Q: Does the visual analysis work with offline models?**
    *   A: Currently, the visual analysis feature relies on Google's Gemini Vision model (Online) due to its image processing capabilities.
*   **Q: Where is my API key stored if I paste it in the sidebar?**
    *   A: It's stored only in the Streamlit session state for the current browser session and is not saved persistently (unless you use `config.json`).

## Commercial Licensing

**GretahAI TestInsight is proprietary commercial software developed by Cogniron.**

- **License Type**: Commercial/Proprietary License
- **Usage Rights**: Valid commercial license required for all use
- **Distribution**: Unauthorized distribution prohibited
- **Modifications**: Source code modifications require explicit written permission
- **Enterprise Analytics**: Advanced analytics features available with enterprise licensing

For licensing inquiries and terms, contact <EMAIL>

## Commercial Support & Contact

**Primary Support Contact**: <EMAIL>

**Commercial Services Available**:
- Enterprise licensing and deployment
- Custom analytics and reporting development
- Professional training and implementation services
- Dedicated technical support with guaranteed response times
- Integration consulting for existing testing infrastructure
- Advanced AI model customization and optimization

**Enterprise Features**:
- Custom dashboard development
- Advanced AI model integration
- Enterprise-grade security and compliance
- Multi-tenant deployment options
- Custom reporting and analytics

**Website**: https://cogniron.com

**Note**: This is commercial software designed for professional testing teams. Enterprise evaluation licenses and proof-of-concept deployments are available upon request.

**Important**: This software is currently in active development. While functional for development and testing purposes, enterprise production deployment requires thorough testing, additional configuration, and validation in your specific environment.
