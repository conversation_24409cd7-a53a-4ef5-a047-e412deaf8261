"""
JIRA issue management operations for the GretahAI CaseForge database system.

This module handles CRUD operations for JIRA issues including creation, updates,
and enhancement tracking for integration with external JIRA systems.
"""

import sqlite3
import streamlit as st
from ..core.connection import get_thread_local_connection, close_thread_local_connection
from ..core.decorators import retry_on_db_lock


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_or_create_jira_issue(database_path, jira_id, summary=None, description=None, status=None, test_run_id=None):
    """Gets or creates a JIRA issue in the database.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        summary: JIRA issue summary
        description: JIRA issue description
        status: JIRA issue status (e.g., "To Do", "In Progress", "Done")
        test_run_id: ID of the test run this JIRA issue is associated with

    Returns:
        The JIRA issue ID in the database
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        # Check if the JIRA issue already exists
        cursor.execute("SELECT id FROM jira_issues WHERE jira_id = ?", (jira_id,))
        result = cursor.fetchone()

        if result:
            # JIRA issue exists, return its ID
            jira_issue_id = result[0]
            print(f"Found existing JIRA issue: {jira_id} (ID: {jira_issue_id})")

            # Update the test_run_id if provided and different
            if test_run_id:
                cursor.execute("UPDATE jira_issues SET test_run_id = ? WHERE id = ?", (test_run_id, jira_issue_id))
                conn.commit()

            return jira_issue_id
        else:
            # JIRA issue doesn't exist, create it
            cursor.execute('''
            INSERT INTO jira_issues (jira_id, summary, description, status, test_run_id)
            VALUES (?, ?, ?, ?, ?)
            ''', (jira_id, summary, description, status, test_run_id))

            jira_issue_id = cursor.lastrowid
            conn.commit()
            print(f"Created new JIRA issue: {jira_id} (ID: {jira_issue_id})")
            return jira_issue_id

    except sqlite3.Error as e:
        print(f"Error getting or creating JIRA issue {jira_id}: {e}")
        if conn:
            conn.rollback()
        return None
    finally:
        close_thread_local_connection()


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def update_jira_issue_in_database(database_path, jira_id, summary, description, status):
    """Updates the JIRA issue in the database with the actual summary and description from the JIRA API.

    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
        summary: Summary of the JIRA issue
        description: Description of the JIRA issue
        status: Status of the JIRA issue

    Returns:
        True if the update was successful, False otherwise
    """
    # Create a dedicated connection for this function
    conn = None
    try:
        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        # Update the JIRA issue
        cursor.execute('''
        UPDATE jira_issues 
        SET summary = ?, description = ?, status = ? 
        WHERE jira_id = ?
        ''', (summary, description, status, jira_id))

        if cursor.rowcount > 0:
            conn.commit()
            print(f"Updated JIRA issue {jira_id} successfully")
            return True
        else:
            print(f"No JIRA issue found with ID: {jira_id}")
            return False

    except sqlite3.Error as e:
        print(f"Error updating JIRA issue {jira_id}: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def update_jira_issue_enhancement(database_path, jira_id, enhanced_description, enhanced_timestamp):
    """Updates the enhanced description and timestamp for a JIRA issue."""
    conn = None
    try:
        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        # Update the enhanced description and timestamp
        cursor.execute('''
        UPDATE jira_issues 
        SET enhanced_description = ?, enhanced_timestamp = ? 
        WHERE jira_id = ?
        ''', (enhanced_description, enhanced_timestamp, jira_id))

        if cursor.rowcount > 0:
            conn.commit()
            print(f"Updated enhanced description for JIRA issue {jira_id}")
            return True
        else:
            print(f"No JIRA issue found with ID: {jira_id}")
            return False

    except sqlite3.Error as e:
        print(f"Error updating enhanced description for JIRA issue {jira_id}: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_jira_issue_enhancement(database_path, jira_id):
    """
    Retrieves the enhanced description and timestamp for a JIRA issue.
    
    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ID (e.g., "TP-1")
    
    Returns:
        Tuple of (enhanced_description, enhanced_timestamp) if found, (None, None) otherwise
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path, timeout=30)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT enhanced_description, enhanced_timestamp 
        FROM jira_issues 
        WHERE jira_id = ?
        ''', (jira_id,))

        result = cursor.fetchone()
        if result:
            return result[0], result[1]
        else:
            return None, None

    except sqlite3.Error as e:
        print(f"Error retrieving enhanced description for JIRA issue {jira_id}: {e}")
        return None, None
    finally:
        close_thread_local_connection()


@retry_on_db_lock()
def delete_jira_issue(database_path, jira_id):
    """
    Deletes a JIRA issue and all associated data from the database.
    
    This function removes a JIRA issue along with all related test runs, test cases,
    and test steps. It maintains referential integrity and provides comprehensive
    cleanup of all associated data.

    Args:
        database_path (str): Absolute path to the SQLite database file
        jira_id (str): JIRA ticket identifier to delete

    Returns:
        bool: True if deletion successful, False otherwise

    Deletion Process:
        1. Finds all test cases associated with the JIRA ID
        2. Deletes test steps for those test cases
        3. Deletes test case executions
        4. Deletes the test cases themselves
        5. Deletes test runs for the JIRA ID
        6. Finally deletes the JIRA issue record

    Data Relationships Handled:
        - test_steps → test_cases → jira_issues
        - test_case_executions → test_cases → jira_issues
        - test_runs → jira_issues

    Error Handling:
        - Uses transactions for data consistency
        - Rolls back on any error
        - Provides detailed error logging
        - Returns clear success/failure status

    Example:
        success = delete_jira_issue(db_path, 'TP-1')
    """
    conn = None
    try:
        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        # Start transaction
        cursor.execute("BEGIN TRANSACTION")

        # Get all test case IDs for this JIRA ID
        cursor.execute("SELECT id FROM test_cases WHERE jira_id = ?", (jira_id,))
        test_case_ids = [row[0] for row in cursor.fetchall()]

        if test_case_ids:
            # Delete test steps for these test cases
            test_case_ids_str = ','.join(['?'] * len(test_case_ids))
            cursor.execute(f"DELETE FROM test_steps WHERE test_case_id IN ({test_case_ids_str})", test_case_ids)
            steps_deleted = cursor.rowcount

            # Delete test case executions
            cursor.execute(f"DELETE FROM test_case_executions WHERE test_case_id IN ({test_case_ids_str})", test_case_ids)
            executions_deleted = cursor.rowcount

            # Delete test cases
            cursor.execute("DELETE FROM test_cases WHERE jira_id = ?", (jira_id,))
            cases_deleted = cursor.rowcount

            print(f"Deleted {cases_deleted} test cases, {steps_deleted} steps, {executions_deleted} executions")

        # Delete test runs
        cursor.execute("DELETE FROM test_runs WHERE jira_id = ?", (jira_id,))
        runs_deleted = cursor.rowcount

        # Delete JIRA issue
        cursor.execute("DELETE FROM jira_issues WHERE jira_id = ?", (jira_id,))
        jira_deleted = cursor.rowcount

        cursor.execute("COMMIT")

        print(f"Successfully deleted JIRA issue {jira_id} and all associated data")
        print(f"Summary: {jira_deleted} JIRA issue, {runs_deleted} test runs deleted")

        return True

    except sqlite3.Error as e:
        print(f"Error deleting JIRA issue {jira_id}: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()


@retry_on_db_lock()
def delete_all_jira_issues(database_path, admin_password=None):
    """
    Deletes all JIRA issues and associated data from the database.
    
    This function performs a complete cleanup of all JIRA-related data.
    Requires admin authentication for security.

    Args:
        database_path (str): Absolute path to the SQLite database file
        admin_password (str, optional): Admin password for verification

    Returns:
        bool: True if deletion successful, False otherwise
    """
    conn = None
    try:
        # Check admin permissions
        try:
            import admin_config
            if not admin_config.is_operation_allowed("delete_jira_issues", admin_password):
                print("Permission denied: Admin password required for delete operations")
                return False
        except ImportError:
            print("Warning: admin_config module not available, skipping permission check")

        conn = get_thread_local_connection(database_path, timeout=60)
        cursor = conn.cursor()

        # Start transaction
        cursor.execute("BEGIN TRANSACTION")

        # Delete all test steps
        cursor.execute("DELETE FROM test_steps")
        steps_deleted = cursor.rowcount

        # Delete all test case executions
        cursor.execute("DELETE FROM test_case_executions")
        executions_deleted = cursor.rowcount

        # Delete all test cases
        cursor.execute("DELETE FROM test_cases")
        cases_deleted = cursor.rowcount

        # Delete all test runs
        cursor.execute("DELETE FROM test_runs")
        runs_deleted = cursor.rowcount

        # Delete all JIRA issues
        cursor.execute("DELETE FROM jira_issues")
        jira_deleted = cursor.rowcount

        # Reset auto-increment counters
        cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ('test_steps', 'test_cases', 'test_runs', 'jira_issues', 'test_case_executions')")

        cursor.execute("COMMIT")

        print(f"Successfully deleted all JIRA issues and associated data")
        print(f"Summary: {jira_deleted} JIRA issues, {cases_deleted} test cases, {steps_deleted} steps, {runs_deleted} runs, {executions_deleted} executions")

        return True

    except sqlite3.Error as e:
        print(f"Error deleting all JIRA issues: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        close_thread_local_connection()


@retry_on_db_lock()
def get_unique_jira_ids(db_path, user_name=None):
    """
    Retrieves all unique JIRA IDs from the database, optionally filtered by user.
    
    Args:
        db_path (str): Path to the SQLite database
        user_name (str, optional): Filter by user who created test cases
        
    Returns:
        list: List of unique JIRA IDs
    """
    conn = None
    try:
        conn = get_thread_local_connection(db_path, timeout=30)
        cursor = conn.cursor()

        if user_name:
            cursor.execute('''
            SELECT DISTINCT jira_id 
            FROM jira_issues j
            WHERE EXISTS (
                SELECT 1 FROM test_cases tc 
                WHERE tc.jira_id = j.jira_id 
                AND tc.user_name = ?
            )
            ORDER BY jira_id
            ''', (user_name,))
        else:
            cursor.execute('SELECT DISTINCT jira_id FROM jira_issues ORDER BY jira_id')

        jira_ids = [row[0] for row in cursor.fetchall()]
        return jira_ids

    except sqlite3.Error as e:
        print(f"Error retrieving unique JIRA IDs: {e}")
        return []
    finally:
        close_thread_local_connection()


def get_enhanced_jira_description(database_path, jira_id):
    """
    Retrieve enhanced JIRA description from database.
    
    FUNCTION TYPE: DATABASE FUNCTION
    
    Fetches the enhanced version of a JIRA description that was previously
    processed and stored in the database.

    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str): JIRA issue ID to get enhanced description for

    Returns:
        tuple: (enhanced_description: str, timestamp: str)
            - enhanced_description: The enhanced description text or None
            - timestamp: When the enhancement was created or None

    Usage Example:
        enhanced_desc, timestamp = get_enhanced_jira_description(db_path, "TP-1")
        if enhanced_desc:
            st.success(f"Found enhancement from {timestamp}")
        else:
            st.info("No enhanced version available")
    """
    try:
        # Import here to avoid circular imports
        import db_helper as db_helper
        
        return db_helper.get_jira_issue_enhancement(database_path, jira_id)
    except Exception as e:
        if 'st' in globals():
            st.error(f"Error retrieving enhanced description: {str(e)}")
        return None, None


def save_enhanced_jira_description(database_path, jira_id, enhanced_description, timestamp):
    """
    Save enhanced JIRA description to database.
    
    FUNCTION TYPE: DATABASE FUNCTION
    
    Stores an enhanced version of a JIRA description in the database
    for future retrieval and use.

    Args:
        database_path (str): Path to the SQLite database file
        jira_id (str): JIRA issue ID
        enhanced_description (str): The enhanced description text
        timestamp (str): When the enhancement was created

    Returns:
        bool: True if save successful, False otherwise

    Usage Example:
        success = save_enhanced_jira_description(
            db_path, "TP-1", enhanced_text, "2024-01-15 10:30:00"
        )
        if success:
            st.success("Enhanced description saved")
    """
    try:
        # Import here to avoid circular imports
        import db_helper as db_helper
        
        db_helper.update_jira_issue_enhancement(
            database_path,
            jira_id,
            enhanced_description,
            timestamp
        )
        return True
    except Exception as e:
        if 'st' in globals():
            st.error(f"Error saving enhanced description: {str(e)}")
        return False
