#!/usr/bin/env python3
"""
GretahAI ScriptWeaver - Comprehensive Script Parsing Validation
Complete validation of ALL parsing functions across ALL stages (1-10).
"""

import os
import re
import json
import sys
import ast
import time
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from core.ai_helpers import (
        clean_llm_response, 
        extract_json_from_response, 
        extract_markdown_from_response,
        markdown_table_to_json,
        json_to_markdown_table,
        analyze_step_table
    )
except ImportError as e:
    print(f"Warning: Could not import parsing functions: {e}")
    clean_llm_response = None

def find_all_parsing_usage():
    """Find ALL parsing function usage across ALL stage files"""
    stage_files = [f"stages/stage{i}.py" for i in range(1, 11)]
    
    # Extended list of parsing-related functions
    parsing_functions = [
        "clean_llm_response",
        "extract_json_from_response", 
        "extract_markdown_from_response",
        "markdown_table_to_json",
        "json_to_markdown_table",
        "analyze_step_table",
        "json.loads",
        "json.dumps",
        "re.search",
        "re.findall",
        "ast.parse"
    ]
    
    all_usage = []
    
    for stage_file in stage_files:
        if not os.path.exists(stage_file):
            continue
            
        try:
            with open(stage_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
            for func_name in parsing_functions:
                # Find all occurrences with context
                pattern = rf'{func_name}\s*\([^)]*\)'
                matches = re.finditer(pattern, content)
                
                for match in matches:
                    # Find line number
                    line_start = content.rfind('\n', 0, match.start()) + 1
                    line_num = content[:match.start()].count('\n') + 1
                    line_content = lines[line_num - 1].strip()
                    
                    # Get surrounding context (3 lines before and after)
                    context_start = max(0, line_num - 4)
                    context_end = min(len(lines), line_num + 3)
                    context_lines = lines[context_start:context_end]
                    
                    all_usage.append({
                        "file": stage_file,
                        "function": func_name,
                        "line_number": line_num,
                        "usage": match.group(0),
                        "line_content": line_content,
                        "context": context_lines,
                        "purpose": _determine_parsing_purpose(line_content, func_name)
                    })
                    
        except Exception as e:
            all_usage.append({
                "file": stage_file,
                "function": "ERROR",
                "error": str(e)
            })
    
    return all_usage

def _determine_parsing_purpose(line_content, func_name):
    """Determine the purpose of parsing based on context"""
    line_lower = line_content.lower()
    
    if "script" in line_lower:
        return "Script Processing"
    elif "json" in line_lower or func_name == "json.loads":
        return "JSON Processing"
    elif "markdown" in line_lower or "table" in line_lower:
        return "Table Processing"
    elif "response" in line_lower:
        return "AI Response Processing"
    elif "validation" in line_lower:
        return "Validation Processing"
    else:
        return "General Processing"

def test_all_parsing_functions():
    """Test ALL parsing functions with comprehensive scenarios"""
    test_results = []
    
    # Test 1: clean_llm_response with all formats
    if clean_llm_response:
        test_results.extend(_test_clean_llm_response_comprehensive())
    
    # Test 2: extract_json_from_response
    if extract_json_from_response:
        test_results.extend(_test_extract_json_comprehensive())
    
    # Test 3: extract_markdown_from_response
    if extract_markdown_from_response:
        test_results.extend(_test_extract_markdown_comprehensive())
    
    # Test 4: markdown_table_to_json
    if markdown_table_to_json:
        test_results.extend(_test_markdown_to_json_comprehensive())
    
    # Test 5: json_to_markdown_table
    if json_to_markdown_table:
        test_results.extend(_test_json_to_markdown_comprehensive())
    
    # Test 6: analyze_step_table
    if analyze_step_table:
        test_results.extend(_test_analyze_step_table_comprehensive())
    
    return test_results

def _test_clean_llm_response_comprehensive():
    """Comprehensive testing of clean_llm_response function"""
    test_cases = [
        # Python script formats
        {
            "name": "Python script - basic format",
            "input": """```python
import pytest
def test_example():
    assert True
```""",
            "format": "python",
            "should_contain": ["import pytest", "def test_example"],
            "should_not_contain": ["```python", "```"]
        },
        
        # JSON formats
        {
            "name": "JSON response - step table",
            "input": """```json
[
    {"step_no": 1, "action": "navigate", "target": "login_page"},
    {"step_no": 2, "action": "click", "target": "login_button"}
]
```""",
            "format": "json",
            "should_contain": ['"step_no": 1', '"action": "navigate"'],
            "should_not_contain": ["```json", "```"]
        },
        
        # Markdown formats
        {
            "name": "Markdown table format",
            "input": """```markdown
| Step No | Action | Target |
|---------|--------|--------|
| 1 | navigate | login_page |
| 2 | click | login_button |
```""",
            "format": "markdown",
            "should_contain": ["| Step No |", "| 1 | navigate |"],
            "should_not_contain": ["```markdown", "```"]
        },
        
        # Mixed content with AI commentary
        {
            "name": "Mixed content - AI response with code",
            "input": """I'll generate a test script for you:

```python
import pytest
from selenium import webdriver

@pytest.fixture
def browser():
    driver = webdriver.Chrome()
    yield driver
    driver.quit()

def test_login(browser):
    browser.get("https://example.com")
    # Login implementation
    assert "Dashboard" in browser.title
```

This script includes proper fixtures and assertions.""",
            "format": "python",
            "should_contain": ["import pytest", "@pytest.fixture", "def test_login"],
            "should_not_contain": ["I'll generate", "This script includes"]
        },
        
        # Multiple code blocks
        {
            "name": "Multiple code blocks - select correct format",
            "input": """First, here's the JSON data:
```json
{"status": "success"}
```

Now the Python script:
```python
def main():
    print("Hello World")
```

And some markdown:
```markdown
| Column | Value |
|--------|-------|
| Test | Data |
```""",
            "format": "python",
            "should_contain": ["def main()", 'print("Hello World")'],
            "should_not_contain": ['"status": "success"', "| Column |"]
        }
    ]
    
    results = []
    for test_case in test_cases:
        try:
            result = clean_llm_response(test_case["input"], test_case["format"])
            
            contains_expected = all(content in result for content in test_case["should_contain"])
            excludes_unwanted = all(content not in result for content in test_case["should_not_contain"])
            
            # Test syntax validity for Python code
            syntax_valid = True
            if test_case["format"] == "python":
                try:
                    ast.parse(result)
                except SyntaxError:
                    syntax_valid = False
            
            status = "PASS" if contains_expected and excludes_unwanted and syntax_valid else "FAIL"
            
            results.append({
                "function": "clean_llm_response",
                "test": test_case["name"],
                "status": status,
                "format": test_case["format"],
                "input_length": len(test_case["input"]),
                "output_length": len(result),
                "syntax_valid": syntax_valid,
                "contains_expected": contains_expected,
                "excludes_unwanted": excludes_unwanted
            })
            
        except Exception as e:
            results.append({
                "function": "clean_llm_response",
                "test": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            })
    
    return results

def _test_extract_json_comprehensive():
    """Comprehensive testing of extract_json_from_response function"""
    test_cases = [
        {
            "name": "JSON in code block",
            "input": """```json
[{"step": 1, "action": "click"}]
```""",
            "expected_length": 1
        },
        {
            "name": "JSON array pattern",
            "input": """[{"step": 1, "action": "click"}, {"step": 2, "action": "type"}]""",
            "expected_length": 2
        },
        {
            "name": "Mixed content with JSON",
            "input": """Here's the step table:
```json
[
    {"step_no": 1, "action": "navigate", "target": "homepage"},
    {"step_no": 2, "action": "click", "target": "login_link"}
]
```
This completes the conversion.""",
            "expected_length": 2
        }
    ]
    
    results = []
    for test_case in test_cases:
        try:
            result = extract_json_from_response(test_case["input"])
            
            is_list = isinstance(result, list)
            correct_length = len(result) == test_case["expected_length"]
            
            status = "PASS" if is_list and correct_length else "FAIL"
            
            results.append({
                "function": "extract_json_from_response",
                "test": test_case["name"],
                "status": status,
                "result_type": type(result).__name__,
                "result_length": len(result) if isinstance(result, list) else 0,
                "expected_length": test_case["expected_length"]
            })
            
        except Exception as e:
            results.append({
                "function": "extract_json_from_response",
                "test": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            })
    
    return results

def _test_extract_markdown_comprehensive():
    """Comprehensive testing of extract_markdown_from_response function"""
    test_cases = [
        {
            "name": "Markdown in code block",
            "input": """```markdown
| Step No | Action | Target |
|---------|--------|--------|
| 1 | click | button |
```""",
            "should_contain": ["| Step No |", "| 1 | click |"]
        },
        {
            "name": "Table pattern without code block",
            "input": """| Step No | Action | Target |
|---------|--------|--------|
| 1 | navigate | homepage |
| 2 | click | login |""",
            "should_contain": ["| Step No |", "| 1 | navigate |"]
        }
    ]
    
    results = []
    for test_case in test_cases:
        try:
            result = extract_markdown_from_response(test_case["input"])
            
            contains_expected = all(content in result for content in test_case["should_contain"])
            is_string = isinstance(result, str)
            not_empty = len(result.strip()) > 0
            
            status = "PASS" if contains_expected and is_string and not_empty else "FAIL"
            
            results.append({
                "function": "extract_markdown_from_response",
                "test": test_case["name"],
                "status": status,
                "result_type": type(result).__name__,
                "result_length": len(result),
                "contains_expected": contains_expected
            })
            
        except Exception as e:
            results.append({
                "function": "extract_markdown_from_response",
                "test": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            })
    
    return results

def _test_markdown_to_json_comprehensive():
    """Comprehensive testing of markdown_table_to_json function"""
    test_cases = [
        {
            "name": "Standard step table",
            "input": """| Step No | Action | Target | Expected Result |
|---------|--------|--------|-----------------|
| 1 | navigate | homepage | Page loads |
| 2 | click | login_button | Login form appears |""",
            "expected_keys": ["step_no", "action", "target", "expected_result"],
            "expected_rows": 2
        },
        {
            "name": "Simple two-column table",
            "input": """| Name | Value |
|------|-------|
| test | data |
| example | value |""",
            "expected_keys": ["name", "value"],
            "expected_rows": 2
        }
    ]

    results = []
    for test_case in test_cases:
        try:
            result = markdown_table_to_json(test_case["input"])

            is_list = isinstance(result, list)
            correct_length = len(result) == test_case["expected_rows"]
            has_expected_keys = all(
                all(key in row for key in test_case["expected_keys"])
                for row in result
            ) if result else False

            status = "PASS" if is_list and correct_length and has_expected_keys else "FAIL"

            results.append({
                "function": "markdown_table_to_json",
                "test": test_case["name"],
                "status": status,
                "result_type": type(result).__name__,
                "result_length": len(result) if isinstance(result, list) else 0,
                "expected_rows": test_case["expected_rows"],
                "has_expected_keys": has_expected_keys
            })

        except Exception as e:
            results.append({
                "function": "markdown_table_to_json",
                "test": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            })

    return results

def _test_json_to_markdown_comprehensive():
    """Comprehensive testing of json_to_markdown_table function"""
    test_cases = [
        {
            "name": "Step table JSON to markdown",
            "input": [
                {"step_no": 1, "action": "navigate", "target": "homepage"},
                {"step_no": 2, "action": "click", "target": "login_button"}
            ],
            "should_contain": ["| Step No |", "| Action |", "| Target |", "| 1 |", "| navigate |"]
        },
        {
            "name": "Simple data JSON to markdown",
            "input": [
                {"name": "test", "value": "data"},
                {"name": "example", "value": "value"}
            ],
            "should_contain": ["| Name |", "| Value |", "| test |", "| data |"]
        }
    ]

    results = []
    for test_case in test_cases:
        try:
            result = json_to_markdown_table(test_case["input"])

            is_string = isinstance(result, str)
            contains_expected = all(content in result for content in test_case["should_contain"])
            has_table_structure = "|" in result and "---" in result

            status = "PASS" if is_string and contains_expected and has_table_structure else "FAIL"

            results.append({
                "function": "json_to_markdown_table",
                "test": test_case["name"],
                "status": status,
                "result_type": type(result).__name__,
                "result_length": len(result),
                "contains_expected": contains_expected,
                "has_table_structure": has_table_structure
            })

        except Exception as e:
            results.append({
                "function": "json_to_markdown_table",
                "test": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            })

    return results

def _test_analyze_step_table_comprehensive():
    """Comprehensive testing of analyze_step_table function"""
    test_cases = [
        {
            "name": "UI interaction step table",
            "input": (
                "| Step No | Action | Target |\n|---------|--------|---------|\n| 1 | click | button |",
                [{"step_no": 1, "action": "click", "target": "button", "locator_strategy": "id"}]
            ),
            "expected_ui_required": True
        },
        {
            "name": "Non-UI step table",
            "input": (
                "| Step No | Action | Target |\n|---------|--------|---------|\n| 1 | verify | text |",
                [{"step_no": 1, "action": "verify", "target": "text"}]
            ),
            "expected_ui_required": False
        }
    ]

    results = []
    for test_case in test_cases:
        try:
            result = analyze_step_table(test_case["input"])

            is_dict = isinstance(result, dict)
            has_required_keys = all(key in result for key in ["requires_ui_elements", "reason", "actions"])
            correct_ui_requirement = result.get("requires_ui_elements") == test_case["expected_ui_required"]

            status = "PASS" if is_dict and has_required_keys and correct_ui_requirement else "FAIL"

            results.append({
                "function": "analyze_step_table",
                "test": test_case["name"],
                "status": status,
                "result_type": type(result).__name__,
                "has_required_keys": has_required_keys,
                "ui_required": result.get("requires_ui_elements"),
                "expected_ui_required": test_case["expected_ui_required"]
            })

        except Exception as e:
            results.append({
                "function": "analyze_step_table",
                "test": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            })

    return results

def validate_stage_specific_parsing():
    """Validate parsing in specific stage contexts"""
    validation_results = []

    # Stage 3: AI response parsing for validation
    stage3_validation = {
        "stage": "Stage 3",
        "purpose": "AI Validation Response Parsing",
        "test_input": """{"overall_completeness_score": 8, "is_complete": true, "validation_results": {"navigation": {"has_navigation": true}}}""",
        "parsing_method": "JSON regex extraction"
    }

    try:
        # Test the JSON extraction pattern used in Stage 3
        import re
        json_match = re.search(r'\{.*\}', stage3_validation["test_input"], re.DOTALL)
        if json_match:
            clean_response = json_match.group(0)
            validation_result = json.loads(clean_response)
            stage3_validation["status"] = "PASS"
            stage3_validation["extracted_keys"] = list(validation_result.keys())
        else:
            stage3_validation["status"] = "FAIL"
            stage3_validation["error"] = "No JSON match found"
    except Exception as e:
        stage3_validation["status"] = "ERROR"
        stage3_validation["error"] = str(e)

    validation_results.append(stage3_validation)

    # Stage 10: Script parsing for execution
    stage10_validation = {
        "stage": "Stage 10",
        "purpose": "Script Content Parsing",
        "test_input": """```python
import pytest
def test_example():
    assert True
```""",
        "parsing_method": "clean_llm_response"
    }

    try:
        if clean_llm_response:
            result = clean_llm_response(stage10_validation["test_input"], "python")
            # Test syntax validity
            ast.parse(result)
            stage10_validation["status"] = "PASS"
            stage10_validation["parsed_length"] = len(result)
            stage10_validation["syntax_valid"] = True
        else:
            stage10_validation["status"] = "SKIPPED"
            stage10_validation["error"] = "clean_llm_response not available"
    except Exception as e:
        stage10_validation["status"] = "ERROR"
        stage10_validation["error"] = str(e)

    validation_results.append(stage10_validation)

    return validation_results

def main():
    """Main comprehensive validation function"""
    print("=" * 100)
    print("GRETAH SCRIPTWEAVER - COMPREHENSIVE SCRIPT PARSING VALIDATION")
    print("=" * 100)

    start_time = time.time()

    # Test 1: Find all parsing usage across stages
    print("\n1. PARSING FUNCTION USAGE ANALYSIS ACROSS ALL STAGES")
    print("-" * 80)

    all_usage = find_all_parsing_usage()

    # Group by stage and function
    usage_by_stage = {}
    for usage in all_usage:
        if "error" not in usage:
            stage = usage["file"]
            if stage not in usage_by_stage:
                usage_by_stage[stage] = {}
            func = usage["function"]
            if func not in usage_by_stage[stage]:
                usage_by_stage[stage][func] = []
            usage_by_stage[stage][func].append(usage)

    total_usage_count = 0
    for stage, functions in usage_by_stage.items():
        print(f"\n📄 {stage}:")
        for func, usages in functions.items():
            total_usage_count += len(usages)
            print(f"   {func}: {len(usages)} usage(s)")
            for usage in usages[:2]:  # Show first 2 usages
                print(f"      Line {usage['line_number']}: {usage['purpose']}")

    print(f"\nTotal parsing function usages found: {total_usage_count}")

    # Test 2: Comprehensive function testing
    print("\n2. COMPREHENSIVE PARSING FUNCTION TESTING")
    print("-" * 80)

    all_test_results = test_all_parsing_functions()

    # Group results by function
    results_by_function = {}
    for result in all_test_results:
        func = result["function"]
        if func not in results_by_function:
            results_by_function[func] = []
        results_by_function[func].append(result)

    total_tests = 0
    total_passed = 0

    for func, results in results_by_function.items():
        passed = sum(1 for r in results if r["status"] == "PASS")
        total = len(results)
        total_tests += total
        total_passed += passed

        print(f"\n🔧 {func}:")
        print(f"   Tests: {passed}/{total} passed ({(passed/total*100):.1f}%)")

        for result in results:
            status_icon = "✅" if result["status"] == "PASS" else "❌" if result["status"] == "FAIL" else "⚠️"
            print(f"   {status_icon} {result['test']}")
            if result["status"] == "ERROR":
                print(f"      Error: {result.get('error', 'Unknown error')}")

    # Test 3: Stage-specific validation
    print("\n3. STAGE-SPECIFIC PARSING VALIDATION")
    print("-" * 80)

    stage_results = validate_stage_specific_parsing()

    for result in stage_results:
        status_icon = "✅" if result["status"] == "PASS" else "❌" if result["status"] == "FAIL" else "⚠️"
        print(f"{status_icon} {result['stage']} ({result['purpose']}): {result['status']}")
        if result["status"] == "PASS":
            if "extracted_keys" in result:
                print(f"   Extracted keys: {result['extracted_keys']}")
            if "parsed_length" in result:
                print(f"   Parsed length: {result['parsed_length']} characters")
        elif result["status"] in ["FAIL", "ERROR"]:
            print(f"   Error: {result.get('error', 'Unknown error')}")

    # Summary
    execution_time = time.time() - start_time

    print("\n" + "=" * 100)
    print("COMPREHENSIVE VALIDATION SUMMARY")
    print("=" * 100)

    function_test_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
    stage_pass_count = sum(1 for r in stage_results if r["status"] == "PASS")

    print(f"Execution Time:       {execution_time:.2f} seconds")
    print(f"Usage Analysis:       {total_usage_count} parsing function calls found")
    print(f"Function Tests:       {total_passed}/{total_tests} passed ({function_test_rate:.1f}%)")
    print(f"Stage Validation:     {stage_pass_count}/{len(stage_results)} stages validated")
    print(f"Functions Tested:     {len(results_by_function)} parsing functions")
    print(f"Stages Analyzed:      10 stage files")

    overall_status = "PASS" if function_test_rate >= 90 and total_usage_count > 0 else "NEEDS_ATTENTION"
    status_icon = "✅" if overall_status == "PASS" else "⚠️"

    print(f"\nOverall Status:       {status_icon} {overall_status}")

    if overall_status == "NEEDS_ATTENTION":
        print("\nRecommendations:")
        if function_test_rate < 90:
            print("- Review failing parsing function implementations")
        if total_usage_count == 0:
            print("- Verify parsing functions are being used across stages")
    else:
        print("\n🎉 All parsing functions are working correctly across all stages!")

if __name__ == "__main__":
    main()
