"""
GretahAI Test Generator Tab Rendering Module
============================================

This module contains functions for rendering different tabs in the test generator interface.
Extracted from the main test_generator.py file to improve code organization.

Tab Functions:
- render_raw_ai_output_tab(): Renders the raw AI output tab with attachments
- render_generated_test_cases_tab(): Renders the generated test cases tab with editing
- render_most_recent_test_cases_tab(): Renders the most recent test cases tab
- render_test_results_tabs(): Main orchestration function for all tabs
"""

import base64
import io
import os
import re
from datetime import datetime
from pathlib import Path
import pandas as pd
import streamlit as st

# Import necessary functions from other modules
from helpers.file import (
    process_attachment_for_display
)

from helpers.data import (
    validate_test_case_dataframe,
    count_valid_test_cases
)

# Import database functions directly from the modularized db_helper package
from db_helper import (
    save_test_cases_to_database,
    get_test_cases_from_database,
    get_latest_test_run,
    create_test_run,
    update_test_run,
    DATABASE_PATH
)

def mark_new_test_cases_generated():
    """
    Mark that new test cases have been generated to trigger filter auto-update.
    This should be called from the test generator after successful test case generation.
    """
    from datetime import datetime
    st.session_state["auto_update_filter"] = True
    st.session_state["test_cases_generated"] = True
    st.session_state["last_generation_timestamp"] = datetime.now().isoformat()

def render_raw_ai_output_tab(data, issue):
    """
    Render the raw AI output tab with response and attachments.

    Args:
        data (dict): Scenario data containing response and metadata
        issue: JIRA issue object with potential attachments

    Returns:
        None (renders directly to Streamlit UI)
    """
    response = data["response"]
    processing_time = data.get("processing_time", 0)
    tokens_used = data.get("tokens_used", 0)

    if response:
        st.markdown('<h2 class="sub-header">AI Response</h2>', unsafe_allow_html=True)

        # Display generation info
        if processing_time:
            # Simplified enhanced description check
            use_enhanced_toggle = st.session_state.get("use_enhanced_description", False)
            enhanced_desc = st.session_state.get("enhanced_description", None)
            enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")

            model_info = f"{data.get('ai_provider', 'AI')} ({data.get('model_used', 'model')})"
            if use_enhanced_toggle and enhanced_desc:
                col1, col2 = st.columns([2, 1])
                with col1:
                    st.info(f"✨ Generated in {processing_time:.1f} seconds using {model_info}")
                with col2:
                    st.success(f"Using enhanced description from {enhanced_timestamp}")
            else:
                st.info(f"Generated in {processing_time:.1f} seconds using {model_info} (Original description)")

        # Display token usage if available
        if tokens_used and data.get('ai_provider') == "Google AI Studio":
            st.info(f"Used approximately {tokens_used:,} tokens")

        # Display the raw response in a text area
        st.text_area("Raw Output", response, height=500)
    else:
        st.info("No AI response available. Generate test cases to see the raw output.")

    # Display attachments if available
    render_attachments_section(issue)


def render_raw_ai_output_expander(data, issue):
    """
    Render the raw AI output as a collapsible expander component.
    This replaces the tab-based approach with a more streamlined UI.

    Args:
        data (dict): Scenario data containing response and metadata
        issue: JIRA issue object with potential attachments

    Returns:
        None (renders directly to Streamlit UI)
    """
    response = data["response"]
    processing_time = data.get("processing_time", 0)
    tokens_used = data.get("tokens_used", 0)

    # Create expander with appropriate title and collapsed by default
    # Using consistent styling with CaseForge theme
    with st.expander("🔍 Raw AI Output (Advanced)", expanded=False):
        if response:
            # Add a styled header for the AI response section
            st.markdown("""
            <div class="info-box">
                <h3 style="margin: 0; color: var(--secondary-color);">AI Response Details</h3>
                <p style="margin: 5px 0 0 0; font-size: 0.9rem; color: var(--text-color-light);">
                    Raw output from the AI model for debugging and advanced analysis
                </p>
            </div>
            """, unsafe_allow_html=True)

            # Display generation info in a professional layout
            if processing_time:
                # Simplified enhanced description check
                use_enhanced_toggle = st.session_state.get("use_enhanced_description", False)
                enhanced_desc = st.session_state.get("enhanced_description", None)
                enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")

                model_info = f"{data.get('ai_provider', 'AI')} ({data.get('model_used', 'model')})"

                # Create a professional info panel
                if use_enhanced_toggle and enhanced_desc:
                    col1, col2 = st.columns([2, 1])
                    with col1:
                        st.markdown(f"""
                        <div class="feature-panel">
                            ✨ Generated in {processing_time:.1f} seconds using {model_info}
                        </div>
                        """, unsafe_allow_html=True)
                    with col2:
                        st.markdown(f"""
                        <div class="success-box">
                            Using enhanced description from {enhanced_timestamp}
                        </div>
                        """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="feature-panel">
                        ⚡ Generated in {processing_time:.1f} seconds using {model_info} (Original description)
                    </div>
                    """, unsafe_allow_html=True)

            # Display token usage if available
            if tokens_used and data.get('ai_provider') == "Cloud":
                st.markdown(f"""
                <div class="info-box">
                    📊 Used approximately {tokens_used:,} tokens
                </div>
                """, unsafe_allow_html=True)

            # Display the raw response in a text area with better styling
            st.markdown("**Raw AI Output:**")
            st.text_area(
                "Raw Output",
                response,
                height=400,
                key="raw_ai_output_expander",
                help="This is the unprocessed output from the AI model. Use this for debugging or advanced analysis."
            )

            # Display attachments if available
            render_attachments_section(issue)
        else:
            st.markdown("""
            <div class="warning-box">
                <strong>No AI response available</strong><br>
                Generate test cases to see the raw output here.
            </div>
            """, unsafe_allow_html=True)


def render_attachments_section(issue):
    """
    Render the attachments section for JIRA issue.
    
    Args:
        issue: JIRA issue object with potential attachments
        
    Returns:
        None (renders directly to Streamlit UI)
    """
    if issue and hasattr(issue, 'fields') and hasattr(issue.fields, 'attachment') and issue.fields.attachment:
        st.markdown('<div class="jira-details"><h3>Attachments</h3></div>', unsafe_allow_html=True)

        # Create a directory for storing attachments if it doesn't exist
        attached_images_dir = Path("attached_images")
        attached_images_dir.mkdir(exist_ok=True)

        # Count image attachments first
        image_attachments = [
            att for att in issue.fields.attachment
            if any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
        ]

        if image_attachments:
            render_image_attachments_grid(image_attachments, attached_images_dir)

        # Display non-image attachments
        other_attachments = [
            att for att in issue.fields.attachment
            if not any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])
        ]

        if other_attachments:
            render_other_attachments_list(other_attachments, attached_images_dir)


def render_image_attachments_grid(image_attachments, attached_images_dir):
    """
    Render image attachments in a grid layout.
    
    Args:
        image_attachments (list): List of image attachment objects
        attached_images_dir (Path): Directory for storing attachments
        
    Returns:
        None (renders directly to Streamlit UI)
    """
    # Create a container with custom styling for attachments
    with st.container():
        st.markdown("""
        <style>
        .attachment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .attachment-item {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            background: white;
        }
        .attachment-item img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
        }
        .attachment-item p {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
        </style>
        """, unsafe_allow_html=True)

        # Start the grid container
        st.markdown('<div class="attachment-grid">', unsafe_allow_html=True)

        for att in image_attachments:
            # Download and save the image
            img_data = att.get()
            img_path = attached_images_dir / att.filename
            try:
                with open(img_path, 'wb') as f:
                    f.write(img_data)

                # Display the attachment using the helper function
                success, img_data_processed, img_path_processed, error = process_attachment_for_display(att, attached_images_dir)

                if success:
                    # Create a unique key for each image based on filename and timestamp
                    image_key = f"{att.filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                    # Display the image with a preview button and download link
                    st.markdown(f"""
                    <div class="attachment-item">
                        <img src="data:image/{os.path.splitext(att.filename)[1][1:]};base64,{base64.b64encode(img_data).decode()}"
                             alt="{att.filename}"/>
                        <p>{att.filename}</p>
                    </div>
                    """, unsafe_allow_html=True)

                    # Add buttons in columns
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("🔍 View", key=f"view_{image_key}"):
                            st.session_state.modal_image = {
                                "show": True,
                                "path": str(img_path_processed),
                                "filename": att.filename
                            }
                            st.rerun()

                    with col2:
                        with open(img_path_processed, "rb") as file:
                            st.download_button(
                                label="💾 Save",
                                data=file,
                                file_name=att.filename,
                                mime=f"image/{os.path.splitext(att.filename)[1][1:]}",
                                key=f"download_{image_key}",
                                disabled=False
                            )
                else:
                    st.error(error)

            except Exception as e:
                st.error(f"Error processing attachment {att.filename}: {str(e)}")

        # End the grid container
        st.markdown('</div>', unsafe_allow_html=True)


def render_other_attachments_list(other_attachments, attached_images_dir):
    """
    Render non-image attachments as a downloadable list.
    
    Args:
        other_attachments (list): List of non-image attachment objects
        attached_images_dir (Path): Directory for storing attachments
        
    Returns:
        None (renders directly to Streamlit UI)
    """
    st.markdown('<div class="jira-details"><h4>Other Attachments</h4></div>', unsafe_allow_html=True)
    for att in other_attachments:
        try:
            # Download the file
            file_data = att.get()
            file_path = attached_images_dir / att.filename

            with open(file_path, 'wb') as f:
                f.write(file_data)

            # Create a download button for the file
            with open(file_path, 'rb') as f:
                file_data_for_download = f.read()

            # Determine MIME type based on file extension
            file_ext = os.path.splitext(att.filename)[1].lower()
            mime_type = "application/octet-stream"  # Default
            if file_ext == ".pdf":
                mime_type = "application/pdf"
            elif file_ext in [".txt", ".log"]:
                mime_type = "text/plain"
            elif file_ext in [".json"]:
                mime_type = "application/json"
            elif file_ext in [".csv"]:
                mime_type = "text/csv"

            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"📄 {att.filename}")
            with col2:
                st.download_button(
                    label="💾 Download",
                    data=file_data_for_download,
                    file_name=att.filename,
                    mime=mime_type,
                    key=f"download_{att.filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    disabled=False
                )

        except Exception as e:
            st.error(f"Error processing attachment {att.filename}: {str(e)}")


def render_test_results_tabs(data, issue, response, output_file, processing_time, tokens_used):
    """
    Render the main test results tab interface.

    Args:
        data (dict): Scenario data containing all test generation results
        issue: JIRA issue object
        response (str): AI response text
        output_file (str): Path to output file
        processing_time (float): Time taken for generation
        tokens_used (int): Number of tokens used

    Returns:
        None (renders directly to Streamlit UI)
    """
    # Create tabs for organization - Raw AI Output moved to expander at bottom
    tab1, tab2 = st.tabs(["📊 Generated Test Cases", "🕒 Most Recent Test Cases"])

    # Generated Test Cases tab
    with tab1:
        render_generated_test_cases_tab_simple(output_file)

    # Most Recent Test Cases tab
    with tab2:
        render_most_recent_test_cases_tab()


def render_generated_test_cases_tab_simple(output_file):
    """
    Render the generated test cases tab with exact logic from backup file.
    
    Args:
        output_file (str): Path to output file or database URL
        
    Returns:
        None (renders directly to Streamlit UI)
    """
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    import db_helper as db_helper
    
    st.markdown('<h2 class="sub-header">Generated Test Cases</h2>', unsafe_allow_html=True)

    try:
        # Get the current JIRA ID and test type from the input fields
        current_jira_id = st.session_state.get("jira_case_id_input", "")
        current_test_type = st.session_state.get("test_type_select", "all")

        # Check if the user is logged in
        is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
        current_user = st.session_state.get("admin_username", "")

        if not is_admin_logged_in or not current_user:
            st.warning("Please log in through the Admin Panel to view your test cases.")
            return        
        
        # Check if new test cases were just generated and trigger filter update
        # Track last generation state to detect new generations
        last_jira_test_type = st.session_state.get("last_generated_jira_test_type", "")
        current_jira_test_type = f"{current_jira_id}_{current_test_type}"
        
        # Check for generation flags that indicate new test cases
        test_cases_generated_flag = st.session_state.get("test_cases_generated", False)
        auto_update_flag = st.session_state.get("auto_update_filter", False)
        
        # Also check for fresh generation based on timestamp
        last_generation_timestamp = st.session_state.get("last_generation_timestamp", "")
        last_processed_timestamp = st.session_state.get("last_processed_generation_timestamp", "")
        fresh_generation = last_generation_timestamp != last_processed_timestamp
        
        # Debug information to help troubleshoot
        st.write(f"🔍 Debug - last_jira_test_type: '{last_jira_test_type}', current: '{current_jira_test_type}', generated_flag: {test_cases_generated_flag}, auto_update_flag: {auto_update_flag}, fresh_generation: {fresh_generation}")
        
        # Trigger auto-update if:
        # 1. The JIRA ID + test type combination changed, OR
        # 2. The test_cases_generated flag is set, OR  
        # 3. The auto_update_filter flag is set, OR
        # 4. There's a fresh generation based on timestamp
        if (last_jira_test_type != current_jira_test_type or 
            test_cases_generated_flag or 
            auto_update_flag or
            fresh_generation):
            # Trigger filter auto-update
            st.session_state["auto_update_filter"] = True
            st.session_state["last_generated_jira_test_type"] = current_jira_test_type
            
            # Reset the generation flag after using it
            if test_cases_generated_flag:
                st.session_state["test_cases_generated"] = False
                
            # Mark this generation as processed
            if last_generation_timestamp:
                st.session_state["last_processed_generation_timestamp"] = last_generation_timestamp
        
        # Initialize parsed_df as an empty DataFrame
        parsed_df = pd.DataFrame()

        # Always load data from database based on current JIRA ID and test type
        # This ensures we show all data for the selected filters, not just recently generated
        if current_jira_id:
            # For "all" test type, we need a special approach to get all test cases
            if current_test_type == "all":
                # First try to get the test run with type "all"
                latest_test_run_id = db_helper.get_latest_test_run_id(db_helper.DATABASE_PATH, current_jira_id, "all", current_user)

                if latest_test_run_id:
                    # Get test cases from database for "all" type
                    parsed_df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, "all", current_user)
                else:
                    # Fallback to getting individual test types
                    parsed_df = pd.DataFrame()
                    test_types = ["positive", "negative", "security", "performance"]
                    
                    for test_type in test_types:
                        try:
                            temp_df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, test_type, current_user)
                            if not temp_df.empty:
                                parsed_df = pd.concat([parsed_df, temp_df], ignore_index=True)
                        except Exception as e:
                            continue
            else:
                # For specific test types, use the helper function
                parsed_df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, current_test_type, current_user)

            # Filter test cases by the current user (additional safety check)
            if not parsed_df.empty and "user_name" in parsed_df.columns:
                parsed_df = parsed_df[parsed_df["user_name"] == current_user]        # Display the dataframe if it's not empty
        if not parsed_df.empty:
            # Sort by timestamp to get the most recent first
            if "Timestamp" in parsed_df.columns:
                parsed_df = parsed_df.sort_values("Timestamp", ascending=False)

            # Convert from step-level to test case-level display (exactly like Most Recent Test Cases tab)            # Convert from step-level to test case-level display (exactly like Most Recent Test Cases tab)
            if "Test Case ID" in parsed_df.columns:
                # Group by Test Case ID to create the proper display format
                display_data = []
                for test_case_id, group in parsed_df.groupby("Test Case ID"):
                    if pd.isna(test_case_id) or str(test_case_id).strip() == "":
                        continue
                    
                    # Sort by Step No to ensure proper order
                    group_sorted = group.sort_values("Step No") if "Step No" in group.columns else group
                    
                    first_step = True
                    for _, row in group_sorted.iterrows():
                        if first_step:
                            # First row: show all metadata + first step
                            display_row = {
                                "Timestamp": row.get("Timestamp", ""),
                                "Project": row.get("Project", ""),
                                "Feature": row.get("Feature", ""),
                                "User Story ID": row.get("User Story ID", ""),
                                "Test Case ID": test_case_id,
                                "Test Case Objective": row.get("Test Case Objective", ""),
                                "Prerequisite": row.get("Prerequisite", ""),
                                "Step No": row.get("Step No", ""),
                                "Test Steps": row.get("Test Steps", ""),
                                "Expected Result": row.get("Expected Result", ""),
                                "Actual Result": row.get("Actual Result", ""),
                                "Test Status": row.get("Test Status", ""),
                                "Priority": row.get("Priority", ""),
                                "Defect ID": row.get("Defect ID", ""),
                                "Comments": row.get("Comments", ""),
                                "Test Type": row.get("Test Type", ""),
                                "Test Group": row.get("Test Group", ""),
                            }
                            first_step = False
                        else:
                            # Subsequent rows: only step details, empty metadata
                            display_row = {
                                "Timestamp": "",
                                "Project": "",
                                "Feature": "",
                                "User Story ID": "",
                                "Test Case ID": "",
                                "Test Case Objective": "",
                                "Prerequisite": "",
                                "Step No": row.get("Step No", ""),
                                "Test Steps": row.get("Test Steps", ""),
                                "Expected Result": row.get("Expected Result", ""),
                                "Actual Result": row.get("Actual Result", ""),
                                "Test Status": row.get("Test Status", ""),
                                "Priority": "",
                                "Defect ID": row.get("Defect ID", ""),
                                "Comments": row.get("Comments", ""),
                                "Test Type": "",
                                "Test Group": "",
                            }
                        display_data.append(display_row)
                
                parsed_df = pd.DataFrame(display_data)            
            # Add filter box for test case type with smart auto-selection
            if "Test Type" in parsed_df.columns:
                available_test_types = ["All"] + list(parsed_df["Test Type"].dropna().unique())
                
                # Get the current test type from the generator input
                current_test_type_from_input = st.session_state.get("test_type_select", "all")
                
                # Smart default selection logic - prioritize showing currently selected test type
                default_filter = "All"  # Start with default
                
                # If user selected a specific test type (not "all"), try to show that type
                if current_test_type_from_input and current_test_type_from_input != "all":
                    # Look for matching test type in available options (case insensitive)
                    for available_type in available_test_types:
                        if available_type.lower() == current_test_type_from_input.lower():
                            default_filter = available_type
                            break
                
                # Check if filter should auto-update
                auto_update = st.session_state.get("auto_update_filter", False)
                
                # Debug information (can be removed later)
                if auto_update:
                    st.info(f"🔄 Auto-updating filter to show '{default_filter}' test cases")
                
                # Get existing filter state
                existing_filter = st.session_state.get("generated_test_type_filter", None)
                
                # Use smart default if auto-update is triggered or this is the first time
                if auto_update or existing_filter is None:
                    # Reset the auto-update flag
                    if auto_update:
                        st.session_state["auto_update_filter"] = False
                        # Force the selectbox to update by clearing its state
                        if "generated_test_type_filter" in st.session_state:
                            del st.session_state["generated_test_type_filter"]
                        # Also clear any cached editor state to force refresh
                        if "test_generator_editor_data" in st.session_state:
                            del st.session_state["test_generator_editor_data"]
                else:
                    # Use existing filter if available and valid
                    if existing_filter and existing_filter in available_test_types:
                        default_filter = existing_filter
                
                # Get the index for the selectbox
                try:
                    default_index = available_test_types.index(default_filter)
                except ValueError:
                    default_index = 0  # Default to "All"
                
                selected_filter_type = st.selectbox(
                    "Filter by Test Case Type:",
                    options=available_test_types,
                    index=default_index,
                    key="generated_test_type_filter",
                    help="Filter test cases by type. Automatically updates to show newly generated test cases."
                )
                
                # Add a reset filter button for manual control
                col1, col2 = st.columns([3, 1])
                with col2:
                    if st.button("🔄 Reset Filter", key="reset_generated_filter", help="Reset filter to show all test cases"):
                        # Clear the filter state to force reset
                        if "generated_test_type_filter" in st.session_state:
                            del st.session_state["generated_test_type_filter"]
                        st.rerun()
                
                # Apply filter to dataframe
                if selected_filter_type != "All":
                    filtered_df = parsed_df[parsed_df["Test Type"] == selected_filter_type]
                else:
                    filtered_df = parsed_df
            else:
                filtered_df = parsed_df

            # Display test case count using helper function
            test_case_count = count_valid_test_cases(filtered_df)
            
            if test_case_count > 0:
                # Get the actual test type from the dataframe
                actual_test_type = current_test_type
                if "dashboard_test_type" in filtered_df.columns:
                    # Get the most common test type in the dataframe
                    test_types = filtered_df["dashboard_test_type"].dropna().unique()
                    if len(test_types) == 1:
                        actual_test_type = test_types[0]

                # Create an expandable summary section
                with st.expander("📊 Test Case Info", expanded=True):
                    # Create a more compact summary with columns
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.markdown(f"👤 **User:** {current_user}")
                        st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                    with col2:
                        st.markdown(f"🏷️ **Test Type:** {actual_test_type.upper()}")
                        st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                    with col3:
                        # Show which description was used for generation
                        use_enhanced_toggle = st.session_state.get("use_enhanced_description", False)
                        enhanced_desc = st.session_state.get("enhanced_description", None)
                        enhanced_timestamp = st.session_state.get("enhanced_timestamp", "")

                        if use_enhanced_toggle and enhanced_desc:
                            st.success("✨ Using Enhanced Description")
                            if enhanced_timestamp:
                                st.caption(f"Enhanced on: {enhanced_timestamp}")
                        else:
                            st.info("📄 Using Original Description")

            # Configure column editability - only specific columns should be editable
            column_config = {}
            editable_columns = ["Feature", "Test Case Objective", "Prerequisite", "Test Steps", "Expected Result", "Test Status", "Priority"]
            
            # Hide Test Group column from display
            if 'Test Group' in filtered_df.columns:
                filtered_df = filtered_df.drop('Test Group', axis=1)
                
            for col in filtered_df.columns:
                if col in editable_columns:
                    # Make these columns editable
                    column_config[col] = st.column_config.TextColumn(col, disabled=False)
                else:
                    # Make all other columns read-only
                    column_config[col] = st.column_config.TextColumn(col, disabled=True)

            # Session state management for AI modifications - same pattern as test analysis
            tab_key = "test_generator"
            editor_data_key = f"{tab_key}_editor_data"
            table_updated_key = f"{tab_key}_table_updated_with_ai"
            original_df_key = f"original_{tab_key}_df"
            
            # Initialize session state if not present
            if original_df_key not in st.session_state:
                st.session_state[original_df_key] = filtered_df.copy()
            
            # Determine which data to display in the editor
            if st.session_state.get(table_updated_key, False) and editor_data_key in st.session_state:
                display_df = st.session_state[editor_data_key]
            else:
                display_df = filtered_df
                st.session_state[editor_data_key] = filtered_df.copy()

            # Generate a unique key for the data editor that changes when AI updates occur
            # This ensures the data editor refreshes with new data
            ai_update_counter = st.session_state.get(f"{tab_key}_ai_update_counter", 0)
            editor_key = f"generated_test_cases_editor_{ai_update_counter}"

            # Display the dataframe as an editable table
            edited_df = st.data_editor(
                display_df,
                use_container_width=True,
                num_rows="dynamic",
                column_config=column_config,
                key=editor_key
            )

            # Update session state with current editor data
            st.session_state[editor_data_key] = edited_df.copy()

            # Add a save button to update the database with edited data
            if st.button("💾 Save Changes to Database", key="save_generated_test_cases"):
                with st.spinner("Saving changes to database..."):
                    try:
                        # Check if there are changes
                        original_data = st.session_state.get(original_df_key, filtered_df)
                        if not edited_df.equals(original_data):
                            # Get the current JIRA ID and test type
                            db_jira_id = current_jira_id
                            db_test_type = current_test_type                            
                            # Add debug information
                            st.info(f"Preparing to save changes for JIRA ID: {db_jira_id}, Test Type: {db_test_type}, User: {current_user}")

                            # Validate the dataframe before saving
                            if edited_df.empty:
                                st.error("Cannot save empty dataframe to database.")
                                return

                            # Check for required columns
                            required_columns = ["Test Case ID", "Test Case Objective"]
                            missing_columns = [col for col in required_columns if col not in edited_df.columns]
                            if missing_columns:
                                st.error(f"Missing required columns in dataframe: {', '.join(missing_columns)}")
                                return

                            # Log the shape of the dataframe
                            st.info(f"Dataframe shape: {edited_df.shape[0]} rows, {edited_df.shape[1]} columns")

                            # Update the database with the edited data
                            try:
                                success, message = db_helper.update_test_cases_in_database(
                                    db_helper.DATABASE_PATH,
                                    edited_df,
                                    db_jira_id,
                                    db_test_type,
                                    current_user,
                                    is_edited=True
                                )

                                if success:
                                    st.success(f"✅ {message}")
                                    # Update original data after successful save
                                    st.session_state[original_df_key] = edited_df.copy()
                                    st.session_state[table_updated_key] = False  # Reset AI update flag after save
                                else:
                                    st.error(f"❌ {message}")
                            except Exception as db_error:
                                st.error(f"Database error: {str(db_error)}")
                        else:
                            st.info("No changes detected.")
                    except Exception as e:
                        st.error(f"Error saving test cases: {str(e)}")

            # Add AI modification interface with proper tab key
            render_ai_modification_interface_for_generator(edited_df, "test_generator")

        else:
            if current_jira_id:
                st.info(f"No test cases found for user '{current_user}' with JIRA ID '{current_jira_id}' and test type '{current_test_type}'.")
            else:
                st.info("Please enter a JIRA ID to view test cases.")

    except Exception as e:
        st.error(f"Error loading generated test cases: {str(e)}")
        st.info("The file might be corrupted or in an unexpected format.")


def render_most_recent_test_cases_tab():
    """
    Render the most recent test cases tab with database data.
      Returns:
        None (renders directly to Streamlit UI)
    """
    import db_helper as db_helper
    
    st.markdown('<h2 class="sub-header">Most Recent Test Cases</h2>', unsafe_allow_html=True)

    try:
        import time
        # Get the current JIRA ID and test type from the input fields
        current_jira_id = st.session_state.get("jira_case_id_input", "")
        current_test_type = st.session_state.get("test_type_select", "all")

        # Check if the user is logged in
        is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
        current_user = st.session_state.get("admin_username", "")

        if not is_admin_logged_in or not current_user:
            st.warning("Please log in through the Admin Panel to view your test cases.")
            return

        # For "all" test type, we need a special approach to get all test cases
        if current_test_type == "all":
            # First try to get the test run with type "all"
            latest_test_run_id = db_helper.get_latest_test_run_id(db_helper.DATABASE_PATH, current_jira_id, "all", current_user)

            if latest_test_run_id:
                # Get test cases from database for "all" type
                parsed_df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, "all", current_user)
            else:
                # Fallback to getting individual test types
                parsed_df = pd.DataFrame()
                test_types = ["positive", "negative", "security", "performance"]
                
                for test_type in test_types:
                    try:
                        temp_df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, test_type, current_user)
                        if not temp_df.empty:
                            parsed_df = pd.concat([parsed_df, temp_df], ignore_index=True)
                    except Exception as e:
                        continue
        else:
            # For specific test types, use the helper function
            parsed_df = db_helper.get_test_cases_from_database(db_helper.DATABASE_PATH, current_jira_id, current_test_type, current_user)

        if not parsed_df.empty:
            # Filter test cases by the current user (additional safety check)
            if "user_name" in parsed_df.columns:
                parsed_df = parsed_df[parsed_df["user_name"] == current_user]

            if not parsed_df.empty:
                # Sort by timestamp to get the most recent first
                if "Timestamp" in parsed_df.columns:
                    parsed_df = parsed_df.sort_values("Timestamp", ascending=False)

                # Display test case count using helper function
                test_case_count = count_valid_test_cases(parsed_df)

                # Create an expandable summary section
                with st.expander("📊 Test Case Info", expanded=True):
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.markdown(f"👤 **User:** {current_user}")
                        st.markdown(f"🔑 **JIRA ID:** {current_jira_id}")
                    with col2:
                        st.markdown(f"🏷️ **Test Type:** {current_test_type.upper()}")
                        st.markdown(f"📝 **Total Test Cases:** {test_case_count}")
                    with col3:
                        if not parsed_df.empty and "Timestamp" in parsed_df.columns:
                            latest_timestamp = parsed_df["Timestamp"].iloc[0]
                            st.markdown(f"🕒 **Latest:** {latest_timestamp}")

                # Configure column editability
                column_config = {}
                editable_columns = ["Feature", "Test Case Objective", "Prerequisite", "Test Steps", "Expected Result", "Test Status", "Priority"]
                
                # Hide Test Group column from display
                if 'Test Group' in parsed_df.columns:
                    parsed_df = parsed_df.drop('Test Group', axis=1)
                
                for col in parsed_df.columns:
                    if col in editable_columns:
                        column_config[col] = st.column_config.TextColumn(col, disabled=False)
                    else:
                        column_config[col] = st.column_config.TextColumn(col, disabled=True)

                # Session state management for AI modifications - same pattern as test analysis
                tab_key = "most_recent"
                editor_data_key = f"{tab_key}_editor_data"
                table_updated_key = f"{tab_key}_table_updated_with_ai"
                original_df_key = f"original_{tab_key}_df"
                
                # Initialize session state if not present
                if original_df_key not in st.session_state:
                    st.session_state[original_df_key] = parsed_df.copy()
                
                # Determine which data to display in the editor
                if st.session_state.get(table_updated_key, False) and editor_data_key in st.session_state:
                    display_df = st.session_state[editor_data_key]
                else:
                    display_df = parsed_df
                    st.session_state[editor_data_key] = parsed_df.copy()

                # Generate a unique key for the data editor that changes when AI updates occur
                # This ensures the data editor refreshes with new data
                ai_update_counter = st.session_state.get(f"{tab_key}_ai_update_counter", 0)
                editor_key = f"most_recent_test_cases_editor_{ai_update_counter}"

                # Display the dataframe as an editable table
                edited_df = st.data_editor(
                    display_df,
                    use_container_width=True,
                    num_rows="dynamic",
                    column_config=column_config,
                    key=editor_key
                )

                # Update session state with current editor data
                st.session_state[editor_data_key] = edited_df.copy()

                # Add a save button
                if st.button("💾 Save Changes to Database", key="save_most_recent_test_cases"):
                    with st.spinner("Saving changes to database..."):
                        try:
                            original_data = st.session_state.get(original_df_key, parsed_df)
                            if not edited_df.equals(original_data):
                                # Update the database with edited data
                                success, message = db_helper.update_test_cases_in_database(
                                    db_helper.DATABASE_PATH,
                                    edited_df,
                                    current_jira_id,
                                    current_test_type,
                                    current_user,
                                    is_edited=True
                                )

                                if success:
                                    st.success(f"✅ {message}")
                                    # Update original data after successful save
                                    st.session_state[original_df_key] = edited_df.copy()
                                    st.session_state[table_updated_key] = False  # Reset AI update flag after save
                                else:
                                    st.error(f"❌ {message}")
                            else:
                                st.info("No changes detected.")
                        except Exception as e:
                            st.error(f"Error saving test cases: {str(e)}")

                # Add AI modification interface with proper tab key
                render_ai_modification_interface_for_generator(edited_df, "most_recent")

            else:
                st.info(f"No test cases found for user '{current_user}' with JIRA ID '{current_jira_id}' and test type '{current_test_type}'.")
        else:
            st.info(f"No test cases found in the database for JIRA ID '{current_jira_id}' and test type '{current_test_type}'. Generate some test cases first.")

    except Exception as e:
        st.error(f"Error loading test cases from database: {str(e)}")
        import traceback
        st.code(traceback.format_exc())


def display_test_cases_dataframe(df, output_file=None, source="generated"):
    """
    Display test cases dataframe with editing capabilities and save functionality.
    
    Args:
        df (pd.DataFrame): Test cases dataframe
        output_file (str, optional): Path to output file for generated cases
        source (str): Source of data ("generated" or "database")
        
    Returns:
        None (renders directly to Streamlit UI)
    """
    # Count valid test cases
    test_case_count = count_valid_test_cases(df)
    st.info(f"Found {test_case_count} valid test cases")
    
    # Display the dataframe with editing capabilities
    edited_df = st.data_editor(
        df,
        use_container_width=True,
        num_rows="dynamic",
        key=f"test_cases_editor_{source}"
    )
    
    # Show save button for generated test cases
    if source == "generated" and output_file:
        render_save_to_database_button(edited_df, output_file, source)
    elif source == "database":
        render_save_to_database_button(edited_df, None, source)


def render_save_to_database_button(edited_df, output_file, source):
    """
    Render save to database button with validation and save logic.
    
    Args:
        edited_df (pd.DataFrame): Edited test cases dataframe
        output_file (str, optional): Output file path
        source (str): Source of data
          Returns:
        None (renders directly to Streamlit UI)
    """
    import db_helper as db_helper
    
    if st.button("💾 Save Changes to Database", key=f"save_{source}_test_cases"):
        if validate_test_case_dataframe(edited_df)["is_valid"]:
            try:
                # Get JIRA ID and test type from session state or output file
                jira_id = None
                test_type = "positive"  # Default
                user_name = "user"  # Default
                
                # Try to extract from output file if it's a database URL
                if output_file and output_file.startswith("database://"):
                    parts = output_file.replace("database://", "").split("/")
                    if len(parts) >= 2:
                        jira_id = parts[0]
                        test_type = parts[1]
                
                # Try to get from session state
                if not jira_id and hasattr(st.session_state, 'current_issue') and st.session_state.current_issue:
                    jira_id = st.session_state.current_issue.key
                
                # Get user name from session state if available
                if hasattr(st.session_state, 'username') and st.session_state.username:
                    user_name = st.session_state.username
                
                if not jira_id:
                    st.error("Cannot save to database: JIRA ID not found")
                    return
                  # Save to database using the correct function signature
                test_run_id = save_test_cases_to_database(
                    db_helper.DATABASE_PATH, 
                    jira_id,
                    edited_df, 
                    test_type, 
                    user_name=user_name
                )
                
                if test_run_id:
                    success = True
                    message = f"Successfully saved {len(edited_df)} test cases to database"
                else:
                    success = False
                    message = "Failed to save test cases to database"
                
                if success:
                    # Update Excel file if applicable
                    if output_file and os.path.exists(output_file):
                        edited_df.to_excel(output_file, index=False)
                    
                    st.success(f"✅ {message}")
                else:
                    st.error(f"❌ {message}")
                
            except Exception as e:
                st.error(f"Error saving test cases: {str(e)}")
        else:
            st.error("Cannot save invalid test case data. Please fix the issues first.")

    # --- Move AI TestCase Modification expander below the table and save button ---
    render_ai_modification_interface_for_generator(edited_df, tab_key=source)
    # --- End move ---


def render_ai_modification_interface_for_generator(edited_df, tab_key="test_generator"):
    """
    Render AI modification interface specifically for test generator tabs.
    
    Args:
        edited_df (pd.DataFrame): Current edited dataframe
        tab_key (str): Tab identifier for session state management
    """
    from helpers.ui.components import render_test_case_modification_interface
    
    # Get the proper editor key based on tab
    if tab_key == "test_generator":
        data_editor_key = "generated_test_cases_editor"
    elif tab_key == "most_recent":
        data_editor_key = "most_recent_test_cases_editor"
    else:
        data_editor_key = f"{tab_key}_editor"
    
    # Show action buttons at the top of the AI modification interface
    with st.expander("✨ Modify Test Cases with AI", expanded=False):
        # Add the same action buttons from test analysis page
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🔄 Update Table with AI Output", key=f"update_table_with_ai_output_{tab_key}"):
                ai_modified_key = f"ai_modified_df_{tab_key}"
                table_updated_key = f"{tab_key}_table_updated_with_ai"
                editor_data_key = f"{tab_key}_editor_data"
                
                if ai_modified_key in st.session_state and st.session_state[ai_modified_key] is not None:
                    # Update both the editor data and the actual data editor state
                    st.session_state[editor_data_key] = st.session_state[ai_modified_key].copy()
                    st.session_state[table_updated_key] = True
                    
                    # Increment the AI update counter to force data editor refresh
                    counter_key = f"{tab_key}_ai_update_counter"
                    st.session_state[counter_key] = st.session_state.get(counter_key, 0) + 1
                    
                    st.success("✅ Table updated with latest AI modifications.")
                    st.rerun()
                else:
                    st.warning("No new AI modifications to apply.")

        with col2:
            if st.button("↩️ Undo All Modifications", key=f"undo_all_modifications_{tab_key}"):
                original_df_key = f"original_{tab_key}_df"
                table_updated_key = f"{tab_key}_table_updated_with_ai"
                editor_data_key = f"{tab_key}_editor_data"
                ai_modified_key = f"ai_modified_df_{tab_key}"
                
                if original_df_key in st.session_state:
                    st.session_state[editor_data_key] = st.session_state[original_df_key].copy()
                    st.session_state[table_updated_key] = False
                    if ai_modified_key in st.session_state:
                        del st.session_state[ai_modified_key]
                    
                    # Increment the AI update counter to force data editor refresh
                    counter_key = f"{tab_key}_ai_update_counter"
                    st.session_state[counter_key] = st.session_state.get(counter_key, 0) + 1
                    
                    st.success("All modifications have been undone. Original data restored.")
                    st.rerun()

        # Continue with the existing AI modification interface
        selected_model = st.session_state.get("selected_model_persistent", "gemini-2.0-flash")
        google_api_key = st.session_state.get("google_api_key", "")
        
        # Get current display data from session state
        editor_data_key = f"{tab_key}_editor_data"
        current_data = st.session_state.get(editor_data_key, edited_df)
        
        ai_modified_df = render_test_case_modification_interface(
            test_cases_df=current_data,
            jira_issue=st.session_state.get("jira_issue"),
            selected_model=selected_model,
            google_api_key=google_api_key,
            tab_key=tab_key,
            store_in_session=True,
            show_action_buttons=False  # Don't show buttons again since we added them above
        )