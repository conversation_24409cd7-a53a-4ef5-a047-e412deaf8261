"""
JIRA Operations Module for GretahAI CaseForge

This module provides JIRA API integration, issue extraction, and workflow handling.

Functions:
- extract_jira_issue: Extract JIRA issue details using the JIRA API client
- format_jira_description: Format JIRA description text for better UI display
- parse_enhanced_description_json: Parse enhanced description JSON and format it for display
- handle_jira_extraction_workflow: Handle the JIRA extraction workflow
- handle_jira_enhancement_workflow: Handle the JIRA enhancement workflow

© 2025 GretahAI Team
"""

import re
import json
from datetime import datetime
import streamlit as st


def convert_jira_table_to_markdown(text):
    """
    Convert JIRA table syntax to markdown table format.

    Handles multiple JIRA table formats:
    1. Standard JIRA format: ||Header1||Header2|| and |Cell1|Cell2|
    2. Simple format: Lines with consistent column structure
    3. Tab-separated format: Headers and data separated by tabs/spaces

    Args:
        text (str): Text containing JIRA table syntax

    Returns:
        str: Text with JIRA tables converted to markdown format
    """
    if not text:
        return text

    # Split text into lines for processing
    lines = text.split('\n')
    processed_lines = []
    in_table = False
    table_rows = []

    # First, try to detect and convert standard JIRA table format
    for i, line in enumerate(lines):
        stripped_line = line.strip()

        # Check if this line contains standard JIRA table syntax
        if '||' in stripped_line or (in_table and '|' in stripped_line):
            in_table = True

            # Process header row (contains ||)
            if '||' in stripped_line:
                # Extract headers by splitting on || and filtering empty strings
                parts = stripped_line.split('||')
                headers = [part.strip() for part in parts if part.strip()]

                if headers:
                    # Create markdown header row
                    markdown_header = '| ' + ' | '.join(headers) + ' |'
                    # Create separator row with proper alignment
                    separator = '| ' + ' | '.join(['---' for _ in headers]) + ' |'
                    table_rows.append(markdown_header)
                    table_rows.append(separator)
                continue

            # Process data row (contains single |)
            elif '|' in stripped_line and not stripped_line.startswith('|---'):
                # Normalize the row format
                if not stripped_line.startswith('|'):
                    stripped_line = '|' + stripped_line
                if not stripped_line.endswith('|'):
                    stripped_line = stripped_line + '|'

                # Extract cells (excluding empty first/last splits)
                cells = [cell.strip() for cell in stripped_line.split('|')[1:-1]]
                if cells and any(cell for cell in cells):  # Ensure at least one non-empty cell
                    # Convert JIRA links [text|url] to markdown links [text](url)
                    processed_cells = []
                    for cell in cells:
                        # Handle JIRA link format [text|url] or [url|text]
                        cell = _convert_jira_links_in_cell(cell)
                        processed_cells.append(cell)

                    markdown_row = '| ' + ' | '.join(processed_cells) + ' |'
                    table_rows.append(markdown_row)
                continue
        else:
            # If we were in a table and now we're not, finish the table
            if in_table and table_rows:
                processed_lines.extend(table_rows)
                processed_lines.append('')  # Add blank line after table
                table_rows = []
                in_table = False

            # Add non-table line as-is
            processed_lines.append(line)

    # Handle case where table is at the end of text
    if in_table and table_rows:
        processed_lines.extend(table_rows)

    # If no standard tables were found, try to detect simple table format
    if not any('|' in line for line in processed_lines):
        processed_lines = _convert_simple_table_format(lines)

    return '\n'.join(processed_lines)


def _convert_jira_links_in_cell(cell):
    """
    Convert JIRA link format [text|url] to markdown format [text](url) in a table cell.

    Args:
        cell (str): Table cell content that may contain JIRA links

    Returns:
        str: Cell content with JIRA links converted to markdown format
    """
    # Handle JIRA link format [text|url] or [url|text]
    link_matches = re.finditer(r'\[([^|]+)\|([^]]+)\]', cell)
    for match in reversed(list(link_matches)):  # Process in reverse to maintain positions
        part1, part2 = match.groups()
        # Determine which part is URL and which is text
        if part1.startswith(('http://', 'https://', 'www.')):
            url, text = part1, part2
        elif part2.startswith(('http://', 'https://', 'www.')):
            text, url = part1, part2
        else:
            # If neither looks like a URL, treat first as text, second as URL
            text, url = part1, part2

        # Replace the JIRA link with markdown link
        markdown_link = f'[{text}]({url})'
        cell = cell[:match.start()] + markdown_link + cell[match.end():]

    return cell


def _convert_simple_table_format(lines):
    """
    Convert simple table format to markdown.
    
    Detects tables that have:
    - A header line
    - Data lines with consistent structure
    - Possible separator lines or empty lines
    """
    processed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for potential table patterns - specifically the alternating format
        if line and i + 3 < len(lines):
            # Check for the specific pattern: header1, header2, data1, data2, data3, data4...
            next_lines = [lines[j].strip() for j in range(i+1, min(i+10, len(lines))) if lines[j].strip()]
            
            if len(next_lines) >= 3:
                first_line = line
                second_line = next_lines[0] if len(next_lines) > 0 else ""
                
                # Check if first two lines could be headers
                if (_looks_like_table_header(first_line, next_lines[1:]) and 
                    _looks_like_table_header(second_line, next_lines[1:])):
                    
                    # Collect all the data that follows
                    table_data = [first_line, second_line]
                    j = i + 2  # Start after the two header lines
                    
                    # Collect data lines that come after headers
                    while j < len(lines):
                        data_line = lines[j].strip()
                        if data_line and (
                            'http' in data_line.lower() or 
                            len(data_line) > 3 and not data_line.startswith('#') or
                            any(char.isalnum() for char in data_line)
                        ):
                            table_data.append(data_line)
                            j += 1
                        elif not data_line:
                            # Skip empty lines but continue looking
                            j += 1
                        else:
                            # End of table data
                            break
                    
                    # Convert if we have enough data (at least headers + 2 data rows)
                    if len(table_data) >= 4:
                        markdown_table = _convert_detected_table_to_markdown(table_data)
                        if markdown_table != table_data:  # Only use if conversion was successful
                            processed_lines.extend(markdown_table)
                            processed_lines.append("")  # Add blank line after table
                            i = j
                            continue
        
        # No table found, add line as-is
        processed_lines.append(lines[i])
        i += 1
    
    return processed_lines


def _looks_like_table_header(line, next_lines):
    """
    Check if a line looks like a table header by examining structure.
    """
    # Check for common table header patterns
    if any(keyword in line.lower() for keyword in ['link', 'text', 'url', 'destination', 'name', 'value', 'title', 'description']):
        # Look for corresponding data in next few lines
        for next_line in next_lines[:4]:  # Look further ahead
            if next_line.strip() and ('http' in next_line.lower() or 'www' in next_line.lower() or len(next_line.strip()) > 5):
                return True
    return False


def _looks_like_table_data(line, header_line):
    """
    Check if a line looks like table data that corresponds to the header.
    """
    line_stripped = line.strip()
    header_lower = header_line.lower()
    
    # If header mentions URL/link and this line has URL
    if ('url' in header_lower or 'link' in header_lower) and ('http' in line_stripped.lower() or 'www' in line_stripped.lower()):
        return True
    
    # If header mentions text/name and this line looks like descriptive text
    if ('text' in header_lower or 'name' in header_lower or 'title' in header_lower) and len(line_stripped) > 3 and not line_stripped.startswith('http'):
        return True
    
    # Or if this looks like it could be paired data
    return bool(line_stripped) and len(line_stripped) > 2


def _convert_detected_table_to_markdown(table_data):
    """
    Convert detected table data to markdown format.
    
    This will create a proper markdown table.
    """
    if len(table_data) < 2:
        return table_data

    # Try to detect if we have alternating header/data pattern
    headers = []
    rows = []
    
    # Pattern detection for the specific format shown
    if len(table_data) >= 4:
        # Check if first two lines are headers and rest are data
        potential_headers = table_data[:2]
        potential_data = table_data[2:]
        
        # If we have pairs of data (text + URL), this is likely the format
        if len(potential_data) >= 2 and len(potential_data) % 2 == 0:
            headers = potential_headers
            
            # Group data into rows - each pair becomes one row
            for i in range(0, len(potential_data), 2):
                if i + 1 < len(potential_data):
                    text_content = potential_data[i].strip()
                    url_content = potential_data[i + 1].strip()
                    
                    # Create markdown link if we have both text and URL
                    if url_content.startswith('http') and text_content:
                        markdown_link = f'[{text_content}]({url_content})'
                        rows.append([text_content, markdown_link])
                    else:
                        rows.append([text_content, url_content])
    
    # If pattern detection worked, create markdown table
    if headers and rows:
        markdown_lines = []
        
        # Create header row with both columns
        header_row = '| ' + ' | '.join(headers) + ' |'
        markdown_lines.append(header_row)
        
        # Create separator row with proper number of columns
        separator = '| ' + ' | '.join(['---------' for _ in headers]) + ' |'
        markdown_lines.append(separator)
        
        # Create data rows
        for row in rows:
            # Ensure we have the right number of columns
            while len(row) < len(headers):
                row.append('')
            data_row = '| ' + ' | '.join(row[:len(headers)]) + ' |'
            markdown_lines.append(data_row)
        
        return markdown_lines
    
    # Fallback: return original data
    return table_data


def extract_jira_issue(jira_client, case_id):
    """
    Extract JIRA issue details using the JIRA API client with Story type validation.
    
    FUNCTION TYPE: JIRA HELPER FUNCTION
    
    This function serves as a wrapper around the JIRA API to fetch issue details.
    It handles connection validation, error management, and validates that the issue
    is of type "Story" for JIRA operations.

    Args:
        jira_client: JIRA client instance (from jira library)
            - Should be an authenticated JIRA client object
            - None if JIRA connection is not available
        case_id (str): JIRA case ID to extract
            - Format: "PROJECT-123" (e.g., "TP-1", "DEMO-456")
            - Must be a valid JIRA issue key

    Returns:
        tuple: (success: bool, result: issue_object | error_message: str)
            - success: True if extraction successful, False otherwise
            - result: 
                * If success=True: JIRA issue object with fields (summary, description, etc.)
                * If success=False: Error message string describing the failure

    Usage Example:
        success, issue = extract_jira_issue(jira_client, "TP-1")
        if success:
            print(f"Issue: {issue.fields.summary}")
        else:
            print(f"Error: {issue}")

    Dependencies:
        - Requires jira_client to be properly authenticated
        - Relies on JIRA REST API connectivity
        - Only accepts issues of type "Story"
    """
    try:
        if not jira_client:
            return False, "JIRA client not available. Please check your connection."

        # Fetch the issue from JIRA
        issue = jira_client.issue(case_id)
        
        # Validate issue type - only allow Story type
        if hasattr(issue.fields, 'issuetype') and issue.fields.issuetype:
            issue_type = issue.fields.issuetype.name
            if issue_type.lower() != 'story':
                return False, f"Issue type '{issue_type}' is not supported. Only 'Story' type issues are allowed. Please use a Story type JIRA issue."
        else:
            return False, "Unable to determine issue type. Please ensure the issue exists and is accessible."
        
        return True, issue
    except Exception as e:
        error_message = f"Error fetching JIRA issue: {str(e)}"
        return False, error_message


def format_jira_description(description):
    """
    Format JIRA description text for better UI display with full markdown support.

    FUNCTION TYPE: UI FORMATTING HELPER

    This function processes raw JIRA description text and applies formatting
    to make it more readable in the UI, including bullet points, headings,
    table conversion, and paragraph formatting. The output preserves markdown tables
    while applying HTML formatting to other content.

    Args:
        description (str): Raw JIRA description text

    Returns:
        str: Formatted string with markdown tables and HTML content ready for display with st.markdown()

    Usage Example:
        formatted = format_jira_description(issue.fields.description)
        st.markdown(formatted, unsafe_allow_html=True)
    """
    if not description:
        return "<p>No description available.</p>"

    # First, convert JIRA tables to markdown
    formatted_description = convert_jira_table_to_markdown(description)

    # Split content into sections, preserving markdown tables
    sections = _split_content_preserving_tables(formatted_description)

    processed_sections = []
    for section in sections:
        if section['type'] == 'table':
            # Keep markdown tables as-is
            processed_sections.append(section['content'])
        else:
            # Apply HTML formatting to non-table content
            content = section['content']

            # Format bullet points and numbered lists
            content = re.sub(r'^\s*\*\s+(.+?)$', r'<li>\1</li>', content, flags=re.MULTILINE)
            content = re.sub(r'^\s*#\s+(.+?)$', r'<li>\1</li>', content, flags=re.MULTILINE)

            # Wrap lists in ul/ol tags
            if '<li>' in content:
                content = re.sub(r'(<li>.*?</li>)', r'<ul>\1</ul>', content, flags=re.DOTALL)
                # Fix nested lists (remove extra ul tags)
                content = content.replace('</li>\n<ul>', '')
                content = content.replace('</ul>\n<li>', '</li>')

            # Format headings (JIRA h1. h2. h3. syntax)
            content = re.sub(r'^h([1-6])\.\s+(.+?)$', r'<h\1>\2</h\1>', content, flags=re.MULTILINE)

            # Format bold and italic text
            content = re.sub(r'\*([^*]+)\*', r'<strong>\1</strong>', content)
            content = re.sub(r'_([^_]+)_', r'<em>\1</em>', content)

            # Handle JIRA links [text|url] -> markdown links [text](url)
            content = re.sub(r'\[([^|]+)\|([^]]+)\]', r'<a href="\2" target="_blank">\1</a>', content)

            # Handle paragraphs - preserve line breaks and create proper paragraph structure
            if content.strip():
                content = '<p>' + content.replace('\n\n', '</p><p>').replace('\n', '<br>') + '</p>'

                # Clean up any double paragraph tags
                content = content.replace('<p><p>', '<p>')
                content = content.replace('</p></p>', '</p>')

                # Clean up empty paragraphs
                content = re.sub(r'<p>\s*</p>', '', content)

            processed_sections.append(content)

    return '\n\n'.join(processed_sections)


def _split_content_preserving_tables(text):
    """
    Split content into sections, identifying and preserving markdown tables.

    Args:
        text (str): Text that may contain markdown tables

    Returns:
        list: List of dictionaries with 'type' ('table' or 'content') and 'content'
    """
    lines = text.split('\n')
    sections = []
    current_section = {'type': 'content', 'content': []}
    in_table = False

    for i, line in enumerate(lines):
        stripped_line = line.strip()

        # Check if this line is part of a markdown table
        is_table_line = (
            stripped_line.startswith('|') and stripped_line.endswith('|') and stripped_line.count('|') >= 2
        ) or (
            # Check for separator line (like | --- | --- |)
            stripped_line and '|' in stripped_line and
            all(c in '-|: ' for c in stripped_line) and
            stripped_line.count('|') >= 2
        )

        # Additional check: if previous line was a table header and this looks like a separator
        if not is_table_line and i > 0 and not in_table:
            prev_line = lines[i-1].strip()
            if (prev_line.startswith('|') and prev_line.endswith('|') and
                stripped_line and '|' in stripped_line and
                all(c in '-|: ' for c in stripped_line)):
                is_table_line = True

        if is_table_line and not in_table:
            # Starting a new table - save current content section if it has content
            if current_section['content'] and any(c.strip() for c in current_section['content']):
                sections.append({
                    'type': 'content',
                    'content': '\n'.join(current_section['content'])
                })
            # Start new table section
            current_section = {'type': 'table', 'content': [line]}
            in_table = True
        elif is_table_line and in_table:
            # Continue table
            current_section['content'].append(line)
        elif not is_table_line and in_table:
            # End of table - save table section
            sections.append({
                'type': 'table',
                'content': '\n'.join(current_section['content'])
            })
            # Start new content section
            current_section = {'type': 'content', 'content': [line]}
            in_table = False
        else:
            # Regular content line
            current_section['content'].append(line)

    # Add the last section if it has content
    if current_section['content'] and any(c.strip() for c in current_section['content']):
        sections.append({
            'type': current_section['type'],
            'content': '\n'.join(current_section['content'])
        })

    return sections


def parse_enhanced_description_json(enhanced_desc):
    """
    Parse enhanced description JSON and format it for display.
    
    FUNCTION TYPE: DATA PROCESSING HELPER
    
    This function attempts to parse enhanced descriptions stored as JSON
    and format them into structured HTML for better readability.

    Args:
        enhanced_desc (str): Enhanced description potentially in JSON format

    Returns:
        str: Formatted HTML string or cleaned text if JSON parsing fails

    Usage Example:
        formatted = parse_enhanced_description_json(enhanced_description)
        st.markdown(formatted, unsafe_allow_html=True)
    """
    try:
        # Parse JSON (remove any markdown code blocks if present)
        if "```json" in enhanced_desc:
            json_text = enhanced_desc.split("```json")[1].split("```")[0].strip()
        elif "```" in enhanced_desc:
            json_text = enhanced_desc.split("```")[1].strip()
        else:
            json_text = enhanced_desc.strip()
        
        # Clean up JSON text to help parsing
        json_text = re.sub(r'[\n\r\t]', ' ', json_text)  # Replace newlines/tabs with spaces
        json_text = re.sub(r',\s*}', '}', json_text)  # Fix trailing commas
        json_text = re.sub(r',\s*]', ']', json_text)  # Fix trailing commas in arrays
        
        enhanced_data = json.loads(json_text)
        
        # Format the JSON data as structured HTML
        formatted_description = '<div class="jira-details">'
        
        # User Story Section
        if "userStory" in enhanced_data and enhanced_data["userStory"]:
            formatted_description += f'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">User Story:</h4>'
            formatted_description += f'<p>{enhanced_data["userStory"]}</p>'
        
        # Description Section
        if "description" in enhanced_data and enhanced_data["description"]:
            formatted_description += f'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Description:</h4>'
            formatted_description += '<ul>'
            for item in enhanced_data["description"]:
                formatted_description += f'<li>{item}</li>'
            formatted_description += '</ul>'
        
        # Acceptance Criteria Section
        if "acceptanceCriteria" in enhanced_data and enhanced_data["acceptanceCriteria"]:
            formatted_description += f'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Acceptance Criteria:</h4>'
            formatted_description += '<ol>'
            for idx, item in enumerate(enhanced_data["acceptanceCriteria"], 1):
                formatted_description += f'<li><strong>{idx}.</strong> {item}</li>'
            formatted_description += '</ol>'
            
        formatted_description += '</div>'
        return formatted_description
        
    except (json.JSONDecodeError, ValueError) as e:
        # If JSON parsing fails, create a human-readable format instead of showing raw JSON
        print(f"Error parsing enhanced description as JSON: {e}")
        
        # First, attempt to clean up the JSON-like string for better display
        clean_text = enhanced_desc
        clean_text = re.sub(r'```json', '', clean_text)
        clean_text = re.sub(r'```', '', clean_text)
        clean_text = re.sub(r'\n+', '<br>', clean_text)  # Replace newlines with <br> tags
        clean_text = re.sub(r'^\s*-\s+', r'<li>', clean_text, flags=re.MULTILINE)  # Replace bullet points
        clean_text = re.sub(r'^\s*\d+\.\s+', r'<li>', clean_text, flags=re.MULTILINE)  # Replace numbered list
        clean_text = re.sub(r'(<li>.*?</li>)', r'<ul>\1</ul>', clean_text, flags=re.DOTALL)  # Wrap in ul tags
        clean_text = re.sub(r'(<ol>.*?</ol>)', r'<ol>\1</ol>', clean_text, flags=re.DOTALL)  # Wrap in ol tags
        
        # Format "User Story:" as a heading
        clean_text = re.sub(r'(User Story:)', r'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">\1</h4>', clean_text)
        
        # Format "Description:" as a heading
        clean_text = re.sub(r'(Description:)', r'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">\1</h4>', clean_text)
        
        # Format "Acceptance Criteria:" as a heading
        clean_text = re.sub(r'(Acceptance Criteria:)', r'<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">\1</h4>', clean_text)
        
        # Fix numbered list format in Acceptance Criteria section
        acceptance_criteria_match = re.search(r'Acceptance Criteria:.*?(\d+\.\s+.*?)(?=(User Story:|Description:|$)', clean_text, re.DOTALL)
        if acceptance_criteria_match:
            criteria_content = acceptance_criteria_match.group(1)
            fixed_criteria = re.sub(r'(\d+\.\s+[^0-9]+?)(\d+\.)', r'\1<br>\2', criteria_content)
            clean_text = clean_text.replace(criteria_content, fixed_criteria)
        
        # Format each numbered item with proper styling
        clean_text = re.sub(r'(\d+\.\s+)', r'<br><strong>\1</strong>', clean_text)
        
        # Clean up any potential duplicate <br> tags at the beginning
        clean_text = clean_text.replace('<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Acceptance Criteria:</h4><br>', 
                                      '<h4 style="margin-top:15px; margin-bottom:10px; font-weight:bold;">Acceptance Criteria:</h4>')
        
        return clean_text


def handle_jira_extraction_workflow(jira_client, case_id, ai_provider, selected_model):
    """
    Handle the JIRA extraction workflow when extract button is pressed.
    
    Args:
        jira_client: JIRA client instance
        case_id (str): JIRA issue ID to extract
        ai_provider (str): AI provider name
        selected_model (str): AI model name
    
    Returns:
        None (modifies session state and displays results)
    """
    import db_helper
    
    with st.spinner("Extracting JIRA issue..."):
        success, result = extract_jira_issue(jira_client, case_id)
          
        if success:
            # Set session state variables immediately
            st.session_state.jira_issue_extracted = True
            st.session_state.jira_issue = result
            
            # Clear previous test case generation data when extracting a new JIRA issue
            st.session_state.scenario_data = {
                "issue": None,
                "response": None,
                "output_file": None,
                "processing_time": None,
                "ai_provider": st.session_state.get("ai_provider_radio", "Local"),
                "model_used": None,
                "tokens_used": None
            }
              
            # Load existing enhanced description from DB
            try:
                ed, ts = db_helper.get_jira_issue_enhancement(db_helper.DATABASE_PATH, case_id)
                if ed:
                    st.session_state.enhanced_description = ed
                    st.session_state.enhanced_timestamp = ts
                else:
                    st.session_state.enhanced_description = None
                    st.session_state.enhanced_timestamp = None
            except Exception as e:
                print(f"Error loading enhanced description: {e}")
                st.session_state.enhanced_description = None
                st.session_state.enhanced_timestamp = None
            
            st.success(f"Successfully extracted JIRA issue: {result.key}")
            
            # Force a rerun to update the UI immediately
            st.rerun()
        else:
            st.error(f"Failed to extract JIRA issue: {result}")
            st.session_state.jira_issue_extracted = False
            st.session_state.jira_issue = None
            
            # Also clear scenario data on failed extraction
            st.session_state.scenario_data = {
                "issue": None,
                "response": None,
                "output_file": None,
                "processing_time": None,
                "ai_provider": st.session_state.get("ai_provider_radio", "Local"),
                "model_used": None,
                "tokens_used": None
            }


def handle_jira_enhancement_workflow(jira_client, case_id, ai_provider, selected_model, google_api_key, temperature):
    """
    Handle the JIRA enhancement workflow when enhance button is pressed.
    
    Args:
        jira_client: JIRA client instance
        case_id (str): JIRA issue ID to enhance
        ai_provider (str): AI provider name
        selected_model (str): AI model name
        google_api_key (str): Google AI API key
        temperature (float): Temperature for AI generation
    
    Returns:
        None (modifies session state and displays results)
    """
    from helpers import run_ollama_with_chat, run_google_ai_studio
    import db_helper
    
    with st.spinner("Enhancing description..."):
        issue = st.session_state.jira_issue
        if not issue or not hasattr(issue, 'fields'):
            st.error("Invalid JIRA issue. Please try extracting the issue again.")
            return

        orig_desc = issue.fields.description or ""
        # Build the enhanced prompt with role definition and temperature-specific guidance
        prompt = (
            "You are a QA engineer and expert in Jira documentation. "
            "Your task is to enhance and restructure the given Jira issue without changing its original meaning. "
            "Do not add new features, behaviors, or assumptions. Only improve the formatting, clarity, and structure for better readability and test case creation.\n\n"

            "### OBJECTIVE ###\n"
            "- Maintain the same meaning and intent.\n"
            "- Restructure content into the standard Jira sections.\n"
            "- Reformat content using bullet points or numbered lists where helpful.\n"
            "- Clarify vague language if needed, but never add new content.\n"
            "- Apply the INVEST framework for user stories (explained below).\n\n"
            "- When enhancing the user story, ensure it follows the INVEST Framework:\n"
            "- Independent: Story should not depend heavily on other stories\n"
            "- Negotiable: Keep details flexible enough for discussion and refinement\n"
            "- Valuable: Clearly articulate value to the customer or end-user\n"
            "- Estimable: Make it possible to estimate the development effort\n"
            "- Small: Keep scope small enough to complete within a sprint\n"
            "- Testable: Include clear criteria that can verify the story works\n\n"

            "### OUTPUT FORMAT ###\n"
            "You must produce a JSON object with the following structure:\n"
            "```json\n"
            "{\n"
            "  \"userStory\": \"As a [user type], I want to [action], so that [benefit]\",\n"
            "  \"description\": [\n"
            "    \"Point 1\",\n"
            "    \"Point 2\",\n"
            "    ...\n"
            "  ],\n"
            "  \"acceptanceCriteria\": [\n"
            "    \"Criterion 1\",\n"
            "    \"Criterion 2\",\n"
            "    ...\n"
            "  ]\n"
            "}\n"
            "```\n\n"

            f"Original Jira Description:\n{orig_desc}\n\n"
        )
        
        # Add additional context if provided
        if st.session_state.get("additional_context_for_enhancement"):
            prompt += f"Additional context from the user:\n{st.session_state['additional_context_for_enhancement']}\n\n"
            
        prompt += "Return ONLY the enhanced JSON structure without any additional text, explanations or markdown formatting:"
        
        # Call the appropriate LLM with temperature for enhancement
        try:
            if ai_provider == "Local":
                enhanced_desc = run_ollama_with_chat(prompt, model=selected_model, temperature=temperature)
            else:
                # For Cloud, use temperature parameter only for enhancement
                result = run_google_ai_studio(
                    prompt, 
                    api_key=google_api_key, 
                    model=selected_model,
                    temperature=temperature  # Pass temperature only for enhancement
                )
                enhanced_desc = result[0] if isinstance(result, tuple) else str(result)

            # Store in session state
            st.session_state.enhanced_description = enhanced_desc
            ts = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            st.session_state.enhanced_timestamp = ts            # Save to database
            db_helper.update_jira_issue_enhancement(
                db_helper.DATABASE_PATH,
                case_id,
                enhanced_desc,
                ts
            )

            # Show success message with provider info
            provider_info = f"using {ai_provider} ({selected_model})"
            st.success(f"Successfully enhanced JIRA issue description {provider_info}")
            
            # Force a rerun to update the UI immediately
            st.rerun()
        except Exception as e:
            st.error(f"Error during enhancement: {e}")
            print(f"Error during enhancement workflow: {e}")
        # Remove the duplicate success message
