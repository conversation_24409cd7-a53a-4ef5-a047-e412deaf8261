# GretahAI ScriptWeaver Developer Guide

**Comprehensive Technical Architecture and Development Documentation**

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

---

## 📋 Table of Contents

1. [System Architecture](#-system-architecture)
2. [Component Architecture](#-component-architecture)
3. [Data Flow & State Management](#-data-flow--state-management)
4. [Stage Architecture](#-stage-architecture)
5. [AI Integration Framework](#-ai-integration-framework)
6. [Development Setup](#-development-setup)
7. [Coding Standards](#-coding-standards)
8. [Testing Framework](#-testing-framework)

## 🏛️ System Architecture

### High-Level Architecture Overview

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[Streamlit App] --> B[Stage Components 1-10]
        B --> C[UI Components Library]
        C --> D[Enterprise Styling]
    end

    subgraph "Business Logic Layer"
        E[StateManager] --> F[Stage Modules]
        F --> G[Core Services]
        G --> H[Template Management]
        H --> I[Performance Monitor]
    end

    subgraph "AI Integration Layer"
        J[AI Request Router] --> K[Google AI API]
        K --> L[Prompt Engineering]
        L --> M[Response Processing]
        M --> N[Template Analysis]
    end

    subgraph "Data Layer"
        O[JSON Step Storage] --> P[SQLite Script DB]
        P --> Q[File System]
        Q --> R[Configuration]
    end

    subgraph "External Services"
        S[Google AI Gemini 2.0] --> T[Selenium WebDriver]
        T --> U[Target Websites]
        U --> V[Chrome Browser]
    end

    A --> E
    G --> J
    G --> O
    J --> S

    style A fill:#e3f2fd
    style E fill:#f3e5f5
    style J fill:#fff3e0
    style O fill:#e8f5e8
    style S fill:#ffebee
```

### Architecture Principles

#### 1. **Separation of Concerns**
- **Presentation Layer**: Streamlit UI components and user interaction
- **Business Logic**: Core application logic and workflow management
- **Data Layer**: Persistent storage and data management
- **External Services**: Third-party integrations and browser automation

#### 2. **Modular Design**
- **Stage Modules**: Independent, single-responsibility stage implementations
- **Core Services**: Reusable business logic components
- **UI Components**: Extracted, reusable interface elements
- **Utility Modules**: Pure functions and helper utilities

#### 3. **Enterprise Patterns**
- **Centralized State Management**: Single source of truth for application state
- **Dependency Injection**: Configurable service dependencies
- **Error Handling**: Comprehensive error recovery and logging
- **Performance Monitoring**: Real-time system monitoring and optimization

## 🧩 Component Architecture

### Core Components Hierarchy

```mermaid
graph TD
    subgraph "Application Core"
        A[app.py] --> B[StateManager]
        B --> C[Stage Router]
    end
    
    subgraph "Stage Layer"
        C --> D[Stage 1-10 Modules]
        D --> E[UI Components]
    end
    
    subgraph "Service Layer"
        F[AI Services] --> G[Element Detection]
        G --> H[Script Generation]
        H --> I[Performance Monitor]
    end
    
    subgraph "Data Layer"
        J[Step Data Storage] --> K[Script Storage]
        K --> L[Configuration]
    end
    
    D --> F
    F --> J
```

### Component Responsibilities

#### **Application Layer**
- **app.py**: Main application entry point and Streamlit configuration
- **StateManager**: Centralized state management with persistence
- **Stage Router**: Navigation and stage transition management

#### **Stage Layer**
- **stage1.py - stage10.py**: Individual stage implementations
- **UI Components**: Extracted UI rendering logic
- **Stage Helpers**: Stage-specific utility functions

#### **Service Layer**
- **AI Services**: Google AI integration and prompt management
- **Element Detection**: Browser automation and element discovery
- **Script Generation**: PyTest script creation and optimization
- **Performance Monitor**: System monitoring and metrics collection

#### **Data Layer**
- **JSON Storage**: Step data and workflow persistence
- **SQLite Database**: Script storage and metadata management
- **File System**: Temporary files, logs, and artifacts

## 🔄 Data Flow & State Management

### StateManager Architecture

```python
@dataclass
class StateManager:
    """Centralized state management for GretahAI ScriptWeaver"""
    
    # Core workflow state
    current_stage: StateStage = StateStage.STAGE1
    excel_file: Optional[pd.DataFrame] = None
    website_url: str = ""
    google_api_key: str = ""
    
    # Test case and step data
    selected_test_case: Optional[Dict] = None
    step_table: Optional[List[Dict]] = None
    current_step_index: int = 0
    
    # Advanced features
    hybrid_editing_enabled: bool = False
    performance_monitoring: bool = True
    debug_mode: bool = False
    
    # Stage 10 specific state
    selected_template_id: Optional[str] = None
    gap_analysis_results: Optional[Dict] = None
    template_based_script: Optional[str] = None
```

### State Transition Flow

```mermaid
stateDiagram-v2
    [*] --> STAGE1: Application Start
    STAGE1 --> STAGE2: Excel Uploaded
    STAGE2 --> STAGE3: Website Configured
    STAGE3 --> STAGE4: Test Case Converted
    STAGE4 --> STAGE5: Elements Detected
    STAGE5 --> STAGE6: Test Data Configured
    STAGE6 --> STAGE7: Script Generated
    STAGE7 --> STAGE8: Script Executed
    STAGE8 --> STAGE3: Return to Test Cases
    STAGE8 --> STAGE9: Browse Scripts
    STAGE9 --> STAGE10: Template Generation
    STAGE10 --> STAGE9: Script Saved
```

### Data Persistence Strategy

#### **Session State Management**
- **Streamlit Session State**: Temporary UI state and user interactions
- **StateManager Persistence**: Cross-session data reliability
- **Automatic Backup**: Regular state snapshots for recovery

#### **Persistent Storage**
- **JSON Files**: Step data with atomic updates and thread safety
- **SQLite Database**: Script storage with proper indexing
- **File System**: Logs, screenshots, and temporary artifacts

## 🎭 Stage Architecture

### Stage Implementation Pattern

Each stage follows a consistent architectural pattern:

```python
def stage_main():
    """Main stage function with consistent structure"""
    
    # 1. State validation and prerequisites
    if not validate_stage_prerequisites():
        display_prerequisite_warning()
        return
    
    # 2. UI rendering delegation
    render_stage_interface()
    
    # 3. Business logic orchestration
    handle_stage_operations()
    
    # 4. State management and transitions
    manage_stage_transitions()

def render_stage_interface():
    """UI rendering delegated to components"""
    from ui_components.stage_components import render_stage_ui
    render_stage_ui(st.session_state.state_manager)

def handle_stage_operations():
    """Core business logic implementation"""
    # Stage-specific operations
    pass

def manage_stage_transitions():
    """State transitions and navigation"""
    # Transition logic and validation
    pass
```

### Stage 10 Advanced Architecture

Stage 10 implements the most sophisticated architecture with complete separation:

```python
# stages/stage10.py - Core orchestration
def stage10_main():
    """Stage 10: Script Playground with template-based generation"""
    
    # Validate prerequisites and API configuration
    if not validate_stage10_prerequisites():
        return
    
    # Delegate UI rendering to components
    from ui_components.stage10_components import render_stage10_interface
    render_stage10_interface()
    
    # Handle template-based operations
    handle_template_operations()

# ui_components/stage10_components.py - UI implementation
def render_stage10_interface():
    """Professional enterprise UI for Stage 10"""
    
    render_template_selection_interface()
    render_gap_analysis_interface()
    render_script_generation_interface()
    render_execution_interface()

# core/template_helpers.py - Business logic
def analyze_template_compatibility(template, test_case):
    """Advanced template compatibility analysis"""
    
    # AI-powered gap analysis
    # Compatibility scoring
    # Missing requirement identification
```

## 🤖 AI Integration Framework

### Centralized AI Architecture

```mermaid
graph TD
    subgraph "AI Request Layer"
        A[Stage Modules] --> B[AI Request Router]
        B --> C[core/ai.py]
    end
    
    subgraph "AI Processing Layer"
        C --> D[Request Validation]
        D --> E[Prompt Engineering]
        E --> F[Google AI API]
    end
    
    subgraph "Response Processing"
        F --> G[Response Validation]
        G --> H[Content Parsing]
        H --> I[Error Handling]
    end
    
    subgraph "Logging & Monitoring"
        I --> J[Request Logging]
        J --> K[Performance Metrics]
        K --> L[Token Tracking]
    end
```

### AI Integration Patterns

#### **Centralized Request Handling**
```python
# core/ai.py
async def generate_llm_response(
    prompt: str,
    request_type: str,
    context: Optional[Dict] = None,
    max_tokens: int = 4000
) -> Dict[str, Any]:
    """Centralized AI request handling with comprehensive logging"""
    
    request_id = generate_request_id()
    
    try:
        # Request validation and preparation
        validated_prompt = validate_and_prepare_prompt(prompt)
        
        # API call with error handling
        response = await call_google_ai_api(validated_prompt, max_tokens)
        
        # Response processing and validation
        processed_response = process_ai_response(response)
        
        # Comprehensive logging
        log_ai_interaction(request_id, prompt, response, context)
        
        return processed_response
        
    except Exception as e:
        log_ai_error(request_id, prompt, e, context)
        raise AIProcessingError(f"AI request failed: {str(e)}")
```

#### **Prompt Engineering Framework**
```python
# core/template_prompt_builder.py
class TemplatePromptBuilder:
    """Advanced prompt engineering for template-based generation"""
    
    def build_gap_analysis_prompt(
        self,
        template_script: Dict[str, Any],
        target_test_case: Dict[str, Any]
    ) -> str:
        """Build sophisticated gap analysis prompt"""
        
        prompt_sections = [
            self._build_context_section(),
            self._build_template_analysis_section(template_script),
            self._build_target_case_section(target_test_case),
            self._build_analysis_instructions(),
            self._build_output_format_specification()
        ]
        
        return "\n\n".join(prompt_sections)
```

### AI Response Processing

#### **Markdown Code Block Parsing**
```python
# core/ai_helpers.py
def clean_llm_response(response_text: str) -> str:
    """Clean AI response by removing markdown code blocks"""
    
    # Handle various markdown formats
    patterns = [
        r'```python\n(.*?)\n```',
        r'```py\n(.*?)\n```',
        r'```\n(.*?)\n```'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, response_text, re.DOTALL)
        if match:
            return match.group(1).strip()
    
    return response_text.strip()
```

## 🛠️ Development Setup

### Development Environment

#### **Prerequisites**
- Python 3.8+ with virtual environment support
- Git for version control
- Chrome browser for testing
- Google AI API key for development

#### **Setup Steps**
```bash
# 1. Clone repository
git clone <repository-url>
cd GretahAI_ScriptWeaver

# 2. Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate  # Windows

# 3. Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies

# 4. Configure environment
cp config.example.json config.json
# Edit config.json with your API keys

# 5. Set environment variables
export SCRIPTWEAVER_DEBUG=true
export GOOGLE_API_KEY="your_api_key"

# 6. Run application
streamlit run app.py
```

### Development Tools

#### **Code Quality Tools**
```bash
# Code formatting
black --line-length 88 .

# Import sorting
isort .

# Type checking
mypy --ignore-missing-imports .

# Linting
flake8 --max-line-length 88 .

# Security scanning
bandit -r .
```

#### **Testing Tools**
```bash
# Unit tests
pytest tests/unit/

# Integration tests
pytest tests/integration/

# End-to-end tests
pytest tests/e2e/

# Coverage reporting
pytest --cov=. --cov-report=html
```

### Development Workflow

#### **Feature Development Process**
1. **Create Feature Branch**: `git checkout -b feature/new-feature`
2. **Implement Changes**: Follow coding standards and patterns
3. **Write Tests**: Unit, integration, and e2e tests as appropriate
4. **Code Review**: Peer review and quality checks
5. **Testing**: Comprehensive testing in development environment
6. **Documentation**: Update relevant documentation
7. **Merge**: Merge to main branch after approval

#### **Debugging Workflow**
1. **Enable Debug Mode**: Set `SCRIPTWEAVER_DEBUG=true`
2. **Check Logs**: Review logs in `ai_logs/` and console output
3. **Use Debug Panels**: Utilize built-in debug interfaces
4. **Performance Monitoring**: Monitor system resources and metrics
5. **State Inspection**: Examine StateManager and session state

## 📏 Coding Standards

### Python Code Standards

#### **Code Style Guidelines**
- **PEP 8 Compliance**: Follow Python Enhancement Proposal 8 standards
- **Line Length**: Maximum 88 characters (Black formatter standard)
- **Import Organization**: Use isort for consistent import ordering
- **Type Hints**: Use type hints for all function signatures and class attributes
- **Docstrings**: Google-style docstrings for all public functions and classes

#### **Naming Conventions**
```python
# Classes: PascalCase
class StateManager:
    pass

# Functions and variables: snake_case
def generate_test_script():
    test_case_id = "TC_001"

# Constants: UPPER_SNAKE_CASE
MAX_RETRY_ATTEMPTS = 3
DEFAULT_TIMEOUT = 30

# Private methods: leading underscore
def _internal_helper_function():
    pass
```

#### **Function Documentation Standards**
```python
def generate_template_based_script(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    gap_analysis: Dict[str, Any]
) -> Tuple[str, Dict[str, Any]]:
    """Generate a test script using template-based approach.

    Args:
        template_script: The template script data with metadata
        target_test_case: The target test case for script generation
        gap_analysis: Results from AI gap analysis

    Returns:
        Tuple containing the generated script and metadata

    Raises:
        ScriptGenerationError: If script generation fails
        ValidationError: If input validation fails

    Example:
        >>> template = load_template("template_001")
        >>> test_case = get_test_case("TC_001")
        >>> analysis = analyze_gaps(template, test_case)
        >>> script, metadata = generate_template_based_script(
        ...     template, test_case, analysis
        ... )
    """
```

### Architecture Patterns

#### **StateManager Integration Pattern**
```python
def stage_function():
    """Standard pattern for stage function implementation"""

    # 1. Access state manager
    state = st.session_state.state_manager

    # 2. Validate prerequisites
    if not validate_stage_prerequisites(state):
        display_prerequisite_warning()
        return

    # 3. Delegate UI rendering
    render_stage_ui(state)

    # 4. Handle business logic
    handle_stage_operations(state)

    # 5. Manage state transitions
    if should_advance_stage(state):
        advance_to_next_stage(state)
```

#### **UI Component Pattern**
```python
def render_component_interface(state: StateManager) -> None:
    """Standard pattern for UI component implementation"""

    # 1. Component header with consistent styling
    st.markdown("### Component Title")

    # 2. Input validation and error handling
    if not validate_component_prerequisites(state):
        st.error("Prerequisites not met")
        return

    # 3. Main component content
    with st.container():
        render_component_content(state)

    # 4. Action buttons with consistent styling
    render_component_actions(state)
```

#### **Error Handling Pattern**
```python
def operation_with_error_handling():
    """Standard error handling pattern"""

    try:
        # Main operation
        result = perform_operation()

        # Success logging
        logger.info(f"Operation completed successfully: {result}")

        return result

    except ValidationError as e:
        # User input errors
        logger.warning(f"Validation error: {e}")
        st.error(f"Input validation failed: {e}")
        return None

    except APIError as e:
        # External service errors
        logger.error(f"API error: {e}")
        st.error("External service unavailable. Please try again.")
        return None

    except Exception as e:
        # Unexpected errors
        logger.exception(f"Unexpected error: {e}")
        st.error("An unexpected error occurred. Please contact support.")
        return None
```

### Performance Guidelines

#### **Streamlit Performance Patterns**
```python
# Use caching for expensive operations
@st.cache_data
def load_template_data(template_id: str) -> Dict[str, Any]:
    """Cache template data to avoid repeated loading"""
    return load_template_from_storage(template_id)

# Minimize rerun triggers
if st.button("Process", key="unique_key"):
    process_data()
    st.rerun()  # Explicit rerun after state changes

# Use session state efficiently
if "expensive_data" not in st.session_state:
    st.session_state.expensive_data = compute_expensive_data()
```

#### **Memory Management**
```python
# Clean up large objects
def cleanup_session_data():
    """Clean up large session data objects"""

    keys_to_clean = [
        "large_dataframe",
        "cached_ai_responses",
        "temporary_files"
    ]

    for key in keys_to_clean:
        if key in st.session_state:
            del st.session_state[key]
```

## 🧪 Testing Framework

### Testing Architecture

```mermaid
graph TD
    subgraph "Test Layers"
        A[Unit Tests] --> B[Integration Tests]
        B --> C[End-to-End Tests]
        C --> D[Performance Tests]
    end

    subgraph "Test Categories"
        E[Core Logic Tests] --> F[UI Component Tests]
        F --> G[AI Integration Tests]
        G --> H[State Management Tests]
    end

    subgraph "Test Infrastructure"
        I[Test Fixtures] --> J[Mock Services]
        J --> K[Test Data]
        K --> L[Test Utilities]
    end
```

### Unit Testing Standards

#### **Test Structure Pattern**
```python
import pytest
from unittest.mock import Mock, patch
from src.core.ai import generate_llm_response

class TestAIIntegration:
    """Test suite for AI integration functionality"""

    def setup_method(self):
        """Setup for each test method"""
        self.mock_api_key = "test_api_key"
        self.sample_prompt = "Generate test script"

    def test_generate_llm_response_success(self):
        """Test successful AI response generation"""
        # Arrange
        expected_response = {"content": "Generated script"}

        with patch('src.core.ai.call_google_ai_api') as mock_api:
            mock_api.return_value = expected_response

            # Act
            result = generate_llm_response(
                prompt=self.sample_prompt,
                request_type="script_generation"
            )

            # Assert
            assert result["content"] == "Generated script"
            mock_api.assert_called_once()

    def test_generate_llm_response_api_error(self):
        """Test AI response generation with API error"""
        # Arrange
        with patch('src.core.ai.call_google_ai_api') as mock_api:
            mock_api.side_effect = APIError("API unavailable")

            # Act & Assert
            with pytest.raises(AIProcessingError):
                generate_llm_response(
                    prompt=self.sample_prompt,
                    request_type="script_generation"
                )
```

#### **Test Fixtures**
```python
# conftest.py
import pytest
from src.state_manager import StateManager, StateStage

@pytest.fixture
def sample_state_manager():
    """Provide a sample StateManager for testing"""
    state = StateManager()
    state.current_stage = StateStage.STAGE3
    state.website_url = "https://example.com"
    state.google_api_key = "test_key"
    return state

@pytest.fixture
def sample_test_case():
    """Provide sample test case data"""
    return {
        "test_case_id": "TC_001",
        "objective": "Test login functionality",
        "steps": [
            {
                "step_no": 1,
                "test_steps": "Navigate to login page",
                "expected_result": "Login page displayed"
            }
        ]
    }

@pytest.fixture
def mock_ai_service():
    """Provide mock AI service for testing"""
    with patch('src.core.ai.generate_llm_response') as mock:
        mock.return_value = {
            "content": "Generated test script",
            "metadata": {"tokens_used": 100}
        }
        yield mock
```

### Integration Testing

#### **Stage Integration Tests**
```python
class TestStageIntegration:
    """Integration tests for stage workflows"""

    def test_stage3_to_stage4_workflow(self, sample_state_manager):
        """Test workflow from Stage 3 to Stage 4"""
        # Arrange
        state = sample_state_manager
        state.current_stage = StateStage.STAGE3

        # Act
        result = process_stage3_conversion(state)

        # Assert
        assert result.success is True
        assert state.step_table is not None
        assert state.current_stage == StateStage.STAGE4

    def test_template_based_generation_workflow(self):
        """Test complete template-based generation workflow"""
        # Arrange
        template_id = "template_001"
        test_case_id = "TC_001"

        # Act
        result = execute_template_workflow(template_id, test_case_id)

        # Assert
        assert result.script_generated is True
        assert result.gap_analysis_completed is True
        assert result.execution_successful is True
```

### End-to-End Testing

#### **Browser Automation Tests**
```python
from selenium import webdriver
from selenium.webdriver.common.by import By
import pytest

class TestE2EWorkflow:
    """End-to-end testing with browser automation"""

    @pytest.fixture
    def browser(self):
        """Setup browser for E2E testing"""
        driver = webdriver.Chrome()
        yield driver
        driver.quit()

    def test_complete_workflow_e2e(self, browser):
        """Test complete workflow from upload to execution"""
        # Navigate to application
        browser.get("http://localhost:8501")

        # Stage 1: Upload Excel file
        upload_element = browser.find_element(By.CSS_SELECTOR, "[data-testid='file-uploader']")
        upload_element.send_keys("/path/to/test_cases.xlsx")

        # Verify upload success
        assert "File uploaded successfully" in browser.page_source

        # Continue through stages...
        # (Additional E2E test steps)
```

### Performance Testing

#### **Performance Benchmarks**
```python
import time
import psutil
from memory_profiler import profile

class TestPerformance:
    """Performance testing for critical operations"""

    def test_ai_response_time(self):
        """Test AI response time performance"""
        start_time = time.time()

        response = generate_llm_response(
            prompt="Generate simple test script",
            request_type="performance_test"
        )

        end_time = time.time()
        response_time = end_time - start_time

        # Assert response time is within acceptable limits
        assert response_time < 30.0  # 30 seconds max
        assert response is not None

    @profile
    def test_memory_usage_template_loading(self):
        """Test memory usage during template loading"""
        initial_memory = psutil.Process().memory_info().rss

        # Load multiple templates
        templates = []
        for i in range(10):
            template = load_template(f"template_{i:03d}")
            templates.append(template)

        final_memory = psutil.Process().memory_info().rss
        memory_increase = final_memory - initial_memory

        # Assert memory usage is reasonable
        assert memory_increase < 100 * 1024 * 1024  # 100MB max increase
```

### Test Data Management

#### **Test Data Fixtures**
```python
# tests/fixtures/test_data.py
SAMPLE_EXCEL_DATA = {
    "Test Case ID": ["TC_001", "TC_002"],
    "Test Case Objective": [
        "Verify login functionality",
        "Verify logout functionality"
    ],
    "Steps": [
        "1. Navigate to login page\n2. Enter credentials\n3. Click login",
        "1. Click logout button\n2. Verify redirect to login page"
    ]
}

SAMPLE_TEMPLATE_SCRIPT = {
    "template_id": "template_001",
    "script_content": "# Generated test script\nimport pytest\n...",
    "metadata": {
        "creation_date": "2025-01-01",
        "optimization_status": "optimized",
        "test_case_count": 1
    }
}
```

### Continuous Integration

#### **CI/CD Pipeline Configuration**
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run unit tests
      run: pytest tests/unit/ --cov=src --cov-report=xml

    - name: Run integration tests
      run: pytest tests/integration/

    - name: Code quality checks
      run: |
        black --check .
        isort --check-only .
        flake8 .
        mypy --ignore-missing-imports .
```

## 🔧 Development Tools & Utilities

### Debug Utilities

#### **Debug Mode Configuration**
```python
# utils/debug_helpers.py
import os
import logging
from typing import Any, Dict

def setup_debug_logging():
    """Configure comprehensive debug logging"""

    if os.getenv("SCRIPTWEAVER_DEBUG", "false").lower() == "true":
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('debug.log'),
                logging.StreamHandler()
            ]
        )

def debug_state_snapshot(state: StateManager) -> Dict[str, Any]:
    """Create debug snapshot of current state"""

    return {
        "current_stage": state.current_stage.value,
        "excel_loaded": state.excel_file is not None,
        "website_configured": bool(state.website_url),
        "api_key_set": bool(state.google_api_key),
        "test_case_selected": state.selected_test_case is not None,
        "step_table_generated": state.step_table is not None,
        "timestamp": time.time()
    }
```

#### **Performance Monitoring Utilities**
```python
# utils/performance_monitor.py
import time
import psutil
from contextlib import contextmanager
from typing import Generator, Dict, Any

@contextmanager
def performance_monitor(operation_name: str) -> Generator[Dict[str, Any], None, None]:
    """Context manager for monitoring operation performance"""

    start_time = time.time()
    start_memory = psutil.Process().memory_info().rss
    start_cpu = psutil.cpu_percent()

    metrics = {}

    try:
        yield metrics
    finally:
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss
        end_cpu = psutil.cpu_percent()

        metrics.update({
            "operation": operation_name,
            "duration": end_time - start_time,
            "memory_delta": end_memory - start_memory,
            "cpu_usage": (start_cpu + end_cpu) / 2,
            "timestamp": end_time
        })

        log_performance_metrics(metrics)

def log_performance_metrics(metrics: Dict[str, Any]):
    """Log performance metrics for analysis"""

    logger = logging.getLogger("performance")
    logger.info(f"Performance: {metrics}")

    # Store metrics for analysis
    store_metrics_for_analysis(metrics)
```

---

## 📚 Additional Development Resources

### Code Review Guidelines

#### **Review Checklist**
- [ ] Code follows established patterns and standards
- [ ] All functions have appropriate type hints and docstrings
- [ ] Error handling is comprehensive and appropriate
- [ ] Tests are included for new functionality
- [ ] Performance implications are considered
- [ ] Security considerations are addressed
- [ ] Documentation is updated as needed

#### **Architecture Review Points**
- [ ] Component separation is maintained
- [ ] StateManager integration follows patterns
- [ ] UI components are properly extracted
- [ ] AI integration uses centralized services
- [ ] Data persistence follows established patterns

### Deployment Guidelines

#### **Production Deployment Checklist**
- [ ] All environment variables configured
- [ ] API keys and secrets properly secured
- [ ] Database migrations completed
- [ ] Performance monitoring enabled
- [ ] Logging configuration optimized
- [ ] Error tracking configured
- [ ] Backup procedures verified

#### **Monitoring and Maintenance**
- [ ] Performance metrics collection enabled
- [ ] Error rate monitoring configured
- [ ] Resource usage tracking active
- [ ] Automated backup verification
- [ ] Security scanning scheduled
- [ ] Dependency update procedures established

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

**For technical support and development inquiries**: <EMAIL>

**Enterprise Development Program**: Contact us for enterprise development partnerships and custom feature development opportunities.
