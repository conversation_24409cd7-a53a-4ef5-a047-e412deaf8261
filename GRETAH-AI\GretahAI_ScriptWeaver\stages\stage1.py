"""
Stage 1: Upload Excel File

This module handles the Excel file upload and parsing functionality.
Maintains the StateManager pattern and follows the established architectural patterns.

Phase 3b Enhancement: Standardized logging with centralized infrastructure
"""

import os
import logging
import tempfile
import streamlit as st
import pandas as pd
from pathlib import Path
from state_manager import StateStage

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
logger = get_stage_logger("stage1")

# Import helper functions from other modules
from core.excel_parser import parse_excel
from debug_utils import debug
from ui_components.optimization_prevention import clear_optimization_confirmation_state

def validate_uploaded_file(uploaded_file, file_content):
    """
    Validate uploaded file before processing.

    Args:
        uploaded_file: Streamlit uploaded file object
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of validation errors (empty if valid)
    """
    debug("Starting file validation",
          stage="stage1",
          operation="file_validation",
          context={
              'filename': uploaded_file.name,
              'file_size_bytes': len(file_content),
              'file_size_mb': round(len(file_content) / (1024 * 1024), 2)
          })

    errors = []

    # File size validation (50MB limit)
    if len(file_content) == 0:
        error_msg = "File is empty"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={'error_type': 'empty_file', 'filename': uploaded_file.name})
    elif len(file_content) > 50 * 1024 * 1024:
        error_msg = "File too large (max 50MB)"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={
                  'error_type': 'file_too_large',
                  'filename': uploaded_file.name,
                  'file_size_mb': round(len(file_content) / (1024 * 1024), 2)
              })

    # File extension validation
    if not uploaded_file.name.lower().endswith('.xlsx'):
        error_msg = "Invalid file extension (must be .xlsx)"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={
                  'error_type': 'invalid_extension',
                  'filename': uploaded_file.name,
                  'actual_extension': os.path.splitext(uploaded_file.name)[1]
              })

    # Basic Excel file signature check
    if len(file_content) >= 2 and not file_content.startswith(b'PK'):
        error_msg = "File does not appear to be a valid Excel file"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={
                  'error_type': 'invalid_signature',
                  'filename': uploaded_file.name,
                  'file_signature': file_content[:4].hex() if len(file_content) >= 4 else 'too_short'
              })

    if not errors:
        debug("File validation successful",
              stage="stage1",
              operation="file_validation_success",
              context={
                  'filename': uploaded_file.name,
                  'file_size_mb': round(len(file_content) / (1024 * 1024), 2)
              })

    return errors

def safe_get_test_case_count(test_cases):
    """
    Safely get test case count with validation.

    Uses the improved counting logic from excel_parser.get_test_case_count()
    to correctly count test cases regardless of data format.

    Args:
        test_cases: Test cases data structure

    Returns:
        int: Number of test cases (0 if invalid)
    """
    if not test_cases:
        debug("Test cases data is empty or None",
              stage="stage1",
              operation="test_case_count_validation",
              context={'test_cases_type': type(test_cases).__name__, 'is_none': test_cases is None})
        return 0

    if not isinstance(test_cases, list):
        debug(f"Warning: test_cases is not a list, type: {type(test_cases)}",
              stage="stage1",
              operation="test_case_count_validation_error",
              context={
                  'expected_type': 'list',
                  'actual_type': type(test_cases).__name__,
                  'data_preview': str(test_cases)[:100] if test_cases else 'None'
              })
        return 0

    # Use the improved counting logic from excel_parser
    from core.excel_parser import get_test_case_count
    count = get_test_case_count(test_cases)

    debug(f"Test case count validated: {count}",
          stage="stage1",
          operation="test_case_count_validation_success",
          context={'count': count, 'data_type': 'list', 'counting_method': 'excel_parser.get_test_case_count'})
    return count

def validate_stage1_completion(state):
    """
    Validate Stage 1 completion criteria.

    Args:
        state: StateManager instance

    Returns:
        tuple: (is_valid: bool, message: str)
    """
    debug("Starting Stage 1 completion validation",
          stage="stage1",
          operation="completion_validation")

    validation_context = {
        'has_test_cases_attr': hasattr(state, 'test_cases'),
        'has_uploaded_excel_attr': hasattr(state, 'uploaded_excel'),
        'test_cases_count': safe_get_test_case_count(getattr(state, 'test_cases', None))
    }

    if not hasattr(state, 'test_cases') or not state.test_cases:
        error_msg = "No test cases loaded"
        debug(f"Stage 1 completion validation failed: {error_msg}",
              stage="stage1",
              operation="completion_validation_error",
              context={**validation_context, 'error_type': 'no_test_cases'})
        return False, error_msg

    if not hasattr(state, 'uploaded_excel') or not state.uploaded_excel:
        error_msg = "No Excel file uploaded"
        debug(f"Stage 1 completion validation failed: {error_msg}",
              stage="stage1",
              operation="completion_validation_error",
              context={**validation_context, 'error_type': 'no_uploaded_file'})
        return False, error_msg

    if not os.path.exists(state.uploaded_excel):
        error_msg = "Uploaded file no longer exists"
        debug(f"Stage 1 completion validation failed: {error_msg}",
              stage="stage1",
              operation="completion_validation_error",
              context={
                  **validation_context,
                  'error_type': 'file_not_found',
                  'file_path': state.uploaded_excel
              })
        return False, error_msg

    success_msg = "Stage 1 completed successfully"
    debug(f"Stage 1 completion validation successful: {success_msg}",
          stage="stage1",
          operation="completion_validation_success",
          context={
              **validation_context,
              'file_path': state.uploaded_excel,
              'file_exists': True
          })
    return True, success_msg

@st.cache_data
def parse_excel_cached(file_content, _cache_version="v2.0"):
    """
    Cached version of parse_excel function with improved resource management.

    Args:
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of test cases
    """
    debug("Using cached parse_excel function",
          stage="stage1",
          operation="excel_parsing_cached",
          context={'file_size_bytes': len(file_content)})

    temp_file_path = None
    parsing_start_time = None

    try:
        # Create a temporary file to pass to parse_excel
        debug("Creating temporary file for Excel parsing",
              stage="stage1",
              operation="temp_file_creation")

        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        debug(f"Temporary file created: {temp_file_path}",
              stage="stage1",
              operation="temp_file_created",
              context={'temp_file_path': temp_file_path, 'file_size_bytes': len(file_content)})

        # Parse the Excel file using the existing function
        import time
        parsing_start_time = time.time()

        debug("Starting Excel file parsing",
              stage="stage1",
              operation="excel_parsing_start",
              context={'temp_file_path': temp_file_path})

        test_cases = parse_excel(temp_file_path)

        parsing_duration = time.time() - parsing_start_time
        test_case_count = safe_get_test_case_count(test_cases)

        debug(f"Excel parsing completed successfully",
              stage="stage1",
              operation="excel_parsing_success",
              context={
                  'parsing_duration_seconds': round(parsing_duration, 3),
                  'test_cases_found': test_case_count,
                  'temp_file_path': temp_file_path
              })

        return test_cases

    except Exception as e:
        parsing_duration = time.time() - parsing_start_time if parsing_start_time else 0
        debug(f"Error in cached parse_excel: {e}",
              stage="stage1",
              operation="excel_parsing_error",
              context={
                  'error_type': type(e).__name__,
                  'error_message': str(e),
                  'parsing_duration_seconds': round(parsing_duration, 3) if parsing_duration else 0,
                  'temp_file_path': temp_file_path
              })
        logger.error(f"Error in cached parse_excel: {e}")
        raise
    finally:
        # Ensure cleanup even if parsing fails
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                debug(f"Temporary file cleaned up successfully: {temp_file_path}",
                      stage="stage1",
                      operation="temp_file_cleanup_success",
                      context={'temp_file_path': temp_file_path})
            except Exception as cleanup_error:
                debug(f"Warning: Failed to cleanup temp file {temp_file_path}: {cleanup_error}",
                      stage="stage1",
                      operation="temp_file_cleanup_error",
                      context={
                          'temp_file_path': temp_file_path,
                          'cleanup_error': str(cleanup_error)
                      })

def stage1_upload_excel(state):
    """Phase 1: Upload Excel File."""
    debug("Stage 1: Excel upload interface accessed",
          stage="stage1",
          operation="stage_entry",
          context={'current_stage': state.current_stage.name if hasattr(state, 'current_stage') else 'unknown'})

    st.markdown("<h2 class='stage-header'>Phase 1: Upload Test Case Excel</h2>", unsafe_allow_html=True)

    # Help text in an expander to reduce visual clutter
    with st.expander("About Excel Format", expanded=False):
        st.markdown("""
        Upload an Excel file with the following columns:
        - **Test Case ID**: Unique identifier
        - **Test Case Objective**: Description of what is being tested
        - **Step No**: Step number
        - **Test Steps**: Action to perform
        - **Expected Result**: Expected outcome
        """)

    # Simplified file uploader with clearer label
    uploaded_file = st.file_uploader("Select Excel file (.xlsx)", type=["xlsx"], key="excel_uploader")

    # Tip message positioned near the file uploader for better UX
    st.info("💡 **Tip:** To remove the uploaded file, use the ✖ button on the file uploader above.")

    debug("File uploader rendered",
          stage="stage1",
          operation="ui_render",
          context={'uploader_key': 'excel_uploader', 'accepted_types': ['xlsx']})

    # CRITICAL FIX: Detect when file uploader is cleared via built-in X button
    _handle_file_uploader_state_change(state, uploaded_file)

    if uploaded_file is not None:
        debug(f"File uploaded: {uploaded_file.name}",
              stage="stage1",
              operation="file_upload",
              context={
                  'filename': uploaded_file.name,
                  'file_size_bytes': len(uploaded_file.getvalue()),
                  'file_type': uploaded_file.type
              })

        try:
            # Get the file content
            file_content = uploaded_file.getvalue()

            debug("File content retrieved",
                  stage="stage1",
                  operation="file_content_retrieval",
                  context={
                      'filename': uploaded_file.name,
                      'content_size_bytes': len(file_content),
                      'content_size_mb': round(len(file_content) / (1024 * 1024), 2)
                  })

            # Validate uploaded file before processing
            validation_errors = validate_uploaded_file(uploaded_file, file_content)
            if validation_errors:
                debug(f"File validation failed with {len(validation_errors)} errors",
                      stage="stage1",
                      operation="file_validation_failed",
                      context={
                          'filename': uploaded_file.name,
                          'error_count': len(validation_errors),
                          'errors': validation_errors
                      })
                for error in validation_errors:
                    st.error(f"❌ {error}")
                return

            # Check if this is the same file we've already processed
            current_hash = hash(file_content)
            if hasattr(state, 'last_file_content_hash') and state.last_file_content_hash == current_hash:
                debug("File content unchanged - skipping reprocessing",
                      stage="stage1",
                      operation="file_content_unchanged",
                      context={
                          'filename': uploaded_file.name,
                          'content_hash': current_hash,
                          'skip_processing': True
                      })
                logger.info("File content unchanged - skipping reprocessing")
                # Still show success message and preview for user feedback
                st.success(f"✅ File already processed: {uploaded_file.name}")
            else:
                debug("New or changed file detected - processing",
                      stage="stage1",
                      operation="file_content_changed",
                      context={
                          'filename': uploaded_file.name,
                          'new_hash': current_hash,
                          'old_hash': getattr(state, 'last_file_content_hash', None),
                          'requires_processing': True
                      })
                logger.info("New or changed file detected - processing")
                # Update the content hash in state
                old_hash = getattr(state, 'last_file_content_hash', None)
                state.last_file_content_hash = current_hash
                debug(f"State change: last_file_content_hash = {current_hash} (was: {old_hash})",
                      stage="stage1",
                      operation="state_update",
                      context={
                          'field': 'last_file_content_hash',
                          'new_value': current_hash,
                          'old_value': old_hash
                      })

                # Save the uploaded file to a temporary location
                temp_dir = Path("temp_uploads")
                temp_dir.mkdir(exist_ok=True)

                # Use a consistent filename based on the uploaded file name instead of timestamp
                safe_filename = ''.join(c if c.isalnum() else '_' for c in uploaded_file.name)
                temp_file_path = temp_dir / f"test_cases_{safe_filename}"

                with open(temp_file_path, "wb") as f:
                    f.write(file_content)

                # Update state with file information
                old_excel_path = getattr(state, 'uploaded_excel', None)
                state.uploaded_excel = str(temp_file_path)
                state.uploaded_file = str(temp_file_path)  # Backward compatibility
                debug("Excel file path updated", stage="stage1", operation="file_upload",
                      context={'new_path': state.uploaded_excel, 'old_path': old_excel_path})

                # Process file in a collapsible section
                with st.expander("Processing Results", expanded=True):
                    # Verify the file was saved correctly
                    if os.path.exists(temp_file_path) and os.path.getsize(temp_file_path) > 0:
                        st.success(f"✅ File uploaded: {uploaded_file.name}")

                        # Parse the excel file using the cached function
                        if parse_excel:
                            try:
                                old_test_cases_count = safe_get_test_case_count(getattr(state, 'test_cases', None))
                                state.test_cases = parse_excel_cached(file_content, _cache_version="v2.0")
                                new_test_cases_count = safe_get_test_case_count(state.test_cases)

                                # Clear optimization confirmation state when new test cases are loaded
                                clear_optimization_confirmation_state()

                                debug("Test cases parsed from Excel", stage="stage1", operation="file_processing",
                                      context={'new_count': new_test_cases_count, 'old_count': old_test_cases_count})

                                if new_test_cases_count == 0:
                                    st.warning("⚠️ No test cases found. Check file format.")
                                else:
                                    st.success(f"✅ Parsed {new_test_cases_count} test cases")

                                    # Only advance to Stage 2 if we're currently in Stage 1
                                    if state.current_stage == StateStage.STAGE1_UPLOAD:
                                        # Validate completion before advancing
                                        is_valid, validation_message = validate_stage1_completion(state)

                                        if is_valid:
                                            # Use centralized stage advancement with proper state persistence
                                            success = state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {new_test_cases_count} test cases")

                                            if success:
                                                # Force state update in session state
                                                st.session_state['state'] = state
                                                st.session_state['stage_progression_message'] = f"✅ {validation_message}. Proceeding to Website Configuration."

                                                # Log successful transition for debugging
                                                debug("Stage transition successful", stage="stage1", operation="stage_transition",
                                                      context={'from_stage': 'stage1', 'to_stage': 'stage2', 'test_cases': new_test_cases_count})
                                                logger.info(f"Stage 1 -> Stage 2 transition successful, test_cases count: {new_test_cases_count}")

                                                # Rerun to show Stage 2 immediately
                                                st.rerun()
                                                return  # Exit early since rerun will restart the function
                                            else:
                                                debug("Stage transition failed", stage="stage1", operation="error_handling")
                                                logger.error("Failed to advance from Stage 1 to Stage 2")
                                                st.error("❌ Failed to advance to next stage. Please try again.")
                                        else:
                                            st.warning(f"⚠️ Cannot proceed: {validation_message}")
                            except Exception as e:
                                debug("Error parsing Excel file", stage="stage1", operation="error_handling",
                                      context={'error': str(e), 'error_type': type(e).__name__})
                                st.error(f"❌ Error parsing file: {e}")
                                state.test_cases = None # Ensure it's reset on error
                        else:
                            st.warning("⚠️ Excel parsing function not available")
                    else:
                        st.error("❌ Failed to save file")

            # Always display a preview of the Excel file (using the cached file if available)
            if hasattr(state, 'uploaded_excel') and os.path.exists(state.uploaded_excel):
                try:
                    df = pd.read_excel(state.uploaded_excel) # Read from the saved temp file
                    with st.expander("📊 File Preview", expanded=True):
                        # Show essential metric prominently
                        if hasattr(state, 'test_cases'):
                            test_case_count = safe_get_test_case_count(state.test_cases)
                            if test_case_count > 0:
                                st.success(f"✅ **{test_case_count} test cases** successfully parsed")
                            else:
                                st.warning("⚠️ No test cases found in file")

                        # Clean, focused data preview
                        st.caption("First 10 rows:")
                        st.dataframe(df.head(10), use_container_width=True, hide_index=True)
                except Exception as e:
                    debug("Error reading file for preview", stage="stage1", operation="error_handling",
                          context={'error': str(e), 'error_type': type(e).__name__})
                    st.error(f"❌ Error reading file: {e}")
        except Exception as e:
            debug("Error processing uploaded file", stage="stage1", operation="error_handling",
                  context={'error': str(e), 'error_type': type(e).__name__})
            st.error(f"❌ Error processing file: {e}")


def _handle_file_uploader_state_change(state, uploaded_file):
    """
    Handle changes in the file uploader state, including when files are removed via the built-in X button.

    This function detects when the Streamlit file uploader's built-in "X" button is used
    to remove files and ensures proper state cleanup is performed.

    Args:
        state: StateManager instance
        uploaded_file: Current uploaded file from st.file_uploader (None if cleared)
    """
    # Check if we have state data indicating a file was previously uploaded
    has_previous_file = bool(getattr(state, 'uploaded_excel', None) or getattr(state, 'uploaded_file', None))
    has_current_file = uploaded_file is not None

    debug("File uploader state change detection",
          stage="stage1",
          operation="file_uploader_state_check",
          context={
              'has_previous_file': has_previous_file,
              'has_current_file': has_current_file,
              'previous_file_path': getattr(state, 'uploaded_excel', None),
              'current_file_name': uploaded_file.name if uploaded_file else None
          })

    # Case 1: File was removed via built-in X button (had file before, now None)
    if has_previous_file and not has_current_file:
        debug("File removal detected via built-in X button",
              stage="stage1",
              operation="file_uploader_removal_detected",
              context={
                  'removal_method': 'builtin_x_button',
                  'previous_file_path': getattr(state, 'uploaded_excel', None),
                  'current_stage': state.current_stage.name if hasattr(state, 'current_stage') else 'unknown'
              })

        # CRITICAL FIX: Don't auto-remove file if we have test cases in memory
        # This prevents navigation-induced file uploader resets from clearing valid data
        has_test_cases = bool(getattr(state, 'test_cases', None))

        if has_test_cases:
            debug("File uploader reset detected during navigation - preserving test cases",
                  stage="stage1",
                  operation="navigation_file_reset_protection",
                  context={
                      'has_test_cases': has_test_cases,
                      'test_cases_count': len(state.test_cases) if state.test_cases else 0,
                      'current_stage': state.current_stage.name if hasattr(state, 'current_stage') else 'unknown'
                  })

            # Show info message but don't clear test cases
            # Note: File uploader was reset but test case data is preserved in memory
        else:
            # Only perform cleanup if we don't have test cases (genuine file removal)
            debug("Genuine file removal detected - no test cases to preserve",
                  stage="stage1",
                  operation="genuine_file_removal",
                  context={'has_test_cases': False})

            # Show immediate notification about file removal
            st.warning("⚠️ **File Removed via File Uploader**")

            # Perform automatic cleanup without confirmation (since user already clicked X)
            success = state.remove_uploaded_file("File removed via built-in file uploader X button")

            if success:
                st.success("✅ File state cleaned up successfully!")
                debug("Automatic file cleanup completed",
                      stage="stage1",
                      operation="automatic_file_cleanup",
                      context={'success': True, 'trigger': 'builtin_x_button'})
                # Rerun to refresh the UI and show updated state
                st.rerun()
            else:
                st.error("❌ Failed to clean up file state")
                debug("Automatic file cleanup failed",
                      stage="stage1",
                      operation="automatic_file_cleanup_error",
                      context={'success': False, 'trigger': 'builtin_x_button'})

    # Case 2: New file uploaded (replace existing file)
    elif has_previous_file and has_current_file:
        # Check if it's a different file
        previous_file_path = getattr(state, 'uploaded_excel', None)
        if previous_file_path:
            previous_file_name = os.path.basename(previous_file_path)
            current_file_name = uploaded_file.name

            if previous_file_name != current_file_name:
                debug("File replacement detected",
                      stage="stage1",
                      operation="file_replacement_detected",
                      context={
                          'previous_file': previous_file_name,
                          'new_file': current_file_name
                      })

                # File replacement detected - normal file processing logic will handle the new file
                debug(f"File replacement: {previous_file_name} → {current_file_name}", stage="stage1", operation="file_replacement")

    # Case 3: First file upload (no previous file, now has file)
    elif not has_previous_file and has_current_file:
        debug("First file upload detected",
              stage="stage1",
              operation="first_file_upload",
              context={'file_name': uploaded_file.name})
        # Normal processing will handle this case

    # Case 4: No file before, no file now (normal state)
    else:
        debug("No file state change",
              stage="stage1",
              operation="no_file_state_change",
              context={'stable_state': 'no_file'})
