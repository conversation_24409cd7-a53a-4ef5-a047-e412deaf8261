"""
Flag helpers for GretahAI ScriptWeaver.

This module provides utilities for managing temporary session state flags that need to
self-destruct after the next rerun to prevent stale flag issues.
"""

import streamlit as st
import logging
from contextlib import contextmanager
from typing import Any, Optional

# Configure logging
logger = logging.getLogger("ScriptWeaver.flag_helpers")


@contextmanager
def one_shot_flag(key: str, value: Any = True):
    """
    Context manager for creating self-destructing session state flags.
    
    This ensures that temporary flags are automatically cleaned up after the next
    script rerun, preventing stale flag issues that can cause phantom stage jumps
    or other unexpected behavior.
    
    Args:
        key: The session state key for the flag
        value: The value to set for the flag (default: True)
        
    Usage:
        # Instead of:
        st.session_state['coming_from_stage7'] = True
        st.rerun()
        
        # Use:
        with one_shot_flag('coming_from_stage7'):
            st.rerun()
            
    The flag will be present during the next rerun and automatically deleted thereafter.
    """
    logger.info(f"Setting one-shot flag: {key} = {value}")
    
    # Set the flag in session state
    st.session_state[key] = value
    
    try:
        # Yield control to the calling code
        yield
    finally:
        # Schedule the flag for deletion on the next script run
        # We use a special cleanup key to track flags that need deletion
        cleanup_key = f"_cleanup_{key}"
        st.session_state[cleanup_key] = True
        logger.info(f"Scheduled one-shot flag for cleanup: {key}")


def cleanup_one_shot_flags():
    """
    Clean up all one-shot flags that were scheduled for deletion.
    
    This function should be called at the beginning of each script run to clean up
    flags that were marked for deletion in the previous run.
    """
    cleanup_keys = [k for k in st.session_state.keys() if k.startswith('_cleanup_')]
    
    for cleanup_key in cleanup_keys:
        # Extract the original flag key
        original_key = cleanup_key[9:]  # Remove '_cleanup_' prefix
        
        # Delete the original flag if it exists
        if original_key in st.session_state:
            logger.info(f"Cleaning up one-shot flag: {original_key}")
            del st.session_state[original_key]
        
        # Delete the cleanup marker
        del st.session_state[cleanup_key]


def is_flag_set(key: str) -> bool:
    """
    Check if a flag is currently set in session state.
    
    Args:
        key: The session state key to check
        
    Returns:
        bool: True if the flag exists and is truthy, False otherwise
    """
    return st.session_state.get(key, False)


def get_flag_value(key: str, default: Any = None) -> Any:
    """
    Get the value of a flag from session state.
    
    Args:
        key: The session state key to get
        default: Default value if key doesn't exist
        
    Returns:
        The flag value or default if not found
    """
    return st.session_state.get(key, default)


def set_persistent_flag(key: str, value: Any = True):
    """
    Set a persistent flag that won't be automatically cleaned up.
    
    Use this for flags that need to persist across multiple reruns.
    
    Args:
        key: The session state key for the flag
        value: The value to set for the flag (default: True)
    """
    logger.info(f"Setting persistent flag: {key} = {value}")
    st.session_state[key] = value


def clear_flag(key: str) -> bool:
    """
    Manually clear a flag from session state.
    
    Args:
        key: The session state key to clear
        
    Returns:
        bool: True if the flag was cleared, False if it didn't exist
    """
    if key in st.session_state:
        logger.info(f"Manually clearing flag: {key}")
        del st.session_state[key]
        return True
    return False


def list_active_flags() -> list:
    """
    Get a list of all active flags in session state.
    
    This excludes cleanup markers and other internal session state keys.
    
    Returns:
        list: List of active flag keys
    """
    # Filter out Streamlit internal keys and cleanup markers
    excluded_prefixes = ('_cleanup_', 'FormSubmitter:', 'FileUploader:')
    excluded_keys = ('state', 'stage_progression_message')
    
    active_flags = []
    for key in st.session_state.keys():
        if (not key.startswith(excluded_prefixes) and 
            key not in excluded_keys):
            active_flags.append(key)
    
    return active_flags


def debug_flags():
    """
    Debug function to display all current flags and their values.
    
    This is useful for debugging flag-related issues.
    """
    logger.info("=== DEBUG: Current Session State Flags ===")
    
    active_flags = list_active_flags()
    cleanup_flags = [k for k in st.session_state.keys() if k.startswith('_cleanup_')]
    
    logger.info(f"Active flags ({len(active_flags)}):")
    for key in active_flags:
        value = st.session_state[key]
        logger.info(f"  {key} = {value} ({type(value).__name__})")
    
    logger.info(f"Cleanup markers ({len(cleanup_flags)}):")
    for key in cleanup_flags:
        original_key = key[9:]  # Remove '_cleanup_' prefix
        logger.info(f"  {original_key} (scheduled for cleanup)")
    
    logger.info("=== END DEBUG ===")


# Convenience functions for common flag patterns
@contextmanager
def stage_transition_flag(from_stage: str, to_stage: str):
    """
    Context manager for stage transition flags.
    
    Args:
        from_stage: The stage we're transitioning from (e.g., 'stage7')
        to_stage: The stage we're transitioning to (e.g., 'stage4')
    """
    flag_key = f"transitioning_from_{from_stage}_to_{to_stage}"
    with one_shot_flag(flag_key):
        yield


@contextmanager
def coming_from_stage_flag(stage: str):
    """
    Context manager for 'coming from stage' flags.
    
    Args:
        stage: The stage we're coming from (e.g., 'stage7')
    """
    flag_key = f"coming_from_{stage}"
    with one_shot_flag(flag_key):
        yield


@contextmanager
def operation_in_progress_flag(operation: str):
    """
    Context manager for operation in progress flags.
    
    Args:
        operation: The operation that's in progress (e.g., 'optimization')
    """
    flag_key = f"{operation}_in_progress"
    with one_shot_flag(flag_key):
        yield
