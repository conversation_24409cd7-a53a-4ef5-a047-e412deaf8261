"""
Utilities Module for GretahAI CaseForge

This module provides utility functions including test case counter management
and common operations used across the application.

Components:
- counters: Test case counter and ID management
- common: Shared utility functions and workflows

© 2025 GretahAI Team
"""

from .counters import (
    reset_test_case_counter,
    set_test_case_counter,
    get_test_case_counter,
    get_highest_test_case_id
)

from .common import (
    generate_test_scenarios,
    _generate_test_scenarios_internal
)

__all__ = [
    'reset_test_case_counter',
    'set_test_case_counter',
    'get_test_case_counter',
    'get_highest_test_case_id',
    'generate_test_scenarios',
    '_generate_test_scenarios_internal'
]
