import os
import time
import json
import pytest
import requests
import random
import string
import csv
import yaml
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriver<PERSON>ait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    StaleElementReferenceException,
    ElementNotInteractableException,
    WebDriverException
)
from selenium.webdriver.common.action_chains import ActionChains

# Constants
BASE_URL = "https://the-internet.herokuapp.com"
TEST_DATA_DIR = Path("test_data")
TEST_DATA_DIR.mkdir(exist_ok=True)

# Reuse TestUtils class from test_suite.py
class TestUtils:
    @staticmethod
    def wait_for_page_load(driver, timeout=10):
        """Wait for page to finish loading completely"""
        try:
            WebDriverWait(driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
        except TimeoutException:
            pass
    
    @staticmethod
    def highlight_element(driver, element, duration=0.5):
        """Highlight an element temporarily for visual debugging"""
        original_style = element.get_attribute("style")
        driver.execute_script(
            "arguments[0].setAttribute('style', arguments[1]);", 
            element, 
            "border: 2px solid red; background: yellow;"
        )
        time.sleep(duration)
        driver.execute_script(
            "arguments[0].setAttribute('style', arguments[1]);", 
            element, 
            original_style
        )

    @staticmethod
    def is_element_in_viewport(driver, element):
        """Check if element is fully visible in viewport using JavaScript"""
        return driver.execute_script("""
            var rect = arguments[0].getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        """, element)

# Performance metrics collection
class PerformanceMetrics:
    @staticmethod
    def get_page_load_metrics(driver):
        """Extract detailed page load timing metrics"""
        try:
            timing = driver.execute_script("return window.performance.timing")
            navigation_start = timing["navigationStart"]
            
            metrics = {
                "total_page_load": timing["loadEventEnd"] - navigation_start,
                "dns_time": timing["domainLookupEnd"] - timing["domainLookupStart"],
                "connection_time": timing["connectEnd"] - timing["connectStart"],
                "server_response_time": timing["responseStart"] - timing["requestStart"],
                "page_download_time": timing["responseEnd"] - timing["responseStart"],
                "dom_interactive_time": timing["domInteractive"] - navigation_start,
                "dom_complete_time": timing["domComplete"] - navigation_start
            }
            
            return metrics
        except Exception:
            return {}

# 1. Test for Forgot Password functionality
def test_forgot_password(browser):
    """Test the forgot password functionality and form submission"""
    try:
        # Navigate to forgot password page
        browser.get(f"{BASE_URL}/forgot_password")
        TestUtils.wait_for_page_load(browser)
        
        # Verify page title and form presence
        page_title = browser.find_element(By.TAG_NAME, "h2")
        assert "Forgot Password" in page_title.text, "Forgot password page not loaded"
        
        # Find email input field
        email_field = browser.find_element(By.ID, "email")
        assert email_field.is_displayed(), "Email field not displayed"
        
        # Enter test email
        test_email = f"test{random.randint(1000, 9999)}@example.com"
        email_field.send_keys(test_email)
        
        # Submit the form
        browser.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
        
        # Wait for confirmation page
        WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.ID, "content"))
        )
        
        # Verify success message
        content_div = browser.find_element(By.ID, "content")
        assert "Your e-mail's been sent" in content_div.text, "Success message not found"
        
        # Verify we're on a different page
        current_url = browser.current_url
        assert "email_sent" in current_url or "forgot_password" not in current_url, "URL did not change after submission"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 2. Test for Shifting Content Menu interactions
def test_shifting_content_menu(browser):
    """Test interactions with shifting content menu items"""
    try:
        # Navigate to shifting content page
        browser.get(f"{BASE_URL}/shifting_content")
        TestUtils.wait_for_page_load(browser)
        
        # Verify page loaded correctly
        page_header = browser.find_element(By.TAG_NAME, "h3")
        assert "Shifting Content" in page_header.text
        
        # Click on the Menu Element link
        menu_link = browser.find_element(By.XPATH, "//a[contains(text(), 'Example 1: Menu Element')]")
        menu_link.click()
        
        # Wait for menu page to load
        TestUtils.wait_for_page_load(browser)
        
        # Capture initial positions of menu items
        menu_items = browser.find_elements(By.CSS_SELECTOR, "ul li a")
        assert len(menu_items) >= 5, f"Expected at least 5 menu items, found {len(menu_items)}"
        
        # Store initial positions to compare later
        initial_positions = []
        for item in menu_items:
            initial_positions.append({
                "text": item.text,
                "position": item.location
            })
        
        # Click on "click here" link to shift content
        shift_link = browser.find_element(By.XPATH, "//a[contains(text(), 'click here')]")
        shift_link.click()
        
        # Wait for page to reload
        TestUtils.wait_for_page_load(browser)
        
        # Get new positions of menu items
        new_menu_items = browser.find_elements(By.CSS_SELECTOR, "ul li a")
        assert len(new_menu_items) == len(initial_positions), "Menu item count changed after shift"
        
        # Check that at least one item has shifted
        shifted = False
        for i, item in enumerate(new_menu_items):
            if item.location != initial_positions[i]["position"]:
                shifted = True
                break
                
        assert shifted, "No menu items shifted position"
        
        # Test navigation through each menu item
        for i, item in enumerate(new_menu_items):
            item_text = item.text
            item_href = item.get_attribute("href")
            
            # Click only if it's not an external link
            if BASE_URL in item_href:
                item.click()
                TestUtils.wait_for_page_load(browser)
                
                # Verify navigation occurred
                assert browser.current_url == item_href, f"Navigation failed for menu item: {item_text}"
                
                # Navigate back to menu page
                browser.back()
                TestUtils.wait_for_page_load(browser)
                
                # Re-find menu items as DOM might have been refreshed
                new_menu_items = browser.find_elements(By.CSS_SELECTOR, "ul li a")
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 3. Test for Status Codes Navigation
def test_status_codes_navigation(browser):
    """Navigate to different HTTP status code pages and validate responses"""
    try:
        # Navigate to status codes page
        browser.get(f"{BASE_URL}/status_codes")
        TestUtils.wait_for_page_load(browser)
        
        # Verify page loaded correctly
        page_header = browser.find_element(By.TAG_NAME, "h3")
        assert "Status Codes" in page_header.text
        
        # Find all status code links
        status_links = browser.find_elements(By.CSS_SELECTOR, "ul li a")
        status_codes = [link.text for link in status_links]
        
        # Expected status codes to test
        expected_codes = ["200", "301", "404", "500"]
        for code in expected_codes:
            assert code in status_codes, f"Status code {code} link not found on page"
        
        # Test each status code page
        for code in expected_codes:
            # Find and click the status code link
            link = browser.find_element(By.XPATH, f"//a[text()='{code}']")
            link_url = link.get_attribute("href")
            link.click()
            
            # Wait for page to load
            TestUtils.wait_for_page_load(browser)
            
            # Verify we're on the correct status code page
            current_url = browser.current_url
            assert f"status_codes/{code}" in current_url, f"URL does not contain status code {code}"
            
            # Verify page content mentions the status code
            page_content = browser.find_element(By.TAG_NAME, "body").text
            assert f"This page returned a {code} status code" in page_content, f"Status code {code} not mentioned on page"
            
            # Verify network response code matches expected code
            # Note: Can't directly check the actual HTTP status since browser follows redirects
            # This would normally require additional network monitoring tools
            
            # Go back to status codes page
            back_link = browser.find_element(By.XPATH, "//a[contains(text(), 'here')]")
            back_link.click()
            
            # Wait for page to load
            TestUtils.wait_for_page_load(browser)
            
            # Verify we're back on the status codes page
            assert browser.current_url.endswith("status_codes"), "Failed to return to status codes page"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise

# 4. Test for WYSIWYG Editor interactions
def test_wysiwyg_editor(browser):
    """Test interactions within the WYSIWYG editor iframe"""
    try:
        # Navigate to WYSIWYG editor page
        browser.get(f"{BASE_URL}/tinymce")
        TestUtils.wait_for_page_load(browser)
        
        # Verify page loaded correctly
        page_header = browser.find_element(By.TAG_NAME, "h3")
        assert "TinyMCE WYSIWYG Editor" in page_header.text
        
        # Wait for editor iframe to load
        WebDriverWait(browser, 10).until(
            EC.frame_to_be_available_and_switch_to_it((By.ID, "mce_0_ifr"))
        )
        
        # Find the editor body element
        editor_body = WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.ID, "tinymce"))
        )
        
        # Save initial content
        initial_content = editor_body.text
        
        # Clear existing content
        editor_body.clear()
        
        # Type new content
        test_content = f"Automated test content {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        editor_body.send_keys(test_content)
        
        # Switch back to main frame to interact with toolbar
        browser.switch_to.default_content()
        
        # Click Bold button
        bold_button = browser.find_element(By.CSS_SELECTOR, "button[aria-label='Bold']")
        bold_button.click()
        
        # Switch back to iframe
        browser.switch_to.frame(browser.find_element(By.ID, "mce_0_ifr"))
        
        # Add bold text
        bold_text = "THIS TEXT SHOULD BE BOLD"
        editor_body.send_keys(bold_text)
        
        # Switch back to main frame
        browser.switch_to.default_content()
        
        # Click Italic button
        italic_button = browser.find_element(By.CSS_SELECTOR, "button[aria-label='Italic']")
        italic_button.click()
        
        # Switch back to iframe
        browser.switch_to.frame(browser.find_element(By.ID, "mce_0_ifr"))
        
        # Add italic text
        italic_text = "THIS TEXT SHOULD BE ITALIC"
        editor_body.send_keys(italic_text)
        
        # Verify content contains all the text we added
        current_content = editor_body.get_attribute("innerHTML")
        assert test_content in current_content, "Regular text not found in editor"
        assert bold_text in current_content, "Bold text not found in editor"
        assert italic_text in current_content, "Italic text not found in editor"
        
        # Switch back to main frame to test the menu options
        browser.switch_to.default_content()
        
        # Open File menu
        file_menu_button = browser.find_element(By.XPATH, "//span[text()='File']")
        file_menu_button.click()
        
        # Wait for menu to appear
        WebDriverWait(browser, 5).until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "div.tox-collection__item"))
        )
        
        # Close menu by clicking elsewhere
        page_header.click()
        
        # Switch back to iframe one last time to verify content is still there
        browser.switch_to.frame(browser.find_element(By.ID, "mce_0_ifr"))
        final_content = editor_body.get_attribute("innerHTML")
        
        # Verify content persists
        assert test_content in final_content, "Content lost after menu interaction"
        
    except AssertionError as e:
        raise
    except Exception as e:
        raise
