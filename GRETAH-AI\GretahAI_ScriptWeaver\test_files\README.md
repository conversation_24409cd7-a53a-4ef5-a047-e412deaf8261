# Test Files for File Upload Feature

This directory contains sample files for testing the file upload functionality in GretahAI ScriptWeaver.

## Available Test Files

### Data Files
- **test_data.csv**: Sample CSV file with employee data (10 records)
- **test_config.json**: Sample JSON configuration file

### Documents
- **test_document.txt**: Plain text document for testing

## Usage Instructions

1. **Copy files to a convenient location** for testing
2. **Use these files in the testing scenarios** described in the testing guide
3. **Create additional test files** as needed for specific scenarios

## File Characteristics

### test_data.csv
- **Size**: ~500 bytes
- **Format**: CSV with headers
- **Content**: Employee data with name, email, age, department, salary
- **Use Case**: Data import testing

### test_config.json
- **Size**: ~800 bytes
- **Format**: Valid JSON
- **Content**: Application configuration settings
- **Use Case**: Configuration file upload testing

### test_document.txt
- **Size**: ~1.5KB
- **Format**: Plain text
- **Content**: Sample document with testing information
- **Use Case**: Document upload testing

## Creating Additional Test Files

### For Image Testing
Create or download sample images:
- **Small JPEG**: < 1MB for quick testing
- **Large PNG**: 5-8MB to test size handling
- **Oversized image**: > 10MB to test size limits

### For Security Testing
Create files with suspicious content:
- **Script content**: Files containing `<script>` tags
- **Executable signatures**: Files with PE headers
- **Path traversal**: Files with `../` in content

### For Error Testing
Create problematic files:
- **Empty files**: 0-byte files
- **Corrupted files**: Files with invalid headers
- **Binary files**: Files with mixed content

## Safety Notes

- All provided test files are safe and contain no malicious content
- When creating security test files, ensure they are clearly marked and safely contained
- Do not use real personal or sensitive data in test files
- Clean up test files after testing is complete

## File Organization

```
test_files/
├── README.md           # This file
├── test_data.csv       # Sample CSV data
├── test_config.json    # Sample JSON config
└── test_document.txt   # Sample text document
```

## Testing Workflow

1. **Start with provided files** to verify basic functionality
2. **Create additional files** for specific test scenarios
3. **Test edge cases** with problematic files
4. **Verify security** with potentially malicious files
5. **Clean up** test files after completion

---

© 2025 Cogniron All Rights Reserved.
