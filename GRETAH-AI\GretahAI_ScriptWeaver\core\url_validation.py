#!/usr/bin/env python3
"""
URL Validation Module for GRETAH ScriptWeaver

This module provides comprehensive URL validation functionality including:
- Format validation (protocol, domain, port, IP address)
- Network connectivity validation (optional, configurable)
- Proper error handling and timeout management
- Integration with GRETAH's logging and configuration systems

The module is designed to be reusable across the GRETAH codebase while
maintaining backward compatibility with existing validation patterns.

© 2025 Cogniron All Rights Reserved.
"""

import re
import socket
import requests
import threading
import time
from typing import Tuple, Optional, Dict, Any
from urllib.parse import urlparse

# Import GRETAH core modules
from debug_utils import debug


class URLValidationConfig:
    """Configuration class for URL validation settings."""
    
    def __init__(self):
        self.network_validation_enabled = True
        self.network_timeout = 8  # seconds
        self.connection_timeout = 5  # seconds for socket operations
        self.allow_localhost = True
        self.allow_private_ips = True
        self.max_redirects = 3
        
    @classmethod
    def from_config(cls, config_dict: Optional[Dict[str, Any]] = None) -> 'URLValidationConfig':
        """Create configuration from dictionary or use defaults."""
        instance = cls()
        if config_dict:
            instance.network_validation_enabled = config_dict.get('network_validation_enabled', True)
            instance.network_timeout = config_dict.get('network_timeout', 8)
            instance.connection_timeout = config_dict.get('connection_timeout', 5)
            instance.allow_localhost = config_dict.get('allow_localhost', True)
            instance.allow_private_ips = config_dict.get('allow_private_ips', True)
            instance.max_redirects = config_dict.get('max_redirects', 3)
        return instance


class URLValidationResult:
    """Result object for URL validation operations."""
    
    def __init__(self, is_valid: bool, error_message: str = "", 
                 validation_type: str = "format", network_reachable: Optional[bool] = None,
                 response_time: Optional[float] = None):
        self.is_valid = is_valid
        self.error_message = error_message
        self.validation_type = validation_type
        self.network_reachable = network_reachable
        self.response_time = response_time
        
    def to_tuple(self) -> Tuple[bool, str]:
        """Convert to tuple for backward compatibility."""
        return self.is_valid, self.error_message


def validate_url_format(url: str) -> URLValidationResult:
    """
    Validate URL format without network connectivity check.
    
    Performs comprehensive URL format validation including:
    - Empty/whitespace checks
    - Protocol validation (http/https only)
    - Domain format validation
    - IP address validation
    - Port number validation
    - Special case handling (example URLs)
    
    Args:
        url (str): The URL to validate
        
    Returns:
        URLValidationResult: Validation result with detailed information
    """
    debug("Starting URL format validation",
          stage="url_validation",
          operation="format_validation",
          context={
              'url': url,
              'url_length': len(url) if url else 0,
              'is_empty': not url or url.strip() == ""
          })

    # Check for empty or whitespace-only URLs
    if not url or url.strip() == "":
        error_msg = "URL cannot be empty"
        debug(f"URL format validation failed: {error_msg}",
              stage="url_validation",
              operation="format_validation_error",
              context={'error_type': 'empty_url', 'url': url})
        return URLValidationResult(False, error_msg, "format")

    # Reject example URLs
    if url == "https://example.com":
        error_msg = "Please enter a real website URL"
        debug(f"URL format validation failed: {error_msg}",
              stage="url_validation",
              operation="format_validation_error",
              context={'error_type': 'example_url', 'url': url})
        return URLValidationResult(False, error_msg, "format")

    # Protocol validation - must start with http:// or https://
    if not url.startswith(('http://', 'https://')):
        error_msg = "URL must start with http:// or https://"
        debug(f"URL format validation failed: {error_msg}",
              stage="url_validation",
              operation="format_validation_error",
              context={
                  'error_type': 'invalid_protocol',
                  'url': url,
                  'starts_with_http': url.startswith('http://'),
                  'starts_with_https': url.startswith('https://')
              })
        return URLValidationResult(False, error_msg, "format")

    # Extract domain part for validation
    try:
        # Remove protocol to get the rest of the URL
        if url.startswith('https://'):
            domain_part = url[8:]  # Remove 'https://'
            protocol = 'https'
        else:
            domain_part = url[7:]   # Remove 'http://'
            protocol = 'http'
        
        # Check for incomplete URLs (just protocol)
        if not domain_part or domain_part.strip() == "":
            error_msg = "URL is incomplete - missing domain"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'incomplete_url', 'url': url, 'domain_part': domain_part})
            return URLValidationResult(False, error_msg, "format")
        
        # Split domain part to get just the domain (before any path)
        domain_with_port = domain_part.split('/')[0]
        
        # Check for whitespace in domain
        if ' ' in domain_with_port:
            error_msg = "URL contains invalid characters (spaces)"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'invalid_characters', 'url': url, 'domain': domain_with_port})
            return URLValidationResult(False, error_msg, "format")
        
        # Split domain and port if present
        if ':' in domain_with_port:
            domain, port_str = domain_with_port.rsplit(':', 1)
            # Validate port number
            try:
                port = int(port_str)
                if port < 1 or port > 65535:
                    error_msg = "Invalid port number (must be 1-65535)"
                    debug(f"URL format validation failed: {error_msg}",
                          stage="url_validation",
                          operation="format_validation_error",
                          context={'error_type': 'invalid_port', 'url': url, 'port': port})
                    return URLValidationResult(False, error_msg, "format")
            except ValueError:
                error_msg = "Invalid port number format"
                debug(f"URL format validation failed: {error_msg}",
                      stage="url_validation",
                      operation="format_validation_error",
                      context={'error_type': 'invalid_port_format', 'url': url, 'port_str': port_str})
                return URLValidationResult(False, error_msg, "format")
        else:
            domain = domain_with_port
        
        # Domain format validation
        if not domain or domain.strip() == "":
            error_msg = "URL is missing domain name"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'missing_domain', 'url': url})
            return URLValidationResult(False, error_msg, "format")
        
        # Check for invalid domain patterns
        if domain.startswith('.') or domain.endswith('.'):
            error_msg = "Invalid domain format (cannot start or end with dot)"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'invalid_domain_dots', 'url': url, 'domain': domain})
            return URLValidationResult(False, error_msg, "format")
        
        if '..' in domain:
            error_msg = "Invalid domain format (consecutive dots not allowed)"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'consecutive_dots', 'url': url, 'domain': domain})
            return URLValidationResult(False, error_msg, "format")
        
        # Check for localhost (valid)
        if domain.lower() == 'localhost':
            debug("URL format validation successful - localhost",
                  stage="url_validation",
                  operation="format_validation_success",
                  context={'url': url, 'protocol': protocol, 'domain': domain, 'type': 'localhost'})
            return URLValidationResult(True, "", "format")
        
        # Check for IP address format (basic validation)
        ip_pattern = re.compile(r'^(\d{1,3}\.){3}\d{1,3}$')
        if ip_pattern.match(domain):
            # Validate IP address ranges
            parts = domain.split('.')
            for part in parts:
                if int(part) > 255:
                    error_msg = "Invalid IP address format"
                    debug(f"URL format validation failed: {error_msg}",
                          stage="url_validation",
                          operation="format_validation_error",
                          context={'error_type': 'invalid_ip', 'url': url, 'domain': domain})
                    return URLValidationResult(False, error_msg, "format")
            
            debug("URL format validation successful - IP address",
                  stage="url_validation",
                  operation="format_validation_success",
                  context={'url': url, 'protocol': protocol, 'domain': domain, 'type': 'ip_address'})
            return URLValidationResult(True, "", "format")
        
        # Check for incomplete IP address patterns (e.g., 192.168.1)
        incomplete_ip_pattern = re.compile(r'^(\d{1,3}\.){1,2}\d{1,3}$')
        if incomplete_ip_pattern.match(domain):
            error_msg = "Invalid domain format (incomplete IP address)"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'incomplete_ip', 'url': url, 'domain': domain})
            return URLValidationResult(False, error_msg, "format")
        
        # Check for domains that are all numeric with too many parts (e.g., ***********.1)
        too_many_ip_parts_pattern = re.compile(r'^(\d{1,3}\.){4,}\d{1,3}$')
        if too_many_ip_parts_pattern.match(domain):
            error_msg = "Invalid domain format (too many numeric parts)"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'too_many_ip_parts', 'url': url, 'domain': domain})
            return URLValidationResult(False, error_msg, "format")

        # Domain name validation (must contain at least one dot for TLD, unless it's localhost)
        if '.' not in domain:
            error_msg = "Invalid domain format (missing top-level domain)"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'missing_tld', 'url': url, 'domain': domain})
            return URLValidationResult(False, error_msg, "format")
        
        # Basic domain name pattern validation
        domain_pattern = re.compile(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$')
        if not domain_pattern.match(domain):
            error_msg = "Invalid domain name format"
            debug(f"URL format validation failed: {error_msg}",
                  stage="url_validation",
                  operation="format_validation_error",
                  context={'error_type': 'invalid_domain_pattern', 'url': url, 'domain': domain})
            return URLValidationResult(False, error_msg, "format")
        
        # All format validations passed
        debug("URL format validation successful",
              stage="url_validation",
              operation="format_validation_success",
              context={
                  'url': url,
                  'protocol': protocol,
                  'domain': domain,
                  'type': 'domain_name'
              })
        return URLValidationResult(True, "", "format")
        
    except Exception as e:
        error_msg = f"URL format validation error: {str(e)}"
        debug(f"URL format validation failed: {error_msg}",
              stage="url_validation",
              operation="format_validation_error",
              context={'error_type': 'validation_exception', 'url': url, 'exception': str(e)})
        return URLValidationResult(False, error_msg, "format")


def check_network_connectivity(url: str, config: URLValidationConfig) -> URLValidationResult:
    """
    Check network connectivity to the given URL.

    Performs a lightweight connectivity check using both socket connection
    and HTTP HEAD request to verify the URL is reachable.

    Args:
        url (str): The URL to check connectivity for
        config (URLValidationConfig): Configuration for network validation

    Returns:
        URLValidationResult: Network connectivity result
    """
    start_time = time.time()

    debug("Starting network connectivity check",
          stage="url_validation",
          operation="network_validation",
          context={
              'url': url,
              'timeout': config.network_timeout,
              'connection_timeout': config.connection_timeout
          })

    try:
        # Parse URL to extract host and port
        parsed = urlparse(url)
        host = parsed.hostname
        port = parsed.port

        if not host:
            error_msg = "Cannot extract hostname from URL for connectivity check"
            debug(f"Network validation failed: {error_msg}",
                  stage="url_validation",
                  operation="network_validation_error",
                  context={'error_type': 'hostname_extraction', 'url': url})
            return URLValidationResult(False, error_msg, "network")

        # Set default ports
        if port is None:
            port = 443 if parsed.scheme == 'https' else 80

        # First, try socket connection for basic connectivity
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(config.connection_timeout)
            result = sock.connect_ex((host, port))
            sock.close()

            if result != 0:
                error_msg = f"Cannot establish connection to {host}:{port}"
                debug(f"Network validation failed: {error_msg}",
                      stage="url_validation",
                      operation="network_validation_error",
                      context={'error_type': 'socket_connection', 'url': url, 'host': host, 'port': port})
                return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)

        except socket.gaierror as e:
            error_msg = f"DNS resolution failed for {host}: {str(e)}"
            debug(f"Network validation failed: {error_msg}",
                  stage="url_validation",
                  operation="network_validation_error",
                  context={'error_type': 'dns_resolution', 'url': url, 'host': host, 'error': str(e)})
            return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)

        except Exception as e:
            error_msg = f"Socket connection error: {str(e)}"
            debug(f"Network validation failed: {error_msg}",
                  stage="url_validation",
                  operation="network_validation_error",
                  context={'error_type': 'socket_error', 'url': url, 'error': str(e)})
            return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)

        # If socket connection succeeds, try HTTP HEAD request for more thorough check
        try:
            response = requests.head(
                url,
                timeout=config.network_timeout,
                allow_redirects=True,
                verify=False,  # Don't verify SSL certificates for connectivity check
                headers={'User-Agent': 'GRETAH-ScriptWeaver-URLValidator/1.0'}
            )

            # Consider 2xx, 3xx, and some 4xx responses as "reachable"
            # (404, 405 etc. mean the server is responding, just the specific resource/method isn't available)
            if response.status_code < 500:
                response_time = time.time() - start_time
                debug("Network validation successful",
                      stage="url_validation",
                      operation="network_validation_success",
                      context={
                          'url': url,
                          'status_code': response.status_code,
                          'response_time': response_time,
                          'final_url': response.url
                      })
                return URLValidationResult(True, "", "network", True, response_time)
            else:
                error_msg = f"Server error (HTTP {response.status_code})"
                debug(f"Network validation failed: {error_msg}",
                      stage="url_validation",
                      operation="network_validation_error",
                      context={'error_type': 'server_error', 'url': url, 'status_code': response.status_code})
                return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)

        except requests.exceptions.Timeout:
            error_msg = f"Connection timeout after {config.network_timeout} seconds"
            debug(f"Network validation failed: {error_msg}",
                  stage="url_validation",
                  operation="network_validation_error",
                  context={'error_type': 'timeout', 'url': url, 'timeout': config.network_timeout})
            return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)

        except requests.exceptions.ConnectionError as e:
            error_msg = f"Connection error: {str(e)}"
            debug(f"Network validation failed: {error_msg}",
                  stage="url_validation",
                  operation="network_validation_error",
                  context={'error_type': 'connection_error', 'url': url, 'error': str(e)})
            return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)

        except Exception as e:
            error_msg = f"Network validation error: {str(e)}"
            debug(f"Network validation failed: {error_msg}",
                  stage="url_validation",
                  operation="network_validation_error",
                  context={'error_type': 'request_error', 'url': url, 'error': str(e)})
            return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)

    except Exception as e:
        error_msg = f"Unexpected network validation error: {str(e)}"
        debug(f"Network validation failed: {error_msg}",
              stage="url_validation",
              operation="network_validation_error",
              context={'error_type': 'unexpected_error', 'url': url, 'error': str(e)})
        return URLValidationResult(False, error_msg, "network", False, time.time() - start_time)


def load_validation_config() -> URLValidationConfig:
    """
    Load URL validation configuration from GRETAH config files.

    Returns:
        URLValidationConfig: Configuration object with loaded or default settings
    """
    try:
        from core.config import APP_CONFIG_FILE
        import json
        import os

        config_dict = {}

        # Try to load from main config file
        if os.path.exists(APP_CONFIG_FILE):
            try:
                with open(APP_CONFIG_FILE, 'r') as f:
                    main_config = json.load(f)
                    config_dict = main_config.get('url_validation', {})
            except Exception as e:
                debug(f"Error loading main config for URL validation: {e}",
                      stage="url_validation",
                      operation="config_loading",
                      context={'config_file': APP_CONFIG_FILE, 'error': str(e)})

        # Create configuration with loaded or default values
        config = URLValidationConfig.from_config(config_dict)

        debug("URL validation configuration loaded",
              stage="url_validation",
              operation="config_loading",
              context={
                  'network_validation_enabled': config.network_validation_enabled,
                  'network_timeout': config.network_timeout,
                  'connection_timeout': config.connection_timeout
              })

        return config

    except Exception as e:
        debug(f"Error loading URL validation configuration, using defaults: {e}",
              stage="url_validation",
              operation="config_loading_error",
              context={'error': str(e)})
        return URLValidationConfig()


def validate_website_url(url: str, config: Optional[URLValidationConfig] = None,
                        enable_network_check: Optional[bool] = None) -> Tuple[bool, str]:
    """
    Comprehensive URL validation with optional network connectivity check.

    This is the main validation function that combines format validation
    with optional network connectivity validation. Maintains backward
    compatibility with the original function signature.

    Args:
        url (str): The URL to validate
        config (URLValidationConfig, optional): Configuration object. If None, loads from config files.
        enable_network_check (bool, optional): Override for network validation. If None, uses config setting.

    Returns:
        tuple: (is_valid, error_message) for backward compatibility
    """
    # Load configuration if not provided
    if config is None:
        config = load_validation_config()

    # Override network check setting if specified
    if enable_network_check is not None:
        config.network_validation_enabled = enable_network_check

    debug("Starting comprehensive URL validation",
          stage="url_validation",
          operation="comprehensive_validation",
          context={
              'url': url,
              'network_validation_enabled': config.network_validation_enabled,
              'network_timeout': config.network_timeout
          })

    # First, perform format validation
    format_result = validate_url_format(url)

    if not format_result.is_valid:
        # Format validation failed, return immediately
        debug("URL validation failed at format stage",
              stage="url_validation",
              operation="comprehensive_validation_failed",
              context={'url': url, 'error': format_result.error_message, 'stage': 'format'})
        return format_result.to_tuple()

    # Format validation passed, check if network validation is enabled
    if not config.network_validation_enabled:
        debug("URL validation successful (network check disabled)",
              stage="url_validation",
              operation="comprehensive_validation_success",
              context={'url': url, 'network_check': 'disabled'})
        return format_result.to_tuple()

    # Perform network validation
    network_result = check_network_connectivity(url, config)

    if not network_result.is_valid:
        # Network validation failed
        debug("URL validation failed at network stage",
              stage="url_validation",
              operation="comprehensive_validation_failed",
              context={
                  'url': url,
                  'error': network_result.error_message,
                  'stage': 'network',
                  'response_time': network_result.response_time
              })
        return network_result.to_tuple()

    # Both validations passed
    debug("URL validation successful (format + network)",
          stage="url_validation",
          operation="comprehensive_validation_success",
          context={
              'url': url,
              'network_check': 'enabled',
              'response_time': network_result.response_time
          })

    return True, ""
