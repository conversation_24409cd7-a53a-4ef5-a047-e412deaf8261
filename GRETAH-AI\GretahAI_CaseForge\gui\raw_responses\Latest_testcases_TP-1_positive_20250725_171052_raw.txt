```json
[
  {
    "scenario_name": "Successful Login After Account Creation",
    "type": "positive",
    "prerequisites": "User should have a valid account created with correct username and password.",
    "Test Case Objective": "Verify user can successfully log in with valid credentials after account creation.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "Username should be successfully entered into the username field."},
      {"action": "Verify if user is able to enter valid password in the password field", "expected_result": "Password should be successfully entered into the password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be successfully redirected to the dashboard page."},
      {"action": "Verify if user is able to view the user's dashboard", "expected_result": "User's dashboard should be displayed without any errors."}
    ]
  },
  {
    "scenario_name": "Password Reset Request",
    "type": "positive",
    "prerequisites": "User should have a valid account created with a registered email address and have forgotten their password.",
    "Test Case Objective": "Verify user can successfully request a password reset link to their registered email address.",
    "steps": [
      {"action": "Verify if user is able to click the 'Forgot Password' link", "expected_result": "'Forgot Password' page should be displayed."},
      {"action": "Verify if user is able to enter their registered email address in the email field", "expected_result": "Email address should be successfully entered into the email field."},
      {"action": "Verify if user is able to click the 'Reset Password' button", "expected_result": "A success message indicating that a password reset link has been sent should be displayed."},
      {"action": "Verify if user is able to receive a password reset email", "expected_result": "User should receive a password reset email in their inbox."}
    ]
  }
]
```