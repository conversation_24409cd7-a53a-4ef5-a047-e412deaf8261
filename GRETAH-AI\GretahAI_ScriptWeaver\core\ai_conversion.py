"""
AI-powered test case to step table conversion functionality.

This module handles the conversion of test cases to automation-ready step tables
using Google AI, including validation and enhancement of the conversion results.
"""

import os
import json
import time
import uuid
from typing import Optional, Dict, Any, List
from datetime import datetime

# Import helper functions and classes from ai_helpers.py
from .ai_helpers import (
    extract_json_from_response, extract_markdown_from_response,
    json_to_markdown_table, markdown_table_to_json, error_handler
)

# Import GRETAH standardized logging
from debug_utils import debug


def generate_step_description(step_data: Dict[str, Any]) -> str:
    """
    Generate a human-readable description for a step based on its data.

    Args:
        step_data: Dictionary containing step information

    Returns:
        str: Human-readable description of the step
    """
    try:
        step_type = step_data.get('step_type', 'ui')
        action = step_data.get('action', '')
        locator = step_data.get('locator', '')
        test_data_param = step_data.get('test_data_param', '')
        expected_result = step_data.get('expected_result', '')

        # Generate description based on step type and action
        if step_type == 'ui':
            if action == 'navigate':
                if 'http' in locator.lower():
                    return f"Navigate to the URL: {locator}"
                else:
                    return f"Navigate to the {locator} page"
            elif action == 'click':
                return f"Click the {locator} element"
            elif action == 'type':
                if test_data_param:
                    return f"Enter {test_data_param} in the {locator} field"
                else:
                    return f"Enter text in the {locator} field"
            elif action == 'select':
                return f"Select option from the {locator} dropdown"
            elif action == 'wait_for_element':
                return f"Wait for the {locator} element to appear"
            elif action == 'upload_file':
                return f"Upload file using the {locator} element"
            else:
                return f"Perform {action} action on {locator}"

        elif step_type == 'api':
            if action.startswith('api_'):
                method = action.replace('api_', '').upper()
                return f"Make {method} API call to {locator or 'endpoint'}"
            else:
                return f"Execute API {action}"

        elif step_type == 'setup':
            return f"Setup: {action} for test preparation"

        elif step_type == 'teardown':
            return f"Cleanup: {action} after test completion"

        elif step_type == 'assertion':
            return f"Verify that {expected_result}"

        elif step_type == 'data':
            return f"Data operation: {action}"

        else:
            # Generic fallback
            if action and expected_result:
                return f"{action.capitalize()} and verify {expected_result}"
            elif action:
                return f"{action.capitalize()}"
            else:
                return "Execute test step"

    except Exception as e:
        debug(f"Error generating step description: {e}",
              stage="ai_conversion", operation="generate_step_description",
              context={'error': str(e), 'error_type': type(e).__name__})
        return f"Execute {step_data.get('action', 'test')} step"


def ensure_step_descriptions(step_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Ensure all steps in the list have step_description field.
    Generate descriptions for steps that don't have them.

    Args:
        step_data_list: List of step dictionaries

    Returns:
        List of step dictionaries with step_description field
    """
    updated_steps = []

    for step in step_data_list:
        if not isinstance(step, dict):
            updated_steps.append(step)
            continue

        # Create a copy to avoid modifying the original
        updated_step = step.copy()

        # Generate description if missing
        if 'step_description' not in updated_step or not updated_step['step_description']:
            updated_step['step_description'] = generate_step_description(updated_step)
            debug(f"Generated description for step {updated_step.get('step_no', 'unknown')}: {updated_step['step_description']}",
                  stage="ai_conversion", operation="ensure_step_descriptions",
                  context={'step_no': updated_step.get('step_no', 'unknown'), 'description': updated_step['step_description']})

        updated_steps.append(updated_step)

    return updated_steps


@error_handler
def convert_test_case_to_step_table(test_case_json, api_key=None, website_url=None, state=None):
    """
    Convert a test case JSON to an automation-ready step table using Google AI.

    Args:
        test_case_json (dict): The test case JSON to convert
        api_key (str, optional): API key for Google AI. If None, use initialized client
        website_url (str, optional): Base website URL from Stage 2 for first step navigation
        state (StateManager, optional): Application state manager for configuration access

    Returns:
        tuple: (markdown_table, json_table) where:
            - markdown_table (str): The automation-ready step table in markdown format
            - json_table (list): The step table as a list of dictionaries
    """
    # Import here to avoid circular imports
    from .ai import generate_llm_response, log_ai_interaction

    request_id = str(uuid.uuid4())
    start_time = time.time()

    # Extract timeout configuration from state
    element_timeout = 10  # Default timeout
    api_timeout = 30  # Default API timeout

    if state and hasattr(state, 'element_timeout'):
        element_timeout = state.element_timeout
        api_timeout = max(30, element_timeout * 2)  # API timeout should be longer than UI timeout
        debug(f"Using configured element timeout: {element_timeout}s (API timeout: {api_timeout}s) [Request ID: {request_id}]",
              stage="ai_conversion", operation="convert_test_case_to_step_table",
              context={'request_id': request_id, 'element_timeout': element_timeout, 'api_timeout': api_timeout})
    else:
        debug(f"Using default timeouts: UI={element_timeout}s, API={api_timeout}s [Request ID: {request_id}]",
              stage="ai_conversion", operation="convert_test_case_to_step_table",
              context={'request_id': request_id, 'element_timeout': element_timeout, 'api_timeout': api_timeout})

    # Validate input
    if not test_case_json or not isinstance(test_case_json, dict):
        debug(f"Invalid test case JSON provided [Request ID: {request_id}]",
              stage="ai_conversion", operation="convert_test_case_to_step_table",
              context={'request_id': request_id, 'test_case_valid': False})
        return "Error: Invalid test case JSON provided.", []

    # Create the prompt with the test case JSON
    json_input = json.dumps(test_case_json, indent=2)
    prompt = f"""
Role: You are a senior QA-automation engineer.

Convert the test-case JSON that follows into an **automation-ready step table**.

____________________________________
INPUT JSON
{json_input}
____________________________________

OUTPUT FORMAT
I need the data in JSON format first, followed by a markdown table representation:

1. First, provide a JSON array of objects, where each object represents a step with these exact keys (in this order):
   "step_no", "step_type", "action", "locator_strategy", "locator", "test_data_param", "expected_result", "assertion_type", "condition", "timeout", "step_description"

2. Second, provide the same data as a Markdown table with **one row per step** and these exact columns (in this order):
   | Step No | Step Type | Action | Locator Strategy | Locator | Test Data Param | Expected Result | Assertion Type | Condition | Timeout (s) | Description |

**Instructions**

1. **step_type** – One of:
      - `setup` (e.g. API or DB calls to prepare state)
      - `ui` (interact with the user interface)
      - `api` (direct HTTP/API calls)
      - `data` (data setup or teardown)
      - `assertion` (verifications without UI interactions)
      - `teardown` (cleanup steps)
2. **action** – Convert each test step into an imperative verb:
      - UI: `navigate`, `click`, `type`, `select`, `upload_file`, `wait_for_element`, etc.
      - API: `api_get`, `api_post`, `api_delete`, etc.
      - Data: `db_insert`, `db_delete`, etc.
3. **locator_strategy** – For UI steps choose `css`, `xpath`, `id`, `name`, `aria`, or leave blank for non-UI steps.
4. **locator** – Draft a plausible selector (e.g. `#login-button`, `//input[@name="q"]`) or leave blank if not applicable.
5. **test_data_param** – Placeholder inputs in `{{double_curly}}` form, e.g. `{{username}}`. For loops, set `action` to `for_each` and this to the collection variable.
6. **expected_result** – Succinct description (≤ 10 words), e.g. `dashboard_page`, `201_created`.
7. **assertion_type** – Oracle keywords such as `url_equals`, `element_visible`, `text_equals`, `status_code`, `no_error`.
8. **condition** – If the step runs conditionally, put a boolean expression (e.g. `element_visible("#error")`); otherwise, an empty string.
9. **timeout** – Maximum wait time in seconds (UI elements = {element_timeout}, Navigation = 30, API = {api_timeout}).
10. **step_description** – A concise, human-readable sentence describing what the step does (e.g., "Navigate to the login page", "Enter username in the username field", "Click the login button").
11. Keep the original **step_no** and preserve step order.
12. **Do not** add any commentary between the JSON and the Markdown outputs.

Format your response exactly like this:

```json
[
  {{
    "step_no": "1",
    "step_type": "setup",
    "action": "api_post",
    "locator_strategy": "",
    "locator": "",
    "test_data_param": "{{api_base_url}}/create-user",
    "expected_result": "201_created",
    "assertion_type": "status_code",
    "condition": "",
    "timeout": {api_timeout},
    "step_description": "Create a new user account via API call"
  }}
]
```

```markdown
| Step No | Step Type | Action | Locator Strategy | Locator | Test Data Param | Expected Result | Assertion Type | Condition | Timeout (s) | Description |
|---------|-----------|--------|------------------|---------|-----------------|-----------------|----------------|-----------|-------------|-------------|
| 1 | setup | api_post | | | {{api_base_url}}/create-user | 201_created | status_code | | {api_timeout} | Create a new user account via API call |
```

IMPORTANT: The JSON output must come first, followed by the markdown table. Both outputs must be immediately consumable by a PyTest/Selenium generator.
"""

    # Create context information for this request
    context = {
        'test_case_id': test_case_json.get('id', 'unknown'),
        'test_case_name': test_case_json.get('name', 'unknown'),
        'step_count': len(test_case_json.get('steps', [])),
        'operation': 'convert_to_step_table'
    }

    # Log the prompt for step table generation separately with enhanced prompt generation tracing
    _, prompt_request_id = log_ai_interaction(
        function_name="convert_test_case_to_step_table_prompt",
        prompt=prompt,
        response="See generate_llm_response log for the response",
        model_name="gemini-1.5-flash",
        capture_stack=True,
        is_prompt_generation=True,  # Enable prompt generation tracing
        context=context
    )

    # Use generate_llm_response with enhanced logging and call stack tracing
    debug(f"Converting test case to step table [Request ID: {request_id}]",
          stage="ai_conversion", operation="convert_test_case_to_step_table",
          context={'request_id': request_id})
    response_text = generate_llm_response(
        prompt=prompt,
        model_name="gemini-1.5-flash",
        api_key=api_key,
        context=context,
        category="step_table_generation",
        parent_request_id=None,
        related_request_ids=[prompt_request_id]  # Link to the prompt log
    )

    # Calculate processing time for parsing
    parsing_start_time = time.time()

    # Extract JSON and markdown from the response
    json_table = extract_json_from_response(response_text, request_id)
    markdown_table = extract_markdown_from_response(response_text, request_id)

    # If we have JSON but no markdown table, generate the markdown table from JSON
    if json_table and not markdown_table:
        markdown_table = json_to_markdown_table(json_table)
        debug(f"Generated markdown table from JSON [Request ID: {request_id}]",
              stage="ai_conversion", operation="convert_test_case_to_step_table",
              context={'request_id': request_id, 'generated_from': 'json'})

    # If we have markdown table but no JSON, generate JSON from the markdown table
    if not json_table and markdown_table:
        json_table = markdown_table_to_json(markdown_table)
        debug(f"Created JSON from markdown table with {len(json_table)} entries [Request ID: {request_id}]",
              stage="ai_conversion", operation="convert_test_case_to_step_table",
              context={'request_id': request_id, 'json_entries': len(json_table), 'generated_from': 'markdown'})

    # Calculate parsing time
    parsing_end_time = time.time()
    parsing_time_ms = (parsing_end_time - parsing_start_time) * 1000

    # Calculate total processing time
    total_time_ms = (parsing_end_time - start_time) * 1000

    # Log the parsing results with detailed metrics
    parsing_context = {
        'test_case_id': test_case_json.get('id', 'unknown'),
        'json_steps_found': len(json_table),
        'markdown_table_found': bool(markdown_table),
        'parsing_time_ms': parsing_time_ms,
        'total_processing_time_ms': total_time_ms
    }

    # Log the parsing results
    log_ai_interaction(
        function_name="parse_step_table_results",
        prompt="",  # No prompt for this log entry
        response=f"JSON Steps: {len(json_table)}\nMarkdown Table: {'Found' if markdown_table else 'Not Found'}\nParsing Time: {parsing_time_ms:.2f}ms",
        model_name="N/A",
        request_id=str(uuid.uuid4()),
        parent_request_id=request_id,  # Link to the original request
        context=parsing_context,
        input_tokens=0,
        output_tokens=0,
        latency_ms=parsing_time_ms,
        category="step_table_parsing"
    )

    # CRITICAL FIX: Ensure first step always navigates to base URL
    # This ensures test execution starts from a clean, known state
    if json_table and len(json_table) > 0 and website_url:
        debug("=== VALIDATING FIRST STEP NAVIGATION ===",
              stage="ai_conversion", operation="convert_test_case_to_step_table")
        first_step = json_table[0]

        # Check if first step needs navigation insertion
        navigation_inserted = _insert_navigation_step_if_needed(json_table, website_url, request_id, element_timeout)

        if navigation_inserted:
            debug(f"✅ Navigation step inserted as Step 1, existing steps shifted down for base URL: {website_url}",
                  stage="ai_conversion", operation="convert_test_case_to_step_table",
                  context={'website_url': website_url, 'navigation_inserted': True})

            # CRITICAL: Regenerate markdown table to reflect the JSON changes
            markdown_table = json_to_markdown_table(json_table)
            debug(f"✅ Regenerated markdown table after navigation insertion [Request ID: {request_id}]",
                  stage="ai_conversion", operation="convert_test_case_to_step_table",
                  context={'request_id': request_id, 'new_step_count': len(json_table)})

            # Update any state references that might be affected by step number changes
            _update_state_references_after_step_shift(request_id)
        else:
            debug(f"✅ First step navigation already correct for base URL: {website_url}",
                  stage="ai_conversion", operation="convert_test_case_to_step_table",
                  context={'website_url': website_url, 'navigation_inserted': False})

        # Apply navigation timeout logic to all navigation steps
        navigation_steps_updated = _apply_navigation_timeout_logic(json_table, element_timeout, request_id)

        if navigation_steps_updated > 0:
            debug(f"✅ Applied navigation timeout (30s) to {navigation_steps_updated} navigation steps",
                  stage="ai_conversion", operation="convert_test_case_to_step_table",
                  context={'navigation_steps_updated': navigation_steps_updated})

            # CRITICAL: Regenerate markdown table to reflect timeout changes
            markdown_table = json_to_markdown_table(json_table)
            debug(f"✅ Regenerated markdown table after timeout updates [Request ID: {request_id}]",
                  stage="ai_conversion", operation="convert_test_case_to_step_table",
                  context={'request_id': request_id, 'timeout_updates': navigation_steps_updated})

    debug(f"Step table conversion completed in {total_time_ms:.2f}ms [Request ID: {request_id}]",
          stage="ai_conversion", operation="convert_test_case_to_step_table",
          context={'request_id': request_id, 'total_time_ms': total_time_ms})
    return markdown_table, json_table


def _insert_navigation_step_if_needed(json_table, website_url, request_id, element_timeout=10):
    """
    Insert a navigation step as Step 1 if the first step doesn't navigate to the base URL.

    This function ensures that test execution starts from a clean, known state by inserting
    a navigation step at the beginning and shifting all existing steps down by one position.
    This preserves the original user-intended test case content while ensuring proper navigation.

    Args:
        json_table (list): The complete step table list (modified in place)
        website_url (str): The base website URL from Stage 2
        request_id (str): Request ID for logging
        element_timeout (int): Configured element timeout from StateManager

    Returns:
        bool: True if a navigation step was inserted, False if first step already navigates
    """
    debug(f"Checking if navigation step insertion is needed [Request ID: {request_id}]",
          stage="ai_conversion", operation="insert_navigation_step",
          context={'request_id': request_id})

    if not json_table or len(json_table) == 0:
        debug("No steps in json_table, cannot insert navigation step",
              stage="ai_conversion", operation="insert_navigation_step")
        return False

    first_step = json_table[0]
    step_no = first_step.get('step_no', '1')

    # Ensure this is actually the first step
    if str(step_no) != '1':
        debug(f"Expected first step to have step_no='1', but got '{step_no}'",
              stage="ai_conversion", operation="insert_navigation_step",
              context={'expected_step_no': '1', 'actual_step_no': step_no})
        return False

    # Check current action
    current_action = first_step.get('action', '').lower()
    debug(f"First step current action: '{current_action}'",
          stage="ai_conversion", operation="insert_navigation_step",
          context={'current_action': current_action})

    # Check if first step already has navigation
    navigation_actions = ['navigate', 'go to', 'open', 'visit', 'load', 'browse to']
    has_navigation = any(nav_action in current_action for nav_action in navigation_actions)

    if has_navigation:
        debug(f"First step already has navigation action '{current_action}', no insertion needed",
              stage="ai_conversion", operation="insert_navigation_step",
              context={'current_action': current_action, 'has_navigation': True})
        return False

    # Insert navigation step and shift existing steps
    debug(f"First step action '{current_action}' is not a navigation action, inserting navigation step...",
          stage="ai_conversion", operation="insert_navigation_step",
          context={'current_action': current_action, 'inserting': True})

    return _perform_navigation_step_insertion(json_table, website_url, request_id, element_timeout)


def _perform_navigation_step_insertion(json_table, website_url, request_id, element_timeout=10):
    """
    Perform the actual navigation step insertion and step number shifting.

    Args:
        json_table (list): The complete step table list (modified in place)
        website_url (str): The base website URL from Stage 2
        request_id (str): Request ID for logging
        element_timeout (int): Configured element timeout from StateManager

    Returns:
        bool: True indicating navigation step was inserted
    """
    debug(f"Performing navigation step insertion [Request ID: {request_id}]",
          stage="ai_conversion", operation="perform_navigation_insertion",
          context={'request_id': request_id, 'original_step_count': len(json_table)})

    # Step 1: Shift all existing step numbers up by 1
    _shift_step_numbers(json_table, request_id)

    # Step 2: Create the new navigation step
    navigation_step = _create_navigation_step(website_url, element_timeout, request_id)

    # Step 3: Insert the navigation step at the beginning
    json_table.insert(0, navigation_step)

    debug(f"✅ Navigation step inserted successfully [Request ID: {request_id}]",
          stage="ai_conversion", operation="perform_navigation_insertion",
          context={'request_id': request_id, 'new_step_count': len(json_table),
                  'navigation_step_action': navigation_step['action']})

    return True


def _shift_step_numbers(json_table, request_id):
    """
    Shift all step numbers up by 1 to make room for the new navigation step.

    Args:
        json_table (list): The step table list to modify
        request_id (str): Request ID for logging
    """
    debug(f"Shifting step numbers for {len(json_table)} steps [Request ID: {request_id}]",
          stage="ai_conversion", operation="shift_step_numbers",
          context={'request_id': request_id, 'step_count': len(json_table)})

    for i, step in enumerate(json_table):
        old_step_no = step.get('step_no', str(i + 1))
        new_step_no = str(int(old_step_no) + 1)
        step['step_no'] = new_step_no

        debug(f"Shifted step {old_step_no} → {new_step_no}: {step.get('action', 'unknown action')}",
              stage="ai_conversion", operation="shift_step_numbers",
              context={'old_step_no': old_step_no, 'new_step_no': new_step_no,
                      'action': step.get('action', 'unknown')})

    debug(f"✅ Step number shifting completed [Request ID: {request_id}]",
          stage="ai_conversion", operation="shift_step_numbers",
          context={'request_id': request_id, 'steps_shifted': len(json_table)})


def _create_navigation_step(website_url, element_timeout, request_id):
    """
    Create a new navigation step with proper configuration.

    Args:
        website_url (str): The base website URL from Stage 2
        element_timeout (int): Configured element timeout from StateManager
        request_id (str): Request ID for logging

    Returns:
        dict: The new navigation step configuration
    """
    navigation_timeout = 30  # Navigation steps get longer timeout

    navigation_step = {
        'step_no': '1',
        'step_type': 'ui',
        'action': 'navigate',
        'locator_strategy': 'url',
        'locator': website_url,
        'test_data_param': '{{website_url}}',
        'expected_result': 'Application page loads successfully',
        'assertion_type': 'url_contains',
        'condition': '',
        'timeout': navigation_timeout,
        'step_description': 'Navigate to the application base URL',
        'current_url': website_url,
        'url_history': [],
        'step_execution_urls': {
            'start_url': None,  # No previous URL for first step
            'end_url': website_url,  # Should end at base URL
            'intermediate_urls': []
        },
        '_is_ai_generated': True,
        '_is_locked': False,
        '_original_ai_step': False,  # This is an inserted step, not original AI
        '_navigation_step_inserted': True  # Mark as inserted navigation step
    }

    debug(f"Created navigation step [Request ID: {request_id}]",
          stage="ai_conversion", operation="create_navigation_step",
          context={'request_id': request_id, 'website_url': website_url,
                  'timeout': navigation_timeout})

    return navigation_step


# Legacy function kept for backward compatibility
def _validate_and_fix_first_step_navigation(first_step, website_url, request_id, element_timeout=10):
    """
    Legacy function for backward compatibility.

    This function is deprecated in favor of _insert_navigation_step_if_needed.
    It now logs a warning and returns False to indicate no modification was made.
    """
    debug(f"⚠️ Legacy function _validate_and_fix_first_step_navigation called [Request ID: {request_id}]",
          stage="ai_conversion", operation="legacy_function_warning",
          context={'request_id': request_id, 'function': '_validate_and_fix_first_step_navigation'})

    debug("This function is deprecated. Use _insert_navigation_step_if_needed instead.",
          stage="ai_conversion", operation="legacy_function_warning")

    return False


def _update_state_references_after_step_shift(request_id):
    """
    Update state management references after step numbers have been shifted.

    This function handles updating any cached or stored step references that might
    be affected when a navigation step is inserted and all existing steps are shifted up by 1.

    Args:
        request_id (str): Request ID for logging
    """
    debug(f"Updating state references after step shift [Request ID: {request_id}]",
          stage="ai_conversion", operation="update_state_references",
          context={'request_id': request_id})

    try:
        # Import here to avoid circular imports
        import streamlit as st

        # Check if we have a state manager instance
        if hasattr(st.session_state, 'state_manager') and st.session_state.state_manager:
            state = st.session_state.state_manager

            # Update previous_scripts dictionary - shift all step numbers up by 1
            if hasattr(state, 'previous_scripts') and state.previous_scripts:
                updated_scripts = {}
                for step_no, script_content in state.previous_scripts.items():
                    try:
                        old_step_num = int(step_no)
                        new_step_num = old_step_num + 1
                        updated_scripts[str(new_step_num)] = script_content
                        debug(f"Shifted previous_scripts: {step_no} → {new_step_num}",
                              stage="ai_conversion", operation="update_state_references",
                              context={'old_step': step_no, 'new_step': str(new_step_num)})
                    except (ValueError, TypeError):
                        # Keep non-numeric step numbers as-is
                        updated_scripts[step_no] = script_content
                        debug(f"Kept non-numeric step number unchanged: {step_no}",
                              stage="ai_conversion", operation="update_state_references")

                state.previous_scripts = updated_scripts
                debug(f"Updated previous_scripts dictionary with {len(updated_scripts)} entries",
                      stage="ai_conversion", operation="update_state_references")

            # Update completed_steps list - shift all step numbers up by 1
            if hasattr(state, 'completed_steps') and state.completed_steps:
                updated_completed = []
                for step_no in state.completed_steps:
                    try:
                        old_step_num = int(step_no)
                        new_step_num = old_step_num + 1
                        updated_completed.append(str(new_step_num))
                        debug(f"Shifted completed_steps: {step_no} → {new_step_num}",
                              stage="ai_conversion", operation="update_state_references",
                              context={'old_step': step_no, 'new_step': str(new_step_num)})
                    except (ValueError, TypeError):
                        # Keep non-numeric step numbers as-is
                        updated_completed.append(step_no)
                        debug(f"Kept non-numeric completed step unchanged: {step_no}",
                              stage="ai_conversion", operation="update_state_references")

                state.completed_steps = updated_completed
                debug(f"Updated completed_steps list with {len(updated_completed)} entries",
                      stage="ai_conversion", operation="update_state_references")

            # Update step_context dictionary - shift all step numbers up by 1
            if hasattr(state, 'step_context') and state.step_context:
                updated_context = {}
                for step_no, context_data in state.step_context.items():
                    try:
                        old_step_num = int(step_no)
                        new_step_num = old_step_num + 1
                        updated_context[str(new_step_num)] = context_data
                        debug(f"Shifted step_context: {step_no} → {new_step_num}",
                              stage="ai_conversion", operation="update_state_references",
                              context={'old_step': step_no, 'new_step': str(new_step_num)})
                    except (ValueError, TypeError):
                        # Keep non-numeric step numbers as-is
                        updated_context[step_no] = context_data
                        debug(f"Kept non-numeric step context unchanged: {step_no}",
                              stage="ai_conversion", operation="update_state_references")

                state.step_context = updated_context
                debug(f"Updated step_context dictionary with {len(updated_context)} entries",
                      stage="ai_conversion", operation="update_state_references")

            # Update element_matches and step_matches - these might have nested step references
            for attr_name in ['element_matches', 'step_matches']:
                if hasattr(state, attr_name):
                    attr_value = getattr(state, attr_name)
                    if attr_value and isinstance(attr_value, dict):
                        updated_matches = _shift_nested_step_references(attr_value, request_id)
                        setattr(state, attr_name, updated_matches)
                        debug(f"Updated {attr_name} with shifted step references",
                              stage="ai_conversion", operation="update_state_references",
                              context={'attribute': attr_name})

            debug(f"✅ State references updated successfully after step shift [Request ID: {request_id}]",
                  stage="ai_conversion", operation="update_state_references",
                  context={'request_id': request_id})

            # CRITICAL: Update step context metadata in JSON storage
            _update_step_context_metadata_after_shift(state, request_id)

        else:
            debug(f"No state manager found, skipping state reference updates [Request ID: {request_id}]",
                  stage="ai_conversion", operation="update_state_references",
                  context={'request_id': request_id})

    except Exception as e:
        debug(f"Error updating state references after step shift: {e} [Request ID: {request_id}]",
              stage="ai_conversion", operation="update_state_references_error",
              context={'request_id': request_id, 'error': str(e)})


def _shift_nested_step_references(data_dict, request_id):
    """
    Recursively shift step number references in nested dictionaries.

    Args:
        data_dict (dict): Dictionary that may contain step number keys
        request_id (str): Request ID for logging

    Returns:
        dict: Updated dictionary with shifted step references
    """
    if not isinstance(data_dict, dict):
        return data_dict

    updated_dict = {}
    for key, value in data_dict.items():
        # Try to shift numeric step keys
        try:
            if isinstance(key, str) and key.isdigit():
                old_step_num = int(key)
                new_step_num = old_step_num + 1
                new_key = str(new_step_num)
                debug(f"Shifted nested step reference: {key} → {new_key}",
                      stage="ai_conversion", operation="shift_nested_references",
                      context={'old_key': key, 'new_key': new_key})
            else:
                new_key = key
        except (ValueError, TypeError):
            new_key = key

        # Recursively process nested dictionaries
        if isinstance(value, dict):
            updated_dict[new_key] = _shift_nested_step_references(value, request_id)
        else:
            updated_dict[new_key] = value

    return updated_dict


def _update_step_context_metadata_after_shift(state, request_id):
    """
    Update step context metadata in JSON storage after step number shifts.

    This function ensures that step context data stored in JSON metadata
    reflects the new step numbering after navigation insertion.

    Args:
        state: StateManager instance
        request_id (str): Request ID for logging
    """
    debug(f"Updating step context metadata in JSON storage after step shift [Request ID: {request_id}]",
          stage="ai_conversion", operation="update_step_context_metadata",
          context={'request_id': request_id})

    try:
        # Check if we have a test case to work with
        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            debug(f"No selected test case, skipping step context metadata update [Request ID: {request_id}]",
                  stage="ai_conversion", operation="update_step_context_metadata",
                  context={'request_id': request_id})
            return

        test_case_id = state.selected_test_case.get('Test Case ID')
        if not test_case_id:
            debug(f"No test case ID, skipping step context metadata update [Request ID: {request_id}]",
                  stage="ai_conversion", operation="update_step_context_metadata",
                  context={'request_id': request_id})
            return

        # Load current step data from JSON storage
        from core.step_data_storage import get_step_data_storage
        storage = get_step_data_storage()
        result = storage.load_step_data(test_case_id)

        if not result:
            debug(f"No step data found in JSON storage, skipping metadata update [Request ID: {request_id}]",
                  stage="ai_conversion", operation="update_step_context_metadata",
                  context={'request_id': request_id, 'test_case_id': test_case_id})
            return

        step_data, metadata = result

        # Update step context in metadata if it exists
        if 'step_context' in metadata and metadata['step_context']:
            updated_step_context = {}
            for step_no, context_data in metadata['step_context'].items():
                try:
                    old_step_num = int(step_no)
                    new_step_num = old_step_num + 1
                    updated_step_context[str(new_step_num)] = context_data
                    debug(f"Shifted step context metadata: {step_no} → {new_step_num}",
                          stage="ai_conversion", operation="update_step_context_metadata",
                          context={'old_step': step_no, 'new_step': str(new_step_num)})
                except (ValueError, TypeError):
                    # Keep non-numeric step numbers as-is
                    updated_step_context[step_no] = context_data
                    debug(f"Kept non-numeric step context unchanged: {step_no}",
                          stage="ai_conversion", operation="update_step_context_metadata")

            metadata['step_context'] = updated_step_context
            debug(f"Updated step_context metadata with {len(updated_step_context)} entries",
                  stage="ai_conversion", operation="update_step_context_metadata")

        # Update completed_steps in metadata if it exists
        if 'completed_steps' in metadata and metadata['completed_steps']:
            updated_completed_steps = []
            for step_no in metadata['completed_steps']:
                try:
                    old_step_num = int(step_no)
                    new_step_num = old_step_num + 1
                    updated_completed_steps.append(str(new_step_num))
                    debug(f"Shifted completed_steps metadata: {step_no} → {new_step_num}",
                          stage="ai_conversion", operation="update_step_context_metadata",
                          context={'old_step': step_no, 'new_step': str(new_step_num)})
                except (ValueError, TypeError):
                    # Keep non-numeric step numbers as-is
                    updated_completed_steps.append(step_no)
                    debug(f"Kept non-numeric completed step unchanged: {step_no}",
                          stage="ai_conversion", operation="update_step_context_metadata")

            metadata['completed_steps'] = updated_completed_steps
            debug(f"Updated completed_steps metadata with {len(updated_completed_steps)} entries",
                  stage="ai_conversion", operation="update_step_context_metadata")

        # Add metadata flag to indicate step context was updated for navigation insertion
        metadata['step_context_shifted_for_navigation'] = True
        metadata['step_context_shift_timestamp'] = datetime.now().isoformat()

        # Save the updated metadata back to JSON storage
        success = storage.save_step_data(test_case_id, step_data, metadata)

        if success:
            debug(f"✅ Successfully updated step context metadata in JSON storage [Request ID: {request_id}]",
                  stage="ai_conversion", operation="update_step_context_metadata",
                  context={'request_id': request_id, 'test_case_id': test_case_id})
        else:
            debug(f"❌ Failed to save updated step context metadata [Request ID: {request_id}]",
                  stage="ai_conversion", operation="update_step_context_metadata_error",
                  context={'request_id': request_id, 'test_case_id': test_case_id})

    except Exception as e:
        debug(f"Error updating step context metadata after step shift: {e} [Request ID: {request_id}]",
              stage="ai_conversion", operation="update_step_context_metadata_error",
              context={'request_id': request_id, 'error': str(e)})


def _apply_navigation_timeout_logic(json_table, element_timeout, request_id):
    """
    Apply navigation timeout logic to all navigation steps in the step table.

    Navigation steps (navigate, open, visit) should use 30-second timeouts instead of
    the configured UI element timeout to allow sufficient time for page loads.

    Args:
        json_table (list): List of step dictionaries from the step table
        element_timeout (int): Configured UI element timeout from StateManager
        request_id (str): Request ID for logging

    Returns:
        int: Number of navigation steps that were updated
    """
    debug(f"Applying navigation timeout logic to {len(json_table)} steps [Request ID: {request_id}]",
          stage="ai_conversion", operation="apply_navigation_timeout_logic",
          context={'request_id': request_id, 'total_steps': len(json_table)})

    navigation_actions = ['navigate', 'go to', 'open', 'visit', 'load', 'browse to']
    navigation_timeout = 30  # Standard navigation timeout
    steps_updated = 0

    for i, step in enumerate(json_table):
        step_no = step.get('step_no', str(i + 1))
        action = step.get('action', '').lower()
        current_timeout = step.get('timeout', element_timeout)

        # Check if this is a navigation step
        is_navigation_step = any(nav_action in action for nav_action in navigation_actions)

        if is_navigation_step:
            # Apply navigation timeout if current timeout is less than 30 seconds
            if current_timeout < navigation_timeout:
                step['timeout'] = navigation_timeout
                steps_updated += 1
                debug(f"Updated step {step_no} timeout: {current_timeout}s → {navigation_timeout}s (navigation)",
                      stage="ai_conversion", operation="apply_navigation_timeout_logic",
                      context={'step_no': step_no, 'action': action, 'old_timeout': current_timeout, 'new_timeout': navigation_timeout})
            else:
                debug(f"Step {step_no} already has sufficient timeout: {current_timeout}s (navigation)",
                      stage="ai_conversion", operation="apply_navigation_timeout_logic",
                      context={'step_no': step_no, 'action': action, 'timeout': current_timeout})
        else:
            # For non-navigation steps, ensure they use the configured UI element timeout
            if current_timeout != element_timeout:
                step['timeout'] = element_timeout
                debug(f"Updated step {step_no} timeout: {current_timeout}s → {element_timeout}s (UI element)",
                      stage="ai_conversion", operation="apply_navigation_timeout_logic",
                      context={'step_no': step_no, 'action': action, 'old_timeout': current_timeout, 'new_timeout': element_timeout})

    debug(f"Navigation timeout logic applied: {steps_updated} navigation steps updated to 30s timeout",
          stage="ai_conversion", operation="apply_navigation_timeout_logic",
          context={'steps_updated': steps_updated, 'navigation_timeout': navigation_timeout})

    return steps_updated
