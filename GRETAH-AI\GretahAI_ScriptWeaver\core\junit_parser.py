"""
JUnit XML Parser for GretahAI ScriptWeaver

This module provides functionality to parse JUnit XML test results generated by pytest
and extract test execution data, performance metrics, and artifact information.

Functions:
    parse_junit_xml: Parse JUnit XML file and extract test results
    extract_performance_metrics: Extract performance metrics from test properties
    format_test_results: Format test results for display in Streamlit
"""

import xml.etree.ElementTree as ET
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

logger = logging.getLogger("ScriptWeaver.junit_parser")

def parse_junit_xml(xml_file_path: str) -> Optional[Dict[str, Any]]:
    """
    Parse JUnit XML file and extract comprehensive test results.

    Args:
        xml_file_path: Path to the JUnit XML file

    Returns:
        Dictionary containing parsed test results or None if parsing fails
    """
    try:
        if not Path(xml_file_path).exists():
            logger.error(f"XML file not found: {xml_file_path}")
            return None

        tree = ET.parse(xml_file_path)
        root = tree.getroot()

        # Initialize results structure
        results = {
            "test_suites": [],
            "summary": {},
            "artifacts": {},
            "performance_metrics": {}
        }

        # Parse test suites
        total_tests = 0
        total_failures = 0
        total_errors = 0
        total_skipped = 0
        performance_metrics = {}

        # Handle both single testsuite and testsuites root elements
        test_suites = root.findall('.//testsuite')
        if not test_suites:
            # If root is testsuite itself
            if root.tag == 'testsuite':
                test_suites = [root]

        for suite in test_suites:
            suite_data = {
                "name": suite.get("name", "Unknown"),
                "tests": int(suite.get("tests", 0)),
                "failures": int(suite.get("failures", 0)),
                "errors": int(suite.get("errors", 0)),
                "skipped": int(suite.get("skipped", 0)),
                "time": float(suite.get("time", 0.0)),
                "test_cases": []
            }

            # Update totals
            total_tests += suite_data["tests"]
            total_failures += suite_data["failures"]
            total_errors += suite_data["errors"]
            total_skipped += suite_data["skipped"]

            # Parse test cases
            for testcase in suite.findall('testcase'):
                test_case_data = {
                    "classname": testcase.get("classname", ""),
                    "name": testcase.get("name", ""),
                    "time": float(testcase.get("time", 0.0)),
                    "status": "passed",
                    "message": "",
                    "details": "",
                    "artifacts": {},
                    "performance": {}
                }

                # Check for failure
                failure = testcase.find('failure')
                if failure is not None:
                    test_case_data["status"] = "failed"
                    test_case_data["message"] = failure.get("message", "")
                    test_case_data["details"] = failure.text or ""

                # Check for error
                error = testcase.find('error')
                if error is not None:
                    test_case_data["status"] = "error"
                    test_case_data["message"] = error.get("message", "")
                    test_case_data["details"] = error.text or ""

                # Check for skipped
                skipped = testcase.find('skipped')
                if skipped is not None:
                    test_case_data["status"] = "skipped"
                    test_case_data["message"] = skipped.get("message", "")
                    test_case_data["details"] = skipped.text or ""

                # Parse properties for artifacts, performance metrics, and URL tracking
                properties_dict = {}
                properties = testcase.find('properties')
                if properties is not None:
                    for prop in properties.findall('property'):
                        prop_name = prop.get('name', '')
                        prop_value = prop.get('value', '')

                        # Store all properties for URL tracking and other uses
                        properties_dict[prop_name] = prop_value

                        if prop_name.startswith('artifact_'):
                            artifact_type = prop_name[9:]  # Remove 'artifact_' prefix
                            # Handle multiple artifacts of the same type by storing them in a list
                            if artifact_type not in test_case_data["artifacts"]:
                                test_case_data["artifacts"][artifact_type] = []
                            elif not isinstance(test_case_data["artifacts"][artifact_type], list):
                                # Convert single artifact to list for consistency
                                test_case_data["artifacts"][artifact_type] = [test_case_data["artifacts"][artifact_type]]
                            test_case_data["artifacts"][artifact_type].append(prop_value)
                        elif prop_name.startswith('perf_'):
                            metric_name = prop_name[5:]  # Remove 'perf_' prefix
                            try:
                                test_case_data["performance"][metric_name] = float(prop_value)
                            except (ValueError, TypeError):
                                test_case_data["performance"][metric_name] = prop_value

                # Store all properties for URL tracking and other integrations
                test_case_data["properties"] = properties_dict

                # Store performance metrics for aggregation
                test_id = f"{test_case_data['classname']}::{test_case_data['name']}"
                if test_case_data["performance"]:
                    performance_metrics[test_id] = test_case_data["performance"]

                suite_data["test_cases"].append(test_case_data)

            results["test_suites"].append(suite_data)

        # Create summary
        results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": total_tests - total_failures - total_skipped - total_errors,
            "failed_tests": total_failures + total_errors,
            "skipped_tests": total_skipped,
            "duration": float(root.get("time", sum(s["time"] for s in results["test_suites"]))),
            "performance_metrics": performance_metrics
        }

        return results

    except ET.ParseError as e:
        logger.error(f"Error parsing XML file {xml_file_path}: {e}")
        return None
    except FileNotFoundError:
        logger.error(f"Error: XML file not found at {xml_file_path}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred during XML parsing: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def extract_performance_metrics(results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract and aggregate performance metrics from test results.

    Args:
        results: Parsed test results dictionary

    Returns:
        Dictionary containing aggregated performance metrics
    """
    if not results or "performance_metrics" not in results.get("summary", {}):
        return {}

    performance_data = results["summary"]["performance_metrics"]
    if not performance_data:
        return {}

    # Aggregate metrics
    aggregated = {}
    metric_names = set()

    # Collect all metric names
    for test_metrics in performance_data.values():
        metric_names.update(test_metrics.keys())

    # Calculate statistics for each metric
    for metric_name in metric_names:
        values = []
        for test_metrics in performance_data.values():
            if metric_name in test_metrics:
                try:
                    values.append(float(test_metrics[metric_name]))
                except (ValueError, TypeError):
                    pass

        if values:
            aggregated[metric_name] = {
                "average": sum(values) / len(values),
                "minimum": min(values),
                "maximum": max(values),
                "total": sum(values),
                "count": len(values)
            }

    return {
        "individual_tests": performance_data,
        "aggregated": aggregated,
        "test_count": len(performance_data)
    }

def format_test_results_for_display(results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format test results for display in Streamlit UI.

    Args:
        results: Parsed test results dictionary

    Returns:
        Dictionary formatted for UI display
    """
    if not results:
        return {"error": "No test results available"}

    summary = results.get("summary", {})

    # Format basic statistics
    display_data = {
        "execution_summary": {
            "Total Tests": summary.get("total_tests", 0),
            "Passed": summary.get("passed_tests", 0),
            "Failed": summary.get("failed_tests", 0),
            "Skipped": summary.get("skipped_tests", 0),
            "Duration": f"{summary.get('duration', 0):.2f}s"
        },
        "test_details": [],
        "performance_summary": {},
        "artifacts": {}
    }

    # Format individual test details
    for suite in results.get("test_suites", []):
        for test_case in suite.get("test_cases", []):
            test_detail = {
                "name": test_case["name"],
                "class": test_case["classname"],
                "status": test_case["status"],
                "duration": f"{test_case['time']:.3f}s",
                "message": test_case.get("message", ""),
                "artifacts": test_case.get("artifacts", {}),
                "performance": test_case.get("performance", {}),
                "properties": test_case.get("properties", {})  # Include properties for URL tracking
            }
            display_data["test_details"].append(test_detail)

    # Format performance metrics
    perf_metrics = extract_performance_metrics(results)
    if perf_metrics:
        display_data["performance_summary"] = perf_metrics

    return display_data
