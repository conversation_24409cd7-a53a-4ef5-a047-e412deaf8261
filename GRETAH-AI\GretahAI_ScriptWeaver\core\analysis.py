"""
Module for test step analysis.
Provides functions for analyzing test steps and mapping them to UI elements.
"""

import json
import re
import logging
from difflib import SequenceMatcher
from .ai import generate_llm_response
from .ai_helpers import clean_llm_response

# Configure logging
logger = logging.getLogger("ScriptWeaver.analysis")

def validate_test_case_structure(test_case):
    """
    Validate test case structure and warn about deprecated patterns.

    Args:
        test_case (dict): Test case to validate

    Returns:
        dict: Validation results with warnings and corrections
    """
    warnings = []
    corrections = {}

    if not isinstance(test_case, dict):
        return {
            'valid': False,
            'warnings': ['Test case is not a dictionary'],
            'corrections': {}
        }

    # Check for deprecated lowercase 'steps' key
    if 'steps' in test_case and 'Steps' not in test_case:
        warnings.append("Test case uses deprecated lowercase 'steps' key. Should use uppercase 'Steps'.")
        corrections['steps_key'] = 'Use uppercase "Steps" instead of lowercase "steps"'
        logger.warning(f"Test case {test_case.get('Test Case ID', 'Unknown')} uses deprecated 'steps' key")

    # Check for both keys present (inconsistent data)
    if 'steps' in test_case and 'Steps' in test_case:
        warnings.append("Test case has both 'steps' and 'Steps' keys. Using uppercase 'Steps' as canonical.")
        corrections['duplicate_keys'] = 'Remove lowercase "steps" key, keep uppercase "Steps"'
        logger.warning(f"Test case {test_case.get('Test Case ID', 'Unknown')} has duplicate step keys")

    # Validate step structure within Steps
    steps = test_case.get('Steps', [])
    if isinstance(steps, list):
        for i, step in enumerate(steps):
            if isinstance(step, dict):
                # Check for deprecated lowercase step field keys
                deprecated_fields = {
                    'step no': 'Step No',
                    'test steps': 'Test Steps',
                    'expected result': 'Expected Result'
                }

                for old_key, new_key in deprecated_fields.items():
                    if old_key in step and new_key not in step:
                        warnings.append(f"Step {i+1} uses deprecated '{old_key}' key. Should use '{new_key}'.")
                        corrections[f'step_{i+1}_{old_key}'] = f'Use "{new_key}" instead of "{old_key}"'

    return {
        'valid': len(warnings) == 0,
        'warnings': warnings,
        'corrections': corrections
    }

def map_test_steps_to_elements(test_cases, elements, api_key=None):
    """
    Map test steps to UI elements using AI with context awareness.

    Args:
        test_cases (dict or list): Test case(s) to analyze
        elements (list): List of UI elements to map to test steps
        api_key (str, optional): API key for LLM service

    Returns:
        dict: Mapping of test steps to UI elements
    """
    # Standardize input format
    if isinstance(test_cases, dict):
        test_cases = [test_cases]
    elif not isinstance(test_cases, list):
        return {}

    # Validate input
    if not test_cases or not elements:
        return {}

    # Filter elements for QA automation testing relevance
    # Default values for locator strategy and value
    locator_strategy = None
    locator_value = None

    # Check if any test case has step table information
    for test_case in test_cases:
        if isinstance(test_case, dict):
            steps = test_case.get('Steps', [])
            for step in steps:
                if isinstance(step, dict) and 'locator_strategy' in step and 'locator' in step:
                    locator_strategy = step.get('locator_strategy')
                    locator_value = step.get('locator')
                    if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', ''] and locator_value:
                        # Found valid locator information, break out of the loop
                        break
            if locator_strategy and locator_value:
                # Found valid locator information, break out of the loop
                break

    # Filter elements with locator information if available
    qa_relevant_elements = filter_qa_relevant_elements(elements, locator_strategy, locator_value)

    # Process each test case
    matches = {}
    for test_case in test_cases:
        try:
            # Validate test case format
            if not isinstance(test_case, dict):
                continue

            # Get test case ID (support both naming conventions)
            tc_id = test_case.get('Test Case ID') or test_case.get('id')
            if not tc_id:
                continue

            # Initialize matches for this test case
            matches[tc_id] = {}

            # Get steps using standardized key
            steps = test_case.get('Steps', [])
            if not isinstance(steps, list):
                continue

            # Build step context as we process the steps
            step_context = {
                "test_case_id": tc_id,
                "test_objective": test_case.get('Test Case Objective', ''),
                "previous_steps": []
            }

            # Process each step
            for i, step in enumerate(steps):
                # Validate step format
                if not isinstance(step, dict):
                    continue

                # Get step number, action, and expected result (support both naming conventions)
                step_num = step.get('Step No') or step.get('step_no') or str(i+1)
                action = step.get('Test Steps') or step.get('action', '')
                expected = step.get('Expected Result') or step.get('expected', '')

                # Add step table information to context if available
                if 'step_type' in step:
                    step_context['step_type'] = step.get('step_type')
                if 'locator_strategy' in step:
                    step_context['locator_strategy'] = step.get('locator_strategy')
                if 'locator' in step:
                    step_context['locator'] = step.get('locator')
                if 'test_data_param' in step:
                    step_context['test_data_param'] = step.get('test_data_param')
                if 'assertion_type' in step:
                    step_context['assertion_type'] = step.get('assertion_type')

                # Create prompt for LLM with previous step context and step table information
                prompt = create_element_matching_prompt_with_context(
                    tc_id,
                    step_num,
                    action,
                    expected,
                    qa_relevant_elements,
                    step_context
                )

                # Get LLM response
                response = generate_llm_response(prompt, api_key=api_key)

                # Process response
                if not response or not response.strip():
                    matches[tc_id][str(step_num)] = []
                    continue

                try:
                    # Clean and parse response
                    cleaned_response = clean_llm_response(response, "json")
                    matches_data = json.loads(cleaned_response)

                    # Extract matches
                    step_matches = []
                    for match in matches_data.get('matches', []):
                        step_matches.append(match)

                    # Sort by confidence score
                    step_matches.sort(key=lambda x: x.get('score', 0), reverse=True)

                    # Store matches
                    matches[tc_id][str(step_num)] = step_matches

                    # Update step context for next step
                    step_context["previous_steps"].append({
                        "step_num": step_num,
                        "action": action,
                        "expected": expected,
                        "matched_elements": [m.get("element", {}).get("name", "") for m in step_matches[:3]]
                    })
                except Exception as e:
                    print(f"Error processing step {step_num} response: {e}")
                    # If parsing fails, return empty matches
                    matches[tc_id][str(step_num)] = []
        except Exception as e:
            print(f"Error processing test case {tc_id}: {e}")
            # Skip test cases that cause errors
            continue

    return matches

def create_element_matching_prompt_with_context(tc_id, step_num, action, expected, elements, context):
    """
    Create a prompt for matching UI elements to a test step with context from previous steps.
    Instructs the LLM to return only the single most relevant actionable element for the step, unless the step clearly requires more than one.

    Args:
        tc_id (str): Test case ID
        step_num (str): Step number
        action (str): Test step action
        expected (str): Expected result
        elements (list): List of UI elements
        context (dict): Context information including previous steps and step table data

    Returns:
        str: Prompt for LLM
    """
    previous_steps_text = ""
    if context["previous_steps"]:
        previous_steps_text = "Previous steps in this test case:\n"
        for prev_step in context["previous_steps"][-3:]:  # Only use the last 3 steps for context
            previous_steps_text += f"Step {prev_step['step_num']}: {prev_step['action']} -> {prev_step['expected']}\n"
            if prev_step['matched_elements']:
                previous_steps_text += f"   Used elements: {', '.join(prev_step['matched_elements'])}\n"

    # Include step table information if available
    step_table_info = ""
    if 'step_type' in context:
        step_table_info += f"Step Type: {context.get('step_type', 'N/A')}\n"
    if 'locator_strategy' in context:
        step_table_info += f"Locator Strategy: {context.get('locator_strategy', 'N/A')}\n"
    if 'locator' in context:
        step_table_info += f"Locator Value: {context.get('locator', 'N/A')}\n"
    if 'test_data_param' in context:
        step_table_info += f"Test Data Parameter: {context.get('test_data_param', 'N/A')}\n"
    if 'assertion_type' in context:
        step_table_info += f"Assertion Type: {context.get('assertion_type', 'N/A')}\n"

    # Add guidance based on locator strategy
    locator_guidance = ""
    if 'locator_strategy' in context and context.get('locator_strategy') not in ['none', 'n/a', '']:
        locator_strategy = context.get('locator_strategy', '').lower()
        locator_value = context.get('locator', '')

        locator_guidance = f"""
        IMPORTANT: This step specifies a locator strategy of '{locator_strategy}'
        {f"with value '{locator_value}'" if locator_value else ""}.
        Prioritize elements that match this locator strategy.

        Elements with a 'locator_strategy_match' property set to true are more likely to be relevant.
        Elements with a higher 'locator_strategy_score' or 'qa_relevance_score' are more likely to be relevant.
        """

    return f"""
    Analyze this test step and find matching UI elements, considering the context of previous steps and the step table information.
    Test Case: {tc_id}
    Test Objective: {context.get('test_objective', '')}

    {previous_steps_text}

    Current Step {step_num}:
    Action: {action}
    Expected: {expected}

    Step Table Information:
    {step_table_info}

    {locator_guidance}

    Available UI Elements (JSON array):
    {json.dumps(elements, indent=2)}

    For this step, return ONLY the single most relevant actionable UI element (e.g., for input, click, or verify), unless the step clearly requires more than one (e.g., compound action+verification). If no UI element is required (e.g., navigation, wait, or validation-only steps), return an empty matches list.

    For the returned element, determine:
    1. How well it matches the step requirements (confidence score 0-1)
    2. What action should be performed (click, input, verify, clear, etc.)
    3. Any specific data or values needed
    4. Explain why this element is appropriate for this step considering the step table information and previous steps

    Return ONLY valid JSON in this format (no explanation, no markdown):
    {{
        "requires_ui_element": true/false,
        "reason": string,
        "matches": [
            {{
                "element": {{element object}},
                "score": float,
                "action": string,
                "data": string or null,
                "reasoning": string
            }}
        ]
    }}
    """

def analyze_test_case(test_case, elements):
    """
    Analyze a test case to identify and validate test steps.

    Args:
        test_case (dict): Test case to analyze
        elements (list): List of UI elements

    Returns:
        dict: Analysis results with step metadata
    """
    # Validate test case format
    if not isinstance(test_case, dict):
        return {
            'error': 'Invalid test case format',
            'valid': False
        }

    # Get test case ID
    tc_id = test_case.get('Test Case ID', '') or test_case.get('id', '')
    if not tc_id:
        return {
            'error': 'Test case ID not found',
            'valid': False
        }

    # Create standardized test case structure
    processed_test_case = {
        'Test Case ID': tc_id,
        'Test Case Objective': test_case.get('Test Case Objective', ''),
        'Prerequisite': test_case.get('Prerequisite', ''),
        'Priority': test_case.get('Priority', ''),
        'Test Type': test_case.get('Test Type', ''),
        'Test Group': test_case.get('Test Group', ''),
        'Steps': []
    }

    # Process steps if they exist
    steps = test_case.get('Steps', [])
    if not isinstance(steps, list) or not steps:
        return {
            'error': 'No valid steps found',
            'valid': False,
            'test_case': processed_test_case
        }

    # Filter elements for QA automation relevance if elements are provided
    qa_elements = elements
    if elements:
        qa_elements = filter_qa_relevant_elements(elements)

    # Build step context as we analyze steps
    step_context = {
        "test_case_id": tc_id,
        "test_objective": test_case.get('Test Case Objective', ''),
        "previous_steps": []
    }

    # Process each step
    for step in steps:
        if not isinstance(step, dict):
            continue

        # Create standardized step structure
        processed_step = {
            'Step No': step.get('Step No', 0),
            'Test Steps': step.get('Test Steps', ''),
            'Expected Result': step.get('Expected Result', ''),
            '_metadata': {
                'matched_elements': [],
                'requires_page_load': 'page' in step.get('Test Steps', '').lower() or 'navigate' in step.get('Test Steps', '').lower(),
                'dynamic_content': any(word in step.get('Test Steps', '').lower() for word in ['load', 'wait', 'appear']),
                'validation_required': any(word in step.get('Expected Result', '').lower() for word in ['verify', 'check', 'confirm']),
                'confidence': 0.0,
                'state_changes': [],
                'dependencies': []
            }
        }

        # Find relevant elements for this step
        if qa_elements:
            action = processed_step['Test Steps']
            expected = processed_step['Expected Result']
            relevant_elements = filter_elements_for_step(qa_elements, action, expected)
            processed_step['_metadata']['matched_elements'] = relevant_elements

            # Update step context for next steps
            step_context["previous_steps"].append({
                "step_num": processed_step['Step No'],
                "action": action,
                "expected": expected,
                "matched_elements": [el.get("name", "") for el in relevant_elements[:3]]
            })

        # Add step to processed test case
        processed_test_case['Steps'].append(processed_step)

    return {
        'valid': True,
        'test_case': processed_test_case
    }

def filter_elements_for_step(elements, action, expected, top_n=5):
    """
    Filter UI elements to those most relevant for the given test step.

    Args:
        elements (list): List of UI elements
        action (str): Test step action
        expected (str): Expected result
        top_n (int, optional): Maximum number of elements to return

    Returns:
        list: List of relevant elements
    """
    def tokenize(text):
        """Convert text to lowercase tokens without punctuation"""
        return re.findall(r'\w+', (text or '').lower())

    # Extract tokens from action and expected
    action_tokens = set(tokenize(action))
    expected_tokens = set(tokenize(expected))
    all_tokens = action_tokens | expected_tokens

    # Common action keywords to look for
    action_keywords = {
        'click': ['click', 'press', 'select', 'choose', 'tap'],
        'input': ['enter', 'type', 'input', 'fill', 'write'],
        'verify': ['verify', 'check', 'confirm', 'validate', 'ensure'],
        'select': ['select', 'choose', 'pick'],
        'wait': ['wait', 'until', 'appears', 'loads']
    }

    # Identify likely action type
    action_type = None
    for action_type_name, keywords in action_keywords.items():
        if any(keyword in action.lower() for keyword in keywords):
            action_type = action_type_name
            break

    def element_score(element):
        """Calculate relevance score for an element"""
        attrs = element.get('attributes', {})
        score = 0

        # Check element attributes against tokens
        for key in ['tag', 'type', 'name', 'placeholder', 'value', 'text', 'role', 'id', 'class']:
            val = (attrs.get(key) or '').lower()
            if not val:
                continue

            val_tokens = set(tokenize(val))

            # Score for token overlap
            overlap = all_tokens & val_tokens
            score += len(overlap) * 3

            # Score for partial string matches
            for token in all_tokens:
                if token and val:
                    ratio = SequenceMatcher(None, token, val).ratio()
                    if ratio > 0.7:
                        score += ratio * 2

        # Boost score based on element type and likely action
        tag = attrs.get('tag', '').lower()
        element_type = attrs.get('type', '').lower()

        if action_type == 'click' and (tag in ['button', 'a'] or element_type in ['button', 'submit']):
            score += 5
        elif action_type == 'input' and (tag in ['input', 'textarea'] or element_type in ['text', 'password', 'email']):
            score += 5
        elif action_type == 'select' and tag == 'select':
            score += 5

        # Boost score for visible elements
        if attrs.get('visible', False):
            score += 2

        # Boost for importance score if available
        score += element.get('importance_score', 0) * 0.5

        return score

    # Score all elements
    scored_elements = [(element_score(element), element) for element in elements]

    # Sort by score descending
    scored_elements.sort(reverse=True, key=lambda x: x[0])

    # Take top N elements with positive scores, or fallback to top N
    relevant_elements = [element for score, element in scored_elements if score > 0][:top_n]
    if not relevant_elements:
        relevant_elements = [element for _, element in scored_elements][:top_n]

    return relevant_elements

def filter_qa_relevant_elements(elements, locator_strategy=None, locator_value=None):
    """
    Filter elements to focus on those most relevant for QA automation testing.

    Args:
        elements (list): List of detected UI elements
        locator_strategy (str, optional): Locator strategy from step table (css, xpath, id, name, etc.)
        locator_value (str, optional): Specific locator value to match

    Returns:
        list: List of QA automation-relevant elements
    """
    print(f"\n=== QA RELEVANCE FILTER CALLED ===")
    print(f"Starting with {len(elements)} total UI elements")
    if locator_strategy:
        print(f"Using locator strategy: {locator_strategy}" + (f" with value: {locator_value}" if locator_value else ""))

    qa_relevant_elements = []

    # If we have a specific locator strategy and value from the step table,
    # prioritize elements that match this strategy
    if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', ''] and locator_value:
        # Map locator strategies to element attributes
        strategy_to_attribute = {
            'id': 'id',
            'name': 'name',
            'css': 'selector',
            'xpath': 'xpath',
            'tag': 'tag',
            'class': 'class',
            'link_text': 'text',
            'partial_link_text': 'text',
            'aria': 'role'
        }

        # Get the attribute to match based on the locator strategy
        attribute = strategy_to_attribute.get(locator_strategy.lower())

        if attribute:
            # First pass: find elements that exactly match the locator strategy and value
            for element in elements:
                attrs = element.get('attributes', {})

                # Add a flag to indicate if this element matches the locator strategy
                element['locator_strategy_match'] = False
                element['locator_strategy_score'] = 0.0

                # Check if the element matches the locator strategy
                if attribute == 'selector':
                    # For CSS selectors, we can't do an exact match here
                    # Just mark it for consideration
                    element['locator_strategy_match'] = True
                    element['locator_strategy_score'] = 0.8
                elif attribute == 'xpath':
                    # For XPath selectors, we can't do an exact match here
                    # Just mark it for consideration
                    element['locator_strategy_match'] = True
                    element['locator_strategy_score'] = 0.8
                else:
                    # For other attributes, check for an exact match
                    attr_value = attrs.get(attribute, '')
                    if attr_value:
                        if locator_strategy.lower() == 'partial_link_text':
                            if locator_value.lower() in attr_value.lower():
                                element['locator_strategy_match'] = True
                                element['locator_strategy_score'] = 0.9
                        else:
                            if attr_value.lower() == locator_value.lower():
                                element['locator_strategy_match'] = True
                                element['locator_strategy_score'] = 1.0

            # Add all elements with locator_strategy_match=True to the relevant elements list first
            matched_elements = [e for e in elements if e.get('locator_strategy_match', False)]
            if matched_elements:
                print(f"Found {len(matched_elements)} elements matching locator strategy {locator_strategy}")
                # Sort by locator_strategy_score
                matched_elements.sort(key=lambda x: x.get('locator_strategy_score', 0), reverse=True)
                qa_relevant_elements.extend(matched_elements)

                # If we found exact matches, we can return early
                if any(e.get('locator_strategy_score', 0) == 1.0 for e in matched_elements):
                    print(f"Found exact match for locator strategy {locator_strategy} with value {locator_value}")
                    return qa_relevant_elements

    # Important element types for QA automation
    qa_priority_elements = {
        # Form elements (highest priority for automation)
        'input': 15,
        'button': 15,
        'submit': 15,
        'select': 14,
        'option': 13,
        'textarea': 13,
        'checkbox': 14,
        'radio': 14,

        # Navigation elements (high priority)
        'a': 12,
        'link': 12,
        'nav': 11,
        'menu': 11,
        'tab': 12,

        # Structural/container elements
        'form': 10,
        'table': 9,
        'tr': 8,
        'td': 7,
        'div': 5,
        'span': 5
    }

    # Common automation-specific attributes
    qa_important_attributes = [
        'id', 'name', 'type', 'value', 'href', 'for',
        'role', 'aria-label', 'aria-labelledby', 'data-testid',
        'placeholder', 'title', 'alt'
    ]

    # Map locator strategies to element attributes
    strategy_to_attribute = {
        'id': 'id',
        'name': 'name',
        'css': 'selector',
        'xpath': 'xpath',
        'tag': 'tag',
        'class': 'class',
        'link_text': 'text',
        'partial_link_text': 'text',
        'aria': 'role'
    }

    # Identify duplicate selectors
    seen_selectors = set()

    for element in elements:
        # Skip if we've seen this selector before
        if element['selector'] in seen_selectors:
            continue

        attrs = element.get('attributes', {})
        tag = attrs.get('tag', '').lower()
        element_type = attrs.get('type', '').lower()

        # Calculate QA relevance score
        qa_score = 0

        # Base score from tag type or element type
        qa_score += qa_priority_elements.get(tag, 0)
        qa_score += qa_priority_elements.get(element_type, 0)

        # Boost score for elements with automation-friendly attributes
        for attr in qa_important_attributes:
            if attrs.get(attr):
                qa_score += 2

        # Boost score for visible elements (crucial for automation)
        if attrs.get('visible', False):
            qa_score += 5

        # Boost for elements with labels or text (important for identification)
        if attrs.get('text'):
            qa_score += 3

        # Boost for elements with unique IDs (ideal for automation selectors)
        if attrs.get('id'):
            qa_score += 4

        # Add importance score if available from element detection phase
        qa_score += element.get('importance_score', 0) * 0.7

        # Add locator strategy score if available
        qa_score += element.get('locator_strategy_score', 0) * 1.5

        # Boost score for elements matching the locator strategy
        if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
            attribute = strategy_to_attribute.get(locator_strategy.lower())

            if attribute:
                # Check if the element has the attribute corresponding to the locator strategy
                if attribute == 'selector':
                    # For CSS selectors, all elements have a selector
                    qa_score += 10
                elif attribute == 'xpath':
                    # For XPath, we don't have direct info, so boost all elements
                    qa_score += 5
                else:
                    # For other attributes, check if the element has the attribute
                    if element.get('attributes', {}).get(attribute):
                        qa_score += 10

                        # If a specific locator value is provided, check if it matches
                        if locator_value:
                            attr_value = element.get('attributes', {}).get(attribute, '').lower()
                            locator_value = locator_value.lower()

                            # For partial matches like partial_link_text
                            if locator_strategy.lower() == 'partial_link_text':
                                if locator_value in attr_value:
                                    qa_score += 15
                            else:
                                if attr_value == locator_value:
                                    qa_score += 15

        # Only include elements with a sufficient QA relevance score
        if qa_score >= 8:  # Higher threshold for QA automation relevance
            element['qa_relevance_score'] = qa_score
            qa_relevant_elements.append(element)
            seen_selectors.add(element['selector'])

    # Sort by QA relevance score
    qa_relevant_elements.sort(key=lambda x: x.get('qa_relevance_score', 0), reverse=True)

    print(f"QA relevance filtering complete: Returning {len(qa_relevant_elements)} QA-relevant elements")
    print(f"=== QA RELEVANCE FILTER COMPLETE ===\n")
    return qa_relevant_elements

# Keep the original function for backward compatibility
def create_element_matching_prompt(tc_id, step_num, action, expected, elements):
    """
    Create a prompt for matching UI elements to a test step (original version).

    Args:
        tc_id (str): Test case ID
        step_num (str): Step number
        action (str): Test step action
        expected (str): Expected result
        elements (list): List of UI elements

    Returns:
        str: Prompt for LLM
    """
    return f"""
    Analyze this test step and find matching UI elements if any are needed. If no UI element is required (e.g., navigation or page presence), return an empty matches list.
    Test Case: {tc_id}
    Step {step_num}:
    Action: {action}
    Expected: {expected}
    Available UI Elements (JSON array):
    {json.dumps(elements, indent=2)}
    For each relevant UI element, determine:
    1. How well it matches the step requirements (confidence score 0-1)
    2. What action should be performed (click, input, verify, clear, etc.)
    3. Any specific data or values needed
    Return ONLY valid JSON in this format (no explanation, no markdown):
    {{
        "matches": [
            {{
                "element": {{element object}},
                "score": float,
                "action": string,
                "data": string or null
            }}
        ]
    }}
    """



def filter_matches_by_action(step_matches, action_type):
    """
    Given a list of LLM-matched elements for a step, return only those with the specified action_type (e.g., 'input', 'click', 'verify').
    """
    return [m for m in (step_matches or []) if m.get('action') == action_type]

def get_primary_input_element(step_matches):
    """
    Return the single most relevant input element for a step (or None).
    """
    input_elements = filter_matches_by_action(step_matches, 'input')
    return input_elements[0] if input_elements else None

def get_primary_verification_element(step_matches):
    """
    Return the single most relevant verification/assertion element for a step (or None).
    """
    verify_elements = filter_matches_by_action(step_matches, 'verify')
    return verify_elements[0] if verify_elements else None

# Example usage in downstream code:
# input_elements = filter_matches_by_action(step_matches, 'input')
# click_elements = filter_matches_by_action(step_matches, 'click')
# verify_elements = filter_matches_by_action(step_matches, 'verify')
#
# Use only input_elements for test data generation, etc.
