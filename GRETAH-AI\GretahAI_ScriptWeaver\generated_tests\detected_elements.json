[{"name": "email_field", "selector_type": "css", "selector": "#email", "attributes": {"id": "email", "name": "login_email", "class": "hasHelp  validateEmpty   ", "type": "email", "placeholder": "Email or mobile number", "tag": "input"}}, {"name": "btnnext_button", "selector_type": "css", "selector": "#btnNext", "attributes": {"id": "btnNext", "name": "btnNext", "class": "button actionContinue scTrack:unifiedlogin-login-click-next", "type": "submit", "value": "Next", "text": "Next", "tag": "button"}}, {"name": "createaccount_button", "selector_type": "css", "selector": "#createAccount", "attributes": {"id": "createAccount", "class": "button secondary scTrack:unifiedlogin-click-signup-button", "type": "button", "text": "Sign Up", "tag": "button"}}, {"name": "forgotemail_link", "selector_type": "css", "selector": "#forgotEmail", "attributes": {"id": "forgotEmail", "class": "recoveryOption", "text": "Forgot email?", "tag": "a"}}, {"name": "english_link", "selector_type": "xpath", "selector": "//*[@id=\"content\"]/div[2]/div[1]/ul[1]/li[1]/a[1]", "attributes": {"class": "selected scTrack:unifiedlogin-footer-language_en_US", "text": "English", "tag": "a"}}, {"name": "français_link", "selector_type": "xpath", "selector": "//*[@id=\"content\"]/div[2]/div[1]/ul[1]/li[2]/a[1]", "attributes": {"class": " scTrack:unifiedlogin-footer-language_fr_XC", "text": "Français", "tag": "a"}}, {"name": "español_link", "selector_type": "xpath", "selector": "//*[@id=\"content\"]/div[2]/div[1]/ul[1]/li[3]/a[1]", "attributes": {"class": " scTrack:unifiedlogin-footer-language_es_XC", "text": "Español", "tag": "a"}}, {"name": "中文_link", "selector_type": "xpath", "selector": "//*[@id=\"content\"]/div[2]/div[1]/ul[1]/li[4]/a[1]", "attributes": {"class": " scTrack:unifiedlogin-footer-language_zh_XC", "text": "中文", "tag": "a"}}, {"name": "contact_us_link", "selector_type": "xpath", "selector": "//*[@id=\"main\"]/footer[1]/div[1]/ul[1]/li[1]/a[1]", "attributes": {"text": "Contact Us", "tag": "a"}}, {"name": "privacy_link", "selector_type": "xpath", "selector": "//*[@id=\"main\"]/footer[1]/div[1]/ul[1]/li[2]/a[1]", "attributes": {"text": "Privacy", "tag": "a"}}, {"name": "legal_link", "selector_type": "xpath", "selector": "//*[@id=\"main\"]/footer[1]/div[1]/ul[1]/li[3]/a[1]", "attributes": {"text": "Legal", "tag": "a"}}, {"name": "policy_updates_link", "selector_type": "xpath", "selector": "//*[@id=\"main\"]/footer[1]/div[1]/ul[1]/li[4]/a[1]", "attributes": {"text": "Policy Updates", "tag": "a"}}, {"name": "worldwide_link", "selector_type": "xpath", "selector": "//*[@id=\"main\"]/footer[1]/div[1]/ul[1]/li[5]/a[1]", "attributes": {"text": "Worldwide", "tag": "a"}}, {"name": "login_form", "selector_type": "css", "selector": "[name='login']", "attributes": {"name": "login", "class": "proceed maskable", "text": "Email or mobile number\nForgot email?\nNext", "tag": "form"}}, {"name": "email_or_mobile_number", "selector_type": "xpath", "selector": "//*[@id=\"login_emaildiv\"]/div[1]/label[1]", "attributes": {"class": "<PERSON><PERSON><PERSON><PERSON>", "text": "Email or mobile number", "tag": "label"}}, {"name": "main", "selector_type": "css", "selector": "#main", "attributes": {"id": "main", "class": "main", "role": "main", "text": "Log in to your PayPal account\nEmail or mobile number\nForgot email?\nNext\nor\nSign Up\nEnglishFrançaisEspañol中文\nContact UsPrivacyLegalPolicy UpdatesWorldwide", "tag": "div"}}, {"name": "content", "selector_type": "css", "selector": "#content", "attributes": {"id": "content", "class": "contentContainer activeContent contentContainerBordered", "text": "Log in to your PayPal account\nEmail or mobile number\nForgot email?\nNext\nor\nSign Up\nEnglishFrançaisEspañol中文", "tag": "div"}}, {"name": "logincontent", "selector_type": "css", "selector": "#loginContent", "attributes": {"id": "loginContent", "text": "Email or mobile number\nForgot email?\nNext\nor\nSign Up", "tag": "div"}}, {"name": "loginsection", "selector_type": "css", "selector": "#loginSection", "attributes": {"id": "loginSection", "text": "Email or mobile number\nForgot email?\nNext\nor\nSign Up", "tag": "div"}}, {"name": "splitemail", "selector_type": "css", "selector": "#splitEmail", "attributes": {"id": "splitEmail", "class": "splitEmail ", "text": "Email or mobile number\nForgot email?\nNext", "tag": "div"}}, {"name": "splitemailsection", "selector_type": "css", "selector": "#splitEmailSection", "attributes": {"id": "splitEmailSection", "class": "splitPhoneSection splitEmailSection adjustSection", "text": "Email or mobile number\nForgot email?", "tag": "div"}}, {"name": "loginemaildiv", "selector_type": "css", "selector": "#login_emaildiv", "attributes": {"id": "login_emaildiv", "class": "textInput ", "text": "Email or mobile number", "tag": "div"}}, {"name": "signupcontainer", "selector_type": "css", "selector": "#signupContainer", "attributes": {"id": "signupContainer", "class": "signupContainer  ", "text": "or\nSign Up", "tag": "div"}}, {"name": "div_3582", "selector_type": "xpath", "selector": "//*[@id=\"login\"]/div[1]", "attributes": {"class": "corral ", "text": "Log in to your PayPal account\nEmail or mobile number\nForgot email?\nNext\nor\nSign Up\nEnglishFrançaisEspañol中文", "tag": "div"}}, {"name": "email_or_mobile_number", "selector_type": "xpath", "selector": "//*[@id=\"login_emaildiv\"]/div[1]", "attributes": {"class": "fieldWrapper", "text": "Email or mobile number", "tag": "div"}}, {"name": "next", "selector_type": "xpath", "selector": "//*[@id=\"splitEmail\"]/div[2]", "attributes": {"class": "actions", "text": "Next", "tag": "div"}}, {"name": "or", "selector_type": "xpath", "selector": "//*[@id=\"signupContainer\"]/div[1]", "attributes": {"class": "loginSignUpSeparator ", "text": "or", "tag": "div"}}, {"name": "englishfrançaisespañol中文", "selector_type": "xpath", "selector": "//*[@id=\"content\"]/div[2]", "attributes": {"class": "intent<PERSON><PERSON>er ", "text": "EnglishFrançaisEspañol中文", "tag": "div"}}, {"name": "englishfrançaisespañol中文", "selector_type": "xpath", "selector": "//*[@id=\"content\"]/div[2]/div[1]", "attributes": {"class": "localeSelector  ", "text": "EnglishFrançaisEspañol中文", "tag": "div"}}, {"name": "contact_usprivacylegalpolicy_u", "selector_type": "xpath", "selector": "//*[@id=\"main\"]/footer[1]/div[1]", "attributes": {"class": "legalFooter", "text": "Contact UsPrivacyLegalPolicy UpdatesWorldwide", "tag": "div"}}, {"name": "login", "selector_type": "css", "selector": "#login", "attributes": {"id": "login", "class": "login  ", "text": "Log in to your PayPal account\nEmail or mobile number\nForgot email?\nNext\nor\nSign Up\nEnglishFrançaisEspañol中文", "tag": "section"}}, {"name": "header", "selector_type": "css", "selector": "#header", "attributes": {"id": "header", "tag": "header"}}, {"name": "contact_usprivacylegalpolicy_u", "selector_type": "xpath", "selector": "//*[@id=\"main\"]/footer[1]", "attributes": {"class": "footer footer<PERSON><PERSON>y<PERSON><PERSON>", "role": "contentinfo", "text": "Contact UsPrivacyLegalPolicy UpdatesWorldwide", "tag": "footer"}}, {"name": "paypal_logo", "selector_type": "xpath", "selector": "//*[@id=\"header\"]/p[1]", "attributes": {"class": "paypal-logo paypal-logo-long signin-paypal-logo", "aria-label": "PayPal Logo", "role": "img", "tag": "p"}}, {"name": "email_field_alt", "selector_type": "css", "selector": "input[name=\"email\"]", "attributes": {"name": "email", "tag": "input", "type": "email", "text": "Email field"}}, {"name": "username_field", "selector_type": "css", "selector": "#username", "attributes": {"id": "username", "tag": "input", "type": "text", "text": "Username field"}}, {"name": "password_field", "selector_type": "css", "selector": "#password", "attributes": {"id": "password", "tag": "input", "type": "password", "text": "Password field"}}, {"name": "password_field_alt", "selector_type": "css", "selector": "input[name=\"password\"]", "attributes": {"name": "password", "tag": "input", "type": "password", "text": "Password field"}}, {"name": "login_button", "selector_type": "css", "selector": "button[type=\"submit\"]", "attributes": {"tag": "button", "type": "submit", "text": "Login button"}}, {"name": "login_button_alt", "selector_type": "css", "selector": "#btnLogin", "attributes": {"id": "btnLogin", "tag": "button", "type": "submit", "text": "Login button"}}, {"name": "initial_login_context", "selector_type": "css", "selector": "#initialSplitLoginContext", "attributes": {"id": "initialSplitLoginContext", "tag": "div", "text": "Login context"}}, {"name": "signup_button", "selector_type": "css", "selector": "a[data-name=\"signup-button\"]", "attributes": {"tag": "a", "text": "Sign Up button"}}, {"name": "forgot_password_link", "selector_type": "css", "selector": "a[data-name=\"forgotPassword\"]", "attributes": {"tag": "a", "text": "Forgot password link"}}, {"name": "remember_device_checkbox", "selector_type": "css", "selector": "input[type=\"checkbox\"]", "attributes": {"tag": "input", "type": "checkbox", "text": "Remember device checkbox"}}, {"name": "error_message", "selector_type": "css", "selector": ".error-message", "attributes": {"tag": "div", "class": "error-message", "text": "Error message"}}, {"name": "login_page", "selector_type": "css", "selector": "body", "attributes": {"tag": "body", "text": "Login page"}}, {"name": "countrypickerlink_button", "selector_type": "xpath", "selector": "//*[@id=\"content\"]/div[2]/div[1]/span[1]/button[1]", "attributes": {"class": "country US", "type": "button", "aria-label": "countryPickerLink", "tag": "button"}}]