"""
Stage 10 UI Components Aggregator for GretahAI ScriptWeaver

This module serves as the main aggregator for all Stage 10 UI components.
It imports and re-exports all functions from the modular component files
to maintain backward compatibility and provide a single import point.

Refactored from a single 1,856-line file into logical modules:
- stage10_template_components.py: Template selection and display
- stage10_gap_analysis_components.py: AI gap analysis and filling
- stage10_script_generation_components.py: Script generation controls
- stage10_execution_components.py: Pytest execution and results
- stage10_failure_analysis_components.py: Failure analysis and regeneration
- stage10_navigation_components.py: Navigation and footer components
"""

# Import all functions from modular components
from .stage10_template_components import (
    render_empty_playground_message,
    render_no_test_cases_message,
    render_template_selection_interface,
    render_test_case_selection_interface
)

from .stage10_gap_analysis_components import (
    render_gap_analysis_interface,
    render_gap_filling_form,
    render_gap_handling_options
)

from .stage10_script_generation_components import (
    render_script_generation_controls,
    render_generation_success_display
)

from .stage10_execution_components import (
    render_script_execution_section_header,
    render_script_info_card,
    render_execution_controls_header,
    render_verbose_mode_checkbox,
    render_execution_status_indicator,
    render_execution_action_buttons,
    render_execution_results_header,
    render_execution_results_summary,
    render_execution_metrics_header,
    render_junit_metrics_grid,
    render_execution_output_section,
    render_execution_artifacts_section
)

from .stage10_failure_analysis_components import (
    render_failure_analysis_button,
    render_failure_analysis_results,
    render_regeneration_options
)

from .stage10_navigation_components import (
    render_stage10_footer,
    render_workflow_navigation
)

# Re-export all functions to maintain backward compatibility
__all__ = [
    # Template components
    "render_empty_playground_message",
    "render_no_test_cases_message",
    "render_template_selection_interface",
    "render_test_case_selection_interface",

    # Gap analysis components
    "render_gap_analysis_interface",
    "render_gap_filling_form",
    "render_gap_handling_options",

    # Script generation components
    "render_script_generation_controls",
    "render_generation_success_display",

    # Execution components
    "render_script_execution_section_header",
    "render_script_info_card",
    "render_execution_controls_header",
    "render_verbose_mode_checkbox",
    "render_execution_status_indicator",
    "render_execution_action_buttons",
    "render_execution_results_header",
    "render_execution_results_summary",
    "render_execution_metrics_header",
    "render_junit_metrics_grid",
    "render_execution_output_section",
    "render_execution_artifacts_section",

    # Failure analysis components
    "render_failure_analysis_button",
    "render_failure_analysis_results",
    "render_regeneration_options",

    # Navigation components
    "render_stage10_footer",
    "render_workflow_navigation"
]
