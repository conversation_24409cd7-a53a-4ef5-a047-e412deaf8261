#!/usr/bin/env python3
"""
Minimal test to check basic imports without Streamlit dependencies.
"""

def test_basic_imports():
    """Test basic Python imports without Streamlit."""
    try:
        print("Testing basic imports...")
        
        # Test debug_utils
        print("1. Testing debug_utils...")
        import debug_utils
        print("   ✅ debug_utils OK")
        
        # Test if we can import the modules without executing Streamlit code
        print("2. Testing module syntax...")
        import ast
        
        # Check template components syntax
        with open('ui_components/stage10_template_components.py', 'r') as f:
            ast.parse(f.read())
        print("   ✅ Template components syntax OK")
        
        # Check gap analysis components syntax
        with open('ui_components/stage10_gap_analysis_components.py', 'r') as f:
            ast.parse(f.read())
        print("   ✅ Gap analysis components syntax OK")
        
        # Check script generation components syntax
        with open('ui_components/stage10_script_generation_components.py', 'r') as f:
            ast.parse(f.read())
        print("   ✅ Script generation components syntax OK")
        
        # Check execution components syntax
        with open('ui_components/stage10_execution_components.py', 'r') as f:
            ast.parse(f.read())
        print("   ✅ Execution components syntax OK")
        
        # Check failure analysis components syntax
        with open('ui_components/stage10_failure_analysis_components.py', 'r') as f:
            ast.parse(f.read())
        print("   ✅ Failure analysis components syntax OK")
        
        # Check navigation components syntax
        with open('ui_components/stage10_navigation_components.py', 'r') as f:
            ast.parse(f.read())
        print("   ✅ Navigation components syntax OK")
        
        # Check aggregator syntax
        with open('ui_components/stage10_components.py', 'r') as f:
            ast.parse(f.read())
        print("   ✅ Aggregator syntax OK")
        
        print("\n🎉 All syntax checks passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Minimal Syntax Test")
    print("=" * 50)
    
    if test_basic_imports():
        print("\n✅ Refactoring syntax is correct!")
        print("The issue might be with runtime dependencies.")
    else:
        print("\n❌ Syntax errors detected!")
    
    print("=" * 50)
