"""
Script Management Mixin for GretahAI ScriptWeaver State Management.

This module contains the script management functionality extracted from StateManager
as part of Phase 4 of the parallel refactoring approach using mixin pattern.

The ScriptManagementMixin provides methods for:
- Script continuity tracking and management
- Script history management and persistence
- Manual script editing and reversion
- Script file path synchronization
- Script storage initialization and operations
- Script statistics and metadata management

This mixin is designed to be used with the StateManager class to provide
script management capabilities while maintaining clean separation of concerns.

© 2025 Cogniron All Rights Reserved.
"""

import os
import re
import time
from typing import Any, Dict, List, Optional
from datetime import datetime

# Import structured debug function for GRETAH logging compliance
from debug_utils import debug


class ScriptManagementMixin:
    """
    Mixin class providing script management functionality for StateManager.
    
    This mixin contains all methods related to script generation, editing,
    history tracking, and persistent storage management.
    
    The mixin assumes the following state fields exist in the implementing class:
    - script_imports: List[str]
    - script_fixtures: List[str] 
    - script_variables: Dict[str, str]
    - script_functions: Dict[str, str]
    - browser_initialized: bool
    - previous_scripts: Dict[str, str]
    - combined_script_content: Optional[str]
    - script_manually_edited: bool
    - original_ai_script_content: str
    - manual_edit_timestamp: Optional[datetime]
    - script_edit_mode: bool
    - script_history: List[Dict[str, Any]]
    - script_metadata: Dict[str, Dict[str, Any]]
    - _script_storage: Any
    - generated_script_path: Optional[str]
    - last_script_file: str
    - last_script_content: str
    - selected_test_case: Optional[Dict[str, Any]]
    - selected_step_table_entry: Optional[Dict[str, Any]]
    """

    def update_script_continuity(self, script: str, step_no: str):
        """
        Store the generated (or merged) script and extract key elements for continuity.

        Args:
            script: The script content to analyze and store
            step_no: The step number this script belongs to
        """
        debug(f"Updating script continuity for step {step_no}",
              stage="state_management", operation="script_continuity",
              context={'step_no': step_no, 'script_length': len(script)})

        # Store the script content
        self.combined_script_content = script
        self.previous_scripts[step_no] = script

        # Extract imports
        import_pattern = r'^(import\s+\w+|from\s+\w+\s+import\s+.+)$'
        imports = re.findall(import_pattern, script, re.MULTILINE)
        for imp in imports:
            if imp not in self.script_imports:
                self.script_imports.append(imp)

        # Extract fixture definitions
        fixture_pattern = r'@pytest\.fixture[^\n]*\ndef\s+(\w+)\([^)]*\):'
        fixtures = re.findall(fixture_pattern, script, re.MULTILINE)
        for fixture in fixtures:
            if fixture not in self.script_fixtures:
                self.script_fixtures.append(fixture)

        # Extract function definitions (excluding test functions and fixtures)
        function_pattern = r'def\s+(\w+)\([^)]*\):'
        functions = re.findall(function_pattern, script, re.MULTILINE)
        for func in functions:
            if not func.startswith('test_') and func not in self.script_fixtures:
                # Store the full function definition
                func_match = re.search(rf'def\s+{func}\([^)]*\):.*?(?=\ndef|\Z)', script, re.DOTALL)
                if func_match:
                    self.script_functions[func] = func_match.group(0)

        # Extract variable assignments (simple pattern)
        var_pattern = r'^(\w+)\s*=\s*(.+)$'
        variables = re.findall(var_pattern, script, re.MULTILINE)
        for var_name, var_value in variables:
            if not var_name.startswith('test_') and var_name not in ['driver', 'browser']:
                self.script_variables[var_name] = var_value

        # Check if browser is initialized in this script
        if 'webdriver' in script.lower() or 'driver' in script.lower():
            self.browser_initialized = True

        debug(f"Script continuity updated for step {step_no}",
              stage="state_management", operation="script_continuity",
              context={'step_no': step_no, 'script_length': len(script),
                      'total_imports': len(self.script_imports), 'total_fixtures': len(self.script_fixtures),
                      'total_functions': len(self.script_functions), 'total_variables': len(self.script_variables)})

        return True

    def add_script_to_history(self, script_content: str, script_type: str, step_no: str = None, file_path: str = None, metadata: Dict[str, Any] = None):
        """
        Add a script to the script history for Stage 9 browsing.

        Args:
            script_content: The script content
            script_type: Type of script (e.g., 'generated', 'manual_edit', 'optimized')
            step_no: Step number (optional)
            file_path: File path where script is saved (optional)
            metadata: Additional metadata (optional)
        """
        script_entry = {
            'id': f"{script_type}_{int(time.time())}_{len(self.script_history)}",
            'content': script_content,
            'type': script_type,
            'step_no': step_no,
            'file_path': file_path,
            'timestamp': datetime.now().isoformat(),
            'test_case_id': self.selected_test_case.get('Test Case ID', 'unknown') if self.selected_test_case else 'unknown',
            'metadata': metadata or {}
        }

        # Add to history
        self.script_history.append(script_entry)

        # Store metadata separately for quick access
        self.script_metadata[script_entry['id']] = {
            'type': script_type,
            'step_no': step_no,
            'timestamp': script_entry['timestamp'],
            'test_case_id': script_entry['test_case_id'],
            'file_size': len(script_content),
            'line_count': len(script_content.splitlines()),
            'metadata': metadata or {}
        }

        debug("Added script to history",
              stage="state_management", operation="script_history",
              context={'script_type': script_type, 'script_id': script_entry['id'],
                      'step_no': step_no, 'script_length': len(script_content),
                      'test_case_id': script_entry['test_case_id']})

        # Also save to persistent storage
        self._save_to_persistent_storage(script_content, script_type, step_no, file_path, metadata)

    def save_manual_script_edit(self, edited_content: str, reason: str = "Manual edit"):
        """
        Save manually edited script content and update state.

        Args:
            edited_content: The manually edited script content
            reason: Reason for the edit (for logging)
        """
        # Store original AI script if not already stored
        if not self.original_ai_script_content and self.last_script_content:
            self.original_ai_script_content = self.last_script_content

        # Update script content and state
        self.last_script_content = edited_content
        self.script_manually_edited = True
        self.manual_edit_timestamp = datetime.now()

        # Create new file for edited script
        edited_script_file = None
        if self.selected_test_case and self.selected_step_table_entry:
            test_case_id = self.selected_test_case.get('Test Case ID', 'unknown')
            step_no = self.selected_step_table_entry.get('Step', 'unknown')

            script_dir = "generated_tests"
            os.makedirs(script_dir, exist_ok=True)

            # Create filename with manual edit indicator
            edited_script_file = os.path.join(
                script_dir,
                f"test_{test_case_id}_step_{step_no}_manual_edit_{int(time.time())}.py"
            )

            # Save edited script to file
            with open(edited_script_file, "w") as f:
                f.write(edited_content)

            # Update file path in state
            self.last_script_file = edited_script_file
            self.generated_script_path = edited_script_file  # CRITICAL FIX: Update generated_script_path

        # Add to script history
        self.add_script_to_history(
            edited_content,
            "manual_edit",
            step_no=str(self.selected_step_table_entry.get('Step', 'unknown')) if self.selected_step_table_entry else None,
            file_path=edited_script_file,
            metadata={
                'reason': reason,
                'original_ai_script_available': bool(self.original_ai_script_content),
                'edit_timestamp': self.manual_edit_timestamp.isoformat()
            }
        )

        # Update JSON storage with new script path
        json_update_success = False
        if self.selected_step_table_entry:
            try:
                # CRITICAL FIX: Use correct field name for step number
                step_no_str = str(self.selected_step_table_entry.get('step_no',
                                 self.selected_step_table_entry.get('Step', 'unknown')))

                # Update the step data in JSON storage
                self.update_single_step_in_json(step_no_str, {
                    'script_file_path': edited_script_file,
                    '_script_file_path': edited_script_file,  # Also save with underscore for compatibility
                    'script_content': edited_content,
                    'script_manually_edited': True,
                    'manual_edit_timestamp': self.manual_edit_timestamp.isoformat(),
                    'original_ai_script_available': bool(self.original_ai_script_content)
                }, f"manual_script_edit_step_{step_no_str}")
                json_update_success = True
            except Exception as e:
                debug(f"Failed to update JSON storage after manual script edit: {e}",
                      stage="state_management", operation="validation_error",
                      context={'step_no': step_no_str, 'error': str(e)})

        debug("Manual script edit saved",
              stage="state_management", operation="script_editing",
              context={'reason': reason, 'step_no': step_no, 'test_case_id': test_case_id,
                      'script_file': edited_script_file, 'script_length': len(edited_content),
                      'json_update_success': json_update_success,
                      'original_ai_stored': bool(self.original_ai_script_content)})

    def revert_to_ai_script(self):
        """
        Revert manually edited script back to original AI-generated version.
        """
        if not self.script_manually_edited or not self.original_ai_script_content:
            debug("Cannot revert: No manual edits or original AI script not available",
                  stage="state_management", operation="validation_warning")
            return False

        # Restore original AI script
        self.last_script_content = self.original_ai_script_content
        self.script_manually_edited = False
        self.manual_edit_timestamp = None

        # Create new file for reverted script
        original_ai_script_path = None
        if self.selected_test_case and self.selected_step_table_entry:
            test_case_id = self.selected_test_case.get('Test Case ID', 'unknown')
            step_no = self.selected_step_table_entry.get('Step', 'unknown')

            script_dir = "generated_tests"
            os.makedirs(script_dir, exist_ok=True)

            # Create filename indicating revert to AI
            original_ai_script_path = os.path.join(
                script_dir,
                f"test_{test_case_id}_step_{step_no}_ai_reverted_{int(time.time())}.py"
            )

            # Save reverted script to file
            with open(original_ai_script_path, "w") as f:
                f.write(self.original_ai_script_content)

            # Update file paths in state
            self.last_script_file = original_ai_script_path
            self.generated_script_path = original_ai_script_path

        # Add revert action to script history
        self.add_script_to_history(
            self.original_ai_script_content,
            "ai_reverted",
            step_no=str(self.selected_step_table_entry.get('Step', 'unknown')) if self.selected_step_table_entry else None,
            file_path=original_ai_script_path,
            metadata={
                'reverted_from_manual_edit': True,
                'revert_timestamp': datetime.now().isoformat()
            }
        )

        # Update JSON storage with reverted script
        json_update_success = False
        if self.selected_step_table_entry:
            try:
                # CRITICAL FIX: Use correct field name for step number
                step_no_str = str(self.selected_step_table_entry.get('step_no',
                                 self.selected_step_table_entry.get('Step', 'unknown')))

                # Update the step data in JSON storage
                self.update_single_step_in_json(step_no_str, {
                    'script_file_path': original_ai_script_path,
                    '_script_file_path': original_ai_script_path,  # Also save with underscore for compatibility
                    'script_content': self.original_ai_script_content,
                    'script_manually_edited': False,
                    'manual_edit_timestamp': None,
                    'reverted_to_ai': True,
                    'revert_timestamp': datetime.now().isoformat()
                }, f"revert_to_ai_step_{step_no_str}")
                json_update_success = True
            except Exception as e:
                debug(f"Failed to update JSON storage after script revert: {e}",
                      stage="state_management", operation="validation_error",
                      context={'step_no': step_no_str, 'error': str(e)})

        debug("Reverted to original AI script",
              stage="state_management", operation="script_editing",
              context={'step_no': step_no_str, 'original_ai_script_path': original_ai_script_path,
                      'json_update_success': json_update_success,
                      'script_length': len(self.original_ai_script_content)})
        return True

    def toggle_script_edit_mode(self):
        """
        Toggle script edit mode on/off.
        """
        self.script_edit_mode = not self.script_edit_mode

        debug("Script edit mode toggled",
              stage="state_management", operation="script_editing",
              context={'edit_mode': 'ON' if self.script_edit_mode else 'OFF'})

    def get_script_status_info(self):
        """
        Get information about current script status for UI display.

        Returns:
            dict: Script status information including edit status, timestamps, etc.
        """
        status = {
            'is_manually_edited': self.script_manually_edited,
            'has_original_ai_script': bool(self.original_ai_script_content),
            'edit_timestamp': self.manual_edit_timestamp,
            'in_edit_mode': self.script_edit_mode,
            'can_revert': self.script_manually_edited and bool(self.original_ai_script_content)
        }

        return status

    def sync_script_path_for_current_step(self) -> bool:
        """
        Synchronize the generated_script_path with the current step's script file from JSON data.

        This method ensures that state.generated_script_path always points to the correct
        script file for the currently selected step, reading from JSON storage as the
        single source of truth.

        Returns:
            bool: True if synchronization was successful, False otherwise
        """
        debug("=== SYNC SCRIPT PATH DEBUG START ===",
              stage="state_management", operation="sync_debug")

        # Debug: Check if selected_step_table_entry exists
        if not hasattr(self, 'selected_step_table_entry'):
            debug("SYNC FAIL: selected_step_table_entry attribute missing",
                  stage="state_management", operation="validation_warning")
            return False

        if not self.selected_step_table_entry:
            debug("SYNC FAIL: selected_step_table_entry is None/empty",
                  stage="state_management", operation="validation_warning",
                  context={"selected_step_table_entry": self.selected_step_table_entry})
            return False

        debug(f"SYNC DEBUG: selected_step_table_entry exists with keys: {list(self.selected_step_table_entry.keys())}",
              stage="state_management", operation="sync_debug",
              context={"entry_data": self.selected_step_table_entry})

        # CRITICAL FIX: Handle both 'step_no' and 'Step' field names
        step_no = str(self.selected_step_table_entry.get('step_no', ''))
        if not step_no:
            # Fallback to 'Step' for backward compatibility
            step_no = str(self.selected_step_table_entry.get('Step', ''))

        if not step_no:
            debug("SYNC FAIL: no step number in selected entry (checked both 'step_no' and 'Step')",
                  stage="state_management", operation="validation_warning",
                  context={"selected_step_table_entry": self.selected_step_table_entry,
                          "available_keys": list(self.selected_step_table_entry.keys())})
            return False

        debug(f"SYNC DEBUG: step_no extracted: '{step_no}'",
              stage="state_management", operation="sync_debug")

        # Get step data from JSON storage
        step_data = self.get_step_data_from_json(step_no)
        if not step_data:
            debug(f"SYNC FAIL: No JSON data found for step {step_no}",
                  stage="state_management", operation="validation_warning",
                  context={"step_no": step_no})
            return False

        debug(f"SYNC DEBUG: JSON step_data found with keys: {list(step_data.keys())}",
              stage="state_management", operation="sync_debug",
              context={"step_no": step_no, "step_data": step_data})

        # Get script file path from JSON data (check both field names)
        script_file_path = step_data.get('script_file_path') or step_data.get('_script_file_path')
        if not script_file_path:
            debug(f"SYNC FAIL: No script file path in JSON data for step {step_no}",
                  stage="state_management", operation="validation_warning",
                  context={"step_no": step_no, "step_data_keys": list(step_data.keys()),
                          "step_data": step_data,
                          "checked_fields": ["script_file_path", "_script_file_path"]})
            return False

        debug(f"SYNC DEBUG: script_file_path found: '{script_file_path}'",
              stage="state_management", operation="sync_debug")

        # Check if file exists
        import os
        file_exists = os.path.exists(script_file_path)
        debug(f"SYNC DEBUG: script file exists check: {file_exists}",
              stage="state_management", operation="sync_debug",
              context={"script_file_path": script_file_path, "file_exists": file_exists})

        if not file_exists:
            debug(f"SYNC FAIL: Script file does not exist: {script_file_path}",
                  stage="state_management", operation="validation_warning",
                  context={"step_no": step_no, "script_file_path": script_file_path})
            return False

        # Update the generated_script_path if it's different
        old_generated_script_path = getattr(self, 'generated_script_path', '')
        if old_generated_script_path != script_file_path:
            self.generated_script_path = script_file_path
            debug(f"SYNC SUCCESS: generated_script_path synchronized for step {step_no}",
                  stage="state_management",
                  operation="state_change",
                  context={"step_no": step_no, "old_path": old_generated_script_path, "new_path": script_file_path})

            # Also update last_script_file for consistency
            self.last_script_file = script_file_path
            debug(f"SYNC SUCCESS: last_script_file synchronized: {script_file_path}",
                  stage="state_management",
                  operation="state_change",
                  context={"step_no": step_no, "script_file_path": script_file_path})
        else:
            debug(f"SYNC SUCCESS: script path already synchronized for step {step_no}",
                  stage="state_management", operation="sync_debug",
                  context={"script_file_path": script_file_path})

        debug("=== SYNC SCRIPT PATH DEBUG END ===",
              stage="state_management", operation="sync_debug")
        return True

    def _init_script_storage(self):
        """Initialize persistent script storage."""
        try:
            from core.script_storage import get_script_storage, ScriptStorage

            # Get the storage instance
            self._script_storage = get_script_storage()

            # Validate storage is working
            if self._script_storage is None:
                debug("Script storage initialization failed: storage instance is None",
                      stage="script_storage",
                      operation="validation_error",
                      context={"error_type": "NoneStorage"})
                return

            # Test basic operations
            try:
                # Try to get scripts (this will create tables if they don't exist)
                scripts = self._script_storage.get_all_scripts()
                debug(f"Script storage initialized successfully",
                      stage="script_storage",
                      operation="initialization",
                      context={"scripts_count": len(scripts) if scripts else 0})

                # Load any existing historical scripts
                self._load_historical_scripts()

            except Exception as storage_error:
                debug(f"Script storage validation failed: {storage_error}",
                      stage="script_storage",
                      operation="validation_error",
                      context={"error_type": "StorageValidation", "error": str(storage_error)})
                self._script_storage = None

        except ImportError as e:
            debug(f"Failed to import script storage: {e}",
                  stage="script_storage",
                  operation="validation_error",
                  context={"error_type": "ImportError", "error": str(e)})
            self._script_storage = None
        except Exception as e:
            debug(f"Failed to initialize script storage: {e}",
                  stage="script_storage",
                  operation="validation_error",
                  context={"error_type": "Exception", "error": str(e)})
            self._script_storage = None

    def _save_to_persistent_storage(self, script_content: str, script_type: str, step_no: str = None,
                                   file_path: str = None, metadata: Dict[str, Any] = None):
        """Save script to persistent storage."""
        if self._script_storage is None:
            return

        try:
            test_case_id = self.selected_test_case.get('Test Case ID', 'unknown') if self.selected_test_case else 'unknown'

            self._script_storage.save_script(
                script_content=script_content,
                script_type=script_type,
                test_case_id=test_case_id,
                step_no=step_no,
                file_path=file_path,
                metadata=metadata or {}
            )

            debug(f"Saved script to persistent storage",
                  stage="script_storage",
                  operation="save_script",
                  context={"script_type": script_type, "step_no": step_no, "test_case_id": test_case_id})

        except Exception as e:
            debug(f"Failed to save script to persistent storage: {e}",
                  stage="script_storage",
                  operation="validation_error",
                  context={"script_type": script_type, "step_no": step_no, "error": str(e)})

    def _load_historical_scripts(self):
        """Load historical scripts from persistent storage."""
        if self._script_storage is None:
            return

        try:
            # Get all scripts from storage
            all_scripts = self._script_storage.get_all_scripts()

            # Convert to our format and add to history
            for script_data in all_scripts:
                script_entry = {
                    'id': f"historical_{script_data.get('id', int(time.time()))}",
                    'content': script_data.get('script_content', ''),
                    'type': script_data.get('script_type', 'unknown'),
                    'step_no': script_data.get('step_no'),
                    'file_path': script_data.get('file_path'),
                    'timestamp': script_data.get('created_at', datetime.now().isoformat()),
                    'test_case_id': script_data.get('test_case_id', 'unknown'),
                    'metadata': script_data.get('metadata', {})
                }

                # Add to history if not already present
                if not any(existing['id'] == script_entry['id'] for existing in self.script_history):
                    self.script_history.append(script_entry)

                    # Store metadata
                    self.script_metadata[script_entry['id']] = {
                        'type': script_entry['type'],
                        'step_no': script_entry['step_no'],
                        'timestamp': script_entry['timestamp'],
                        'test_case_id': script_entry['test_case_id'],
                        'file_size': len(script_entry['content']),
                        'line_count': len(script_entry['content'].splitlines()),
                        'metadata': script_entry['metadata']
                    }

            debug(f"Loaded {len(all_scripts)} historical scripts from persistent storage",
                  stage="script_storage",
                  operation="load_historical",
                  context={"scripts_loaded": len(all_scripts)})

        except Exception as e:
            debug(f"Failed to load historical scripts: {e}",
                  stage="script_storage",
                  operation="validation_error",
                  context={"operation_type": "load_historical", "error": str(e)})

    def get_all_scripts_with_history(self) -> List[Dict[str, Any]]:
        """
        Get all scripts including historical ones from persistent storage.

        Returns:
            List of all scripts with complete history
        """
        try:
            if self._script_storage:
                self._load_historical_scripts()
            return self.script_history
        except Exception as e:
            debug(f"Failed to get all scripts with history: {e}",
                  stage="script_storage",
                  operation="validation_error",
                  context={"operation_type": "get_all_scripts", "error": str(e)})
            return self.script_history

    def get_script_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about all scripts including historical ones.

        Returns:
            Dictionary containing script statistics
        """
        try:
            if self._script_storage:
                return self._script_storage.get_script_statistics()
            else:
                # Fallback to in-memory statistics
                from core.script_browser_helpers import get_script_statistics
                return get_script_statistics(self.script_history)
        except Exception as e:
            debug(f"Failed to get script statistics: {e}",
                  stage="script_storage",
                  operation="validation_error",
                  context={"operation_type": "get_script_statistics", "error": str(e)})
            # Fallback to in-memory statistics
            from core.script_browser_helpers import get_script_statistics
            return get_script_statistics(self.script_history)

    def clear_all_script_history(self, confirm: bool = False, reason: str = "") -> bool:
        """
        Clear ALL script history from both in-memory storage and persistent database.

        This method permanently deletes all scripts, metadata, and sessions.
        This action cannot be undone.

        Args:
            confirm: Must be True to actually perform the deletion
            reason: Reason for clearing (for logging)

        Returns:
            bool: True if clearing was successful, False otherwise
        """
        if not confirm:
            debug("Script history clear cancelled: confirmation required",
                  stage="script_storage", operation="validation_warning",
                  context={"reason": reason})
            return False

        try:
            # Clear persistent storage
            persistent_count = 0
            if self._script_storage:
                persistent_count = self._script_storage.clear_all_scripts()
                debug(f"Cleared persistent script storage: {persistent_count} scripts deleted",
                      stage="script_storage", operation="clear_all",
                      context={"reason": reason, "persistent_scripts_deleted": persistent_count})

            # Clear in-memory storage
            in_memory_count = len(self.script_history)
            self.script_history.clear()
            self.script_metadata.clear()

            debug(f"Cleared all script history: {in_memory_count} in-memory scripts, {persistent_count} persistent scripts",
                  stage="script_storage", operation="clear_all",
                  context={"in_memory_scripts_removed": in_memory_count,
                          "persistent_scripts_removed": persistent_count,
                          "total_scripts_removed": in_memory_count + persistent_count,
                          "reason": reason})

            return True

        except Exception as e:
            debug(f"Failed to clear script history: {e}",
                  stage="script_storage", operation="validation_error",
                  context={"reason": reason, "error": str(e), "error_type": type(e).__name__})
            # Try to provide more specific error information
            if "clear_all_data" in str(e):
                debug("Error suggests missing clear_all_data method - should use clear_all_scripts instead",
                      stage="script_storage", operation="method_error",
                      context={"suggested_fix": "Use clear_all_scripts() instead of clear_all_data()"})
            return False
