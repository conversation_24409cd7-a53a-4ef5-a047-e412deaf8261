#!/usr/bin/env python3

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import os
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import db_helper as db  # Import database helper
import io
from helpers import create_formatted_excel_from_scenarios, export_test_cases_to_csv, format_csv_for_external_tools

def render_visualization(current_user):
    """
    Render the visualization tab for test analysis.

    Args:
        current_user (str): The current user's username
    """
    st.subheader("Test Case Analytics Dashboard")
    st.info("Visualize your test case data to gain insights into your testing coverage and distribution.")

    # Initialize session state for visualization data
    if "viz_data" not in st.session_state:
        st.session_state["viz_data"] = pd.DataFrame()
    if "viz_data_source" not in st.session_state:
        st.session_state["viz_data_source"] = None
    if "viz_selected_jira_id" not in st.session_state:
        st.session_state["viz_selected_jira_id"] = None
    if "viz_selected_run_id" not in st.session_state:
        st.session_state["viz_selected_run_id"] = None

    # Check if the database exists
    if os.path.exists(db.DATABASE_PATH):
        # Get data from database
        try:
            # Get unique JIRA IDs
            jira_ids = db.get_unique_jira_ids(db.DATABASE_PATH, user_name=current_user)

            # Get test runs
            try:
                test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
                if test_runs is None:
                    test_runs = []
            except Exception as e:
                st.error(f"Error retrieving test runs: {str(e)}")
                test_runs = []

            # If no data found, show a helpful message
            if not jira_ids and not test_runs:
                st.warning("No test data found for your user. Please generate some test cases first.")
                st.info("Go to the Test Generator tab to create test cases, then come back to this tab to visualize them.")
                return
        except Exception as e:
            st.error(f"Error testing database functions: {str(e)}")
            st.info("There might be an issue with the database. Please check the database connection and try again.")
            return
    else:
        st.error(f"Database not found at {db.DATABASE_PATH}")
        return

    # Create a selection interface for data source
    st.markdown("### 📊 Select Data Source")

    # Create tabs for different data sources
    data_source_tabs = st.tabs(["By JIRA ID", "By Test Run", "All Data"])

    # Initialize variables
    df_all = st.session_state["viz_data"]
    selected_data_source = st.session_state["viz_data_source"]

    # Tab 1: By JIRA ID
    with data_source_tabs[0]:
        # Get unique JIRA IDs from the database
        jira_ids = db.get_unique_jira_ids(db.DATABASE_PATH, user_name=current_user)

        if jira_ids:
            # Create a selectbox for JIRA IDs
            selected_jira_id = st.selectbox(
                "Select JIRA ID",
                options=jira_ids,
                index=jira_ids.index(st.session_state["viz_selected_jira_id"]) if st.session_state["viz_selected_jira_id"] in jira_ids else 0,
                key="viz_jira_id_select"
            )

            # Add a test type filter
            test_type_options = ["All Types", "positive", "negative", "security", "performance"]
            selected_test_type = st.selectbox(
                "Select Test Type",
                options=test_type_options,
                key="viz_test_type_select"
            )

            # Apply button
            if st.button("Show Visualizations for Selected JIRA ID", key="viz_jira_apply", use_container_width=True):
                with st.spinner("Loading data..."):
                    # Get test cases for the selected JIRA ID with proper filtering
                    if selected_test_type == "All Types":
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            test_type="all",
                            user_name=current_user
                        )
                    else:
                        # Get specific test type for this JIRA ID
                        test_cases_data = db.get_test_cases_by_filters(
                            db.DATABASE_PATH,
                            jira_id=selected_jira_id,
                            test_type=selected_test_type,
                            user_name=current_user
                        )

                    if not test_cases_data.empty:
                        # Store in session state
                        st.session_state["viz_data"] = test_cases_data
                        st.session_state["viz_data_source"] = "jira"
                        st.session_state["viz_selected_jira_id"] = selected_jira_id
                        df_all = test_cases_data
                        selected_data_source = "jira"
                        
                        # Show success message with run information
                        if "Test Run ID" in df_all.columns:
                            unique_run_ids = df_all["Test Run ID"].dropna().unique()
                            # Removed success message about loaded rows from test runs
                        else:
                            # Removed success message about loaded rows for JIRA ID
                            pass
                    else:
                        if selected_test_type == "All Types":
                            st.error(f"No test cases found for JIRA ID: {selected_jira_id} with test_type='all'")
                            st.info("Make sure you have test runs with test_type='all' in your database for this JIRA ID.")
                        else:
                            st.error(f"No test cases found for JIRA ID: {selected_jira_id} with test type: {selected_test_type}")
        else:
            st.warning("No JIRA IDs found in the database. Generate some test cases first.")

    # Tab 2: By Test Run
    with data_source_tabs[1]:
        # Get all test runs for the current user
        try:
            test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)
            if test_runs is None:
                test_runs = []
        except Exception as e:
            st.error(f"Error retrieving test runs: {str(e)}")
            test_runs = []

        if test_runs:
            # Create a dataframe for better display
            test_runs_df = pd.DataFrame(test_runs)

            # Add a timestamp column if it exists
            if 'timestamp' in test_runs_df.columns:
                test_runs_df['timestamp'] = pd.to_datetime(test_runs_df['timestamp'])
                test_runs_df = test_runs_df.sort_values('timestamp', ascending=False)

            # Format the dataframe for display
            display_runs = test_runs_df[['id', 'jira_id', 'test_type', 'timestamp', 'num_test_cases', 'status', 'notes']].copy()

            # Format the timestamp
            if 'timestamp' in display_runs.columns:
                display_runs['timestamp'] = pd.to_datetime(display_runs['timestamp']).dt.strftime("%b %d, %Y %H:%M")

            # Rename columns for better display
            display_runs.columns = ['Run ID', 'JIRA ID', 'Test Type', 'Timestamp', '# Test Cases', 'Status', 'Notes']

            # Convert test type to uppercase
            display_runs['Test Type'] = display_runs['Test Type'].str.upper()

            # --- Always show metrics for the currently selected run (or zeros if none) ---
            # Determine selected run
            run_options = [
                f"Run {run['id']}: {run['jira_id']} - {run['test_type'].upper()} ({run['timestamp'].strftime('%b %d, %Y %H:%M') if isinstance(run['timestamp'], pd.Timestamp) else run['timestamp']})"
                for _, run in test_runs_df.iterrows()
            ]
            default_index = 0
            if st.session_state["viz_selected_run_id"] is not None:
                for i, option in enumerate(run_options):
                    if f"Run {st.session_state['viz_selected_run_id']}:" in option:
                        default_index = i
                        break
            selected_run_option = st.selectbox(
                "Select Test Run",
                options=run_options,
                index=default_index,
                key="viz_test_run_select"
            )
            selected_run_id = None
            if selected_run_option:
                selected_run_id = int(selected_run_option.split(":")[0].replace("Run ", "").strip())

            # --- Metrics Card Section ---
            st.markdown("#### 🧮 Test Run Metrics")
            pass_count = fail_count = not_run_count = total_test_cases = 0
            if st.session_state.get("viz_data_source") == "run" and st.session_state.get("viz_selected_run_id") == selected_run_id:
                test_cases_df = st.session_state["viz_data"]
                if "Test Case ID" in test_cases_df.columns:
                    test_case_rows = test_cases_df[
                        test_cases_df["Test Case ID"].notna() &
                        (test_cases_df["Test Case ID"].astype(str).str.strip() != "") &
                        (test_cases_df["Test Case ID"].astype(str) != "")
                    ]
                    unique_test_case_ids = test_case_rows["Test Case ID"].unique()
                    total_test_cases = len(unique_test_case_ids)
                else:
                    total_test_cases = 0
                    test_case_rows = pd.DataFrame()
                if "Test Status" in test_cases_df.columns and total_test_cases > 0:
                    test_case_statuses = test_case_rows.groupby("Test Case ID")["Test Status"].first().fillna("")
                    pass_count = (test_case_statuses.str.upper() == "PASS").sum()
                    fail_count = (test_case_statuses.str.upper() == "FAIL").sum()
                    not_run_count = total_test_cases - pass_count - fail_count
                else:
                    pass_count = 0
                    fail_count = 0
                    not_run_count = total_test_cases
            metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)
            with metric_col1:
                st.metric("Pass", pass_count)
            with metric_col2:
                st.metric("Fail", fail_count)
            with metric_col3:
                st.metric("Not Run", not_run_count)
            with metric_col4:
                st.metric("Total Test Cases", total_test_cases)

            # Add expander for the test runs table
            with st.expander("Expand to View Test Runs Table 📋", expanded=False):
                st.dataframe(
                    display_runs,
                    use_container_width=True,
                    column_config={
                        "Run ID": st.column_config.NumberColumn("Run ID", width="small"),
                        "JIRA ID": st.column_config.TextColumn("JIRA ID", width="small"),
                        "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                        "Timestamp": st.column_config.TextColumn("Created", width="medium"),
                        "# Test Cases": st.column_config.NumberColumn("# Test Cases", width="small"),
                        "Status": st.column_config.TextColumn("Status", width="small"),
                        "Notes": st.column_config.TextColumn("Notes", width="medium")
                    },
                    hide_index=True
                )

            # Format the timestamp for better readability
            if 'timestamp' in test_runs_df.columns:
                test_runs_df['formatted_timestamp'] = pd.to_datetime(test_runs_df['timestamp']).dt.strftime("%b %d, %Y %H:%M")
            else:
                test_runs_df['formatted_timestamp'] = "Unknown"

            # Apply button
            if st.button("Show Visualizations for Selected Test Run", key="viz_run_apply", use_container_width=True):
                with st.spinner("Loading data..."):
                    # Get test cases for the selected test run
                    test_cases_data = db.get_test_cases_by_test_run(db.DATABASE_PATH, selected_run_id)

                    if (isinstance(test_cases_data, list) and len(test_cases_data) > 0) or (hasattr(test_cases_data, 'empty') and not test_cases_data.empty):
                        # Convert to DataFrame if needed
                        if isinstance(test_cases_data, list):
                            test_cases_df = pd.DataFrame(test_cases_data)
                        else:
                            test_cases_df = test_cases_data
                        # Store in session state
                        st.session_state["viz_data"] = test_cases_df
                        st.session_state["viz_data_source"] = "run"
                        st.session_state["viz_selected_run_id"] = selected_run_id
                        df_all = test_cases_df
                        selected_data_source = "run"
                        st.success(f"Loaded {len(df_all)} test cases for Test Run ID: {selected_run_id}")
                    else:
                        st.error(f"No test cases found for Test Run ID: {selected_run_id}")
        st.markdown("This view shows visualizations for all your test cases across all JIRA IDs and test runs.")

        # Add date range filter
        default_start_date = datetime.now() - timedelta(days=90)
        default_end_date = datetime.now()
        date_range = st.date_input(
            "Date Range",
            value=(default_start_date, default_end_date),
            key="viz_all_date_range"
        )

        # Apply button
        if st.button("Show Visualizations for All Data", key="viz_all_apply", use_container_width=True):
            with st.spinner("Loading all test case data..."):
                # Convert date range to string format
                start_date = date_range[0].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 0 else None
                end_date = date_range[1].strftime("%Y-%m-%d") if isinstance(date_range, tuple) and len(date_range) > 1 else None

                # Get all test cases within the date range
                all_data = db.get_test_cases_by_filters(
                    db.DATABASE_PATH,
                    start_date=start_date,
                    end_date=end_date,
                    user_name=current_user
                )

                if not all_data.empty:
                    # Store in session state
                    st.session_state["viz_data"] = all_data
                    st.session_state["viz_data_source"] = "all"
                    df_all = all_data
                    selected_data_source = "all"
                    st.success(f"Loaded {len(df_all)} test cases from {start_date} to {end_date}")
                else:
                    st.error("No test cases found for the selected date range.")

    # Add a divider
    st.markdown("---")

    # Check if we have data to visualize (either freshly loaded or from session state)
    if not df_all.empty:
        # Display summary metrics at the top
        st.markdown("### 📊 Key Metrics")

        # Create metrics row
        metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)

        # Calculate metrics with improved logic to count test cases, not steps
        try:
            # For total test cases - count unique test case IDs only from rows that have them
            if "Test Case ID" in df_all.columns:
                # Get rows where Test Case ID is not empty (these represent actual test cases)
                test_case_rows = df_all[
                    df_all["Test Case ID"].notna() & 
                    (df_all["Test Case ID"].astype(str).str.strip() != "") &
                    (df_all["Test Case ID"].astype(str) != "")
                ]
                
                # Count unique test case IDs (now includes run ID to avoid deduplication)
                unique_test_case_ids = test_case_rows["Test Case ID"].unique()
                total_test_cases = len(unique_test_case_ids)

            else:
                total_test_cases = 0
                test_case_rows = pd.DataFrame()

            # For JIRA IDs - count unique JIRA IDs from test case level only (use correct column name)
            if "JIRA ID" in df_all.columns and total_test_cases > 0:
                # Group by test case ID and get the first JIRA ID for each test case
                test_case_jira_ids = test_case_rows.groupby("Test Case ID")["JIRA ID"].first()
                unique_jira_ids = len(test_case_jira_ids.dropna().unique())
            else:
                unique_jira_ids = 0

            # For test status metrics - use test case level data only
            if "Test Status" in df_all.columns and total_test_cases > 0:
                # Group by test case ID and get the first status for each test case
                test_case_statuses = test_case_rows.groupby("Test Case ID")["Test Status"].first()
                                
                # Count passed test cases (case-insensitive)
                pass_count = len(test_case_statuses[
                    test_case_statuses.str.upper().str.contains("PASS", na=False)
                ])
                
                # Calculate pass rate
                pass_rate = int((pass_count / total_test_cases) * 100) if total_test_cases > 0 else 0
                
            else:
                pass_count = 0
                pass_rate = 0

            # For priority metrics - use test case level data only
            if "Priority" in df_all.columns and total_test_cases > 0:
                # Group by test case ID and get the first priority for each test case
                test_case_priorities = test_case_rows.groupby("Test Case ID")["Priority"].first()
                                
                # Count high priority test cases (case-insensitive)
                high_priority = len(test_case_priorities[
                    test_case_priorities.str.upper().str.contains("HIGH", na=False)
                ])
                
            else:
                high_priority = 0

        except Exception as e:
            st.error(f"Error calculating metrics: {str(e)}")
            total_test_cases = 0
            unique_jira_ids = 0
            pass_count = 0
            pass_rate = 0
            high_priority = 0

        # Display metrics
        with metric_col1:
            st.metric("Total Test Cases", total_test_cases)

        with metric_col2:
            st.metric("Pass Rate", f"{pass_rate}%", delta=f"{pass_count}/{total_test_cases}" if total_test_cases > 0 else None)

        with metric_col3:
            st.metric("High Priority", high_priority)

        with metric_col4:
            st.metric("User Stories", unique_jira_ids)

        # Add context information
        if selected_data_source == "jira":
            st.info(f"Showing visualizations for JIRA ID: **{selected_jira_id}**")
        elif selected_data_source == "run":
            st.info(f"Showing visualizations for Test Run ID: **{selected_run_id}**")
        elif selected_data_source == "all":
            st.info(f"Showing visualizations for all test cases from **{date_range[0].strftime('%Y-%m-%d')}** to **{date_range[1].strftime('%Y-%m-%d')}**")

        # Create tabs for different visualizations
        viz_tabs = st.tabs(["Test Type Distribution", "Test Status", "Priority Distribution"])

        # Tab 1: Test Type Distribution - Fix to count test cases, not steps
        with viz_tabs[0]:
            st.markdown("### Test Case Distribution by Type")

            if "Test Type" in df_all.columns and total_test_cases > 0:
                try:
                    # Use only test case rows for counting
                    test_case_types = test_case_rows.groupby("Test Case ID")["Test Type"].first()
                    
                    # Count test cases by test type
                    test_type_counts = test_case_types.value_counts().reset_index()
                    test_type_counts.columns = ["Test Type", "Count"]
                    
                    # Clean and standardize test type values
                    test_type_counts["Test Type"] = test_type_counts["Test Type"].fillna("Unknown")

                except Exception as e:
                    st.error(f"Error processing test type data: {str(e)}")
                    test_type_counts = pd.DataFrame(columns=["Test Type", "Count"])

                # Define colors for test types
                type_colors = {
                    "positive": "#4CAF50",
                    "negative": "#F44336",
                    "security": "#2196F3",
                    "performance": "#FF9800",
                    "Unknown": "#9E9E9E"
                }

                # Create a pie chart
                fig = px.pie(
                    test_type_counts,
                    values="Count",
                    names="Test Type",
                    title="Test Cases by Type",
                    color="Test Type",
                    color_discrete_map=type_colors,
                    hole=0.4
                )

                # Add hover information and configure proper interaction
                fig.update_traces(
                    textposition='inside',
                    textinfo='percent+label',
                    hoverinfo='label+percent+value',
                    marker=dict(line=dict(color='#FFFFFF', width=2)),
                    hovertemplate='<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percent}<extra></extra>'
                )

                # Improve layout with proper legend interaction for highlighting only
                fig.update_layout(
                    height=500,
                    legend_title_text="Test Types",
                    legend=dict(
                        orientation="h", 
                        yanchor="bottom", 
                        y=1.02, 
                        xanchor="right", 
                        x=1,
                        itemclick=False,  # Disable click interaction
                        itemdoubleclick=False,  # Disable double-click interaction
                        itemsizing="constant"  # Keep legend item size constant
                    ),
                    margin=dict(t=60, b=20, l=20, r=20)
                )

                # Display the chart
                st.plotly_chart(fig, use_container_width=True)
                
            else:
                st.warning("No Test Type data available for visualization.")

        # Tab 2: Test Status
        with viz_tabs[1]:
            st.markdown("### Test Case Status Analysis")

            if "Test Status" in df_all.columns and total_test_cases > 0:
                try:
                    # Use the same logic as Key Metrics - count test cases, not test steps
                    # Use only test case rows (rows with valid Test Case IDs)
                    if "Test Case ID" in df_all.columns:
                        # Get rows where Test Case ID is not empty (these represent actual test cases)
                        test_case_rows_for_status = df_all[
                            df_all["Test Case ID"].notna() &
                            (df_all["Test Case ID"].astype(str).str.strip() != "") &
                            (df_all["Test Case ID"].astype(str) != "")
                        ]

                        # Group by test case ID and get the first status for each test case
                        test_case_statuses = test_case_rows_for_status.groupby("Test Case ID")["Test Status"].first()

                        # Clean and standardize test status values
                        test_case_statuses = test_case_statuses.fillna("Not Run")

                        # Count test cases by status
                        status_counts = test_case_statuses.value_counts().reset_index()
                        status_counts.columns = ["Status", "Count"]
                    else:
                        # Fallback if no Test Case ID column
                        status_counts = pd.DataFrame(columns=["Status", "Count"])

                except Exception as e:
                    st.error(f"Error processing test status data: {str(e)}")
                    # Create an empty DataFrame with the right columns
                    status_counts = pd.DataFrame(columns=["Status", "Count"])

                # Only create chart if we have data
                if not status_counts.empty and status_counts["Count"].sum() > 0:
                    # Define colors for test status
                    status_colors = {
                        "Pass": "#4CAF50",
                        "Fail": "#F44336",
                        "Blocked": "#FF9800",
                        "Not Run": "#9E9E9E"
                    }

                    # Create a bar chart
                    fig = px.bar(
                        status_counts,
                        x="Status",
                        y="Count",
                        color="Status",
                        color_discrete_map=status_colors,
                        title="Test Case Status Distribution"
                    )

                    # Improve layout
                    fig.update_layout(
                        height=400,
                        xaxis_title="Test Status",
                        yaxis_title="Number of Test Cases",
                        legend_title_text="Status",
                        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                    )

                    # Display the chart
                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.warning("No status data available for the selected test cases.")

            else:
                st.warning("No Test Status data available for visualization.")

        # Tab 3: Priority Distribution
        with viz_tabs[2]:
            st.markdown("### Test Case Priority Distribution")

            if "Priority" in df_all.columns and total_test_cases > 0:
                try:
                    # Use the same logic as Key Metrics - count test cases, not test steps
                    # Use only test case rows (rows with valid Test Case IDs)
                    if "Test Case ID" in df_all.columns:
                        # Get rows where Test Case ID is not empty (these represent actual test cases)
                        test_case_rows_for_priority = df_all[
                            df_all["Test Case ID"].notna() &
                            (df_all["Test Case ID"].astype(str).str.strip() != "") &
                            (df_all["Test Case ID"].astype(str) != "")
                        ]

                        # Group by test case ID and get the first priority for each test case
                        test_case_priorities = test_case_rows_for_priority.groupby("Test Case ID")["Priority"].first()

                        # Clean and standardize priority values
                        test_case_priorities = test_case_priorities.fillna("Medium")

                        # Count test cases by priority
                        priority_counts = test_case_priorities.value_counts().reset_index()
                        priority_counts.columns = ["Priority", "Count"]
                    else:
                        # Fallback if no Test Case ID column
                        priority_counts = pd.DataFrame(columns=["Priority", "Count"])

                except Exception as e:
                    st.error(f"Error processing priority data: {str(e)}")
                    priority_counts = pd.DataFrame(columns=["Priority", "Count"])

                # Only create chart if we have data
                if not priority_counts.empty and priority_counts["Count"].sum() > 0:
                    # Define colors for priority levels
                    priority_colors = {"High": "#F44336", "Medium": "#FF9800", "Low": "#4CAF50"}

                    # Create a donut chart
                    fig = px.pie(
                        priority_counts,
                        values="Count",
                        names="Priority",
                        title="Test Case Priority Distribution",
                        color="Priority",
                        color_discrete_map=priority_colors,
                        hole=0.6
                    )

                    # Add hover information
                    fig.update_traces(
                        textposition='inside',
                        textinfo='percent+label',
                        hoverinfo='label+percent+value',
                        marker=dict(line=dict(color='#FFFFFF', width=2))
                    )

                    # Improve layout with disabled legend interactions
                    fig.update_layout(
                        height=500,
                        legend_title_text="Priority",
                        legend=dict(
                            orientation="h", 
                            yanchor="bottom", 
                            y=1.02, 
                            xanchor="right", 
                            x=1,
                            itemclick=False,  # Disable click interaction
                            itemdoubleclick=False,  # Disable double-click interaction
                            itemsizing="constant"  # Keep legend item size constant
                        ),
                        margin=dict(t=60, b=20, l=20, r=20)
                    )

                    # Display the chart
                    st.plotly_chart(fig, use_container_width=True)

                    # Display priority counts as metrics
                    col1, col2, col3 = st.columns(3)

                    # Calculate percentages
                    total = priority_counts["Count"].sum()

                    with col1:
                        high_count = priority_counts[priority_counts["Priority"] == "High"]["Count"].sum() if "High" in priority_counts["Priority"].values else 0
                        high_pct = (high_count / total) * 100 if total > 0 else 0
                        st.metric("High Priority", high_count, f"{high_pct:.1f}%")

                    with col2:
                        medium_count = priority_counts[priority_counts["Priority"] == "Medium"]["Count"].sum() if "Medium" in priority_counts["Priority"].values else 0
                        medium_pct = (medium_count / total) * 100 if total > 0 else 0
                        st.metric("Medium Priority", medium_count, f"{medium_pct:.1f}%")

                    with col3:
                        low_count = priority_counts[priority_counts["Priority"] == "Low"]["Count"].sum() if "Low" in priority_counts["Priority"].values else 0
                        low_pct = (low_count / total) * 100 if total > 0 else 0
                        st.metric("Low Priority", low_count, f"{low_pct:.1f}%")

                    # Add export section similar to test_generator tab
                    st.markdown("---")
                    st.markdown("### 📤 Export Filtered Data")
                    
                    # Create columns for the export section
                    export_col1, export_col2, export_col3 = st.columns([2, 1, 1])
                    
                    with export_col1:
                        # Priority filter dropdown
                        available_priorities = ["All Priorities"] + priority_counts["Priority"].tolist()
                        selected_export_priority = st.selectbox(
                            "Filter by Priority Level",
                            options=available_priorities,
                            key="viz_priority_export_select"
                        )
                    
                    with export_col2:
                        # Export format selection
                        export_format = st.selectbox(
                            "Export Format",
                            options=["Excel", "CSV"],
                            key="viz_priority_export_format"
                        )
                    
                    with export_col3:
                        st.markdown("<br>", unsafe_allow_html=True)  # Add some spacing
                        generate_export = st.button("📥 Generate Export", key="generate_viz_priority_export", use_container_width=True)
                    
                    # Handle export generation
                    if generate_export:
                        try:
                            export_data = df_all.copy()
                            # Apply priority filter if not "All Priorities"
                            if selected_export_priority != "All Priorities":
                                if "Priority" in export_data.columns:
                                    export_data = export_data[
                                        export_data["Priority"].fillna("").str.strip().str.title() == selected_export_priority
                                    ]

                            # --- Convert to test case generator style: step-level, but only first row per test case has metadata ---
                            if "Test Case ID" in export_data.columns:
                                export_data = export_data[
                                    export_data["Test Case ID"].notna() &
                                    (export_data["Test Case ID"].astype(str).str.strip() != "") &
                                    (export_data["Test Case ID"].astype(str) != "")
                                ]
                                # Sort for consistent output
                                export_data = export_data.sort_values(["Test Case ID", "Step No"] if "Step No" in export_data.columns else ["Test Case ID"])
                                # Build rows as in test_generator: all steps, but only first row per test case has metadata
                                column_mapping = {
                                    "User Story ID": "User Story ID",  # JIRA ID mapping
                                    "JIRA ID": "User Story ID",        # Alternative JIRA ID mapping
                                    "Test Case ID": "Test Case ID",
                                    "Test Case Objective": "Test Case Objective", 
                                    "Prerequisite": "Prerequisite",
                                    "Step No": "Step No",
                                    "Test Steps": "Test Steps",
                                    "Expected Result": "Expected Result",
                                    "Actual Result": "Actual Result", 
                                    "Test Status": "Test Status",
                                    "Priority": "Priority",
                                    "Defect ID": "Defect ID",
                                    "Comments": "Comments",
                                    "Test Type": "Test Type",
                                    "Test Group": "Test Group",
                                    "Timestamp": "Timestamp",
                                    "Project": "Project",
                                    "Feature": "Feature"
                                }
                                required_columns = [
                                    "Timestamp", "Project", "Feature", "User Story ID", "Test Case ID",
                                    "Test Case Objective", "Prerequisite", "Step No", "Test Steps", 
                                    "Expected Result", "Actual Result", "Test Status", "Priority",
                                    "Defect ID", "Comments", "Test Type"
                                ]
                                structured_data = []
                                # Re-number Test Case IDs in TC_001, TC_002... style for export, matching test case generator
                                test_case_id_map = {}
                                tc_counter = 1
                                for test_case_id, group in export_data.groupby("Test Case ID"):
                                    group_sorted = group.sort_values("Step No") if "Step No" in group.columns else group
                                    # Assign new formatted ID
                                    formatted_id = f"TC_{tc_counter:03d}"
                                    test_case_id_map[test_case_id] = formatted_id
                                    tc_counter += 1
                                    first_step = True
                                    for _, row in group_sorted.iterrows():
                                        structured_row = {}
                                        if first_step:
                                            for viz_col, gen_col in column_mapping.items():
                                                if viz_col == "Test Case ID":
                                                    structured_row[gen_col] = formatted_id
                                                elif viz_col in export_data.columns:
                                                    structured_row[gen_col] = row.get(viz_col, "")
                                                else:
                                                    structured_row[gen_col] = ""
                                            if not structured_row.get("User Story ID", ""):
                                                structured_row["User Story ID"] = row.get("JIRA ID", row.get("jira_id", ""))
                                            first_step = False
                                        else:
                                            for col in required_columns:
                                                if col == "Test Case ID":
                                                    structured_row[col] = ""
                                                elif col in ["Step No", "Test Steps", "Expected Result", "Actual Result"]:
                                                    structured_row[col] = row.get(col, "")
                                                else:
                                                    structured_row[col] = ""
                                        structured_data.append(structured_row)
                                export_data = pd.DataFrame(structured_data)
                                for col in required_columns:
                                    if col not in export_data.columns:
                                        export_data[col] = ""
                                export_data = export_data[required_columns]

                            if not export_data.empty:
                                from pathlib import Path
                                temp_dir = Path("Test_cases") / "visualization_exports"
                                temp_dir.mkdir(parents=True, exist_ok=True)
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                if selected_export_priority == "All Priorities":
                                    filename_suffix = "all_priorities"
                                else:
                                    filename_suffix = f"{selected_export_priority.lower()}_priority"
                                current_export_format = export_format
                                if current_export_format == "Excel":
                                    temp_filename = f"visualization_export_{filename_suffix}_{timestamp}.xlsx"
                                    temp_path = temp_dir / temp_filename
                                    create_formatted_excel_from_scenarios(
                                        export_data,
                                        str(temp_path),
                                        is_dataframe=True,
                                        save_to_db=False,
                                        test_type="filtered",
                                        create_excel=True
                                    )
                                    with open(temp_path, "rb") as f:
                                        file_content = f.read()
                                    mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                else:
                                    temp_filename = f"visualization_export_{filename_suffix}_{timestamp}.csv"
                                    csv_df = format_csv_for_external_tools(export_data)
                                    file_content = csv_df.to_csv(index=False).encode('utf-8')
                                    mime_type = "text/csv"
                                st.session_state["viz_export_ready"] = True
                                st.session_state["viz_export_content"] = file_content
                                st.session_state["viz_export_name"] = temp_filename
                                st.session_state["viz_export_type"] = current_export_format
                                st.session_state["viz_export_mime_type"] = mime_type
                                unique_test_cases = 0
                                if "Test Case ID" in export_data.columns:
                                    test_case_rows = export_data[
                                        export_data["Test Case ID"].notna() & 
                                        (export_data["Test Case ID"].astype(str).str.strip() != "") &
                                        (export_data["Test Case ID"].astype(str) != "")
                                    ]
                                    unique_test_cases = len(test_case_rows["Test Case ID"].unique())
                                else:
                                    unique_test_cases = len(export_data)
                                st.success(f"✅ {current_export_format} export prepared! {unique_test_cases} unique test cases ready for download.")
                            else:
                                st.warning(f"⚠️ No test cases found with {selected_export_priority} priority.")
                        except Exception as e:
                            st.error(f"❌ Error preparing {export_format} export: {str(e)}")
                            print(f"Export error details: {e}")
                            import traceback
                            print(f"Full traceback: {traceback.format_exc()}")
                    
                    # Show download buttons if export data is ready (same pattern as test generator)
                    if st.session_state.get("viz_export_ready", False) and st.session_state.get("viz_export_content") is not None:
                        file_content = st.session_state["viz_export_content"]
                        filename = st.session_state.get("viz_export_name", "export.xlsx")
                        format_type = st.session_state.get("viz_export_type", "Excel")
                        mime_type = st.session_state.get("viz_export_mime_type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                        
                        st.markdown("#### 📥 Download Ready")
                        
                        # Create download button (same as test generator)
                        col1, col2 = st.columns([3, 1])
                        with col1:
                            st.download_button(
                                label=f"💾 Download {format_type} File",
                                data=file_content,
                                file_name=filename,
                                mime=mime_type,
                                key="download_viz_priority_export",
                                use_container_width=True
                            )
                        
                        with col2:
                            # Clear export button (same as test generator)
                            if st.button("🗑️ Clear", key="clear_viz_priority_export", use_container_width=True):
                                # Clear export data from session state
                                for key in ["viz_export_ready", "viz_export_content", "viz_export_name", 
                                          "viz_export_type", "viz_export_mime_type"]:
                                    if key in st.session_state:
                                        del st.session_state[key]
                                st.rerun()

                else:
                    st.warning("No priority data available for the selected test cases.")

            else:
                st.warning("No Priority data available for visualization.")

    else:
        st.info("👆 Please select a data source and click the respective 'Show Visualizations' button to display charts and download options.")
