[tool:pytest]
# Pytest configuration for GretahAI ScriptWeaver
# This configuration ensures consistent test execution across all stages

# Test discovery patterns
testpaths = .
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Required plugins
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --capture=no
    -v

# Register custom markers
markers =
    order: specify test execution order using pytest-order plugin
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    browser: marks tests that require browser automation
    stage1: marks tests for Stage 1 functionality
    stage2: marks tests for Stage 2 functionality
    stage3: marks tests for Stage 3 functionality
    stage4: marks tests for Stage 4 functionality
    stage5: marks tests for Stage 5 functionality
    stage6: marks tests for Stage 6 functionality
    stage7: marks tests for Stage 7 functionality
    stage8: marks tests for Stage 8 functionality
    stage9: marks tests for Stage 9 functionality
    stage10: marks tests for Stage 10 functionality

# Minimum version requirements
minversion = 6.0

# Test output configuration
console_output_style = progress
junit_family = xunit2

# Logging configuration
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d: %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# Warnings configuration
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*pytest.mark.order.*:pytest.PytestUnknownMarkWarning

# Test timeout (in seconds)
timeout = 300

# Parallel execution settings (if pytest-xdist is installed)
# addopts = -n auto

# Coverage settings (if pytest-cov is installed)
# addopts = --cov=. --cov-report=html --cov-report=term-missing
