"""
Enhanced CRUD operations for test cases and test steps in the unified dashboard.

This module provides granular CRUD operations for the unified test cases interface,
including functions for adding individual test cases, adding test steps to existing
test cases, and deleting individual test steps with proper validation and error handling.
"""

import sqlite3
import pandas as pd
from datetime import datetime
from typing import Optional, Tuple, Dict, Any, List
from ..core.decorators import retry_on_db_lock
from ..models.test_cases import get_highest_test_case_id_number
from ..models.jira_issues import get_or_create_jira_issue


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def add_new_test_case(database_path: str, jira_id: str, test_type: str, user_name: str, 
                     project: str = "", feature: str = "", test_case_objective: str = "",
                     prerequisite: str = "", priority: str = "Medium", 
                     test_group: str = "", comments: str = "") -> Tuple[bool, str, Optional[str]]:
    """
    Add a new test case with proper ID generation and database integration.
    
    Args:
        database_path: Path to the SQLite database
        jira_id: JIRA ticket identifier (e.g., "TP-1")
        test_type: Dashboard test type ("positive", "negative", "security", "performance", "all")
        user_name: Name of the user creating the test case
        project: Project name (optional)
        feature: Feature name (optional)
        test_case_objective: Test case objective (optional)
        prerequisite: Prerequisites (optional)
        priority: Priority level (default: "Medium")
        test_group: Test group (optional)
        comments: Comments (optional)
    
    Returns:
        Tuple[bool, str, Optional[str]]: (success, message, test_case_id)
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()
        
        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Get or create JIRA issue
        jira_issue_result = get_or_create_jira_issue(database_path, jira_id)
        if not jira_issue_result:
            conn.rollback()
            return False, f"Failed to get or create JIRA issue for {jira_id}", None
        
        db_jira_id = jira_issue_result[0]
        
        # Generate new test case ID
        highest_id = get_highest_test_case_id_number(database_path, jira_id, "all")
        new_test_case_number = highest_id + 1
        test_case_id = f"TC_{new_test_case_number:03d}"
        
        # Insert the new test case
        cursor.execute('''
            INSERT INTO test_cases (
                jira_id, test_case_id, test_case_objective, prerequisite,
                priority, test_type, test_group, project, feature,
                timestamp, is_latest, dashboard_test_type, user_name, 
                jira_issue_id, is_edited, ai_modified, modification_source
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            jira_id,
            test_case_id,
            test_case_objective,
            prerequisite,
            priority,
            "",  # test_type (legacy field)
            test_group,
            project,
            feature,
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            1,  # is_latest
            test_type.lower(),
            user_name,
            db_jira_id,
            1,  # is_edited (manually added)
            0,  # ai_modified
            'manual'  # modification_source
        ))
        
        # Add a default test step if none provided
        test_case_db_id = cursor.lastrowid
        cursor.execute('''
            INSERT INTO test_steps (
                test_case_id, step_number, test_step, expected_result,
                actual_result, test_status, defect_id, comments, 
                dashboard_test_type, user_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_case_db_id,
            1,  # step_number
            "Enter test step description",  # placeholder
            "Enter expected result",  # placeholder
            "",  # actual_result
            "Not Run",  # test_status
            "",  # defect_id
            comments,
            test_type.lower(),
            user_name
        ))
        
        conn.commit()
        return True, f"Successfully created test case {test_case_id}", test_case_id
        
    except sqlite3.Error as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Database error creating test case: {str(e)}", None
    except Exception as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Error creating test case: {str(e)}", None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def add_test_step_to_case(database_path: str, test_case_id: str, test_step: str = "",
                         expected_result: str = "", user_name: str = "",
                         test_status: str = "Not Run", comments: str = "",
                         insert_position: str = "end") -> Tuple[bool, str]:
    """
    Add a new test step to an existing test case with position control.

    Args:
        database_path: Path to the SQLite database
        test_case_id: Test case ID (e.g., "TC_001")
        test_step: Test step description
        expected_result: Expected result
        user_name: Name of the user adding the step
        test_status: Test status (default: "Not Run")
        comments: Comments
        insert_position: Position to insert ("end", "beginning", or step number like "2")

    Returns:
        Tuple[bool, str]: (success, message)
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()
        
        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Get the database ID for the test case
        cursor.execute("SELECT id, dashboard_test_type FROM test_cases WHERE test_case_id = ?", (test_case_id,))
        result = cursor.fetchone()
        
        if not result:
            conn.rollback()
            return False, f"Test case {test_case_id} not found"
        
        test_case_db_id = result[0]
        dashboard_test_type = result[1] or "positive"
        
        # Determine the step number based on insert position
        if insert_position == "end":
            # Get the next step number (append to end)
            cursor.execute("SELECT MAX(step_number) FROM test_steps WHERE test_case_id = ?", (test_case_db_id,))
            max_step_result = cursor.fetchone()
            step_number = (max_step_result[0] or 0) + 1
        elif insert_position == "beginning":
            # Insert at the beginning (step 1) and shift others
            step_number = 1
            # Shift existing steps up by 1
            cursor.execute('''
                UPDATE test_steps
                SET step_number = step_number + 1
                WHERE test_case_id = ?
            ''', (test_case_db_id,))
        else:
            # Insert at specific position
            try:
                step_number = int(insert_position)
                # Validate the position
                cursor.execute("SELECT MAX(step_number) FROM test_steps WHERE test_case_id = ?", (test_case_db_id,))
                max_step_result = cursor.fetchone()
                max_step = max_step_result[0] or 0

                if step_number < 1:
                    step_number = 1
                elif step_number > max_step + 1:
                    step_number = max_step + 1

                # Shift existing steps at and after this position up by 1
                cursor.execute('''
                    UPDATE test_steps
                    SET step_number = step_number + 1
                    WHERE test_case_id = ? AND step_number >= ?
                ''', (test_case_db_id, step_number))
            except ValueError:
                # Invalid position, default to end
                cursor.execute("SELECT MAX(step_number) FROM test_steps WHERE test_case_id = ?", (test_case_db_id,))
                max_step_result = cursor.fetchone()
                step_number = (max_step_result[0] or 0) + 1
        
        # Insert the new test step
        cursor.execute('''
            INSERT INTO test_steps (
                test_case_id, step_number, test_step, expected_result,
                actual_result, test_status, defect_id, comments,
                dashboard_test_type, user_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_case_db_id,
            step_number,
            test_step or f"Test step {step_number}",
            expected_result or f"Expected result for step {step_number}",
            "",  # actual_result
            test_status,
            "",  # defect_id
            comments,
            dashboard_test_type,
            user_name
        ))

        conn.commit()

        # Create appropriate success message based on insertion position
        if insert_position == "beginning":
            message = f"Successfully inserted step {step_number} at the beginning of test case {test_case_id}"
        elif insert_position == "end":
            message = f"Successfully added step {step_number} to test case {test_case_id}"
        else:
            message = f"Successfully inserted step {step_number} at position {insert_position} in test case {test_case_id}"

        return True, message
        
    except sqlite3.Error as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Database error adding test step: {str(e)}"
    except Exception as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Error adding test step: {str(e)}"
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_test_step(database_path: str, test_case_id: str, step_number: int) -> Tuple[bool, str]:
    """
    Delete a specific test step from a test case.

    Args:
        database_path: Path to the SQLite database
        test_case_id: Test case ID (e.g., "TC_001")
        step_number: Step number to delete

    Returns:
        Tuple[bool, str]: (success, message)
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")

        # Get the database ID for the test case
        cursor.execute("SELECT id FROM test_cases WHERE test_case_id = ?", (test_case_id,))
        result = cursor.fetchone()

        if not result:
            conn.rollback()
            return False, f"Test case {test_case_id} not found"

        test_case_db_id = result[0]

        # Check if the step exists
        cursor.execute("SELECT id FROM test_steps WHERE test_case_id = ? AND step_number = ?",
                      (test_case_db_id, step_number))
        step_result = cursor.fetchone()

        if not step_result:
            conn.rollback()
            return False, f"Step {step_number} not found in test case {test_case_id}"

        # Delete the test step
        cursor.execute("DELETE FROM test_steps WHERE test_case_id = ? AND step_number = ?",
                      (test_case_db_id, step_number))

        if cursor.rowcount == 0:
            conn.rollback()
            return False, f"Failed to delete step {step_number} from test case {test_case_id}"

        # Renumber remaining steps to maintain sequence
        cursor.execute('''
            UPDATE test_steps
            SET step_number = step_number - 1
            WHERE test_case_id = ? AND step_number > ?
        ''', (test_case_db_id, step_number))

        conn.commit()
        return True, f"Successfully deleted step {step_number} from test case {test_case_id}"

    except sqlite3.Error as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Database error deleting test step: {str(e)}"
    except Exception as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Error deleting test step: {str(e)}"
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def delete_test_case_with_confirmation(database_path: str, test_case_id: str) -> Tuple[bool, str, Dict[str, int]]:
    """
    Delete a test case and all its associated test steps with detailed reporting.

    Args:
        database_path: Path to the SQLite database
        test_case_id: Test case ID to delete (e.g., "TC_001")

    Returns:
        Tuple[bool, str, Dict[str, int]]: (success, message, deletion_counts)
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")

        # Get the database ID for the test case
        cursor.execute("SELECT id FROM test_cases WHERE test_case_id = ?", (test_case_id,))
        result = cursor.fetchone()

        if not result:
            conn.rollback()
            return False, f"Test case {test_case_id} not found", {"test_cases": 0, "test_steps": 0}

        test_case_db_id = result[0]

        # Count test steps before deletion
        cursor.execute("SELECT COUNT(*) FROM test_steps WHERE test_case_id = ?", (test_case_db_id,))
        step_count = cursor.fetchone()[0]

        # Delete test steps first (foreign key constraint)
        cursor.execute("DELETE FROM test_steps WHERE test_case_id = ?", (test_case_db_id,))
        deleted_steps = cursor.rowcount

        # Delete the test case
        cursor.execute("DELETE FROM test_cases WHERE id = ?", (test_case_db_id,))
        deleted_cases = cursor.rowcount

        if deleted_cases == 0:
            conn.rollback()
            return False, f"Failed to delete test case {test_case_id}", {"test_cases": 0, "test_steps": 0}

        conn.commit()

        deletion_counts = {"test_cases": deleted_cases, "test_steps": deleted_steps}
        message = f"Successfully deleted test case {test_case_id} with {deleted_steps} test steps"

        return True, message, deletion_counts

    except sqlite3.Error as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Database error deleting test case: {str(e)}", {"test_cases": 0, "test_steps": 0}
    except Exception as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Error deleting test case: {str(e)}", {"test_cases": 0, "test_steps": 0}
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_case_info(database_path: str, test_case_id: str) -> Optional[Dict[str, Any]]:
    """
    Get detailed information about a test case including step count.

    Args:
        database_path: Path to the SQLite database
        test_case_id: Test case ID (e.g., "TC_001")

    Returns:
        Optional[Dict[str, Any]]: Test case information or None if not found
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get test case details
        cursor.execute('''
            SELECT tc.*, COUNT(ts.id) as step_count
            FROM test_cases tc
            LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
            WHERE tc.test_case_id = ?
            GROUP BY tc.id
        ''', (test_case_id,))

        result = cursor.fetchone()
        if not result:
            return None

        return dict(result)

    except sqlite3.Error as e:
        print(f"Database error getting test case info: {str(e)}")
        return None
    except Exception as e:
        print(f"Error getting test case info: {str(e)}")
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


def validate_test_case_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate test case data before database operations.

    Args:
        data: Dictionary containing test case data

    Returns:
        Tuple[bool, List[str]]: (is_valid, list_of_errors)
    """
    errors = []

    # Required fields
    required_fields = ['jira_id', 'test_type', 'user_name']
    for field in required_fields:
        if not data.get(field, '').strip():
            errors.append(f"{field.replace('_', ' ').title()} is required")

    # Validate test_type
    valid_test_types = ['positive', 'negative', 'security', 'performance', 'all']
    if data.get('test_type', '').lower() not in valid_test_types:
        errors.append(f"Test type must be one of: {', '.join(valid_test_types)}")

    # Validate priority
    valid_priorities = ['High', 'Medium', 'Low']
    priority_value = data.get('priority', '').strip()
    if priority_value:  # Only validate if priority is provided
        if priority_value in ['None', 'null', 'NULL']:
            errors.append("Priority cannot be 'None' or null value")
        elif priority_value not in valid_priorities:
            errors.append(f"Priority must be one of: {', '.join(valid_priorities)}")

    return len(errors) == 0, errors


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def get_test_case_for_cloning(database_path: str, test_case_id: str) -> Optional[Dict[str, Any]]:
    """
    Get complete test case data for cloning purposes.

    Args:
        database_path: Path to the SQLite database
        test_case_id: Test case ID to clone (e.g., "TC_001")

    Returns:
        Optional[Dict[str, Any]]: Complete test case data or None if not found
    """
    conn = None
    try:
        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get test case details
        cursor.execute('''
            SELECT tc.*, ji.jira_id as original_jira_id
            FROM test_cases tc
            LEFT JOIN jira_issues ji ON tc.jira_issue_id = ji.id
            WHERE tc.test_case_id = ?
        ''', (test_case_id,))

        result = cursor.fetchone()
        if not result:
            return None

        test_case_data = dict(result)

        # Get all test steps for this test case
        cursor.execute('''
            SELECT step_number, test_step, expected_result, actual_result,
                   test_status, defect_id, comments
            FROM test_steps
            WHERE test_case_id = (SELECT id FROM test_cases WHERE test_case_id = ?)
            ORDER BY step_number
        ''', (test_case_id,))

        steps = []
        for step_row in cursor.fetchall():
            steps.append(dict(step_row))

        test_case_data['test_steps'] = steps

        return test_case_data

    except sqlite3.Error as e:
        print(f"Database error getting test case for cloning: {str(e)}")
        return None
    except Exception as e:
        print(f"Error getting test case for cloning: {str(e)}")
        return None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


@retry_on_db_lock(max_attempts=10, initial_wait=0.2)
def clone_test_case(database_path: str, source_test_case_id: str, new_jira_id: str,
                   user_name: str, modifications: Dict[str, Any] = None) -> Tuple[bool, str, Optional[str]]:
    """
    Clone an existing test case with all its test steps.

    Args:
        database_path: Path to the SQLite database
        source_test_case_id: Test case ID to clone (e.g., "TC_001")
        new_jira_id: JIRA ID for the cloned test case
        user_name: Name of the user creating the clone
        modifications: Optional dictionary of field modifications

    Returns:
        Tuple[bool, str, Optional[str]]: (success, message, new_test_case_id)
    """
    conn = None
    try:
        # Get the source test case data
        source_data = get_test_case_for_cloning(database_path, source_test_case_id)
        if not source_data:
            return False, f"Source test case {source_test_case_id} not found", None

        conn = sqlite3.connect(database_path, timeout=60, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        cursor = conn.cursor()

        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")

        # Get or create JIRA issue
        jira_issue_result = get_or_create_jira_issue(database_path, new_jira_id)
        if not jira_issue_result:
            conn.rollback()
            return False, f"Failed to get or create JIRA issue for {new_jira_id}", None

        db_jira_id = jira_issue_result[0]

        # Generate new test case ID
        highest_id = get_highest_test_case_id_number(database_path, new_jira_id, "all")
        new_test_case_number = highest_id + 1
        new_test_case_id = f"TC_{new_test_case_number:03d}"

        # Apply modifications if provided
        if modifications is None:
            modifications = {}

        # Clone the test case with modifications
        cursor.execute('''
            INSERT INTO test_cases (
                jira_id, test_case_id, test_case_objective, prerequisite,
                priority, test_type, test_group, project, feature,
                timestamp, is_latest, dashboard_test_type, user_name,
                jira_issue_id, is_edited, ai_modified, modification_source
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            new_jira_id,
            new_test_case_id,
            modifications.get('test_case_objective', source_data.get('test_case_objective', '')),
            modifications.get('prerequisite', source_data.get('prerequisite', '')),
            modifications.get('priority', source_data.get('priority', 'Medium')),
            source_data.get('test_type', ''),
            modifications.get('test_group', source_data.get('test_group', '')),
            modifications.get('project', source_data.get('project', '')),
            modifications.get('feature', source_data.get('feature', '')),
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            1,  # is_latest
            source_data.get('dashboard_test_type', 'positive'),
            user_name,
            db_jira_id,
            1,  # is_edited (cloned test case)
            0,  # ai_modified
            'manual'  # modification_source
        ))

        # Get the new test case database ID
        new_test_case_db_id = cursor.lastrowid

        # Clone all test steps
        test_steps = source_data.get('test_steps', [])
        for step in test_steps:
            cursor.execute('''
                INSERT INTO test_steps (
                    test_case_id, step_number, test_step, expected_result,
                    actual_result, test_status, defect_id, comments,
                    dashboard_test_type, user_name
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                new_test_case_db_id,
                step.get('step_number', 1),
                step.get('test_step', ''),
                step.get('expected_result', ''),
                '',  # Reset actual_result for new test case
                'Not Run',  # Reset test_status for new test case
                '',  # Reset defect_id for new test case
                step.get('comments', ''),
                source_data.get('dashboard_test_type', 'positive'),
                user_name
            ))

        conn.commit()

        step_count = len(test_steps)
        return True, f"Successfully cloned {source_test_case_id} to {new_test_case_id} with {step_count} test steps", new_test_case_id

    except sqlite3.Error as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Database error cloning test case: {str(e)}", None
    except Exception as e:
        if conn:
            try:
                conn.rollback()
            except Exception:
                pass
        return False, f"Error cloning test case: {str(e)}", None
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"Error closing database connection: {close_error}")


def validate_test_step_data(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate test step data before database operations.

    Args:
        data: Dictionary containing test step data

    Returns:
        Tuple[bool, List[str]]: (is_valid, list_of_errors)
    """
    errors = []

    # Required fields
    required_fields = ['test_case_id', 'user_name']
    for field in required_fields:
        if not data.get(field, '').strip():
            errors.append(f"{field.replace('_', ' ').title()} is required")

    # Validate test_status
    valid_statuses = ['Not Run', 'Pass', 'Fail', 'Blocked']
    if data.get('test_status') and data['test_status'] not in valid_statuses:
        errors.append(f"Test status must be one of: {', '.join(valid_statuses)}")

    return len(errors) == 0, errors
